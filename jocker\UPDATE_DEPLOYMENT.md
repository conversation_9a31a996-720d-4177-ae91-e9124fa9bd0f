# Jocker 更新部署指南 v2.8.0 (2025-07-29)

## 本次更新内容

### 🔧 修复内容
1. **期刊图标显示问题** - 修复base64图片显示为字符串的问题
2. **图片重新生成功能** - 修复API Key获取问题，使用统一的AI配置
3. **数学公式渲染** - 增强样式和调试功能，添加调试日志
4. **DOI自动分配** - 为新文章自动生成唯一DOI

### 🎉 新功能
5. **Post-Publication Peer Review** - 恶搞同行评审系统
   - 用户可以提交评论（立即通过"审核"）
   - 管理员可以生成AI评论（假的学术评论者）
   - 完整的评论管理系统（支持删除和批量删除）
   - AI评论基于完整文章内容生成，充满emoji和学术讽刺

### 📁 需要上传的文件

#### 前端文件
```
frontend/dist/
├── index.html (5.71 kB) - 包含增强的数学公式CSS
└── assets/
    └── index-BRtdupRJ.js (801.18 kB) - 新增评论系统的JS文件
```

#### 后端文件
```
backend/
├── dist/ (整个编译后的目录) - 包含DOI生成逻辑和评论系统
├── scripts/migrate-doi.js (新增) - DOI迁移脚本
├── package.json
├── prisma/schema.prisma (更新) - 新增评论表
└── prisma/migrations/ (新增) - 数据库迁移文件
```

## 部署步骤

### 1. 上传文件到服务器

使用scp上传文件：

```bash
# 上传前端文件
scp frontend/dist/index.html user@server:/var/www/jocker/jocker/frontend/
scp frontend/dist/assets/index-BRtdupRJ.js user@server:/var/www/jocker/jocker/frontend/assets/

# 上传后端文件
scp -r backend/dist/* user@server:/var/www/jocker/jocker/backend/
scp backend/scripts/migrate-doi.js user@server:/var/www/jocker/jocker/backend/scripts/
scp -r backend/prisma/migrations/* user@server:/var/www/jocker/jocker/backend/prisma/migrations/
```

### 2. 在服务器上运行DOI迁移

```bash
# 连接到服务器
ssh user@server

# 进入后端目录
cd /var/www/jocker/jocker/backend

# 运行DOI迁移脚本（为现有文章分配DOI）
node scripts/migrate-doi.js
```

预期输出：
```
🔄 开始为现有文章分配DOI...
📊 找到 X 篇需要分配DOI的文章
✅ 文章 1 "文章标题" 分配DOI: 10.1000/jocker.2025.07.28.0001.article-title
...
🎉 成功为 X 篇文章分配了DOI
```

### 3. 重启服务

```bash
# 重启后端服务
pm2 restart jocker-backend

# 如果前端也使用PM2，重启前端服务
pm2 restart jocker-frontend
```

## 验证部署

### 1. 检查DOI功能
- 登录管理员账户
- 查看现有文章是否都有DOI
- 创建新文章测试DOI自动分配

### 2. 测试期刊图标
- 进入管理员页面 → 期刊图标管理
- 上传一张图片作为期刊图标
- 检查Header中是否正确显示图片（而不是长串字符）

### 3. 测试图片重新生成
- 找到状态为"失败"的图片
- 点击"重新生成"按钮
- 应该使用你在AI配置中设置的API Key和供应商

### 4. 检查数学公式渲染
- 查看包含数学公式的文章
- 打开浏览器控制台，应该看到类似日志：
  ```
  🔢 检测到 X 个块级公式
  📐 处理第 1 个块级公式: E = mc^2...
  ✅ 完成 X 个块级公式的样式处理
  ✅ KaTeX渲染完成
  ```
- 检查公式是否有红色虚线边框（调试样式）
- 确认公式居中显示并有编号

## 故障排除

### DOI迁移问题
如果迁移脚本报错：
```bash
# 检查数据库连接
cd /var/www/jocker/jocker/backend
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
prisma.article.count()
  .then(count => console.log('文章总数:', count))
  .catch(console.error)
  .finally(() => prisma.\$disconnect());
"
```

### 图片重新生成失败
1. 检查AI配置：管理员页面 → AI配置管理
2. 确认API Key有效且有余额
3. 查看浏览器控制台错误信息

### 数学公式样式问题
1. 检查是否有红色虚线边框（如果没有，说明CSS未加载）
2. 查看浏览器控制台是否有KaTeX相关错误
3. 确认数学公式日志输出正常

## 重要提醒

1. **备份数据库**：部署前建议备份数据库文件
2. **DOI迁移是安全的**：只添加DOI字段，不会修改现有数据
3. **新功能测试**：部署后请测试所有新功能
4. **监控日志**：密切关注错误日志

## 新功能使用说明

### 期刊图标管理
- 位置：管理员页面 → 期刊图标管理
- 支持Emoji和图片上传两种模式
- 图片会自动转换为base64存储

### DOI系统
- 新文章自动分配DOI
- DOI格式：`10.1000/jocker.YYYY.MM.DD.NNNN.title-slug`
- PDF导出时使用DOI作为文件名

### 图片重新生成
- 使用统一的AI配置
- 支持Gemini和OpenAI两种供应商
- 失败时会显示详细错误信息

如果遇到问题，请提供详细的错误日志和控制台输出。
