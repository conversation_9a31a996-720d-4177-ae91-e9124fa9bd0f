"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateImagePrompt = exports.processFigurePlaceholders = exports.updateFigureStatus = void 0;
const database_1 = __importDefault(require("../config/database"));
const updateFigureStatus = async (figureId, status, imageUrl, errorMessage) => {
    try {
        await database_1.default.figure.update({
            where: { id: figureId },
            data: {
                status,
                imageUrl: imageUrl || undefined,
                errorMsg: errorMessage || undefined,
                updatedAt: new Date(),
            },
        });
    }
    catch (error) {
        console.error(`更新图片状态失败 (ID: ${figureId}):`, error);
    }
};
exports.updateFigureStatus = updateFigureStatus;
const processFigurePlaceholders = async (articleId, content, articleTitle, articleCategory) => {
    const figureRegex = /\[Figure (\d+): ([^\]]+)\]/g;
    const figureIds = [];
    let processedContent = content;
    let match;
    while ((match = figureRegex.exec(content)) !== null) {
        const figureNumber = parseInt(match[1]);
        const figureDescription = match[2];
        try {
            const figure = await database_1.default.figure.create({
                data: {
                    article: { connect: { id: articleId } },
                    figureNumber,
                    title: `Figure ${figureNumber}`,
                    description: figureDescription,
                    caption: figureDescription,
                    imagePrompt: figureDescription,
                    status: 'pending',
                },
            });
            figureIds.push(figure.id);
            const placeholder = `[Figure ${figureNumber}: ${figureDescription}]`;
            const replacement = `[Figure ${figureNumber}: ${figureDescription}](#figure-${figure.id})`;
            processedContent = processedContent.replace(placeholder, replacement);
        }
        catch (error) {
            console.error(`创建图片记录失败:`, error);
        }
    }
    return { figureIds, processedContent };
};
exports.processFigurePlaceholders = processFigurePlaceholders;
const generateImagePrompt = async (figureDescription, articleTitle, articleCategory, figureNumber) => {
    throw new Error('图片提示词生成功能已迁移到前端，请使用前端的图片生成功能');
};
exports.generateImagePrompt = generateImagePrompt;
//# sourceMappingURL=figureService.js.map