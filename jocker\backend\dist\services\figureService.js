"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateFigureStatus = exports.getArticleFigures = exports.processFigurePlaceholders = exports.createFigureRecord = exports.generateImagePrompt = exports.extractFigurePlaceholders = void 0;
const genai_1 = require("@google/genai");
const database_1 = __importDefault(require("../config/database"));
const getGeminiConfig = async () => {
    try {
        const userConfig = await database_1.default.geminiConfig.findFirst({
            where: { isActive: true },
            orderBy: { updatedAt: 'desc' },
        });
        if (userConfig) {
            console.log(`🔧 使用用户配置的Gemini设置 - 文本模型: ${userConfig.textModel}`);
            return {
                apiKey: userConfig.apiKey,
                textModel: userConfig.textModel,
                imageModel: userConfig.imageModel,
            };
        }
    }
    catch (error) {
        console.warn('获取用户Gemini配置失败，使用默认配置:', error);
    }
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
        throw new Error('Google AI API Key 未配置');
    }
    console.log('🔧 使用服务器默认Gemini配置 - 文本模型: gemini-2.5-flash-preview-04-17');
    return {
        apiKey,
        textModel: 'gemini-2.5-flash-preview-04-17',
        imageModel: 'imagen-3.0-generate-002',
    };
};
const FIGURE_PLACEHOLDER_REGEX = /\[FIGURE:(\d+):([^\]]+)\]/g;
const extractFigurePlaceholders = (content) => {
    const placeholders = [];
    let match;
    while ((match = FIGURE_PLACEHOLDER_REGEX.exec(content)) !== null) {
        placeholders.push({
            figureNumber: parseInt(match[1]),
            description: match[2].trim(),
            placeholder: match[0],
        });
    }
    return placeholders.sort((a, b) => a.figureNumber - b.figureNumber);
};
exports.extractFigurePlaceholders = extractFigurePlaceholders;
const generateImagePrompt = async (figureDescription, articleTitle, articleCategory, figureNumber) => {
    const geminiConfig = await getGeminiConfig();
    const ai = new genai_1.GoogleGenAI({ apiKey: geminiConfig.apiKey });
    const prompt = `You are an expert at creating detailed prompts for AI image generation for academic papers.

Based on the following information, create a detailed, specific prompt for generating a high-quality academic figure:

Article Title: "${articleTitle}"
Article Category: "${articleCategory}"
Figure Number: ${figureNumber}
Figure Description: "${figureDescription}"

Requirements for the image prompt:
1. The image should look professional and academic
2. Use a clean, scientific illustration style
3. Include appropriate labels, axes, or annotations if it's a chart/graph
4. The style should be suitable for a scientific journal
5. Avoid any text that might be illegible when generated
6. Focus on visual elements rather than text-heavy content

Generate a detailed prompt (100-150 words) that would create a professional academic figure. Start your response with "Create a professional academic illustration showing" and be very specific about visual elements, style, and composition.

IMPORTANT: Return ONLY the image generation prompt, no explanations or additional text.`;
    try {
        console.log(`📝 使用模型 ${geminiConfig.textModel} 生成图片提示词`);
        const response = await ai.models.generateContent({
            model: geminiConfig.textModel,
            contents: prompt,
        });
        const imagePrompt = response.text?.trim() || '';
        if (!imagePrompt) {
            throw new Error('AI 未能生成图片提示词');
        }
        return imagePrompt;
    }
    catch (error) {
        console.error('生成图片提示词失败:', error);
        const fallbackPrompt = `Create a professional academic illustration showing ${figureDescription}. Use a clean, scientific style with clear visual elements. The image should be suitable for publication in a scientific journal, with professional composition and academic aesthetics.`;
        return fallbackPrompt;
    }
};
exports.generateImagePrompt = generateImagePrompt;
const createFigureRecord = async (articleId, figureNumber, description, imagePrompt) => {
    try {
        const caption = `Figure ${figureNumber}: ${description.charAt(0).toUpperCase() + description.slice(1)}.`;
        const figure = await database_1.default.figure.create({
            data: {
                articleId,
                figureNumber,
                title: `Figure ${figureNumber}`,
                description,
                caption,
                imagePrompt,
                status: 'pending',
            },
        });
        return figure.id;
    }
    catch (error) {
        console.error('创建图片记录失败:', error);
        throw new Error('创建图片记录失败');
    }
};
exports.createFigureRecord = createFigureRecord;
const processFigurePlaceholders = async (articleId, content, articleTitle, articleCategory) => {
    const placeholders = (0, exports.extractFigurePlaceholders)(content);
    const figureIds = [];
    let processedContent = content;
    for (const placeholder of placeholders) {
        try {
            const imagePrompt = await (0, exports.generateImagePrompt)(placeholder.description, articleTitle, articleCategory, placeholder.figureNumber);
            const figureId = await (0, exports.createFigureRecord)(articleId, placeholder.figureNumber, placeholder.description, imagePrompt);
            figureIds.push(figureId);
            const figureComponent = `[FIGURE_COMPONENT:${figureId}]`;
            processedContent = processedContent.replace(placeholder.placeholder, figureComponent);
            console.log(`✅ 处理图片 ${placeholder.figureNumber}: ${figureId}`);
        }
        catch (error) {
            console.error(`❌ 处理图片 ${placeholder.figureNumber} 失败:`, error);
            continue;
        }
    }
    return { figureIds, processedContent };
};
exports.processFigurePlaceholders = processFigurePlaceholders;
const getArticleFigures = async (articleId) => {
    return await database_1.default.figure.findMany({
        where: { articleId },
        orderBy: { figureNumber: 'asc' },
    });
};
exports.getArticleFigures = getArticleFigures;
const updateFigureStatus = async (figureId, status, imageUrl, errorMsg) => {
    return await database_1.default.figure.update({
        where: { id: figureId },
        data: {
            status,
            imageUrl: imageUrl || undefined,
            errorMsg: errorMsg || undefined,
            updatedAt: new Date(),
        },
    });
};
exports.updateFigureStatus = updateFigureStatus;
//# sourceMappingURL=figureService.js.map