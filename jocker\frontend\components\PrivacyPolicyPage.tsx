import React from 'react';

interface PrivacyPolicyPageProps {
  onNavigateHome: () => void;
}

export const PrivacyPolicyPage: React.FC<PrivacyPolicyPageProps> = ({ onNavigateHome }) => {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <button
          onClick={onNavigateHome}
          className="mb-8 inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回首页
        </button>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🤫 Privacy Policy</h1>
          <p className="text-xl text-gray-600">We care about your privacy... just kidding</p>
          <div className="mt-4 text-sm text-gray-500">
            Last Updated: July 28, 2025 | Valid Until: We forget about it
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-white rounded-lg shadow-lg p-8 space-y-8">
          
          {/* 开场白 */}
          <section className="border-l-4 border-blue-500 pl-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🍪 Our Privacy Commitment</h2>
            <p className="text-lg text-gray-700 leading-relaxed">
              We don't know who you are. We don't care. But our cookies do. 🍪
            </p>
            <p className="text-gray-600 mt-2 italic">
              "Privacy is like a unicorn - everyone talks about it, but nobody has actually seen it."
            </p>
          </section>

          {/* 数据收集 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">📊 What Data We Collect</h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                We collect no data… unless you count emotional damage.
              </p>
              <p className="text-gray-600 mb-4 italic">
                "Data is the new oil, but we're more like a bicycle repair shop."
              </p>

              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-red-500 mr-2">•</span>
                  <span>Your IP address is probably already on a whiteboard in our office</span>
                </li>
                <li className="flex items-start">
                  <span className="text-orange-500 mr-2">•</span>
                  <span>We log every button click and laugh about it during our midnight meetings</span>
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">•</span>
                  <span>Your browser history? We don't look, but our servers are very gossipy</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-500 mr-2">•</span>
                  <span>Mouse movements are tracked by our intern's pet hamster</span>
                </li>
                <li className="flex items-start">
                  <span className="text-purple-500 mr-2">•</span>
                  <span>We collect your tears of laughter (for scientific purposes only)</span>
                </li>
              </ul>
            </div>
          </section>

          {/* Cookie政策 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🍪 Cookie Usage Policy</h2>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                We use cookies, mostly because we're hungry.
              </p>
              <p className="text-gray-600 mb-4 italic">
                "Cookies are like academic papers - nobody wants them, but everyone accepts them anyway."
              </p>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🍪 Essential Cookies</h3>
                  <p className="text-sm text-gray-600">To remember if you agreed to our nonsense.</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🧁 Analytics Cookies</h3>
                  <p className="text-sm text-gray-600">To analyze why you visited this weird website.</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🎂 Marketing Cookies</h3>
                  <p className="text-sm text-gray-600">To send you more useless academic jokes.</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🍩 Mystery Cookies</h3>
                  <p className="text-sm text-gray-600">We have no idea what this one does either.</p>
                </div>
              </div>
            </div>
          </section>

          {/* 数据处理 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🔒 我们如何处理你的信息</h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                If you send us personal info, we'll read it out loud and laugh.
              </p>
              <p className="text-gray-600 mb-4">如果你发送个人信息给我们，我们会大声读出来然后大笑。</p>
              
              <div className="space-y-3 text-gray-700">
                <p>• 我们的数据处理团队由三只猫和一台1990年的计算器组成。</p>
                <p>• 所有敏感信息都存储在一个标有"绝密"的鞋盒里。</p>
                <p>• 我们使用军用级加密...的表情包来保护你的数据。</p>
                <p>• 数据备份？我们把硬盘埋在办公室后院的树下。</p>
              </div>
            </div>
          </section>

          {/* 权利声明 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">⚖️ 你的权利（理论上）</h2>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                To request data deletion, scream "DELETE ME" into the void.
              </p>
              <p className="text-gray-600 mb-4">要请求删除数据，请对着虚空大喊"删除我"。</p>
              
              <div className="grid md:grid-cols-2 gap-4 mt-4">
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">访问权</h3>
                  <p className="text-sm text-gray-600">你有权知道我们对你了解多少（剧透：不多）。</p>
                </div>
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">更正权</h3>
                  <p className="text-sm text-gray-600">你可以要求我们更正错误信息（但我们可能会故意搞错）。</p>
                </div>
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">删除权</h3>
                  <p className="text-sm text-gray-600">你可以要求删除数据，我们会考虑的...大概。</p>
                </div>
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">投诉权</h3>
                  <p className="text-sm text-gray-600">你可以向我们的投诉部门投诉（就是办公室的垃圾桶）。</p>
                </div>
              </div>
            </div>
          </section>

          {/* 联系方式 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">📞 联系我们</h2>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 mb-4">如果你对我们的隐私政策有任何疑问：</p>
              <ul className="space-y-2 text-gray-600">
                <li>📧 邮箱：<EMAIL>（可能会被当作垃圾邮件）</li>
                <li>📱 电话：1-800-NO-PRIVACY（永远占线）</li>
                <li>📮 地址：虚拟世界第404号，找不到大街</li>
                <li>🕊️ 信鸽：请训练你的信鸽找到我们的服务器机房</li>
              </ul>
            </div>
          </section>

          {/* 免责声明 */}
          <section className="border-t pt-8">
            <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-6">
              <h3 className="text-lg font-bold text-yellow-800 mb-2">⚠️ 重要声明</h3>
              <p className="text-yellow-700 text-sm">
                本隐私政策纯属娱乐，请勿当真。如果你真的相信了以上内容，
                我们建议你重新评估一下自己的判断力。Joker期刊对因阅读本政策
                而产生的任何心理创伤概不负责。😄
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};
