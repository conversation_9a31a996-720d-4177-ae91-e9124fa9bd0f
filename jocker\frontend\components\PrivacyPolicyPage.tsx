import React from 'react';

interface PrivacyPolicyPageProps {
  onNavigateHome: () => void;
}

// 自定义列表项组件
const ListItem: React.FC<{ icon: string; color: string; en: string; zh: string }> = ({ icon, color, en, zh }) => (
  <div className="flex items-start space-x-3 p-3 bg-white bg-opacity-50 rounded-lg">
    <span className={`${color} text-lg font-bold mt-1`}>{icon}</span>
    <div className="flex-1">
      <p className="text-gray-800 font-medium">{en}</p>
      <p className="text-gray-600 text-sm mt-1">{zh}</p>
    </div>
  </div>
);

export const PrivacyPolicyPage: React.FC<PrivacyPolicyPageProps> = ({ onNavigateHome }) => {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <button
          onClick={onNavigateHome}
          className="mb-8 inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回首页
        </button>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">🤫 Privacy Policy</h1>
          <p className="text-xl text-gray-600">We care about your privacy... just kidding</p>
          <p className="text-lg text-gray-500 mt-2">我们关心你的隐私...开玩笑的</p>
          <div className="mt-4 text-sm text-gray-500">
            Last Updated: July 28, 2025 | Valid Until: We forget about it<br/>
            最后更新：2025年7月28日 | 有效期：直到我们忘记为止
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-white rounded-lg shadow-lg p-8 space-y-8">
          
          {/* 开场白 */}
          <section className="border-l-4 border-blue-500 pl-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🍪 Our Privacy Commitment / 我们的隐私承诺</h2>
            <p className="text-lg text-gray-700 leading-relaxed">
              We don't know who you are. We don't care. But our cookies do. 🍪
            </p>
            <p className="text-gray-600 mt-2">
              我们不知道你是谁，我们也不在乎。但是我们的饼干很在乎。🍪
            </p>
            <p className="text-gray-600 mt-3 italic">
              "Privacy is like a unicorn - everyone talks about it, but nobody has actually seen it."<br/>
              "隐私就像独角兽——每个人都在谈论它，但没人真正见过。"
            </p>
          </section>

          {/* 数据收集 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">📊 What Data We Collect / 我们收集什么数据</h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                We collect no data… unless you count emotional damage.
              </p>
              <p className="text-gray-600 mb-2">
                我们不收集任何数据...除非你把情感伤害也算进去。
              </p>
              <p className="text-gray-600 mb-4 italic">
                "Data is the new oil, but we're more like a bicycle repair shop."<br/>
                "数据是新石油，但我们更像是自行车修理店。"
              </p>

              <div className="space-y-3">
                <ListItem
                  icon="🎯"
                  color="text-red-500"
                  en="Your IP address is probably already on a whiteboard in our office"
                  zh="你的IP地址可能已经写在我们办公室的白板上了"
                />
                <ListItem
                  icon="👆"
                  color="text-orange-500"
                  en="We log every button click and laugh about it during our midnight meetings"
                  zh="我们记录你点击的每一个按钮，然后在深夜会议上嘲笑"
                />
                <ListItem
                  icon="🕵️"
                  color="text-green-500"
                  en="Your browser history? We don't look, but our servers are very gossipy"
                  zh="你的浏览器历史？我们不看，但我们的服务器很八卦"
                />
                <ListItem
                  icon="🐹"
                  color="text-blue-500"
                  en="Mouse movements are tracked by our intern's pet hamster"
                  zh="鼠标移动由我们实习生的宠物仓鼠跟踪"
                />
                <ListItem
                  icon="😂"
                  color="text-purple-500"
                  en="We collect your tears of laughter (for scientific purposes only)"
                  zh="我们收集你的笑出眼泪（仅用于科学目的）"
                />
              </div>
            </div>
          </section>

          {/* Cookie政策 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🍪 Cookie Usage Policy / Cookie使用政策</h2>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                We use cookies, mostly because we're hungry.
              </p>
              <p className="text-gray-600 mb-2">
                我们使用Cookie，主要是因为我们饿了。
              </p>
              <p className="text-gray-600 mb-4 italic">
                "Cookies are like academic papers - nobody wants them, but everyone accepts them anyway."<br/>
                "Cookie就像学术论文——没人想要，但每个人都会接受。"
              </p>

              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🍪 Essential Cookies / 必要Cookie</h3>
                  <p className="text-sm text-gray-600 mb-1">To remember if you agreed to our nonsense.</p>
                  <p className="text-sm text-gray-500">用于记住你是否同意我们的胡说八道。</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🧁 Analytics Cookies / 分析Cookie</h3>
                  <p className="text-sm text-gray-600 mb-1">To analyze why you visited this weird website.</p>
                  <p className="text-sm text-gray-500">用于分析你为什么会访问这个奇怪的网站。</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🎂 Marketing Cookies / 营销Cookie</h3>
                  <p className="text-sm text-gray-600 mb-1">To send you more useless academic jokes.</p>
                  <p className="text-sm text-gray-500">用于向你推送更多无用的学术笑话。</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h3 className="font-bold text-gray-800 mb-2">🍩 Mystery Cookies / 神秘Cookie</h3>
                  <p className="text-sm text-gray-600 mb-1">We have no idea what this one does either.</p>
                  <p className="text-sm text-gray-500">我们也不知道这个是干什么的。</p>
                </div>
              </div>
            </div>
          </section>

          {/* 数据处理 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🔒 我们如何处理你的信息</h2>
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                If you send us personal info, we'll read it out loud and laugh.
              </p>
              <p className="text-gray-600 mb-4">如果你发送个人信息给我们，我们会大声读出来然后大笑。</p>
              
              <div className="space-y-3 text-gray-700">
                <p>• 我们的数据处理团队由三只猫和一台1990年的计算器组成。</p>
                <p>• 所有敏感信息都存储在一个标有"绝密"的鞋盒里。</p>
                <p>• 我们使用军用级加密...的表情包来保护你的数据。</p>
                <p>• 数据备份？我们把硬盘埋在办公室后院的树下。</p>
              </div>
            </div>
          </section>

          {/* 权利声明 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">⚖️ 你的权利（理论上）</h2>
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
              <p className="text-lg font-medium text-gray-800 mb-3">
                To request data deletion, scream "DELETE ME" into the void.
              </p>
              <p className="text-gray-600 mb-4">要请求删除数据，请对着虚空大喊"删除我"。</p>
              
              <div className="grid md:grid-cols-2 gap-4 mt-4">
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">访问权</h3>
                  <p className="text-sm text-gray-600">你有权知道我们对你了解多少（剧透：不多）。</p>
                </div>
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">更正权</h3>
                  <p className="text-sm text-gray-600">你可以要求我们更正错误信息（但我们可能会故意搞错）。</p>
                </div>
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">删除权</h3>
                  <p className="text-sm text-gray-600">你可以要求删除数据，我们会考虑的...大概。</p>
                </div>
                <div>
                  <h3 className="font-bold text-purple-800 mb-2">投诉权</h3>
                  <p className="text-sm text-gray-600">你可以向我们的投诉部门投诉（就是办公室的垃圾桶）。</p>
                </div>
              </div>
            </div>
          </section>

          {/* 联系方式 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">📞 联系我们</h2>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 mb-4">如果你对我们的隐私政策有任何疑问：</p>
              <ul className="space-y-2 text-gray-600">
                <li>📧 邮箱：<EMAIL>（可能会被当作垃圾邮件）</li>
                <li>📱 电话：1-800-NO-PRIVACY（永远占线）</li>
                <li>📮 地址：虚拟世界第404号，找不到大街</li>
                <li>🕊️ 信鸽：请训练你的信鸽找到我们的服务器机房</li>
              </ul>
            </div>
          </section>

          {/* 免责声明 */}
          <section className="border-t pt-8">
            <div className="bg-yellow-100 border border-yellow-300 rounded-lg p-6">
              <h3 className="text-lg font-bold text-yellow-800 mb-2">⚠️ 重要声明</h3>
              <p className="text-yellow-700 text-sm">
                本隐私政策纯属娱乐，请勿当真。如果你真的相信了以上内容，
                我们建议你重新评估一下自己的判断力。Joker期刊对因阅读本政策
                而产生的任何心理创伤概不负责。😄
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};
