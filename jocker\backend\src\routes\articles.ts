import { Router } from 'express';
import {
  getArticleList,
  getArticle,
  getArticleRealViews,
  createNewArticle,
  updateExistingArticle,
  deleteExistingArticle,
  batchDeleteArticles,
  updateArticleViews,
  getTrending,
  getFeatured,
  likeExistingArticle,
  getArticleFigures,
} from '../controllers/articleController';
import {
  getArticleComments,
  submitComment,
} from '../controllers/commentController';
import { validate, createArticleSchema, updateArticleSchema } from '../middleware/validation';
import { authenticateToken, optionalAuth, requireEditor } from '../middleware/auth';

const router = Router();

/**
 * @route   GET /api/articles
 * @desc    获取文章列表（支持分页和搜索）
 * @access  Public
 */
router.get('/', optionalAuth, getArticleList);

/**
 * @route   GET /api/articles/trending
 * @desc    获取热门文章
 * @access  Public
 */
router.get('/trending', getTrending);

/**
 * @route   GET /api/articles/featured
 * @desc    获取推荐文章
 * @access  Public
 */
router.get('/featured', getFeatured);

/**
 * @route   GET /api/articles/:id
 * @desc    获取单篇文章
 * @access  Public
 */
router.get('/:id', optionalAuth, getArticle);

/**
 * @route   POST /api/articles
 * @desc    创建文章
 * @access  Public (个人使用，无需认证)
 */
router.post('/', validate(createArticleSchema), createNewArticle);

/**
 * @route   PUT /api/articles/:id
 * @desc    更新文章
 * @access  Public (个人使用，无需认证)
 */
router.put('/:id', validate(updateArticleSchema), updateExistingArticle);

/**
 * @route   DELETE /api/articles/:id
 * @desc    删除文章
 * @access  Public (个人使用，无需认证)
 */
router.delete('/:id', deleteExistingArticle);

/**
 * @route   POST /api/articles/:id/like
 * @desc    点赞文章
 * @access  Public
 */
router.post('/:id/like', likeExistingArticle);

/**
 * @route   GET /api/articles/:id/figures
 * @desc    获取文章图片列表
 * @access  Public
 */
router.get('/:id/figures', getArticleFigures);

/**
 * @route   GET /api/articles/:id/real-views
 * @desc    获取文章真实观看次数
 * @access  Public
 */
router.get('/:id/real-views', getArticleRealViews);

/**
 * @route   DELETE /api/articles/batch
 * @desc    批量删除文章
 * @access  Private (Admin/Editor)
 */
router.delete('/batch', authenticateToken, requireEditor, batchDeleteArticles);

/**
 * @route   PUT /api/articles/:id/views
 * @desc    更新文章观看次数
 * @access  Private (Admin/Editor)
 */
router.put('/:id/views', authenticateToken, requireEditor, updateArticleViews);

/**
 * @route   GET /api/articles/:id/comments
 * @desc    获取文章评论列表
 * @access  Public
 */
router.get('/:id/comments', getArticleComments);

/**
 * @route   POST /api/articles/:id/comments
 * @desc    提交文章评论
 * @access  Private
 */
router.post('/:id/comments', authenticateToken, submitComment);

export default router;
