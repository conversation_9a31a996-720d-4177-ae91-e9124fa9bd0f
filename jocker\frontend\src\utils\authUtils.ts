/**
 * 统一的权限检查工具
 * 避免在各个组件中重复权限检查逻辑
 */
import React from 'react';

export interface UserInfo {
  id: number;
  email: string;
  username: string;
  name?: string;
  role: string;
  status: string;
}

/**
 * 检查用户是否已登录
 */
export const isUserLoggedIn = (): boolean => {
  const token = localStorage.getItem('jocker_admin_token');
  const userStr = localStorage.getItem('jocker_admin_user');
  return !!(token && userStr);
};

/**
 * 获取当前用户信息
 */
export const getCurrentUser = (): UserInfo | null => {
  const userStr = localStorage.getItem('jocker_admin_user');
  if (!userStr) return null;
  
  try {
    return JSON.parse(userStr);
  } catch (error) {
    console.error('解析用户信息失败:', error);
    return null;
  }
};

/**
 * 检查用户是否为管理员
 * 这是最严格的管理员检查，确保用户有token且角色为ADMIN
 */
export const isUserAdmin = (): boolean => {
  const token = localStorage.getItem('jocker_admin_token');
  const user = getCurrentUser();
  
  return !!(token && user && user.role === 'ADMIN');
};

/**
 * 检查用户是否为编辑者或管理员
 */
export const isUserEditor = (): boolean => {
  const user = getCurrentUser();
  return !!(user && (user.role === 'ADMIN' || user.role === 'EDITOR'));
};

/**
 * 获取用户角色
 */
export const getUserRole = (): string | null => {
  const user = getCurrentUser();
  return user?.role || null;
};

/**
 * 清除用户认证信息
 */
export const clearUserAuth = (): void => {
  localStorage.removeItem('jocker_admin_token');
  localStorage.removeItem('jocker_admin_logged_in');
  localStorage.removeItem('jocker_admin_user');
  localStorage.removeItem('jocker_user_role');
};

/**
 * Hook: 获取用户认证状态
 * 返回 { isLoggedIn, isAdmin, user, userRole }
 */
export const useAuthStatus = () => {
  // 使用useState来确保状态变化时组件重新渲染
  const [authState, setAuthState] = React.useState(() => ({
    isLoggedIn: isUserLoggedIn(),
    isAdmin: isUserAdmin(),
    user: getCurrentUser(),
    userRole: getUserRole()
  }));

  // 监听localStorage变化
  React.useEffect(() => {
    const updateAuthState = () => {
      setAuthState({
        isLoggedIn: isUserLoggedIn(),
        isAdmin: isUserAdmin(),
        user: getCurrentUser(),
        userRole: getUserRole()
      });
    };

    // 监听storage事件
    window.addEventListener('storage', updateAuthState);

    // 定期检查状态变化（作为备用）
    const interval = setInterval(updateAuthState, 1000);

    return () => {
      window.removeEventListener('storage', updateAuthState);
      clearInterval(interval);
    };
  }, []);

  return authState;
};
