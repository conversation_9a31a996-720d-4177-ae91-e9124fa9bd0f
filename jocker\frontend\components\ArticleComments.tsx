import React, { useState, useEffect } from 'react';
import { articleApi } from '../src/services/api';
import { aiService } from '../src/services/aiService';

interface Comment {
  id: string;
  authorName: string;
  authorTitle?: string;
  title: string;
  content: string;
  isAI: boolean;
  createdAt: string;
  user?: {
    id: number;
    username: string;
    name?: string;
    avatar?: string;
  };
}

interface ArticleCommentsProps {
  articleId: number;
  isLoggedIn: boolean;
  isAdmin: boolean;
  article: any; // 文章信息，用于生成AI评论
}

export const ArticleComments: React.FC<ArticleCommentsProps> = ({
  articleId,
  isLoggedIn,
  isAdmin,
  article
}) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [generating, setGenerating] = useState(false);

  // 评论表单状态
  const [commentTitle, setCommentTitle] = useState('');
  const [commentContent, setCommentContent] = useState('');
  const [authorName, setAuthorName] = useState('');

  // AI生成表单状态
  const [generateCount, setGenerateCount] = useState(3);

  // 文章信息已通过props传入，无需state

  // 加载评论
  const loadComments = async () => {
    try {
      setLoading(true);
      const response = await articleApi.getComments(articleId);
      setComments(response.comments || []);
    } catch (error) {
      console.error('加载评论失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadComments();
  }, [articleId]);

  // 提交用户评论
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentTitle.trim() || !commentContent.trim()) {
      alert('请填写评论标题和内容');
      return;
    }

    setSubmitting(true);
    try {
      const response = await articleApi.submitComment(articleId, {
        title: commentTitle.trim(),
        content: commentContent.trim(),
        authorName: authorName.trim() || undefined
      });

      // 显示恶搞的审核通过消息
      if (response && response.message) {
        alert(response.message);
      } else {
        alert('感谢您的学术反馈！您的评论已通过我们的元-算法伦理审查，现已发布。（审查时间：0.001纳秒）');
      }

      // 重新加载评论
      await loadComments();

      // 清空表单
      setCommentTitle('');
      setCommentContent('');
      setAuthorName('');
      setShowCommentForm(false);
    } catch (error: any) {
      console.error('提交评论失败:', error);
      alert('提交评论失败: ' + (error?.message || '未知错误'));
    } finally {
      setSubmitting(false);
    }
  };

  // 生成AI评论
  const handleGenerateComments = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!article) {
      alert('文章信息不完整，无法生成评论');
      return;
    }

    setGenerating(true);
    try {
      // 假的AI评论者署名池
      const aiReviewers = [
        { name: 'Dr. AlphaGPT', title: 'Prof.' },
        { name: 'SynthetixCritic', title: 'Dr.' },
        { name: 'MetaLogic-7', title: 'Prof.' },
        { name: 'NeuralNonsense', title: 'Dr.' },
        { name: 'QuantumQuibble', title: 'Prof.' },
        { name: 'DeepDrivel', title: 'Dr.' },
        { name: 'LogicLoop-9000', title: 'Prof.' },
        { name: 'BinaryBabble', title: 'Dr.' }
      ];

      const generatedComments = [];

      for (let i = 0; i < generateCount; i++) {
        const reviewer = aiReviewers[Math.floor(Math.random() * aiReviewers.length)];

        // 使用AI服务生成评论内容
        const prompt = `作为一个学术评论者，请对以下文章写一个简短的同行评审评论（100-200字）：

文章标题：${article.title}
文章摘要：${article.abstract || '暂无摘要'}
文章分类：${article.category}

请以${reviewer.name}的身份，写一个既专业又略带幽默的学术评论。评论应该：
1. 指出文章的优点或创新点
2. 提出一些建设性的质疑或建议
3. 保持学术语调但可以适当幽默
4. 不要超过200字

只返回评论内容，不要包含署名。`;

        const commentContent = await aiService.generateText({
          prompt,
          maxTokens: 300
        });

        // 生成评论标题
        const titleTemplates = [
          `对《${article.title}》的深度思考`,
          `关于《${article.title}》的学术质疑`,
          `《${article.title}》：一个革命性的突破？`,
          `重新审视《${article.title}》的核心论点`,
          `《${article.title}》中的方法论创新`,
        ];

        const commentTitle = titleTemplates[Math.floor(Math.random() * titleTemplates.length)];

        // 提交评论到后端
        const comment = await articleApi.submitComment(articleId, {
          title: commentTitle,
          content: commentContent,
          authorName: `${reviewer.title} ${reviewer.name}`,
        });

        generatedComments.push(comment);
      }

      alert(`成功生成 ${generateCount} 条AI评论！`);

      // 重新加载评论
      await loadComments();
      setShowGenerateForm(false);
    } catch (error: any) {
      console.error('生成AI评论失败:', error);
      alert('生成AI评论失败: ' + (error.message || '未知错误'));
    } finally {
      setGenerating(false);
    }
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <span className="mr-2">📝</span>
          Post-Publication Peer Review
          <span className="ml-2 text-sm text-gray-500">({comments.length})</span>
        </h3>
        
        <div className="flex space-x-2">
          {isAdmin && (
            <button
              onClick={() => setShowGenerateForm(!showGenerateForm)}
              className="px-3 py-1.5 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors"
            >
              🤖 生成AI评论
            </button>
          )}
          
          {isLoggedIn && (
            <button
              onClick={() => setShowCommentForm(!showCommentForm)}
              className="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
            >
              ✍️ 撰写评论
            </button>
          )}
        </div>
      </div>

      {/* AI生成评论表单 */}
      {showGenerateForm && isAdmin && (
        <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <h4 className="font-medium text-purple-900 mb-3">生成AI同行评审</h4>
          <form onSubmit={handleGenerateComments} className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-purple-700 mb-1">
                生成数量 (1-10)
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={generateCount}
                onChange={(e) => setGenerateCount(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
                className="w-full px-3 py-2 border border-purple-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="输入要生成的评论数量"
              />
            </div>
            
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={generating}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {generating ? '生成中...' : '生成AI评论'}
              </button>
              <button
                type="button"
                onClick={() => setShowGenerateForm(false)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 用户评论表单 */}
      {showCommentForm && isLoggedIn && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-3">提交学术评论</h4>
          <form onSubmit={handleSubmitComment} className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                评论标题 *
              </label>
              <input
                type="text"
                value={commentTitle}
                onChange={(e) => setCommentTitle(e.target.value)}
                placeholder="例如：对本文方法论的深度思考"
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                署名（可选）
              </label>
              <input
                type="text"
                value={authorName}
                onChange={(e) => setAuthorName(e.target.value)}
                placeholder="留空则使用您的用户名"
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                评论内容 *
              </label>
              <textarea
                value={commentContent}
                onChange={(e) => setCommentContent(e.target.value)}
                placeholder="请提供您的学术见解和建设性意见..."
                rows={4}
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={submitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? '提交中...' : '提交评论'}
              </button>
              <button
                type="button"
                onClick={() => setShowCommentForm(false)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 评论列表 */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">加载评论中...</p>
        </div>
      ) : comments.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p className="text-lg">🤔 暂无同行评审</p>
          <p className="text-sm mt-2">成为第一个发表学术见解的人！</p>
        </div>
      ) : (
        <div className="space-y-4">
          {comments.map((comment) => (
            <div
              key={comment.id}
              className={`p-4 rounded-lg border ${
                comment.isAI 
                  ? 'bg-purple-50 border-purple-200' 
                  : 'bg-gray-50 border-gray-200'
              }`}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <span className={`text-sm font-medium ${
                    comment.isAI ? 'text-purple-700' : 'text-blue-700'
                  }`}>
                    {comment.authorTitle} {comment.authorName}
                  </span>
                  {comment.isAI && (
                    <span className="px-2 py-0.5 bg-purple-200 text-purple-800 text-xs rounded-full">
                      🤖 AI Reviewer
                    </span>
                  )}
                </div>
                <span className="text-xs text-gray-500">
                  {formatDate(comment.createdAt)}
                </span>
              </div>
              
              <h4 className="font-medium text-gray-900 mb-2">
                {comment.title}
              </h4>
              
              <p className="text-gray-700 text-sm leading-relaxed">
                {comment.content}
              </p>
            </div>
          ))}
        </div>
      )}

      {/* 说明文字 */}
      <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-xs text-yellow-800">
          <span className="font-medium">📋 评审说明：</span>
          所有评论将通过我们的元-算法伦理审查系统进行评估。
          AI生成的评论代表了最前沿的人工智能学术观点。
          用户评论将立即通过审核（审查时间：0.001纳秒）。
        </p>
      </div>
    </div>
  );
};
