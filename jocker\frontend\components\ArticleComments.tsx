import React, { useState, useEffect } from 'react';
import { articleApi } from '../src/services/api';
import { aiService } from '../src/services/aiService';
import { isUserAdmin, isUserLoggedIn } from '../src/utils/authUtils';
import { Avatar } from './Avatar';

// API基础URL
const API_BASE_URL = (import.meta as any).env?.VITE_API_BASE_URL || '/api';

// 临时的删除函数，直接调用API
const deleteCommentDirect = async (commentId: string): Promise<{ message: string }> => {
  const token = localStorage.getItem('jocker_admin_token');
  if (!token) {
    throw new Error('未找到认证 Token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/admin/comments/${commentId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  const result = await response.json();
  if (!result.success) {
    throw new Error(result.message || '删除失败');
  }
  return result;
};

const batchDeleteCommentsDirect = async (commentIds: string[]): Promise<{ message: string; deletedCount: number }> => {
  const token = localStorage.getItem('jocker_admin_token');
  if (!token) {
    throw new Error('未找到认证 Token，请先登录');
  }

  console.log('发送批量删除请求，数据:', { commentIds });
  console.log('使用API URL:', `${API_BASE_URL}/admin/comments/batch`);

  const response = await fetch(`${API_BASE_URL}/admin/comments/batch`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ commentIds }),
  });

  console.log('批量删除响应状态:', response.status);

  const result = await response.json();
  if (!result.success) {
    throw new Error(result.message || '批量删除失败');
  }
  return result;
};

interface Comment {
  id: string;
  authorName: string;
  authorTitle?: string;
  title: string;
  content: string;
  isAI: boolean;
  createdAt: string;
  user?: {
    id: number;
    username: string;
    name?: string;
    avatar?: string;
  };
}

interface ArticleCommentsProps {
  articleId: number;
  isLoggedIn: boolean;
  article: any; // 文章信息，用于生成AI评论
  content?: string | null; // 文章的实际内容
}

export const ArticleComments: React.FC<ArticleCommentsProps> = ({
  articleId,
  isLoggedIn,
  article,
  content
}) => {
  // 使用统一的管理员权限检查
  const isRealAdmin = isUserAdmin();

  // 调试信息
  console.log('🔍 ArticleComments 权限状态:', {
    isLoggedIn,
    isRealAdmin,
    hasToken: !!localStorage.getItem('jocker_admin_token'),
    hasUser: !!localStorage.getItem('jocker_admin_user'),
    userInfo: localStorage.getItem('jocker_admin_user')
  });

  // 直接测试localStorage
  const directTokenCheck = localStorage.getItem('jocker_admin_token');
  const directUserCheck = localStorage.getItem('jocker_admin_user');
  const directLoginCheck = !!(directTokenCheck && directUserCheck);

  console.log('🔍 直接localStorage检查:', {
    directTokenCheck: directTokenCheck ? 'exists' : 'null',
    directUserCheck: directUserCheck ? 'exists' : 'null',
    directLoginCheck,
    isUserLoggedInResult: isUserLoggedIn(),
    // 更详细的检查
    tokenValue: directTokenCheck,
    userValue: directUserCheck,
    tokenType: typeof directTokenCheck,
    userType: typeof directUserCheck,
    tokenTruthy: !!directTokenCheck,
    userTruthy: !!directUserCheck
  });
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCommentForm, setShowCommentForm] = useState(false);
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [selectedComments, setSelectedComments] = useState<string[]>([]);
  const [deleting, setDeleting] = useState(false);

  // 评论表单状态
  const [commentTitle, setCommentTitle] = useState('');
  const [commentContent, setCommentContent] = useState('');
  const [authorName, setAuthorName] = useState('');

  // AI生成表单状态
  const [generateCount, setGenerateCount] = useState(3);

  // 文章信息已通过props传入，无需state

  // 删除单个评论
  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('确定要删除这条评论吗？')) {
      return;
    }

    try {
      await deleteCommentDirect(commentId);
      alert('评论删除成功！🗑️');
      await loadComments();
    } catch (error: any) {
      console.error('删除评论失败:', error);
      alert('删除评论失败: ' + (error?.message || '未知错误'));
    }
  };

  // 批量删除评论
  const handleBatchDelete = async () => {
    if (selectedComments.length === 0) {
      alert('请先选择要删除的评论');
      return;
    }

    if (!confirm(`确定要删除选中的 ${selectedComments.length} 条评论吗？`)) {
      return;
    }

    setDeleting(true);
    try {
      console.log('批量删除评论，选中的ID:', selectedComments);
      const response = await batchDeleteCommentsDirect(selectedComments);
      alert(response.message);
      setSelectedComments([]);
      await loadComments();
    } catch (error: any) {
      console.error('批量删除评论失败:', error);
      alert('批量删除失败: ' + (error?.message || '未知错误'));
    } finally {
      setDeleting(false);
    }
  };

  // 切换评论选择状态
  const toggleCommentSelection = (commentId: string) => {
    setSelectedComments(prev =>
      prev.includes(commentId)
        ? prev.filter(id => id !== commentId)
        : [...prev, commentId]
    );
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedComments.length === comments.length) {
      setSelectedComments([]);
    } else {
      setSelectedComments(comments.map(comment => comment.id));
    }
  };

  // 加载评论
  const loadComments = async () => {
    try {
      setLoading(true);
      const response = await articleApi.getComments(articleId);
      setComments(response.comments || []);
    } catch (error) {
      console.error('加载评论失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadComments();
  }, [articleId]);

  // 提交用户评论
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentTitle.trim() || !commentContent.trim()) {
      alert('请填写评论标题和内容');
      return;
    }

    setSubmitting(true);
    try {
      const response = await articleApi.submitComment(articleId, {
        title: commentTitle.trim(),
        content: commentContent.trim(),
        authorName: authorName.trim() || undefined
      });

      // 显示恶搞的审核通过消息
      if (response && response.message) {
        alert(response.message);
      } else {
        alert('感谢您的学术反馈！您的评论已通过我们的元-算法伦理审查，现已发布。（审查时间：0.001纳秒）');
      }

      // 重新加载评论
      await loadComments();

      // 清空表单
      setCommentTitle('');
      setCommentContent('');
      setAuthorName('');
      setShowCommentForm(false);
    } catch (error: any) {
      console.error('提交评论失败:', error);
      alert('提交评论失败: ' + (error?.message || '未知错误'));
    } finally {
      setSubmitting(false);
    }
  };

  // 生成AI评论
  const handleGenerateComments = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!article) {
      alert('文章信息不完整，无法生成评论');
      return;
    }

    setGenerating(true);
    try {
      // 获取文章的完整内容
      const fullArticleContent = content || article.content || 'No content available';
      console.log('Generating AI comments for article:', {
        contentLength: fullArticleContent.length,
        hasContent: !!content,
        hasArticleContent: !!article.content,
        preview: fullArticleContent.substring(0, 200)
      });
      // 检查是否为新闻文章
      const isNewsArticle = article.category === 'News & Comment';

      // 一次性生成多条评论
      const prompt = isNewsArticle
        ? `You are a panel of hilarious internet commenters reacting to this satirical news article. Write funny, casual comments like you'd see on social media or news websites. Be witty, sarcastic, and use internet slang and emojis 😂🤣💀🔥.
Each comment should focus on a **different aspect** of the news (e.g., the absurdity, the "witnesses", the official responses, the implications, etc.) and read like it was written by different types of internet users with ridiculous usernames.
Your tone: **internet trolls meets comedy gold**. Make each commenter username absurd but believable for online forums (e.g., @AcademicMemeQueen, @ProfessorChaos42, @LabRatWhistleblower, @CoffeeAddictPhD).`

        : `You are a panel of savage and satirical academic reviewers from the "International Journal of Brutal Honesty." Your job is to write hilariously sarcastic peer reviews of the following academic article. Be sharp, original, and brutally funny — like academic roasts at a comedy central special. Use clever metaphors, academic jargon twisted for humor, and lots of emojis 😂💀🙄🔥.
Each review should focus on a **different aspect** of the article (e.g., methodology, originality, writing style, citations, conclusions, etc.) and read like it was written by an overworked academic with zero patience for nonsense.
Your tone: **sassy professor meets stand-up comic**. Make each reviewer name and title ridiculous but believable in an academic parody setting (e.g., Dr. Petty von Citation, Prof. Snarkus Maximus).

${isNewsArticle ? 'NEWS ARTICLE:' : 'FULL ARTICLE:'}
Title: "${article.title}"
${isNewsArticle ? 'Summary:' : 'Abstract:'} ${article.excerpt || 'No summary provided'}
Category: ${article.category}
Content: ${fullArticleContent}

Return a JSON array of ${generateCount} ${isNewsArticle ? 'hilarious internet comments' : 'satirical peer reviews'} in the following format:

[
  {
    "reviewerName": "${isNewsArticle ? 'Funny internet username (e.g., AcademicMemeQueen, ProfessorChaos42)' : 'Ridiculous academic name'}",
    "reviewerTitle": "${isNewsArticle ? '@' : 'Dr. or Prof.'}",
    "commentTitle": "${isNewsArticle ? 'Casual comment title with emojis (e.g., This is peak academia right here 💀😂)' : 'Witty roast headline with emojis (e.g. A Bold Leap... Backwards 🕳️🚶‍♂️)'}",
    "content": "${isNewsArticle ? '30-80 words of hilarious internet commentary. Be casual, use slang, memes, and emojis. React like a real person scrolling through news. Make jokes about the absurdity, quote funny parts, use internet humor 😂🤣💀🔥' : '50-100 words of creative, ruthless academic snark. Roast with flair. Use analogies, emoji, and absurd academic sarcasm. No two reviews should target the same flaw. Channel your inner bitter reviewer 😂🤡💩🙃💀🙄🔥'}"
  }
]

Rules:
- ${isNewsArticle ? 'Keep the comments funny and casual, like real internet users reacting to absurd news. Use modern slang and memes! 😂' : 'Keep the roasts creative, but still sound like they “could” be real reviews — from unhinged reviewers. 😈'}

IMPORTANT: Return ONLY a valid JSON array, no other text before or after. Example:
${isNewsArticle
  ? '[{"reviewerName": "AcademicMemeQueen", "reviewerTitle": "@", "commentTitle": "This is peak academia right here 💀😂", "content": "Bruh imagine being the grad student who had to write this report 😭 This is not what I expected to put in my thesis lmaooo 🐦‍⬛✊"}]'
  : '[{"reviewerName": "Dr. Petty von Citation", "reviewerTitle": "Dr.", "commentTitle": "A Bold Leap... Backwards 🕳️🚶‍♂️", "content": "This methodology exhibits the same rigor as a paper airplane in a hurricane. The authors have successfully demonstrated that correlation does indeed equal causation when you squint hard enough. 😂💀"}]'
}
`;

      const response = await aiService.generateText({
        prompt
      });

      // 更鲁棒的JSON解析
      let reviewsData;
      try {
        console.log('🤖 AI原始响应:', response);

        // 多步骤清理和解析
        let cleanResponse = response.trim();

        // 移除常见的非JSON前缀/后缀
        cleanResponse = cleanResponse
          .replace(/^[^[{]*/, '') // 移除开头的非JSON字符
          .replace(/[^}\]]*$/, '') // 移除结尾的非JSON字符
          .replace(/```json\s*/g, '') // 移除markdown代码块
          .replace(/```\s*/g, '') // 移除代码块结束标记
          .replace(/^Here.*?:/gm, '') // 移除"Here are the comments:"等前缀
          .trim();

        // 确保以[开头，]结尾
        if (!cleanResponse.startsWith('[')) {
          const arrayStart = cleanResponse.indexOf('[');
          if (arrayStart !== -1) {
            cleanResponse = cleanResponse.substring(arrayStart);
          }
        }
        if (!cleanResponse.endsWith(']')) {
          const arrayEnd = cleanResponse.lastIndexOf(']');
          if (arrayEnd !== -1) {
            cleanResponse = cleanResponse.substring(0, arrayEnd + 1);
          }
        }

        console.log('🧹 清理后的响应:', cleanResponse);
        reviewsData = JSON.parse(cleanResponse);

        // 确保是数组
        if (!Array.isArray(reviewsData)) {
          throw new Error('Response is not an array');
        }

        console.log('✅ JSON解析成功:', reviewsData);
      } catch (parseError) {
        console.warn('❌ JSON解析失败，使用备用格式:', parseError);
        // 备用方案：生成默认评论
        reviewsData = [];
        for (let i = 0; i < generateCount; i++) {
          reviewsData.push({
            reviewerName: isNewsArticle ? `InternetUser${i + 1}` : `Dr. Savage AI-${i + 1}`,
            reviewerTitle: isNewsArticle ? '@' : 'Dr.',
            commentTitle: isNewsArticle
              ? `This is wild 😂 #${i + 1}`
              : `Brutally Honest Review #${i + 1} 🔥💀`,
            content: isNewsArticle
              ? `Can't believe this actually happened lol 😭 This is peak academia right here 💀 Someone really wrote a whole report about this 🤣`
              : `This article is a masterpiece of academic mediocrity! 😂 The author has successfully proven that they can string words together in a vaguely coherent manner. 🤡 Truly groundbreaking work in the field of stating the obvious! 💀🎪`
          });
        }
      }

      // 提交所有评论到后端
      const generatedComments = [];
      for (const reviewData of reviewsData) {
        try {
          const comment = await articleApi.submitComment(articleId, {
            title: reviewData.commentTitle,
            content: reviewData.content,
            authorName: `${reviewData.reviewerTitle} ${reviewData.reviewerName}`,
          });
          generatedComments.push(comment);
        } catch (error) {
          console.error('提交评论失败:', error);
        }
      }

      alert(`成功生成 ${generatedComments.length} 条AI评论！`);

      // 重新加载评论
      await loadComments();
      setShowGenerateForm(false);
    } catch (error: any) {
      console.error('生成AI评论失败:', error);
      alert('生成AI评论失败: ' + (error.message || '未知错误'));
    } finally {
      setGenerating(false);
    }
  };

  // 格式化时间
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
          <span className="mr-2">📝</span>
          Post-Publication Peer Review
          <span className="ml-2 text-sm text-gray-500">({comments.length})</span>
        </h3>
        
        <div className="flex space-x-2">
          {isRealAdmin && (
            <>
              <button
                onClick={() => setShowGenerateForm(!showGenerateForm)}
                className="px-3 py-1.5 bg-purple-600 text-white text-sm rounded-lg hover:bg-purple-700 transition-colors"
              >
                🤖 生成AI评论
              </button>

              {comments.length > 0 && (
                <button
                  onClick={toggleSelectAll}
                  className="px-3 py-1.5 bg-gray-600 text-white text-sm rounded-lg hover:bg-gray-700 transition-colors"
                >
                  {selectedComments.length === comments.length ? '取消全选' : '全选'}
                </button>
              )}

              {selectedComments.length > 0 && (
                <button
                  onClick={handleBatchDelete}
                  disabled={deleting}
                  className="px-3 py-1.5 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {deleting ? '删除中...' : `删除选中 (${selectedComments.length})`}
                </button>
              )}
            </>
          )}

          {/* 调试信息显示 */}
          <div className="text-xs text-gray-500 mb-2">
            调试: isLoggedIn={isLoggedIn ? 'true' : 'false'},
            hasToken={localStorage.getItem('jocker_admin_token') ? 'true' : 'false'}
          </div>

          {isLoggedIn && (
            <button
              onClick={() => setShowCommentForm(!showCommentForm)}
              className="px-3 py-1.5 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
            >
              ✍️ 撰写评论
            </button>
          )}

          {/* 如果没有登录，显示提示 */}
          {!isLoggedIn && (
            <div className="text-sm text-gray-500">
              请登录后撰写评论
            </div>
          )}
        </div>
      </div>

      {/* AI生成评论表单 */}
      {showGenerateForm && isRealAdmin && (
        <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
          <h4 className="font-medium text-purple-900 mb-3">生成AI同行评审</h4>
          <form onSubmit={handleGenerateComments} className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-purple-700 mb-1">
                生成数量 (1-10)
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={generateCount}
                onChange={(e) => setGenerateCount(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
                className="w-full px-3 py-2 border border-purple-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                placeholder="输入要生成的评论数量"
              />
            </div>
            
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={generating}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {generating ? '生成中...' : '生成AI评论'}
              </button>
              <button
                type="button"
                onClick={() => setShowGenerateForm(false)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 用户评论表单 */}
      {showCommentForm && isLoggedIn && (
        <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-3">提交学术评论</h4>
          <form onSubmit={handleSubmitComment} className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                评论标题 *
              </label>
              <input
                type="text"
                value={commentTitle}
                onChange={(e) => setCommentTitle(e.target.value)}
                placeholder="例如：对本文方法论的深度思考"
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                署名（可选）
              </label>
              <input
                type="text"
                value={authorName}
                onChange={(e) => setAuthorName(e.target.value)}
                placeholder="留空则使用您的用户名"
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">
                评论内容 *
              </label>
              <textarea
                value={commentContent}
                onChange={(e) => setCommentContent(e.target.value)}
                placeholder="请提供您的学术见解和建设性意见..."
                rows={4}
                className="w-full px-3 py-2 border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
            </div>
            
            <div className="flex space-x-2">
              <button
                type="submit"
                disabled={submitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? '提交中...' : '提交评论'}
              </button>
              <button
                type="button"
                onClick={() => setShowCommentForm(false)}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
              >
                取消
              </button>
            </div>
          </form>
        </div>
      )}

      {/* 评论列表 */}
      {loading ? (
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="text-gray-500 mt-2">加载评论中...</p>
        </div>
      ) : comments.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <p className="text-lg">🤔 暂无同行评审</p>
          <p className="text-sm mt-2">成为第一个发表学术见解的人！</p>
        </div>
      ) : (
        <div className="space-y-4">
          {comments.map((comment) => (
            <div
              key={comment.id}
              className={`p-4 rounded-lg border ${
                comment.isAI
                  ? 'bg-purple-50 border-purple-200'
                  : 'bg-gray-50 border-gray-200'
              } ${selectedComments.includes(comment.id) ? 'ring-2 ring-red-300' : ''}`}
            >
              <div className="flex items-start space-x-3 mb-2">
                {/* 头像 */}
                <Avatar
                  name={comment.authorName}
                  size="md"
                  className="flex-shrink-0 mt-1"
                />

                <div className="flex-1">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {isRealAdmin && (
                        <input
                          type="checkbox"
                          checked={selectedComments.includes(comment.id)}
                          onChange={() => toggleCommentSelection(comment.id)}
                          className="w-4 h-4 text-red-600 bg-gray-100 border-gray-300 rounded focus:ring-red-500"
                        />
                      )}
                      <span className={`text-sm font-medium ${
                    comment.isAI ? 'text-purple-700' : 'text-blue-700'
                  }`}>
                    {comment.authorTitle} {comment.authorName}
                  </span>
                  {comment.isAI && (
                    <span className="px-2 py-0.5 bg-purple-200 text-purple-800 text-xs rounded-full">
                      🤖 AI Reviewer
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">
                    {formatDate(comment.createdAt)}
                  </span>
                  {isRealAdmin && (
                    <button
                      onClick={() => handleDeleteComment(comment.id)}
                      className="text-red-600 hover:text-red-800 text-xs px-2 py-1 rounded hover:bg-red-100 transition-colors"
                      title="删除评论"
                    >
                      🗑️
                    </button>
                  )}
                </div>
              </div>

                  <h4 className="font-medium text-gray-900 mb-2">
                    {comment.title}
                  </h4>

                  <p className="text-gray-700 text-sm leading-relaxed">
                    {comment.content}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* 说明文字 */}
      <div className="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-xs text-yellow-800">
          <span className="font-medium">📋 评审说明：</span>
          所有评论将通过我们的元-算法伦理审查系统进行评估。
          AI生成的评论代表了最前沿的人工智能学术观点。
          用户评论将立即通过审核（审查时间：0.001纳秒）。
        </p>
      </div>
    </div>
  );
};
