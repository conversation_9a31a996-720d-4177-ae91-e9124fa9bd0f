import React, { useState, useEffect } from 'react';
import { articleApi } from '../src/services/api';
import { aiService } from '../src/services/aiService';
import { isUserAdmin, isUserLoggedIn } from '../src/utils/authUtils';
import { Avatar } from './Avatar';

const API_BASE_URL = (import.meta as any).env?.VITE_API_BASE_URL || '/api';

const deleteCommentDirect = async (commentId: string): Promise<{ message: string }> => {
  const token = localStorage.getItem('jocker_admin_token');
  if (!token) {
    throw new Error('未找到认证 Token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/admin/comments/${commentId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  const result = await response.json();
  if (!result.success) {
    throw new Error(result.message || '删除失败');
  }
  return result;
};

const batchDeleteCommentsDirect = async (commentIds: string[]): Promise<{ message: string }> => {
  const token = localStorage.getItem('jocker_admin_token');
  if (!token) {
    throw new Error('未找到认证 Token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/admin/comments/batch`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ commentIds }),
  });

  const result = await response.json();
  if (!result.success) {
    throw new Error(result.message || '批量删除失败');
  }
  return result;
};

interface Comment {
  id: string;
  authorName: string;
  authorTitle?: string;
  title: string;
  content: string;
  isAI: boolean;
  createdAt: string;
  user?: {
    id: string;
    name?: string;
    avatar?: string;
  };
}

interface ArticleCommentsProps {
  articleId: number;
  isLoggedIn: boolean;
  article: any;
  content?: string | null;
}

export const ArticleComments: React.FC<ArticleCommentsProps> = ({
  articleId,
  isLoggedIn,
  article,
  content
}) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [commentTitle, setCommentTitle] = useState('');
  const [commentContent, setCommentContent] = useState('');
  const [authorName, setAuthorName] = useState('');

  const [selectedComments, setSelectedComments] = useState<string[]>([]);
  const [deleting, setDeleting] = useState(false);
  const [showGenerateForm, setShowGenerateForm] = useState(false);
  const [generating, setGenerating] = useState(false);
  const [generateCount, setGenerateCount] = useState(3);

  useEffect(() => {
    loadComments();
  }, [articleId]);

  const loadComments = async () => {
    try {
      setLoading(true);
      const response = await articleApi.getComments(articleId);
      setComments(response.comments || []);
    } catch (error) {
      console.error('加载评论失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!commentTitle.trim() || !commentContent.trim()) {
      alert('请填写评论标题和内容');
      return;
    }

    setSubmitting(true);
    try {
      const response = await articleApi.submitComment(articleId, {
        title: commentTitle.trim(),
        content: commentContent.trim(),
        authorName: authorName.trim() || undefined
      });

      if (response.message) {
        alert('评论提交成功！感谢您的参与！🎉');
      } else {
        alert('感谢您的学术反馈！您的评论已通过我们的元-算法伦理审查，现已发布。（审查时间：0.001纳秒）');
      }

      await loadComments();
      setCommentTitle('');
      setCommentContent('');
      setAuthorName('');
    } catch (error: any) {
      console.error('提交评论失败:', error);
      alert('提交评论失败: ' + (error.message || '未知错误'));
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('确定要删除这条评论吗？')) {
      return;
    }

    try {
      await deleteCommentDirect(commentId);
      alert('评论删除成功！🗑️');
      await loadComments();
    } catch (error: any) {
      console.error('删除评论失败:', error);
      alert('删除评论失败: ' + (error?.message || '未知错误'));
    }
  };

  const handleBatchDelete = async () => {
    if (selectedComments.length === 0) {
      alert('请先选择要删除的评论');
      return;
    }

    if (!confirm(`确定要删除选中的 ${selectedComments.length} 条评论吗？`)) {
      return;
    }

    setDeleting(true);
    try {
      const response = await batchDeleteCommentsDirect(selectedComments);
      alert(response.message);
      setSelectedComments([]);
      await loadComments();
    } catch (error: any) {
      console.error('批量删除评论失败:', error);
      alert('批量删除失败: ' + (error?.message || '未知错误'));
    } finally {
      setDeleting(false);
    }
  };

  // 简化的AI评论生成函数
  const generateAIComments = async (articleId: number, article: any, content: string, generateCount: number) => {
  const isNewsArticle = article.category === 'News & Comment';
  
  // 简化的prompt
  const prompt = `Generate exactly ${generateCount} ${isNewsArticle ? 'internet comments' : 'academic reviews'} as a JSON array.

Article: "${article.title}"
Content: ${content.substring(0, 500)}...

IMPORTANT: Use lots of emojis in both commentTitle and content! Make it fun and expressive! 😂🤣💀🔥🎪🤡

Return ONLY this JSON format:
[{"reviewerName": "Username", "reviewerTitle": "${isNewsArticle ? '@' : 'Dr.'}", "commentTitle": "Title with emojis 😂", "content": "Comment text with emojis 🤣💀"}]`;

  const response = await aiService.generateText({ prompt });

  // 更鲁棒的JSON解析
  let reviewsData;
  try {
    console.log('🤖 AI原始响应:', response);
    
    // 多步骤清理和解析
    let cleanResponse = response.trim();
    
    // 移除常见的非JSON前缀/后缀
    cleanResponse = cleanResponse
      .replace(/^[^[{]*/, '') // 移除开头的非JSON字符
      .replace(/[^}\]]*$/, '') // 移除结尾的非JSON字符
      .replace(/```json\s*/g, '') // 移除markdown代码块
      .replace(/```\s*/g, '') // 移除代码块结束标记
      .replace(/^Here.*?:/gm, '') // 移除"Here are the comments:"等前缀
      .trim();
    
    // 确保以[开头，]结尾
    if (!cleanResponse.startsWith('[')) {
      const arrayStart = cleanResponse.indexOf('[');
      if (arrayStart !== -1) {
        cleanResponse = cleanResponse.substring(arrayStart);
      }
    }
    if (!cleanResponse.endsWith(']')) {
      const arrayEnd = cleanResponse.lastIndexOf(']');
      if (arrayEnd !== -1) {
        cleanResponse = cleanResponse.substring(0, arrayEnd + 1);
      }
    }
    
    console.log('🧹 清理后的响应:', cleanResponse);
    reviewsData = JSON.parse(cleanResponse);

    // 确保是数组
    if (!Array.isArray(reviewsData)) {
      throw new Error('Response is not an array');
    }
    
    console.log('✅ JSON解析成功:', reviewsData);
  } catch (parseError) {
    console.warn('❌ JSON解析失败，使用备用格式:', parseError);
    // 备用方案：生成默认评论
    reviewsData = [];
    for (let i = 0; i < generateCount; i++) {
      reviewsData.push({
        reviewerName: isNewsArticle ? `InternetUser${i + 1}` : `Dr. Savage AI-${i + 1}`,
        reviewerTitle: isNewsArticle ? '@' : 'Dr.',
        commentTitle: isNewsArticle
          ? `This is absolutely wild! 😂🤯 #${i + 1}`
          : `Brutally Honest Academic Review 🔥💀📚 #${i + 1}`,
        content: isNewsArticle
          ? `Can't believe this actually happened lol 😭 This is peak academia right here 💀 Someone really wrote a whole report about this 🤣 I'm dying! 💀🎪`
          : `This article is a masterpiece of academic mediocrity! 😂🎓 The author has successfully proven that they can string words together in a vaguely coherent manner. 🤡📝 Truly groundbreaking work in the field of stating the obvious! 💀🎪🏆`
      });
    }
  }

    return reviewsData;
  };

  const handleGenerateAIComments = async () => {
    if (!confirm(`确定要生成 ${generateCount} 条AI评论吗？`)) {
      return;
    }

    setGenerating(true);
    try {
      const fullArticleContent = content || article.content || 'No content available';
      const reviewsData = await generateAIComments(articleId, article, fullArticleContent, generateCount);

      const generatedComments = [];
      for (const reviewData of reviewsData) {
        try {
          const comment = await articleApi.submitComment(articleId, {
            title: reviewData.commentTitle,
            content: reviewData.content,
            authorName: `${reviewData.reviewerTitle} ${reviewData.reviewerName}`,
          });
          generatedComments.push(comment);
        } catch (error) {
          console.error('提交评论失败:', error);
        }
      }

      alert(`成功生成 ${generatedComments.length} 条AI评论！`);
      await loadComments();
      setShowGenerateForm(false);
    } catch (error: any) {
      console.error('生成AI评论失败:', error);
      alert('生成AI评论失败: ' + (error.message || '未知错误'));
    } finally {
      setGenerating(false);
    }
  };

  return (
    <div className="bg-gray-50 border-t border-gray-200 mt-12">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-gray-900">
            {article.category === 'News & Comment' ? '💬 读者评论' : '🎓 同行评议'}
          </h2>
          <div className="text-sm text-gray-600">
            {comments.length} 条{article.category === 'News & Comment' ? '评论' : '评议'}
          </div>
        </div>

        {isLoggedIn && (
          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <h3 className="text-lg font-semibold text-gray-900">管理员控制</h3>
                {comments.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedComments.length === comments.length && comments.length > 0}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedComments(comments.map(c => c.id));
                        } else {
                          setSelectedComments([]);
                        }
                      }}
                      className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    />
                    <label className="text-sm text-gray-600">全选</label>
                  </div>
                )}
              </div>
              <div className="flex items-center space-x-3">
                {selectedComments.length > 0 && (
                  <button
                    onClick={handleBatchDelete}
                    disabled={deleting}
                    className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 text-sm"
                  >
                    {deleting ? '删除中...' : `批量删除 (${selectedComments.length})`}
                  </button>
                )}
                <button
                  onClick={() => setShowGenerateForm(!showGenerateForm)}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 text-sm"
                >
                  🤖 生成AI{article.category === 'News & Comment' ? '评论' : '评议'}
                </button>
              </div>
            </div>

            {showGenerateForm && (
              <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                <h4 className="font-medium text-gray-900 mb-3">
                  生成AI{article.category === 'News & Comment' ? '评论' : '评议'}
                </h4>
                <div className="flex items-center space-x-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      生成数量
                    </label>
                    <input
                      type="number"
                      min="1"
                      max="10"
                      value={generateCount}
                      onChange={(e) => setGenerateCount(Number(e.target.value))}
                      className="border border-gray-300 rounded-md px-3 py-2 text-sm w-20"
                      placeholder="3"
                    />
                  </div>
                  <div className="flex-1">
                    <button
                      onClick={handleGenerateAIComments}
                      disabled={generating}
                      className="w-full px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                    >
                      {generating ? '生成中...' : `生成 ${generateCount} 条AI${article.category === 'News & Comment' ? '评论' : '评议'}`}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        <div className="space-y-6">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">加载评论中...</p>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600 text-lg">还没有评论</p>
              <p className="text-gray-500 text-sm mt-2">成为第一个发表学术见解的人！</p>
            </div>
          ) : (
            comments.map((comment) => (
              <div key={comment.id} className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Avatar
                      name={comment.authorName}
                      size="md"
                    />
                    <div>
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-gray-900">{comment.authorName}</h4>
                        {comment.isAI && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            🤖 AI Generated
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        {new Date(comment.createdAt).toLocaleDateString('zh-CN', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>

                  {isLoggedIn && (
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={selectedComments.includes(comment.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedComments([...selectedComments, comment.id]);
                          } else {
                            setSelectedComments(selectedComments.filter(id => id !== comment.id));
                          }
                        }}
                        className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                      />
                      <button
                        onClick={() => handleDeleteComment(comment.id)}
                        className="text-red-600 hover:text-red-800 text-sm"
                        title="删除评论"
                      >
                        🗑️
                      </button>
                    </div>
                  )}
                </div>

                <h3 className="text-lg font-semibold text-gray-900 mb-3">{comment.title}</h3>
                <div className="prose prose-sm max-w-none text-gray-700">
                  {comment.content.split('\n').map((paragraph, index) => (
                    <p key={index} className="mb-2">{paragraph}</p>
                  ))}
                </div>
              </div>
            ))
          )}
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6 mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            发表{article.category === 'News & Comment' ? '评论' : '学术评议'}
          </h3>

          <form onSubmit={handleSubmitComment} className="space-y-4">
            <div>
              <label htmlFor="authorName" className="block text-sm font-medium text-gray-700 mb-1">
                {article.category === 'News & Comment' ? '昵称' : '学者姓名'} (可选)
              </label>
              <input
                type="text"
                id="authorName"
                value={authorName}
                onChange={(e) => setAuthorName(e.target.value)}
                placeholder={article.category === 'News & Comment' ? '匿名网友' : '匿名学者'}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label htmlFor="commentTitle" className="block text-sm font-medium text-gray-700 mb-1">
                {article.category === 'News & Comment' ? '评论标题' : '评议标题'}
              </label>
              <input
                type="text"
                id="commentTitle"
                value={commentTitle}
                onChange={(e) => setCommentTitle(e.target.value)}
                placeholder={article.category === 'News & Comment' ? '说说你的看法...' : '您的学术见解...'}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <div>
              <label htmlFor="commentContent" className="block text-sm font-medium text-gray-700 mb-1">
                {article.category === 'News & Comment' ? '评论内容' : '详细评议'}
              </label>
              <textarea
                id="commentContent"
                value={commentContent}
                onChange={(e) => setCommentContent(e.target.value)}
                placeholder={article.category === 'News & Comment'
                  ? '分享你对这篇新闻的看法、疑问或者吐槽...'
                  : '请提供您的专业评议，包括对研究方法、结论、创新性等方面的意见...'}
                required
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
              />
            </div>

            <button
              type="submit"
              disabled={submitting}
              className="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 font-medium"
            >
              {submitting ? '提交中...' : `发表${article.category === 'News & Comment' ? '评论' : '评议'}`}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};
