"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.likeArticle = exports.getArticlesForAdmin = exports.getFeaturedArticles = exports.getTrendingArticles = exports.deleteArticle = exports.updateArticle = exports.createArticle = exports.getArticleById = exports.getArticles = void 0;
const database_1 = __importDefault(require("../config/database"));
const generateUniqueDOI = async (title) => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const cleanTitle = title
        .replace(/[^\w\s-]/g, '')
        .replace(/\s+/g, '-')
        .toLowerCase()
        .substring(0, 30);
    const startOfDay = new Date(year, now.getMonth(), now.getDate());
    const endOfDay = new Date(year, now.getMonth(), now.getDate() + 1);
    const todayArticleCount = await database_1.default.article.count({
        where: {
            createdAt: {
                gte: startOfDay,
                lt: endOfDay
            }
        }
    });
    const sequence = String(todayArticleCount + 1).padStart(4, '0');
    return `10.1000/jocker.${year}.${month}.${day}.${sequence}.${cleanTitle}`;
};
const getArticles = async (query) => {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', q, category, author, published, featured, } = query;
    const skip = (page - 1) * limit;
    const where = {};
    if (q) {
        where.OR = [
            { title: { contains: q, mode: 'insensitive' } },
            { excerpt: { contains: q, mode: 'insensitive' } },
            { author: { contains: q, mode: 'insensitive' } },
            { category: { contains: q, mode: 'insensitive' } },
        ];
    }
    if (category) {
        where.category = { contains: category, mode: 'insensitive' };
    }
    if (author) {
        where.author = { contains: author, mode: 'insensitive' };
    }
    if (published !== undefined) {
        where.published = published;
    }
    if (featured !== undefined) {
        where.featured = featured;
    }
    const total = await database_1.default.article.count({ where });
    const articles = await database_1.default.article.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
            [sortBy]: sortOrder,
        },
        select: {
            id: true,
            title: true,
            author: true,
            category: true,
            excerpt: true,
            imageUrl: true,
            imagePrompt: true,
            doi: true,
            views: true,
            likes: true,
            createdAt: true,
            updatedAt: true,
            published: true,
            featured: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    const totalPages = Math.ceil(total / limit);
    return {
        data: articles,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
};
exports.getArticles = getArticles;
const getArticleById = async (id) => {
    const article = await database_1.default.article.findUnique({
        where: { id },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    if (!article) {
        throw new Error('文章不存在');
    }
    return article;
};
exports.getArticleById = getArticleById;
const createArticle = async (articleData, userId) => {
    const doi = await generateUniqueDOI(articleData.title);
    const data = {
        ...articleData,
        doi,
        published: articleData.published ?? false,
        featured: articleData.featured ?? false,
    };
    if (userId) {
        data.userId = userId;
    }
    const article = await database_1.default.article.create({
        data,
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    console.log(`✅ 新文章创建成功，DOI: ${doi}`);
    return article;
};
exports.createArticle = createArticle;
const updateArticle = async (id, articleData, userId) => {
    const existingArticle = await database_1.default.article.findUnique({
        where: { id },
    });
    if (!existingArticle) {
        throw new Error('文章不存在');
    }
    const article = await database_1.default.article.update({
        where: { id },
        data: articleData,
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    return article;
};
exports.updateArticle = updateArticle;
const deleteArticle = async (id, userId) => {
    const existingArticle = await database_1.default.article.findUnique({
        where: { id },
    });
    if (!existingArticle) {
        throw new Error('文章不存在');
    }
    await database_1.default.article.delete({
        where: { id },
    });
};
exports.deleteArticle = deleteArticle;
const getTrendingArticles = async (limit = 5) => {
    return database_1.default.article.findMany({
        where: { published: true },
        orderBy: [
            { views: 'desc' },
            { likes: 'desc' },
            { createdAt: 'desc' },
        ],
        take: limit,
        select: {
            id: true,
            title: true,
            author: true,
            category: true,
            excerpt: true,
            imageUrl: true,
            imagePrompt: true,
            doi: true,
            views: true,
            likes: true,
            createdAt: true,
            updatedAt: true,
            published: true,
            featured: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
};
exports.getTrendingArticles = getTrendingArticles;
const getFeaturedArticles = async (limit = 3) => {
    return database_1.default.article.findMany({
        where: {
            published: true,
            featured: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
            id: true,
            title: true,
            author: true,
            category: true,
            excerpt: true,
            imageUrl: true,
            imagePrompt: true,
            doi: true,
            views: true,
            likes: true,
            createdAt: true,
            updatedAt: true,
            published: true,
            featured: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
};
exports.getFeaturedArticles = getFeaturedArticles;
const getArticlesForAdmin = async (query) => {
    const { page = 1, limit = 50, sortBy = 'createdAt', sortOrder = 'desc', q, category, author, published, featured, } = query;
    const skip = (page - 1) * limit;
    const where = {};
    if (q) {
        where.OR = [
            { title: { contains: q, mode: 'insensitive' } },
            { excerpt: { contains: q, mode: 'insensitive' } },
            { author: { contains: q, mode: 'insensitive' } },
            { category: { contains: q, mode: 'insensitive' } },
        ];
    }
    if (category) {
        where.category = { contains: category, mode: 'insensitive' };
    }
    if (author) {
        where.author = { contains: author, mode: 'insensitive' };
    }
    if (published !== undefined) {
        where.published = published;
    }
    if (featured !== undefined) {
        where.featured = featured;
    }
    const total = await database_1.default.article.count({ where });
    const articles = await database_1.default.article.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
            [sortBy]: sortOrder,
        },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    const totalPages = Math.ceil(total / limit);
    return {
        data: articles,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
};
exports.getArticlesForAdmin = getArticlesForAdmin;
const likeArticle = async (id) => {
    const article = await database_1.default.article.update({
        where: { id },
        data: { likes: { increment: 1 } },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    return article;
};
exports.likeArticle = likeArticle;
//# sourceMappingURL=articleService.js.map