"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.likeArticle = exports.getArticlesForAdmin = exports.getFeaturedArticles = exports.getTrendingArticles = exports.deleteArticle = exports.updateArticle = exports.createArticle = exports.getArticleById = exports.getArticles = void 0;
const database_1 = __importDefault(require("../config/database"));
const getArticles = async (query) => {
    const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc', q, category, author, published, featured, } = query;
    const skip = (page - 1) * limit;
    const where = {};
    if (q) {
        where.OR = [
            { title: { contains: q, mode: 'insensitive' } },
            { excerpt: { contains: q, mode: 'insensitive' } },
            { author: { contains: q, mode: 'insensitive' } },
            { category: { contains: q, mode: 'insensitive' } },
        ];
    }
    if (category) {
        where.category = { contains: category, mode: 'insensitive' };
    }
    if (author) {
        where.author = { contains: author, mode: 'insensitive' };
    }
    if (published !== undefined) {
        where.published = published;
    }
    if (featured !== undefined) {
        where.featured = featured;
    }
    const total = await database_1.default.article.count({ where });
    const articles = await database_1.default.article.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
            [sortBy]: sortOrder,
        },
        select: {
            id: true,
            title: true,
            author: true,
            category: true,
            excerpt: true,
            imageUrl: true,
            imagePrompt: true,
            doi: true,
            views: true,
            likes: true,
            createdAt: true,
            updatedAt: true,
            published: true,
            featured: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    const totalPages = Math.ceil(total / limit);
    return {
        data: articles,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
};
exports.getArticles = getArticles;
const getArticleById = async (id) => {
    const article = await database_1.default.article.findUnique({
        where: { id },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    if (!article) {
        throw new Error('文章不存在');
    }
    return article;
};
exports.getArticleById = getArticleById;
const createArticle = async (articleData, userId) => {
    const data = {
        ...articleData,
        published: articleData.published ?? false,
        featured: articleData.featured ?? false,
    };
    if (userId) {
        data.userId = userId;
    }
    const article = await database_1.default.article.create({
        data,
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    return article;
};
exports.createArticle = createArticle;
const updateArticle = async (id, articleData, userId) => {
    const existingArticle = await database_1.default.article.findUnique({
        where: { id },
    });
    if (!existingArticle) {
        throw new Error('文章不存在');
    }
    const article = await database_1.default.article.update({
        where: { id },
        data: articleData,
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    return article;
};
exports.updateArticle = updateArticle;
const deleteArticle = async (id, userId) => {
    const existingArticle = await database_1.default.article.findUnique({
        where: { id },
    });
    if (!existingArticle) {
        throw new Error('文章不存在');
    }
    await database_1.default.article.delete({
        where: { id },
    });
};
exports.deleteArticle = deleteArticle;
const getTrendingArticles = async (limit = 5) => {
    return database_1.default.article.findMany({
        where: { published: true },
        orderBy: [
            { views: 'desc' },
            { likes: 'desc' },
            { createdAt: 'desc' },
        ],
        take: limit,
        select: {
            id: true,
            title: true,
            author: true,
            category: true,
            excerpt: true,
            imageUrl: true,
            imagePrompt: true,
            doi: true,
            views: true,
            likes: true,
            createdAt: true,
            updatedAt: true,
            published: true,
            featured: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
};
exports.getTrendingArticles = getTrendingArticles;
const getFeaturedArticles = async (limit = 3) => {
    return database_1.default.article.findMany({
        where: {
            published: true,
            featured: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
            id: true,
            title: true,
            author: true,
            category: true,
            excerpt: true,
            imageUrl: true,
            imagePrompt: true,
            doi: true,
            views: true,
            likes: true,
            createdAt: true,
            updatedAt: true,
            published: true,
            featured: true,
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
};
exports.getFeaturedArticles = getFeaturedArticles;
const getArticlesForAdmin = async (query) => {
    const { page = 1, limit = 50, sortBy = 'createdAt', sortOrder = 'desc', q, category, author, published, featured, } = query;
    const skip = (page - 1) * limit;
    const where = {};
    if (q) {
        where.OR = [
            { title: { contains: q, mode: 'insensitive' } },
            { excerpt: { contains: q, mode: 'insensitive' } },
            { author: { contains: q, mode: 'insensitive' } },
            { category: { contains: q, mode: 'insensitive' } },
        ];
    }
    if (category) {
        where.category = { contains: category, mode: 'insensitive' };
    }
    if (author) {
        where.author = { contains: author, mode: 'insensitive' };
    }
    if (published !== undefined) {
        where.published = published;
    }
    if (featured !== undefined) {
        where.featured = featured;
    }
    const total = await database_1.default.article.count({ where });
    const articles = await database_1.default.article.findMany({
        where,
        skip,
        take: limit,
        orderBy: {
            [sortBy]: sortOrder,
        },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    const totalPages = Math.ceil(total / limit);
    return {
        data: articles,
        pagination: {
            page,
            limit,
            total,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
        },
    };
};
exports.getArticlesForAdmin = getArticlesForAdmin;
const likeArticle = async (id) => {
    const article = await database_1.default.article.update({
        where: { id },
        data: { likes: { increment: 1 } },
        include: {
            user: {
                select: {
                    id: true,
                    username: true,
                    name: true,
                    avatar: true,
                },
            },
        },
    });
    return article;
};
exports.likeArticle = likeArticle;
//# sourceMappingURL=articleService.js.map