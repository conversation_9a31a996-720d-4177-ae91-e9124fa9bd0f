
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  username: 'username',
  password: 'password',
  name: 'name',
  avatar: 'avatar',
  role: 'role',
  status: 'status',
  lastLogin: 'lastLogin',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ArticleScalarFieldEnum = {
  id: 'id',
  title: 'title',
  author: 'author',
  category: 'category',
  excerpt: 'excerpt',
  content: 'content',
  imageUrl: 'imageUrl',
  imagePrompt: 'imagePrompt',
  doi: 'doi',
  published: 'published',
  featured: 'featured',
  views: 'views',
  likes: 'likes',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  userId: 'userId'
};

exports.Prisma.TagScalarFieldEnum = {
  id: 'id',
  name: 'name',
  color: 'color',
  createdAt: 'createdAt'
};

exports.Prisma.ArticleTagScalarFieldEnum = {
  articleId: 'articleId',
  tagId: 'tagId'
};

exports.Prisma.FigureScalarFieldEnum = {
  id: 'id',
  articleId: 'articleId',
  figureNumber: 'figureNumber',
  title: 'title',
  description: 'description',
  caption: 'caption',
  imagePrompt: 'imagePrompt',
  imageUrl: 'imageUrl',
  thumbnailUrl: 'thumbnailUrl',
  width: 'width',
  height: 'height',
  fileSize: 'fileSize',
  mimeType: 'mimeType',
  status: 'status',
  errorMsg: 'errorMsg',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIGenerationLogScalarFieldEnum = {
  id: 'id',
  type: 'type',
  prompt: 'prompt',
  response: 'response',
  success: 'success',
  errorMsg: 'errorMsg',
  duration: 'duration',
  relatedId: 'relatedId',
  createdAt: 'createdAt'
};

exports.Prisma.ArticleCommentScalarFieldEnum = {
  id: 'id',
  articleId: 'articleId',
  authorName: 'authorName',
  authorTitle: 'authorTitle',
  title: 'title',
  content: 'content',
  isAI: 'isAI',
  isApproved: 'isApproved',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.FounderScalarFieldEnum = {
  id: 'id',
  name: 'name',
  title: 'title',
  description: 'description',
  avatarUrl: 'avatarUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.ArticleViewScalarFieldEnum = {
  id: 'id',
  articleId: 'articleId',
  userId: 'userId',
  ipAddress: 'ipAddress',
  userAgent: 'userAgent',
  fingerprint: 'fingerprint',
  createdAt: 'createdAt'
};

exports.Prisma.ArticleAudioScalarFieldEnum = {
  id: 'id',
  articleId: 'articleId',
  title: 'title',
  description: 'description',
  fileName: 'fileName',
  fileSize: 'fileSize',
  duration: 'duration',
  mimeType: 'mimeType',
  audioUrl: 'audioUrl',
  uploadedBy: 'uploadedBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AdvertisementCoverScalarFieldEnum = {
  id: 'id',
  imageUrl: 'imageUrl',
  title: 'title',
  description: 'description',
  linkUrl: 'linkUrl',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.MerchandiseImageScalarFieldEnum = {
  id: 'id',
  itemId: 'itemId',
  imageUrl: 'imageUrl',
  title: 'title',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AIConfigScalarFieldEnum = {
  id: 'id',
  provider: 'provider',
  apiKey: 'apiKey',
  baseUrl: 'baseUrl',
  isActive: 'isActive',
  textModel: 'textModel',
  imageModel: 'imageModel',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemLogScalarFieldEnum = {
  id: 'id',
  version: 'version',
  date: 'date',
  type: 'type',
  title: 'title',
  description: 'description',
  changes: 'changes',
  createdBy: 'createdBy',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JournalIconScalarFieldEnum = {
  id: 'id',
  icon: 'icon',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};


exports.Prisma.ModelName = {
  User: 'User',
  Article: 'Article',
  Tag: 'Tag',
  ArticleTag: 'ArticleTag',
  Figure: 'Figure',
  AIGenerationLog: 'AIGenerationLog',
  ArticleComment: 'ArticleComment',
  Founder: 'Founder',
  ArticleView: 'ArticleView',
  ArticleAudio: 'ArticleAudio',
  AdvertisementCover: 'AdvertisementCover',
  MerchandiseImage: 'MerchandiseImage',
  AIConfig: 'AIConfig',
  SystemLog: 'SystemLog',
  JournalIcon: 'JournalIcon'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
