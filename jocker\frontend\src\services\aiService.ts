import { GoogleGenAI } from '@google/genai';
import OpenAI from 'openai';
import { adminApi } from './api';

interface AIConfig {
  // 文本生成配置
  textProvider: 'gemini' | 'openai';
  textApiKey: string;
  textBaseUrl?: string;
  textModel: string;

  // 图片生成配置
  imageProvider: 'gemini' | 'openai';
  imageApiKey: string;
  imageBaseUrl?: string;
  imageModel: string;

  isActive: boolean;

  // 兼容旧配置
  provider?: 'gemini' | 'openai';
  apiKey?: string;
  baseUrl?: string;
}

interface TextGenerationOptions {
  model?: string;
  prompt: string;
  responseFormat?: 'json' | 'text';
}

interface ImageGenerationOptions {
  model?: string;
  prompt: string;
  numberOfImages?: number;
  size?: string;
  aspectRatio?: string;
}

class AIService {
  private config: AIConfig | null = null;

  // 文本生成客户端
  private textGeminiClient: any = null;
  private textOpenaiClient: OpenAI | null = null;

  // 图片生成客户端
  private imageGeminiClient: any = null;
  private imageOpenaiClient: OpenAI | null = null;

  async getConfig(): Promise<AIConfig> {
    if (!this.config) {
      try {
        // 使用专门的API获取包含完整API Key的配置
        const result = await adminApi.getAIConfigForClient();
        console.log('🔍 获取到的AI配置:', {
          textProvider: result.config?.textProvider || result.config?.provider,
          imageProvider: result.config?.imageProvider || result.config?.provider,
          hasTextApiKey: !!(result.config?.textApiKey || result.config?.apiKey),
          hasImageApiKey: !!(result.config?.imageApiKey || result.config?.apiKey),
          textBaseUrl: result.config?.textBaseUrl,
          imageBaseUrl: result.config?.imageBaseUrl,
          textModel: result.config?.textModel,
          imageModel: result.config?.imageModel,
          isActive: result.config?.isActive,
          rawConfig: result.config // 完整配置用于调试
        });

        if (result.config && result.config.isActive) {
          const config = result.config;

          // 兼容新旧配置格式
          if (config.textProvider && config.imageProvider) {
            // 新格式：分别配置文本和图片
            if (!config.textApiKey || !config.imageApiKey) {
              console.error('❌ 新格式配置中缺少文本或图片API Key');
              throw new Error('配置中缺少文本或图片API Key');
            }

            this.config = {
              textProvider: config.textProvider,
              textApiKey: config.textApiKey,
              textBaseUrl: config.textBaseUrl,
              textModel: config.textModel || 'gemini-2.5-flash',

              imageProvider: config.imageProvider,
              imageApiKey: config.imageApiKey,
              imageBaseUrl: config.imageBaseUrl,
              imageModel: config.imageModel || 'imagen-3.0-generate-002',

              isActive: config.isActive,

              // 保持兼容性
              provider: config.textProvider,
              apiKey: config.textApiKey,
              baseUrl: config.textBaseUrl
            };

            console.log(`🔧 使用分离配置 - 文本: ${this.config.textProvider}/${this.config.textModel}, 图片: ${this.config.imageProvider}/${this.config.imageModel}`);
          } else {
            // 旧格式：统一配置
            if (!config.apiKey) {
              console.error('❌ 旧格式配置中缺少API Key');
              throw new Error('配置中缺少API Key');
            }

            this.config = {
              textProvider: config.provider || 'gemini',
              textApiKey: config.apiKey,
              textBaseUrl: config.baseUrl,
              textModel: config.textModel || 'gemini-2.5-flash',

              imageProvider: config.provider || 'gemini',
              imageApiKey: config.apiKey,
              imageBaseUrl: config.baseUrl,
              imageModel: config.imageModel || 'imagen-3.0-generate-002',

              isActive: config.isActive,

              // 保持兼容性
              provider: config.provider || 'gemini',
              apiKey: config.apiKey,
              baseUrl: config.baseUrl
            };

            console.log(`🔧 使用统一配置 - 供应商: ${this.config.provider}, 文本模型: ${this.config.textModel}, 图片模型: ${this.config.imageModel}`);
          }
        } else {
          throw new Error('没有活跃的AI配置');
        }
      } catch (error) {
        console.log('获取用户AI配置失败，使用默认Gemini配置:', error);
        // 回退到默认配置
        const { apiKey } = await adminApi.getAIApiKey();
        this.config = {
          textProvider: 'gemini',
          textApiKey: apiKey,
          textModel: 'gemini-2.5-flash',

          imageProvider: 'gemini',
          imageApiKey: apiKey,
          imageModel: 'imagen-3.0-generate-002',

          isActive: true,

          // 保持兼容性
          provider: 'gemini',
          apiKey
        };
      }
    }
    return this.config;
  }

  private async initializeClients() {
    const config = await this.getConfig();

    // 检查API Key是否存在
    if (!config.textApiKey || !config.imageApiKey) {
      throw new Error('文本或图片API Key未配置或为空');
    }

    console.log(`🔧 初始化AI客户端 - 文本: ${config.textProvider}, 图片: ${config.imageProvider}`);

    // 初始化文本生成客户端
    if (config.textProvider === 'gemini') {
      if (!this.textGeminiClient) {
        this.textGeminiClient = new GoogleGenAI({ apiKey: config.textApiKey });
        console.log('✅ 文本Gemini客户端初始化成功');
      }
    } else if (config.textProvider === 'openai') {
      try {
        this.textOpenaiClient = new OpenAI({
          apiKey: config.textApiKey,
          baseURL: config.textBaseUrl,
          dangerouslyAllowBrowser: true
        });
        console.log(`✅ 文本OpenAI客户端初始化成功 - baseURL: ${config.textBaseUrl}`);
      } catch (error) {
        console.error('❌ 文本OpenAI客户端初始化失败:', error);
        throw error;
      }
    }

    // 初始化图片生成客户端
    if (config.imageProvider === 'gemini') {
      if (!this.imageGeminiClient) {
        this.imageGeminiClient = new GoogleGenAI({ apiKey: config.imageApiKey });
        console.log('✅ 图片Gemini客户端初始化成功');
      }
    } else if (config.imageProvider === 'openai') {
      try {
        this.imageOpenaiClient = new OpenAI({
          apiKey: config.imageApiKey,
          baseURL: config.imageBaseUrl,
          dangerouslyAllowBrowser: true
        });
        console.log(`✅ 图片OpenAI客户端初始化成功 - baseURL: ${config.imageBaseUrl}`);
      } catch (error) {
        console.error('❌ 图片OpenAI客户端初始化失败:', error);
        throw error;
      }
    }
  }

  async generateText(options: TextGenerationOptions): Promise<string> {
    await this.initializeClients();
    const config = await this.getConfig();
    const model = options.model || config.textModel;

    console.log(`📝 使用 ${config.textProvider} 的模型 ${model} 生成文本`);

    if (config.textProvider === 'gemini') {
      const response = await this.textGeminiClient.models.generateContent({
        model,
        contents: options.prompt,
        config: options.responseFormat === 'json' ? { responseMimeType: "application/json" } : undefined,
      });
      return response.text || '';
    } else if (config.textProvider === 'openai') {
      const response = await this.textOpenaiClient!.chat.completions.create({
        model,
        messages: [{ role: 'user', content: options.prompt }],
        response_format: options.responseFormat === 'json' ? { type: 'json_object' } : undefined,
      });
      return response.choices[0]?.message?.content || '';
    }

    throw new Error(`不支持的文本供应商: ${config.textProvider}`);
  }

  async generateImage(options: ImageGenerationOptions): Promise<string> {
    await this.initializeClients();
    const config = await this.getConfig();
    const model = options.model || config.imageModel;

    console.log(`🎨 使用 ${config.imageProvider} 的模型 ${model} 生成图片`);

    if (config.imageProvider === 'gemini') {
      const imageResponse = await this.imageGeminiClient.models.generateImages({
        model,
        prompt: options.prompt,
        config: {
          numberOfImages: options.numberOfImages || 1,
          outputMimeType: 'image/jpeg',
          aspectRatio: options.aspectRatio || '1:1',
        },
      });

      const base64ImageBytes = imageResponse.generatedImages?.[0]?.image?.imageBytes || '';
      return `data:image/jpeg;base64,${base64ImageBytes}`;
    } else if (config.imageProvider === 'openai') {
      console.log('🖼️ OpenAI图片生成参数:', {
        model,
        prompt: options.prompt,
        n: options.numberOfImages || 1,
        size: options.size || '1024x1024',
        response_format: 'b64_json',
      });

      const response = await this.imageOpenaiClient!.images.generate({
        model,
        prompt: options.prompt,
        n: options.numberOfImages || 1,
        size: options.size || '1024x1024',
        response_format: 'b64_json',
      });

      const base64Image = response.data[0]?.b64_json || '';
      return `data:image/png;base64,${base64Image}`;
    }

    throw new Error(`不支持的图片供应商: ${config.imageProvider}`);
  }

  // 测试API连接
  async testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
    try {
      await this.initializeClients();
      const config = await this.getConfig();

      console.log(`🧪 测试文本: ${config.textProvider}, 图片: ${config.imageProvider} API连接...`);

      // 测试文本生成
      let textTestResult = '';
      if (config.textProvider === 'gemini') {
        const response = await this.textGeminiClient.models.generateContent({
          model: config.textModel,
          contents: 'Hello, this is a test message. Please respond with "Test successful".',
        });
        textTestResult = response.text || '';
      } else if (config.textProvider === 'openai') {
        const response = await this.textOpenaiClient!.chat.completions.create({
          model: config.textModel,
          messages: [{ role: 'user', content: 'Hello, this is a test message. Please respond with "Test successful".' }],
        });
        textTestResult = response.choices[0]?.message?.content || '';
      }

      if (textTestResult.length > 0) {
        return {
          success: true,
          message: `API测试成功！文本: ${config.textProvider}/${config.textModel}, 图片: ${config.imageProvider}/${config.imageModel}`,
          details: {
            textResponse: textTestResult.substring(0, 100) + '...',
            textProvider: config.textProvider,
            imageProvider: config.imageProvider
          }
        };
      } else {
        return {
          success: false,
          message: `文本API测试失败: ${config.textProvider}`,
          details: { textResponse: textTestResult }
        };
      }
    } catch (error) {
      console.error('❌ API测试失败:', error);
      return {
        success: false,
        message: `API测试失败: ${error instanceof Error ? error.message : '未知错误'}`,
        details: { error: error instanceof Error ? error.message : error }
      };
    }
  }

  // 重置配置缓存（当用户更新配置时调用）
  resetConfig() {
    console.log('🔄 重置AI配置缓存');
    this.config = null;
    this.geminiClient = null;
    this.openaiClient = null;
  }
}

// 导出单例实例
export const aiService = new AIService();
