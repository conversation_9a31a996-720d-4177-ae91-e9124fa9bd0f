import { GoogleGenAI } from '@google/genai';
import OpenAI from 'openai';
import { adminApi } from './api';

interface AIConfig {
  provider: 'gemini' | 'openai';
  apiKey: string;
  baseUrl?: string;
  textModel: string;
  imageModel: string;
  isActive: boolean;
}

interface TextGenerationOptions {
  model?: string;
  prompt: string;
  responseFormat?: 'json' | 'text';
}

interface ImageGenerationOptions {
  model?: string;
  prompt: string;
  numberOfImages?: number;
  size?: string;
  aspectRatio?: string;
}

class AIService {
  private config: AIConfig | null = null;
  private geminiClient: any = null;
  private openaiClient: OpenAI | null = null;

  async getConfig(): Promise<AIConfig> {
    if (!this.config) {
      try {
        const result = await adminApi.getAIConfig();
        if (result.config && result.config.isActive) {
          this.config = result.config;
          console.log(`🔧 使用用户配置 - 供应商: ${this.config.provider}, 文本模型: ${this.config.textModel}, 图片模型: ${this.config.imageModel}`);
        } else {
          throw new Error('没有活跃的AI配置');
        }
      } catch (error) {
        console.log('获取用户AI配置失败，使用默认Gemini配置');
        // 回退到默认配置
        const { apiKey } = await adminApi.getAIApiKey();
        this.config = {
          provider: 'gemini',
          apiKey,
          textModel: 'gemini-2.5-flash',
          imageModel: 'imagen-3.0-generate-002',
          isActive: true
        };
      }
    }
    return this.config;
  }

  private async initializeClients() {
    const config = await this.getConfig();
    
    if (config.provider === 'gemini') {
      if (!this.geminiClient) {
        this.geminiClient = new GoogleGenAI({ apiKey: config.apiKey });
      }
    } else if (config.provider === 'openai') {
      if (!this.openaiClient) {
        this.openaiClient = new OpenAI({
          apiKey: config.apiKey,
          baseURL: config.baseUrl,
          dangerouslyAllowBrowser: true
        });
      }
    }
  }

  async generateText(options: TextGenerationOptions): Promise<string> {
    await this.initializeClients();
    const config = await this.getConfig();
    const model = options.model || config.textModel;

    console.log(`📝 使用 ${config.provider} 的模型 ${model} 生成文本`);

    if (config.provider === 'gemini') {
      const response = await this.geminiClient.models.generateContent({
        model,
        contents: options.prompt,
        config: options.responseFormat === 'json' ? { responseMimeType: "application/json" } : undefined,
      });
      return response.text || '';
    } else if (config.provider === 'openai') {
      const response = await this.openaiClient!.chat.completions.create({
        model,
        messages: [{ role: 'user', content: options.prompt }],
        response_format: options.responseFormat === 'json' ? { type: 'json_object' } : undefined,
      });
      return response.choices[0]?.message?.content || '';
    }

    throw new Error(`不支持的供应商: ${config.provider}`);
  }

  async generateImage(options: ImageGenerationOptions): Promise<string> {
    await this.initializeClients();
    const config = await this.getConfig();
    const model = options.model || config.imageModel;

    console.log(`🎨 使用 ${config.provider} 的模型 ${model} 生成图片`);

    if (config.provider === 'gemini') {
      const imageResponse = await this.geminiClient.models.generateImages({
        model,
        prompt: options.prompt,
        config: {
          numberOfImages: options.numberOfImages || 1,
          outputMimeType: 'image/jpeg',
          aspectRatio: options.aspectRatio || '1:1',
        },
      });
      
      const base64ImageBytes = imageResponse.generatedImages?.[0]?.image?.imageBytes || '';
      return `data:image/jpeg;base64,${base64ImageBytes}`;
    } else if (config.provider === 'openai') {
      const response = await this.openaiClient!.images.generate({
        model,
        prompt: options.prompt,
        n: options.numberOfImages || 1,
        size: options.size || '1024x1024',
        response_format: 'b64_json',
      });
      
      const base64Image = response.data[0]?.b64_json || '';
      return `data:image/png;base64,${base64Image}`;
    }

    throw new Error(`不支持的供应商: ${config.provider}`);
  }

  // 重置配置缓存（当用户更新配置时调用）
  resetConfig() {
    this.config = null;
    this.geminiClient = null;
    this.openaiClient = null;
  }
}

// 导出单例实例
export const aiService = new AIService();
