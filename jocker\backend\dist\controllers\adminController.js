"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getGeminiKey = exports.setGeminiKey = exports.getAIConfig = exports.setAIConfig = exports.getAdvertisementCover = exports.uploadAdvertisementCover = exports.deleteUser = exports.updateUserRole = exports.updateUserStatus = exports.getAllUsers = exports.getWebsiteCover = exports.uploadWebsiteCover = exports.getFounderAvatar = exports.uploadFounderAvatar = exports.clearArticleFigures = exports.saveFigure = exports.regenerateFigureImage = exports.getArticleFiguresList = exports.saveArticleContent = exports.generateAndSaveArticleContent = exports.getAIApiKey = exports.getAdminArticleList = void 0;
const asyncHandler_1 = require("../utils/asyncHandler");
const database_1 = __importDefault(require("../config/database"));
const aiService_1 = require("../services/aiService");
const articleService_1 = require("../services/articleService");
exports.getAdminArticleList = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以访问',
        });
    }
    const query = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 50,
        sortBy: req.query.sortBy || 'createdAt',
        sortOrder: req.query.sortOrder || 'desc',
        q: req.query.q,
        category: req.query.category,
        author: req.query.author,
        published: req.query.published ? req.query.published === 'true' : undefined,
        featured: req.query.featured ? req.query.featured === 'true' : undefined,
    };
    const result = await (0, articleService_1.getArticlesForAdmin)(query);
    const response = {
        success: true,
        message: '获取管理员文章列表成功',
        data: result,
    };
    res.status(200).json(response);
});
exports.getAIApiKey = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以获取 API Key',
        });
    }
    const apiKey = process.env.GOOGLE_AI_API_KEY;
    if (!apiKey) {
        return res.status(500).json({
            success: false,
            message: 'Google AI API Key 未配置',
        });
    }
    const response = {
        success: true,
        message: 'API Key 获取成功',
        data: {
            apiKey: apiKey,
        },
    };
    return res.status(200).json(response);
});
exports.generateAndSaveArticleContent = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以生成文章内容',
        });
    }
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        if (article.content && article.content.trim() !== '') {
            return res.status(200).json({
                success: true,
                message: '文章内容已存在',
                data: {
                    content: article.content,
                    cached: true,
                },
            });
        }
        const generatedContent = await (0, aiService_1.generateArticleContentWithFigures)(article);
        const updatedArticle = await database_1.default.article.update({
            where: { id: articleId },
            data: {
                content: generatedContent,
                updatedAt: new Date(),
            },
        });
        const response = {
            success: true,
            message: '文章内容生成并保存成功，图片正在后台生成',
            data: {
                content: generatedContent,
                cached: false,
            },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('生成文章内容失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '生成文章内容失败',
        });
    }
});
exports.saveArticleContent = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const articleId = parseInt(req.params.id);
    const { content } = req.body;
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    if (!content || typeof content !== 'string') {
        return res.status(400).json({
            success: false,
            message: '文章内容不能为空',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        await database_1.default.article.update({
            where: { id: articleId },
            data: {
                content: content,
                updatedAt: new Date(),
            },
        });
        console.log(`✅ 文章 ${articleId} 内容保存成功`);
        const response = {
            success: true,
            message: '文章内容保存成功',
            data: { success: true },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('保存文章内容失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '保存文章内容失败',
        });
    }
});
exports.getArticleFiguresList = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章 ID',
        });
    }
    try {
        const figures = [];
        const response = {
            success: true,
            message: '图片功能已迁移到前端，请使用前端的图片管理功能',
            data: { figures },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取图片列表失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取图片列表失败',
        });
    }
});
exports.regenerateFigureImage = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const figureId = req.params.id;
    try {
        const response = {
            success: true,
            message: '图片生成功能已迁移到前端，请使用前端的图片重新生成功能',
            data: { figureId },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('启动图片重新生成失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '启动图片重新生成失败',
        });
    }
});
exports.saveFigure = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const { articleId, figureNumber, title, description, caption, imagePrompt, imageUrl, status } = req.body;
    try {
        if (!articleId || !figureNumber || !title) {
            return res.status(400).json({
                success: false,
                message: '缺少必需的图片信息',
            });
        }
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        const figure = await database_1.default.figure.upsert({
            where: {
                articleId_figureNumber: {
                    articleId,
                    figureNumber
                }
            },
            update: {
                title,
                description: description || '',
                caption: caption || `Figure ${figureNumber}`,
                imagePrompt: imagePrompt || '',
                imageUrl: imageUrl || '',
                status: status || 'completed',
                updatedAt: new Date()
            },
            create: {
                articleId,
                figureNumber,
                title,
                description: description || '',
                caption: caption || `Figure ${figureNumber}`,
                imagePrompt: imagePrompt || '',
                imageUrl: imageUrl || '',
                status: status || 'completed',
            },
        });
        console.log(`✅ 图片 ${figureNumber} 保存成功: ${figure.id}`);
        const response = {
            success: true,
            message: '图片保存成功',
            data: { figureId: figure.id },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('保存图片失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '保存图片失败',
        });
    }
});
exports.clearArticleFigures = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const { id } = req.params;
    const articleId = parseInt(id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章ID',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        const deletedFigures = await database_1.default.figure.deleteMany({
            where: {
                articleId: articleId
            }
        });
        console.log(`🗑️ 已清理文章 ${articleId} 的 ${deletedFigures.count} 个图片记录`);
        const response = {
            success: true,
            message: `已清理 ${deletedFigures.count} 个图片记录`,
            data: {
                deletedCount: deletedFigures.count
            }
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('清理图片记录失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '清理图片记录失败',
        });
    }
});
exports.uploadFounderAvatar = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以上传创始人头像',
        });
    }
    const { avatarUrl, name, title, description } = req.body;
    if (!avatarUrl || typeof avatarUrl !== 'string') {
        return res.status(400).json({
            success: false,
            message: '头像数据不能为空',
        });
    }
    try {
        const founder = await database_1.default.founder.upsert({
            where: { id: 1 },
            update: {
                avatarUrl,
                name: name || 'Dr. Serious McFunnyface',
                title: title || 'Editor-in-Chief',
                description: description || 'Leading researcher in satirical science and academic absurdity.',
                updatedAt: new Date(),
            },
            create: {
                id: 1,
                avatarUrl,
                name: name || 'Dr. Serious McFunnyface',
                title: title || 'Editor-in-Chief',
                description: description || 'Leading researcher in satirical science and academic absurdity.',
            },
        });
        console.log('✅ 创始人头像上传成功');
        const response = {
            success: true,
            message: '创始人头像上传成功',
            data: { founder },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('上传创始人头像失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '上传创始人头像失败',
        });
    }
});
exports.getFounderAvatar = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    try {
        const founder = await database_1.default.founder.findFirst({
            where: { id: 1 },
        });
        const response = {
            success: true,
            message: '获取创始人信息成功',
            data: { founder },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取创始人信息失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取创始人信息失败',
        });
    }
});
exports.uploadWebsiteCover = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以上传网站封面',
        });
    }
    const { coverUrl, title, description } = req.body;
    if (!coverUrl || typeof coverUrl !== 'string') {
        return res.status(400).json({
            success: false,
            message: '封面数据不能为空',
        });
    }
    try {
        const existingConfig = await database_1.default.founder.findFirst({
            where: { id: 2 },
        });
        let websiteConfig;
        if (existingConfig) {
            websiteConfig = await database_1.default.founder.update({
                where: { id: 2 },
                data: {
                    name: title || '网站封面',
                    title: 'website_cover',
                    description: description || '网站主页封面图片',
                    avatarUrl: coverUrl,
                },
            });
        }
        else {
            websiteConfig = await database_1.default.founder.create({
                data: {
                    id: 2,
                    name: title || '网站封面',
                    title: 'website_cover',
                    description: description || '网站主页封面图片',
                    avatarUrl: coverUrl,
                },
            });
        }
        const response = {
            success: true,
            message: '网站封面上传成功',
            data: { websiteConfig },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('上传网站封面失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '上传网站封面失败',
        });
    }
});
exports.getWebsiteCover = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    try {
        const websiteConfig = await database_1.default.founder.findFirst({
            where: { id: 2 },
        });
        const response = {
            success: true,
            message: '获取网站封面成功',
            data: { websiteConfig },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取网站封面失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取网站封面失败',
        });
    }
});
exports.getAllUsers = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以查看用户列表',
        });
    }
    try {
        const users = await database_1.default.user.findMany({
            select: {
                id: true,
                email: true,
                username: true,
                name: true,
                role: true,
                status: true,
                lastLogin: true,
                createdAt: true,
                updatedAt: true,
                _count: {
                    select: {
                        articles: true
                    }
                }
            },
            orderBy: {
                createdAt: 'desc'
            }
        });
        const response = {
            success: true,
            message: '获取用户列表成功',
            data: { users },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取用户列表失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取用户列表失败',
        });
    }
});
exports.updateUserStatus = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const userId = parseInt(req.params.id);
    const { status } = req.body;
    if (isNaN(userId)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户 ID',
        });
    }
    if (!status || !['ACTIVE', 'DISABLED', 'SUSPENDED'].includes(status)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户状态',
        });
    }
    if (userId === req.user.userId && status !== 'ACTIVE') {
        return res.status(400).json({
            success: false,
            message: '不能禁用自己的账户',
        });
    }
    try {
        const user = await database_1.default.user.update({
            where: { id: userId },
            data: { status },
            select: {
                id: true,
                username: true,
                email: true,
                status: true,
            }
        });
        console.log(`✅ 用户 ${user.username} 状态更新为: ${status}`);
        const response = {
            success: true,
            message: `用户状态更新为 ${status}`,
            data: { user },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('更新用户状态失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '更新用户状态失败',
        });
    }
});
exports.updateUserRole = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const userId = parseInt(req.params.id);
    const { role } = req.body;
    if (isNaN(userId)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户 ID',
        });
    }
    if (!role || !['USER', 'EDITOR', 'ADMIN'].includes(role)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户角色',
        });
    }
    if (userId === req.user.userId && role !== 'ADMIN') {
        return res.status(400).json({
            success: false,
            message: '不能修改自己的管理员权限',
        });
    }
    try {
        const user = await database_1.default.user.update({
            where: { id: userId },
            data: { role },
            select: {
                id: true,
                username: true,
                email: true,
                role: true,
            }
        });
        console.log(`✅ 用户 ${user.username} 角色更新为: ${role}`);
        const response = {
            success: true,
            message: `用户角色更新为 ${role}`,
            data: { user },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('更新用户角色失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '更新用户角色失败',
        });
    }
});
exports.deleteUser = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const userId = parseInt(req.params.id);
    if (isNaN(userId)) {
        return res.status(400).json({
            success: false,
            message: '无效的用户 ID',
        });
    }
    if (userId === req.user.userId) {
        return res.status(400).json({
            success: false,
            message: '不能删除自己的账户',
        });
    }
    try {
        const user = await database_1.default.user.findUnique({
            where: { id: userId },
            select: { username: true, email: true }
        });
        if (!user) {
            return res.status(404).json({
                success: false,
                message: '用户不存在',
            });
        }
        await database_1.default.user.delete({
            where: { id: userId }
        });
        console.log(`🗑️ 用户 ${user.username} 已被删除`);
        const response = {
            success: true,
            message: `用户 ${user.username} 已被删除`,
            data: { deletedUser: user },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('删除用户失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '删除用户失败',
        });
    }
});
exports.uploadAdvertisementCover = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以上传广告封面',
        });
    }
    const { imageUrl, title, description, linkUrl } = req.body;
    if (!imageUrl) {
        return res.status(400).json({
            success: false,
            message: '图片数据不能为空',
        });
    }
    try {
        const existingAd = await database_1.default.advertisementCover.findFirst();
        let advertisement;
        if (existingAd) {
            advertisement = await database_1.default.advertisementCover.update({
                where: { id: existingAd.id },
                data: {
                    imageUrl,
                    title: title || '广告标题',
                    description: description || '广告描述',
                    linkUrl: linkUrl || '#',
                    updatedAt: new Date(),
                },
            });
        }
        else {
            advertisement = await database_1.default.advertisementCover.create({
                data: {
                    imageUrl,
                    title: title || '广告标题',
                    description: description || '广告描述',
                    linkUrl: linkUrl || '#',
                },
            });
        }
        const response = {
            success: true,
            message: '广告封面上传成功',
            data: { advertisement },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('上传广告封面失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '上传广告封面失败',
        });
    }
});
exports.getAdvertisementCover = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    try {
        const advertisement = await database_1.default.advertisementCover.findFirst({
            orderBy: { updatedAt: 'desc' },
        });
        const response = {
            success: true,
            message: advertisement ? '获取广告封面成功' : '暂无广告封面',
            data: { advertisement },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取广告封面失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取广告封面失败',
        });
    }
});
exports.setAIConfig = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以设置AI配置',
        });
    }
    const { provider = 'gemini', apiKey, baseUrl, isActive = true, textModel = 'gemini-2.0-flash-exp', imageModel = 'imagen-3.0-generate-001' } = req.body;
    if (!['gemini', 'openai'].includes(provider)) {
        return res.status(400).json({
            success: false,
            message: '供应商必须是 gemini 或 openai',
        });
    }
    if (!apiKey || typeof apiKey !== 'string' || apiKey.trim() === '') {
        return res.status(400).json({
            success: false,
            message: 'API Key不能为空',
        });
    }
    if (!textModel || typeof textModel !== 'string' || textModel.trim() === '') {
        return res.status(400).json({
            success: false,
            message: '文本模型不能为空',
        });
    }
    if (!imageModel || typeof imageModel !== 'string' || imageModel.trim() === '') {
        return res.status(400).json({
            success: false,
            message: '图片模型不能为空',
        });
    }
    if (provider === 'openai' && (!baseUrl || typeof baseUrl !== 'string' || baseUrl.trim() === '')) {
        return res.status(400).json({
            success: false,
            message: 'OpenAI格式需要提供基础URL',
        });
    }
    try {
        const existingConfig = await database_1.default.aIConfig.findFirst();
        let config;
        if (existingConfig) {
            config = await database_1.default.aIConfig.update({
                where: { id: existingConfig.id },
                data: {
                    provider,
                    apiKey: apiKey.trim(),
                    baseUrl: provider === 'openai' ? baseUrl?.trim() : null,
                    isActive,
                    textModel: textModel.trim(),
                    imageModel: imageModel.trim(),
                    updatedAt: new Date(),
                },
            });
        }
        else {
            config = await database_1.default.aIConfig.create({
                data: {
                    provider,
                    apiKey: apiKey.trim(),
                    baseUrl: provider === 'openai' ? baseUrl?.trim() : null,
                    isActive,
                    textModel: textModel.trim(),
                    imageModel: imageModel.trim(),
                },
            });
        }
        const response = {
            success: true,
            message: 'AI API配置设置成功',
            data: {
                config: {
                    id: config.id,
                    provider: config.provider,
                    baseUrl: config.baseUrl,
                    isActive: config.isActive,
                    textModel: config.textModel,
                    imageModel: config.imageModel,
                    updatedAt: config.updatedAt,
                    apiKeyPreview: config.apiKey.substring(0, 8) + '...'
                }
            },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('设置AI配置失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '设置AI配置失败',
        });
    }
});
exports.getAIConfig = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足，只有管理员可以查看AI配置',
        });
    }
    try {
        const config = await database_1.default.aIConfig.findFirst({
            orderBy: { updatedAt: 'desc' },
        });
        const response = {
            success: true,
            message: config ? '获取AI配置成功' : '暂无AI配置',
            data: {
                config: config ? {
                    id: config.id,
                    provider: config.provider,
                    baseUrl: config.baseUrl,
                    isActive: config.isActive,
                    textModel: config.textModel,
                    imageModel: config.imageModel,
                    updatedAt: config.updatedAt,
                    apiKeyPreview: config.apiKey.substring(0, 8) + '...'
                } : null
            },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取AI配置失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取AI配置失败',
        });
    }
});
exports.setGeminiKey = exports.setAIConfig;
exports.getGeminiKey = exports.getAIConfig;
//# sourceMappingURL=adminController.js.map