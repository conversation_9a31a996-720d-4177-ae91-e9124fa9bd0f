import React from 'react';

interface TermsOfServicePageProps {
  onNavigateHome: () => void;
}

export const TermsOfServicePage: React.FC<TermsOfServicePageProps> = ({ onNavigateHome }) => {
  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <button
          onClick={onNavigateHome}
          className="mb-8 inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回首页
        </button>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">📜 Terms of Confusion</h1>
          <p className="text-xl text-gray-600">Service Terms (aka: Chaos Clauses)</p>
          <div className="mt-4 text-sm text-gray-500">
            Effective Date: Schrödinger's Today | Version: ∞.∞.∞
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-white rounded-lg shadow-lg p-8 space-y-8">
          
          {/* 开场白 */}
          <section className="border-l-4 border-red-500 pl-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🎭 Welcome to the Chaos Realm</h2>
            <p className="text-lg text-gray-700 leading-relaxed">
              By using this site, you agree to everything and nothing, simultaneously.
            </p>
            <p className="text-gray-600 mt-2 italic">
              "Terms and conditions are like a software license - nobody reads them, but everyone clicks 'I agree'."
            </p>
            <div className="mt-4 p-4 bg-red-50 rounded-lg">
              <p className="text-sm text-red-700">
                ⚠️ Warning: Continued reading may cause logical confusion, reality distortion, and uncontrollable laughter.
              </p>
            </div>
          </section>

          {/* 基本条款 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">📋 基本条款</h2>
            <div className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 className="text-lg font-bold text-blue-800 mb-3">第1条：量子协议</h3>
                <p className="text-blue-700 mb-2">
                  You may not sue us, unless you do it in rhyme.
                </p>
                <p className="text-gray-600 text-sm">
                  你不能起诉我们，除非你用押韵的方式。例如："我要告你们，因为你们太搞笑，让我笑到肚子疼，医药费要你们包。"
                </p>
              </div>

              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 className="text-lg font-bold text-green-800 mb-3">第2条：学术表演规范</h3>
                <p className="text-green-700 mb-2">
                  All citations must be shouted dramatically.
                </p>
                <p className="text-gray-600 text-sm">
                  所有引用都必须戏剧性地大声喊出来。不接受小声嘀咕或者默读。
                  示例："根据史密斯等人2023年的研究！！！（挥舞双手）"
                </p>
              </div>

              <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
                <h3 className="text-lg font-bold text-purple-800 mb-3">第3条：现实免责声明</h3>
                <p className="text-purple-700 mb-2">
                  Any resemblance to real science is purely coincidental—and alarming.
                </p>
                <p className="text-gray-600 text-sm">
                  与真实科学的任何相似之处纯属巧合——而且令人担忧。
                  如果我们的内容意外地具有科学价值，我们深表歉意。
                </p>
              </div>
            </div>
          </section>

          {/* 用户行为规范 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🎪 用户行为规范</h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <h3 className="text-lg font-bold text-yellow-800 mb-4">你必须遵守以下规则：</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-3 text-xl">🤡</span>
                  <span>在阅读文章时必须保持严肃的表情（内心可以狂笑）</span>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-3 text-xl">🎭</span>
                  <span>引用我们的文章时必须加上"据不可靠消息来源"的前缀</span>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-3 text-xl">🎨</span>
                  <span>不得将我们的研究方法用于真实的科学实验（我们不负责后果）</span>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-3 text-xl">🎪</span>
                  <span>禁止在学术会议上认真讨论我们的文章（除非是愚人节）</span>
                </li>
                <li className="flex items-start">
                  <span className="text-yellow-600 mr-3 text-xl">🎯</span>
                  <span>必须向至少3个朋友推荐本网站（传播快乐是义务）</span>
                </li>
              </ul>
            </div>
          </section>

          {/* 网站运营条款 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🚀 网站运营条款</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6">
                <h3 className="text-lg font-bold text-red-800 mb-3">⚠️ 服务中断</h3>
                <p className="text-red-700 mb-2">
                  The journal may self-destruct without notice.
                </p>
                <p className="text-gray-600 text-sm">
                  期刊可能会在没有通知的情况下自毁。我们的服务器有自己的想法，
                  有时候会罢工去度假。
                </p>
              </div>

              <div className="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h3 className="text-lg font-bold text-orange-800 mb-3">👥 强制作者身份</h3>
                <p className="text-orange-700 mb-2">
                  Violating the terms results in being added as an author against your will.
                </p>
                <p className="text-gray-600 text-sm">
                  违反条款将导致你被强制添加为作者。你的名字会出现在我们最荒谬的论文上。
                </p>
              </div>
            </div>
          </section>

          {/* 知识产权 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">🧠 知识产权（如果算的话）</h2>
            <div className="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
              <div className="space-y-4 text-gray-700">
                <p>• 我们的所有内容都受到"创意混乱许可证"保护</p>
                <p>• 你可以自由使用我们的内容，但必须承认这很荒谬</p>
                <p>• 我们的Logo是用MS Paint在5分钟内完成的，版权归那只画笔所有</p>
                <p>• 任何人都可以模仿我们的风格，但请做得更好（这不难）</p>
                <p>• 我们的商标是"让学术界更有趣"，但我们还没注册</p>
              </div>
            </div>
          </section>

          {/* 争议解决 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">⚖️ 争议解决机制</h2>
            <div className="bg-pink-50 border border-pink-200 rounded-lg p-6">
              <h3 className="text-lg font-bold text-pink-800 mb-4">如果你对我们有意见：</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-bold text-gray-800 mb-2">🎲 第一步：掷骰子</h4>
                  <p className="text-sm text-gray-600">掷一个20面骰子，如果是20，我们会认真考虑你的意见。</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-bold text-gray-800 mb-2">🎪 第二步：马戏团仲裁</h4>
                  <p className="text-sm text-gray-600">所有争议由我们的马戏团仲裁员解决（就是办公室的小丑玩偶）。</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-bold text-gray-800 mb-2">🎭 第三步：戏剧化表演</h4>
                  <p className="text-sm text-gray-600">你必须用莎士比亚的风格重新陈述你的投诉。</p>
                </div>
                <div className="bg-white p-4 rounded-lg">
                  <h4 className="font-bold text-gray-800 mb-2">🎨 第四步：艺术调解</h4>
                  <p className="text-sm text-gray-600">如果以上都失败了，我们会画一幅画来表达我们的歉意。</p>
                </div>
              </div>
            </div>
          </section>

          {/* 条款修改 */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">📝 条款修改权</h2>
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <p className="text-gray-700 mb-4">我们保留随时修改这些条款的权利，修改方式包括但不限于：</p>
              <ul className="space-y-2 text-gray-600">
                <li>• 在梦中修改（梦境版本优先）</li>
                <li>• 通过占星术决定新条款</li>
                <li>• 让办公室的猫踩键盘生成新内容</li>
                <li>• 根据当天的天气心情调整</li>
                <li>• 使用随机数生成器重写整个文档</li>
              </ul>
            </div>
          </section>

          {/* 免责声明 */}
          <section className="border-t pt-8">
            <div className="bg-red-100 border border-red-300 rounded-lg p-6">
              <h3 className="text-lg font-bold text-red-800 mb-2">🚨 最终免责声明</h3>
              <p className="text-red-700 text-sm">
                如果你真的把这些条款当真，我们建议你立即寻求专业的幽默感治疗。
                Joker期刊对因遵守这些荒谬条款而产生的任何后果概不负责。
                记住：生活已经够严肃了，让我们一起笑一笑吧！🤡
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};
