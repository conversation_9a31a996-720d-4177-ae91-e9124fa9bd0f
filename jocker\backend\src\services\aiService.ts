import prisma from '../config/database';
import { Article, RawArticleFromAI, AIGenerateArticlesRequest, AIGenerateContentRequest } from '../types';

/**
 * 记录 AI 生成日志
 */
const logAIGeneration = async (
  type: string,
  prompt: string,
  response: string,
  success: boolean,
  errorMsg?: string,
  duration?: number
) => {
  try {
    await prisma.aIGenerationLog.create({
      data: {
        type,
        prompt,
        response,
        success,
        errorMsg,
        duration,
      },
    });
  } catch (error) {
    console.error('记录 AI 生成日志失败:', error);
  }
};

/**
 * 生成多篇讽刺科学文章 - 已移除，现在由前端处理
 * 保留此函数以维持API兼容性，但会抛出错误提示使用前端生成
 */
export const generateArticles = async (request: AIGenerateArticlesRequest): Promise<Article[]> => {
  throw new Error('文章生成功能已迁移到前端，请使用前端的文章生成功能');
};

/**
 * 生成文章摘要 - 已移除，现在由前端处理
 */
export const generateSummary = async (articleId: number): Promise<string> => {
  throw new Error('文章摘要生成功能已迁移到前端，请使用前端的文章生成功能');
};

/**
 * 生成文章评审 - 已移除，现在由前端处理
 */
export const generateReview = async (request: AIGenerateContentRequest): Promise<string> => {
  throw new Error('文章评审生成功能已迁移到前端，请使用前端的文章生成功能');
};

/**
 * 生成文章内容 - 已移除，现在由前端处理
 */
export const generateContent = async (article: any): Promise<string> => {
  throw new Error('文章内容生成功能已迁移到前端，请使用前端的文章生成功能');
};

/**
 * 生成文章内容（带图片处理）- 已移除，现在由前端处理
 */
export const generateArticleContentWithFigures = async (article: any): Promise<string> => {
  throw new Error('文章内容生成功能已迁移到前端，请使用前端的文章生成功能');
};

/**
 * 为指定文章生成完整内容 - 已移除，现在由前端处理
 */
export const generateArticleContent = async (request: AIGenerateContentRequest): Promise<string> => {
  throw new Error('文章内容生成功能已迁移到前端，请使用前端的文章生成功能');
};

/**
 * 生成同行评议回复 - 已移除，现在由前端处理
 */
export const generatePeerReview = async (author: string, title: string, abstract: string): Promise<string> => {
  throw new Error('同行评议生成功能已迁移到前端，请使用前端的文章生成功能');
};
