import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import prisma from '../config/database';
import { processFigurePlaceholders } from './figureService';
import { Article, RawArticleFromAI, AIGenerateArticlesRequest, AIGenerateContentRequest } from '../types';

/**
 * 获取Gemini配置（优先使用用户配置，回退到环境变量）
 */
const getGeminiConfig = async () => {
  try {
    // 尝试获取用户配置
    const userConfig = await prisma.geminiConfig.findFirst({
      where: { isActive: true },
      orderBy: { updatedAt: 'desc' },
    });

    if (userConfig) {
      console.log(`🔧 使用用户配置的Gemini设置 - 文本模型: ${userConfig.textModel}, 图片模型: ${userConfig.imageModel}`);
      return {
        apiKey: userConfig.apiKey,
        textModel: userConfig.textModel,
        imageModel: userConfig.imageModel,
      };
    }
  } catch (error) {
    console.warn('获取用户Gemini配置失败，使用默认配置:', error);
  }

  // 回退到环境变量配置
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  if (!apiKey) {
    throw new Error('Google AI API Key 未配置');
  }

  console.log('🔧 使用服务器默认Gemini配置 - 文本模型: gemini-2.5-flash, 图片模型: imagen-3.0-generate-002');
  return {
    apiKey,
    textModel: 'gemini-2.5-flash',
    imageModel: 'imagen-3.0-generate-002',
  };
};

/**
 * 记录 AI 生成日志
 */
const logAIGeneration = async (
  type: string,
  prompt: string,
  response?: string,
  success: boolean = true,
  errorMsg?: string,
  duration?: number
) => {
  try {
    await prisma.aIGenerationLog.create({
      data: {
        type,
        prompt,
        response,
        success,
        errorMsg,
        duration,
      },
    });
  } catch (error) {
    console.error('记录 AI 生成日志失败:', error);
  }
};

/**
 * 生成多篇讽刺科学文章
 */
export const generateArticles = async (request: AIGenerateArticlesRequest): Promise<Article[]> => {
  const { count = 5, theme } = request;
  const startTime = Date.now();

  // 获取Gemini配置
  const geminiConfig = await getGeminiConfig();
  const ai = new GoogleGenAI({ apiKey: geminiConfig.apiKey });

  let prompt = `
    创建 ${count} 篇讽刺科学期刊"Jocker"的文章列表。
    这些文章应该是关于平凡或愚蠢的话题，但用极其严肃的态度来对待。
    ${theme ? `主题围绕: ${theme}` : ''}

    对于每篇文章，请提供：
    - "id": 从 1 到 ${count} 的唯一整数
    - "category": 简短的分类名称
    - "title": 机智的标题
    - "author": 有趣的作者姓名（例如，双关语）
    - "excerpt": 简短摘要（2-3句话）
    - "image_prompt": 与文章相关的逼真AI图像生成器的描述性提示

    只返回有效的JSON数组。
    重要：确保输出是单一的有效JSON数组，没有其他内容。
    所有字符串值必须用双引号括起来。字符串值中的任何双引号都必须用反斜杠转义（例如，"title": "学术界\\"空气引号\\"研究"）。
  `;

  try {
    console.log(`📝 使用模型 ${geminiConfig.textModel} 生成文章列表`);
    const textResponse: GenerateContentResponse = await ai.models.generateContent({
      model: geminiConfig.textModel,
      contents: prompt,
      config: { responseMimeType: "application/json" },
    });

    let jsonStr = (textResponse.text || '').trim();
    
    // 移除可能的代码块标记
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }
    
    const rawArticles: RawArticleFromAI[] = JSON.parse(jsonStr);

    if (!rawArticles || rawArticles.length === 0) {
      throw new Error("AI 没有返回有效的文章数据");
    }

    // 为每篇文章生成图片
    const articlesWithImages = await Promise.all(
      rawArticles.map(async (article) => {
        try {
          console.log(`🎨 使用模型 ${geminiConfig.imageModel} 生成文章封面`);
          const imageResponse = await ai.models.generateImages({
            model: geminiConfig.imageModel,
            prompt: article.image_prompt,
            config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
          });
          
          const base64ImageBytes = imageResponse.generatedImages?.[0]?.image?.imageBytes || '';
          const imageUrl = `data:image/jpeg;base64,${base64ImageBytes}`;
          
          // 保存到数据库
          const savedArticle = await prisma.article.create({
            data: {
              title: article.title,
              author: article.author,
              category: article.category,
              excerpt: article.excerpt,
              imageUrl,
              imagePrompt: article.image_prompt,
              published: true,
              featured: false,
            },
          });

          return savedArticle;
        } catch (error) {
          console.error(`为文章 ${article.title} 生成图片失败:`, error);
          
          // 如果图片生成失败，使用默认图片
          const savedArticle = await prisma.article.create({
            data: {
              title: article.title,
              author: article.author,
              category: article.category,
              excerpt: article.excerpt,
              imageUrl: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+eUn+aIkOWksei0pTwvdGV4dD48L3N2Zz4=',
              imagePrompt: article.image_prompt,
              published: true,
              featured: false,
            },
          });

          return savedArticle;
        }
      })
    );

    const duration = Date.now() - startTime;
    await logAIGeneration('articles', prompt, JSON.stringify(rawArticles), true, undefined, duration);

    return articlesWithImages;

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    await logAIGeneration('articles', prompt, undefined, false, errorMsg, duration);
    
    console.error("生成文章失败:", error);
    throw new Error("AI 生成文章时出现问题，请稍后重试");
  }
};

/**
 * 为指定文章生成完整内容
 */
export const generateArticleContent = async (request: AIGenerateContentRequest): Promise<string> => {
  const { articleId } = request;
  const startTime = Date.now();

  // 获取文章信息
  const article = await prisma.article.findUnique({
    where: { id: articleId },
  });

  if (!article) {
    throw new Error('文章不存在');
  }

  const prompt = `Write a complete satirical scientific research paper for the journal "Jocker". Use an extremely serious, academic tone while discussing absurd topics. The paper should be approximately 600-800 words and include these sections with headers: "Introduction", "Methodology", "Results", and "Conclusion". Use professional jargon and make it witty.

IMPORTANT FORMATTING REQUIREMENTS:
1. Return ONLY the article content in Markdown format
2. Do not include any explanatory text, comments, or meta-commentary
3. Include EXACTLY 2-3 figures using this STRICT format: [Figure X: Title]
   - CRITICAL: Use exactly this format: [Figure 1: Title], [Figure 2: Title], [Figure 3: Title]
   - X must be 1, 2, or 3 (consecutive numbers starting from 1)
   - Title should be 2-4 words maximum (e.g., "Experimental Setup", "Data Analysis", "Results Summary")
   - WRONG formats: [FIGURE:1:...], [figure 1:...], [Figure 1 -...], [Fig 1:...]
   - CORRECT examples: [Figure 1: Experimental Setup], [Figure 2: Data Analysis], [Figure 3: Results Summary]
4. Do NOT add any text after the figure placeholder on the same line
5. Reference figures in text as "Figure 1", "Figure 2", etc. (not "figure 1" or "Fig. 1")
6. Distribute figures: one in Methodology, one in Results, optionally one in Introduction/Conclusion
7. At the end, add "## Figures" section with detailed descriptions:
   - STRICT format: "**Figure X:** [Detailed description explaining what the figure shows and its significance]"
   - Use ONLY English text, no Chinese characters
   - Ensure proper spacing around ** markers
   - Example: "**Figure 1:** Experimental setup showing the sophisticated measurement apparatus..."
7. Include 2-4 mathematical equations using LaTeX format:
   - For inline math: use $equation$ (single dollar signs)
   - For display math (centered, numbered): use $$equation$$ (double dollar signs)
   - Example inline: "The correlation coefficient $r = 0.95$ indicates..."
   - Example display: "$$P(coffee) = \frac{productivity^2}{absurdity \cdot \sqrt{caffeine}}$$"
   - Reference equations in text like "as shown in Equation 1" or "(Eq. 2)"
8. Make equations satirical but mathematically plausible (use real mathematical notation)

Title: "${article.title}"
Author: "${article.author}"
Category: "${article.category}"
Abstract: "${article.excerpt}"

Format with Markdown headers (## Section Name), use **bold** and *italics* for emphasis. Make sure to include relevant figures that would enhance the satirical scientific narrative.`;

  // 获取Gemini配置
  const geminiConfig = await getGeminiConfig();
  const ai = new GoogleGenAI({ apiKey: geminiConfig.apiKey });

  try {
    console.log(`📝 使用模型 ${geminiConfig.textModel} 生成文章摘要`);
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: geminiConfig.textModel,
      contents: prompt,
    });

    const fullText = response.text;

    // 更新文章内容
    await prisma.article.update({
      where: { id: articleId },
      data: { content: fullText },
    });

    const duration = Date.now() - startTime;
    await logAIGeneration('content', prompt, fullText, true, undefined, duration);

    return fullText || '';

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    await logAIGeneration('content', prompt, undefined, false, errorMsg, duration);
    
    console.error("生成文章内容失败:", error);
    throw new Error("AI 生成文章内容时出现问题，请稍后重试");
  }
};

/**
 * 生成同行评议回复（用于提交页面）
 */
export const generatePeerReview = async (author: string, title: string, abstract: string): Promise<string> => {
  const startTime = Date.now();

  const prompt = `
    用户向"Jocker"期刊提交了一篇讽刺研究论文想法。
    他们的姓名是"${author}"。
    提议的标题是"${title}"。
    摘要是："${abstract}"。

    你的任务是充当同行评议者。写一个机智、幽默、略带居高临下的同行评议回复。
    随机决定（50/50的机会）是"接受"还是"拒绝"这篇论文。
    在第一行以"决定：[接受/拒绝]"开始回复。
    回复的语调应该反映决定。如果是接受，要勉强印象深刻。如果是拒绝，要创造性地轻蔑。
    用姓名称呼作者。回复应该大约150字。
  `;

  // 获取Gemini配置
  const geminiConfig = await getGeminiConfig();
  const ai = new GoogleGenAI({ apiKey: geminiConfig.apiKey });

  try {
    console.log(`📝 使用模型 ${geminiConfig.textModel} 生成文章评审`);
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: geminiConfig.textModel,
      contents: prompt,
    });

    const reviewText = response.text;

    const duration = Date.now() - startTime;
    await logAIGeneration('peer_review', prompt, reviewText, true, undefined, duration);

    return reviewText || '';

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    await logAIGeneration('peer_review', prompt, undefined, false, errorMsg, duration);
    
    console.error("生成同行评议失败:", error);
    throw new Error("AI 生成同行评议时出现问题，请稍后重试");
  }
};

/**
 * 生成文章内容（用于数据库存储，包含图片处理）
 * @param article 文章信息
 * @returns 生成的文章内容
 */
export const generateArticleContentWithFigures = async (article: any): Promise<string> => {
  // 获取Gemini配置
  const geminiConfig2 = await getGeminiConfig();
  const ai2 = new GoogleGenAI({ apiKey: geminiConfig2.apiKey });

  const prompt = `Write a complete satirical scientific research paper for the journal "Jocker". Use an extremely serious, academic tone while discussing absurd topics. The paper should be approximately 600-800 words and include these sections with headers: "Introduction", "Methodology", "Results", and "Conclusion". Use professional jargon and make it witty.

IMPORTANT FORMATTING REQUIREMENTS:
1. Return ONLY the article content in Markdown format
2. Do not include any explanatory text, comments, or meta-commentary
3. Include EXACTLY 2-3 figures using this STRICT format: [Figure X: Title]
   - CRITICAL: Use exactly this format: [Figure 1: Title], [Figure 2: Title], [Figure 3: Title]
   - X must be 1, 2, or 3 (consecutive numbers starting from 1)
   - Title should be 2-4 words maximum (e.g., "Experimental Setup", "Data Analysis", "Results Summary")
   - WRONG formats: [FIGURE:1:...], [figure 1:...], [Figure 1 -...], [Fig 1:...]
   - CORRECT examples: [Figure 1: Experimental Setup], [Figure 2: Data Analysis], [Figure 3: Results Summary]
4. Do NOT add any text after the figure placeholder on the same line
5. Reference figures in text as "Figure 1", "Figure 2", etc. (not "figure 1" or "Fig. 1")
6. Distribute figures: one in Methodology, one in Results, optionally one in Introduction/Conclusion
7. At the end, add "## Figures" section with detailed descriptions:
   - STRICT format: "**Figure X:** [Detailed description explaining what the figure shows and its significance]"
   - Use ONLY English text, no Chinese characters
   - Ensure proper spacing around ** markers
   - Example: "**Figure 1:** Experimental setup showing the sophisticated measurement apparatus..."
7. Include 2-4 mathematical equations using LaTeX format:
   - For inline math: use $equation$ (single dollar signs)
   - For display math (centered, numbered): use $$equation$$ (double dollar signs)
   - Example inline: "The correlation coefficient $r = 0.95$ indicates..."
   - Example display: "$$P(coffee) = \frac{productivity^2}{absurdity \cdot \sqrt{caffeine}}$$"
   - Reference equations in text like "as shown in Equation 1" or "(Eq. 2)"
8. Make equations satirical but mathematically plausible (use real mathematical notation)

Title: "${article.title}"
Author: "${article.author}"
Category: "${article.category}"
Abstract: "${article.excerpt}"

Format with Markdown headers (## Section Name), use **bold** and *italics* for emphasis. Make sure to include relevant figures that would enhance the satirical scientific narrative.`;

  const startTime = Date.now();

  // 获取Gemini配置
  const geminiConfig3 = await getGeminiConfig();
  const ai3 = new GoogleGenAI({ apiKey: geminiConfig3.apiKey });

  try {
    console.log(`📝 使用模型 ${geminiConfig3.textModel} 生成文章内容`);
    const response: GenerateContentResponse = await ai3.models.generateContent({
      model: geminiConfig3.textModel,
      contents: prompt,
    });

    const duration = Date.now() - startTime;
    const fullText = response.text || '';

    // 处理文章中的图片占位符
    const { figureIds, processedContent } = await processFigurePlaceholders(
      article.id,
      fullText,
      article.title,
      article.category
    );

    // 记录生成日志
    await logAIGeneration('article', prompt, fullText, true, undefined, duration);

    console.log(`✅ 文章生成完成，包含 ${figureIds.length} 个图片`);

    return processedContent;

  } catch (error) {
    const duration = Date.now() - startTime;
    const errorMsg = error instanceof Error ? error.message : '未知错误';
    await logAIGeneration('article', prompt, undefined, false, errorMsg, duration);

    console.error('AI 生成文章内容失败:', error);
    throw new Error('AI 生成文章内容失败，请稍后重试');
  }
};
