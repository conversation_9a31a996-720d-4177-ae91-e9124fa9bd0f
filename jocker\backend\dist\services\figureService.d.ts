export declare const updateFigureStatus: (figureId: string, status: "PENDING" | "GENERATING" | "COMPLETED" | "FAILED", imageUrl?: string, errorMessage?: string) => Promise<void>;
export declare const processFigurePlaceholders: (articleId: number, content: string, articleTitle: string, articleCategory: string) => Promise<{
    figureIds: string[];
    processedContent: string;
}>;
export declare const generateImagePrompt: (figureDescription: string, articleTitle: string, articleCategory: string, figureNumber: number) => Promise<string>;
//# sourceMappingURL=figureService.d.ts.map