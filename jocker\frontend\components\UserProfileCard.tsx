import React, { useState, useEffect, useRef } from 'react';
import { adminApi } from '../src/services/api';

interface UserInfo {
  id: number;
  username: string;
  name?: string;
  role: string;
  createdAt: string;
  lastLoginAt?: string;
}

interface UserProfileCardProps {
  isVisible: boolean;
  onClose: () => void;
  userInfo: UserInfo;
}

export const UserProfileCard: React.FC<UserProfileCardProps> = ({
  isVisible,
  onClose,
  userInfo
}) => {
  const [showChangePassword, setShowChangePassword] = useState(false);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [passwordError, setPasswordError] = useState('');
  const [passwordSuccess, setPasswordSuccess] = useState('');
  const cardRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭卡片
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (cardRef.current && !cardRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isVisible) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, onClose]);

  // 处理密码修改
  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();
    setPasswordError('');
    setPasswordSuccess('');

    // 验证表单
    if (!passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword) {
      setPasswordError('请填写所有密码字段');
      return;
    }

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setPasswordError('新密码和确认密码不匹配');
      return;
    }

    if (passwordForm.newPassword.length < 6) {
      setPasswordError('新密码长度至少6位');
      return;
    }

    setIsChangingPassword(true);

    try {
      await adminApi.changePassword({
        currentPassword: passwordForm.currentPassword,
        newPassword: passwordForm.newPassword
      });

      setPasswordSuccess('密码修改成功！');
      setPasswordForm({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      
      // 3秒后关闭成功提示
      setTimeout(() => {
        setPasswordSuccess('');
        setShowChangePassword(false);
      }, 3000);

    } catch (error) {
      console.error('密码修改失败:', error);
      setPasswordError(error instanceof Error ? error.message : '密码修改失败');
    } finally {
      setIsChangingPassword(false);
    }
  };

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 获取角色显示名称
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return '管理员';
      case 'EDITOR':
        return '编辑';
      case 'USER':
        return '用户';
      default:
        return role;
    }
  };

  // 获取角色颜色
  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800';
      case 'EDITOR':
        return 'bg-blue-100 text-blue-800';
      case 'USER':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-start justify-end pt-16 pr-4">
      <div
        ref={cardRef}
        className="bg-white rounded-lg shadow-xl border border-gray-200 w-80 max-w-sm animate-in slide-in-from-right duration-200"
      >
        {/* 卡片头部 */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">用户信息</h3>
              <p className="text-blue-100 text-sm">账户详情</p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* 卡片内容 */}
        <div className="p-4 space-y-4">
          {/* 基本信息 */}
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                {userInfo.name ? userInfo.name.charAt(0).toUpperCase() : userInfo.username.charAt(0).toUpperCase()}
              </div>
              <div>
                <h4 className="font-semibold text-gray-900">{userInfo.name || userInfo.username}</h4>
                <p className="text-sm text-gray-500">@{userInfo.username}</p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span className="text-gray-500">用户ID:</span>
                <p className="font-medium">{userInfo.id}</p>
              </div>
              <div>
                <span className="text-gray-500">权限:</span>
                <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(userInfo.role)}`}>
                  {getRoleDisplayName(userInfo.role)}
                </span>
              </div>
              <div className="col-span-2">
                <span className="text-gray-500">注册时间:</span>
                <p className="font-medium">{formatDate(userInfo.createdAt)}</p>
              </div>
              {userInfo.lastLoginAt && (
                <div className="col-span-2">
                  <span className="text-gray-500">最后登录:</span>
                  <p className="font-medium">{formatDate(userInfo.lastLoginAt)}</p>
                </div>
              )}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="border-t pt-4">
            {!showChangePassword ? (
              <button
                onClick={() => setShowChangePassword(true)}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors font-medium"
              >
                🔒 更改密码
              </button>
            ) : (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h5 className="font-medium text-gray-900">更改密码</h5>
                  <button
                    onClick={() => {
                      setShowChangePassword(false);
                      setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
                      setPasswordError('');
                      setPasswordSuccess('');
                    }}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>

                <form onSubmit={handleChangePassword} className="space-y-3">
                  <div>
                    <input
                      type="password"
                      placeholder="当前密码"
                      value={passwordForm.currentPassword}
                      onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      disabled={isChangingPassword}
                    />
                  </div>
                  <div>
                    <input
                      type="password"
                      placeholder="新密码"
                      value={passwordForm.newPassword}
                      onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      disabled={isChangingPassword}
                    />
                  </div>
                  <div>
                    <input
                      type="password"
                      placeholder="确认新密码"
                      value={passwordForm.confirmPassword}
                      onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                      disabled={isChangingPassword}
                    />
                  </div>

                  {passwordError && (
                    <div className="text-red-600 text-sm bg-red-50 p-2 rounded-md">
                      {passwordError}
                    </div>
                  )}

                  {passwordSuccess && (
                    <div className="text-green-600 text-sm bg-green-50 p-2 rounded-md">
                      {passwordSuccess}
                    </div>
                  )}

                  <button
                    type="submit"
                    disabled={isChangingPassword}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                  >
                    {isChangingPassword ? '修改中...' : '确认修改'}
                  </button>
                </form>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
