# 📋 系统日志文件

这个文件夹存放所有版本的日志JSON文件。

## 📁 文件命名规范

- **格式**：`log-v版本号.json`
- **示例**：`log-v2.3.0.json`、`log-v2.4.0.json`

## 🚀 使用方法

### 添加新日志
1. 在此文件夹创建新的JSON文件：`log-v版本号.json`
2. 运行命令：`node quick-log.js v版本号`

### 命令示例
```bash
# 方式1：使用版本号（推荐）
node quick-log.js v2.4.0

# 方式2：使用完整路径
node quick-log.js logs/log-v2.4.0.json
```

## 📝 JSON格式模板

```json
{
  "version": "v2.4.0",
  "type": "minor",
  "title": "功能标题",
  "description": "详细描述（可选）",
  "changes": [
    "🎵 变更1：具体内容",
    "🔧 变更2：具体内容",
    "✨ 变更3：具体内容"
  ]
}
```

## 🏷️ 类型说明

- **major** - 🚀 重大更新（破坏性变更）
- **minor** - ✨ 功能更新（新增功能）
- **patch** - 🔧 修复更新（Bug修复）
- **hotfix** - 🚨 紧急修复（紧急问题修复）

## 📊 当前版本

最新版本：**v2.8.0** - 🔥 Post-Publication Peer Review 恶搞评论系统 & DOI自动分配
