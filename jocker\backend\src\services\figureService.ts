import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import prisma from '../config/database';

/**
 * 获取Gemini配置（优先使用用户配置，回退到环境变量）
 */
const getGeminiConfig = async () => {
  try {
    // 尝试获取用户配置
    const userConfig = await prisma.geminiConfig.findFirst({
      where: { isActive: true },
      orderBy: { updatedAt: 'desc' },
    });

    if (userConfig) {
      console.log(`🔧 使用用户配置的Gemini设置 - 文本模型: ${userConfig.textModel}`);
      return {
        apiKey: userConfig.apiKey,
        textModel: userConfig.textModel,
        imageModel: userConfig.imageModel,
      };
    }
  } catch (error) {
    console.warn('获取用户Gemini配置失败，使用默认配置:', error);
  }

  // 回退到环境变量配置
  const apiKey = process.env.GOOGLE_AI_API_KEY;
  if (!apiKey) {
    throw new Error('Google AI API Key 未配置');
  }

  console.log('🔧 使用服务器默认Gemini配置 - 文本模型: gemini-2.5-flash-preview-04-17');
  return {
    apiKey,
    textModel: 'gemini-2.5-flash-preview-04-17',
    imageModel: 'imagen-3.0-generate-002',
  };
};

/**
 * 图片占位符的正则表达式
 * 匹配格式: [FIGURE:1:description]
 */
const FIGURE_PLACEHOLDER_REGEX = /\[FIGURE:(\d+):([^\]]+)\]/g;

/**
 * 从文章内容中提取图片占位符信息
 */
export interface FigurePlaceholder {
  figureNumber: number;
  description: string;
  placeholder: string;
}

/**
 * 解析文章内容，提取图片占位符
 */
export const extractFigurePlaceholders = (content: string): FigurePlaceholder[] => {
  const placeholders: FigurePlaceholder[] = [];
  let match;

  while ((match = FIGURE_PLACEHOLDER_REGEX.exec(content)) !== null) {
    placeholders.push({
      figureNumber: parseInt(match[1]),
      description: match[2].trim(),
      placeholder: match[0],
    });
  }

  // 按图片编号排序
  return placeholders.sort((a, b) => a.figureNumber - b.figureNumber);
};

/**
 * 为图片生成详细的绘图提示词
 */
export const generateImagePrompt = async (
  figureDescription: string,
  articleTitle: string,
  articleCategory: string,
  figureNumber: number
): Promise<string> => {
  // 获取Gemini配置
  const geminiConfig = await getGeminiConfig();
  const ai = new GoogleGenAI({ apiKey: geminiConfig.apiKey });

  const prompt = `You are an expert at creating detailed prompts for AI image generation for academic papers.

Based on the following information, create a detailed, specific prompt for generating a high-quality academic figure:

Article Title: "${articleTitle}"
Article Category: "${articleCategory}"
Figure Number: ${figureNumber}
Figure Description: "${figureDescription}"

Requirements for the image prompt:
1. The image should look professional and academic
2. Use a clean, scientific illustration style
3. Include appropriate labels, axes, or annotations if it's a chart/graph
4. The style should be suitable for a scientific journal
5. Avoid any text that might be illegible when generated
6. Focus on visual elements rather than text-heavy content

Generate a detailed prompt (100-150 words) that would create a professional academic figure. Start your response with "Create a professional academic illustration showing" and be very specific about visual elements, style, and composition.

IMPORTANT: Return ONLY the image generation prompt, no explanations or additional text.`;

  try {
    console.log(`📝 使用模型 ${geminiConfig.textModel} 生成图片提示词`);
    const response: GenerateContentResponse = await ai.models.generateContent({
      model: geminiConfig.textModel,
      contents: prompt,
    });

    const imagePrompt = response.text?.trim() || '';
    
    if (!imagePrompt) {
      throw new Error('AI 未能生成图片提示词');
    }

    return imagePrompt;

  } catch (error) {
    console.error('生成图片提示词失败:', error);
    
    // 如果 AI 生成失败，使用默认模板
    const fallbackPrompt = `Create a professional academic illustration showing ${figureDescription}. Use a clean, scientific style with clear visual elements. The image should be suitable for publication in a scientific journal, with professional composition and academic aesthetics.`;
    
    return fallbackPrompt;
  }
};

/**
 * 创建图片记录到数据库
 */
export const createFigureRecord = async (
  articleId: number,
  figureNumber: number,
  description: string,
  imagePrompt: string
): Promise<string> => {
  try {
    // 提取图注（从描述生成标准图注）
    const caption = `Figure ${figureNumber}: ${description.charAt(0).toUpperCase() + description.slice(1)}.`;

    const figure = await prisma.figure.create({
      data: {
        articleId,
        figureNumber,
        title: `Figure ${figureNumber}`,
        description,
        caption,
        imagePrompt,
        status: 'pending',
      },
    });

    return figure.id;
  } catch (error) {
    console.error('创建图片记录失败:', error);
    throw new Error('创建图片记录失败');
  }
};

/**
 * 处理文章中的所有图片占位符
 */
export const processFigurePlaceholders = async (
  articleId: number,
  content: string,
  articleTitle: string,
  articleCategory: string
): Promise<{ figureIds: string[]; processedContent: string }> => {
  const placeholders = extractFigurePlaceholders(content);
  const figureIds: string[] = [];
  let processedContent = content;

  for (const placeholder of placeholders) {
    try {
      // 生成图片提示词
      const imagePrompt = await generateImagePrompt(
        placeholder.description,
        articleTitle,
        articleCategory,
        placeholder.figureNumber
      );

      // 创建图片记录
      const figureId = await createFigureRecord(
        articleId,
        placeholder.figureNumber,
        placeholder.description,
        imagePrompt
      );

      figureIds.push(figureId);

      // 替换占位符为图片组件标记
      const figureComponent = `[FIGURE_COMPONENT:${figureId}]`;
      processedContent = processedContent.replace(placeholder.placeholder, figureComponent);

      console.log(`✅ 处理图片 ${placeholder.figureNumber}: ${figureId}`);

    } catch (error) {
      console.error(`❌ 处理图片 ${placeholder.figureNumber} 失败:`, error);
      
      // 如果处理失败，保留原始占位符
      continue;
    }
  }

  return { figureIds, processedContent };
};

/**
 * 获取文章的所有图片
 */
export const getArticleFigures = async (articleId: number) => {
  return await prisma.figure.findMany({
    where: { articleId },
    orderBy: { figureNumber: 'asc' },
  });
};

/**
 * 更新图片状态
 */
export const updateFigureStatus = async (
  figureId: string,
  status: 'pending' | 'generating' | 'completed' | 'failed',
  imageUrl?: string,
  errorMsg?: string
) => {
  return await prisma.figure.update({
    where: { id: figureId },
    data: {
      status,
      imageUrl: imageUrl || undefined,
      errorMsg: errorMsg || undefined,
      updatedAt: new Date(),
    },
  });
};
