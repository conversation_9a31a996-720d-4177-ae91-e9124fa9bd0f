import prisma from '../config/database';

/**
 * 更新图片状态
 */
export const updateFigureStatus = async (
  figureId: string,
  status: 'PENDING' | 'GENERATING' | 'COMPLETED' | 'FAILED',
  imageUrl?: string,
  errorMessage?: string
): Promise<void> => {
  try {
    await prisma.figure.update({
      where: { id: figureId },
      data: {
        status,
        imageUrl: imageUrl || undefined,
        errorMsg: errorMessage || undefined,
        updatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error(`更新图片状态失败 (ID: ${figureId}):`, error);
  }
};

/**
 * 处理文章中的图片占位符
 */
export const processFigurePlaceholders = async (
  articleId: number,
  content: string,
  articleTitle: string,
  articleCategory: string
): Promise<{ figureIds: string[]; processedContent: string }> => {
  const figureRegex = /\[Figure (\d+): ([^\]]+)\]/g;
  const figureIds: string[] = [];
  let processedContent = content;

  let match;
  while ((match = figureRegex.exec(content)) !== null) {
    const figureNumber = parseInt(match[1]);
    const figureDescription = match[2];

    try {
      // 创建图片记录
      const figure = await prisma.figure.create({
        data: {
          article: { connect: { id: articleId } },
          figureNumber,
          title: `Figure ${figureNumber}`,
          description: figureDescription,
          caption: figureDescription,
          imagePrompt: figureDescription,
          status: 'pending',
        },
      });

      figureIds.push(figure.id);

      // 替换占位符为带ID的格式
      const placeholder = `[Figure ${figureNumber}: ${figureDescription}]`;
      const replacement = `[Figure ${figureNumber}: ${figureDescription}](#figure-${figure.id})`;
      processedContent = processedContent.replace(placeholder, replacement);

    } catch (error) {
      console.error(`创建图片记录失败:`, error);
    }
  }

  return { figureIds, processedContent };
};

/**
 * 生成图片提示词 - 已移除，现在由前端处理
 */
export const generateImagePrompt = async (
  figureDescription: string,
  articleTitle: string,
  articleCategory: string,
  figureNumber: number
): Promise<string> => {
  throw new Error('图片提示词生成功能已迁移到前端，请使用前端的图片生成功能');
};
