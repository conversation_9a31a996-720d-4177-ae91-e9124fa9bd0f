import{r as t,l as v,j as e}from"./admin-Bji2VVio.js";import"./react-vendor-DJG_os-6.js";const C=()=>{const[x,u]=t.useState([]),[a,m]=t.useState(!0),[h,g]=t.useState(null),[d,l]=t.useState(1),[c,b]=t.useState(1),[i,p]=t.useState("");t.useEffect(()=>{o()},[d,i]);const o=async()=>{try{m(!0);const s={page:d,limit:10};i&&(s.type=i);const r=await v.getSystemLogs(s);u(r.logs),b(r.pagination.totalPages),g(null)}catch(s){console.error("加载日志失败:",s),g("加载日志失败")}finally{m(!1)}},y=s=>{switch(s){case"major":return"bg-red-100 text-red-800";case"minor":return"bg-blue-100 text-blue-800";case"patch":return"bg-green-100 text-green-800";case"hotfix":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},j=s=>{switch(s){case"major":return"🚀";case"minor":return"✨";case"patch":return"🔧";case"hotfix":return"🚨";default:return"📝"}},N=s=>new Date(s).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric"});return a&&x.length===0?e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),e.jsx("div",{className:"space-y-4",children:[1,2,3].map(s=>e.jsxs("div",{className:"border rounded-lg p-4",children:[e.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-1/4 mb-3"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"h-3 bg-gray-200 rounded"}),e.jsx("div",{className:"h-3 bg-gray-200 rounded w-5/6"})]})]},s))})]})}):h?e.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-red-600 mb-4",children:["❌ ",h]}),e.jsx("button",{onClick:o,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"重试"})]})}):e.jsxs("div",{className:"bg-white rounded-lg shadow",children:[e.jsxs("div",{className:"p-6 border-b border-gray-200",children:[e.jsxs("div",{className:"flex justify-between items-center mb-4",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"📋 更新日志"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("select",{value:i,onChange:s=>{p(s.target.value),l(1)},className:"border border-gray-300 rounded-md px-3 py-1 text-sm",children:[e.jsx("option",{value:"",children:"所有类型"}),e.jsx("option",{value:"major",children:"🚀 重大更新"}),e.jsx("option",{value:"minor",children:"✨ 功能更新"}),e.jsx("option",{value:"patch",children:"🔧 修复更新"}),e.jsx("option",{value:"hotfix",children:"🚨 紧急修复"})]}),e.jsx("button",{onClick:o,disabled:a,className:"bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 disabled:bg-gray-400 text-sm",children:a?"刷新中...":"刷新"})]})]}),e.jsx("p",{className:"text-gray-600",children:"Joker期刊系统的版本更新记录，实时获取最新的功能改进和修复信息。"})]}),e.jsxs("div",{className:"p-6",children:[x.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-500 mb-4",children:"📝 暂无日志记录"}),e.jsx("p",{className:"text-sm text-gray-400",children:"管理员可以在后台添加系统更新日志"})]}):e.jsx("div",{className:"space-y-6",children:x.map(s=>e.jsxs("div",{className:"border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow",children:[e.jsxs("div",{className:"flex items-start justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:"text-2xl",children:j(s.type)}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold text-gray-900",children:s.title||`版本 ${s.version}`}),e.jsxs("div",{className:"flex items-center space-x-3 mt-1",children:[e.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${y(s.type)}`,children:s.type}),e.jsx("span",{className:"text-sm text-gray-500",children:N(s.date)}),s.creator&&e.jsxs("span",{className:"text-sm text-gray-500",children:["by ",s.creator.name||s.creator.username]})]})]})]}),e.jsx("div",{className:"text-lg font-mono text-blue-600",children:s.version})]}),s.description&&e.jsx("div",{className:"mb-4 text-gray-700 bg-gray-50 rounded-lg p-3",children:s.description}),e.jsx("div",{className:"space-y-2",children:s.changes.map((r,n)=>e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("span",{className:"text-blue-500 mt-1",children:"•"}),e.jsx("span",{className:"text-gray-700 flex-1",children:r})]},n))})]},s.id))}),c>1&&e.jsx("div",{className:"mt-8 flex justify-center",children:e.jsxs("nav",{className:"flex items-center space-x-2",children:[e.jsx("button",{onClick:()=>l(s=>Math.max(1,s-1)),disabled:d===1||a,className:"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400",children:"上一页"}),e.jsx("div",{className:"flex items-center space-x-1",children:Array.from({length:Math.min(5,c)},(s,r)=>{const n=r+1;return e.jsx("button",{onClick:()=>l(n),disabled:a,className:`px-3 py-2 text-sm font-medium rounded-md ${d===n?"bg-blue-600 text-white":"text-gray-700 hover:bg-gray-50"}`,children:n},n)})}),e.jsx("button",{onClick:()=>l(s=>Math.min(c,s+1)),disabled:d===c||a,className:"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400",children:"下一页"})]})})]})]})};export{C as DynamicChangelogSection};
