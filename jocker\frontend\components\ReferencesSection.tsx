import React from 'react';

interface Reference {
  id: number;
  text: string;
}

interface ReferencesSectionProps {
  content: string;
}

/**
 * 参考文献组件
 * 解析文章内容中的参考文献并以学术期刊格式显示
 */
export const ReferencesSection: React.FC<ReferencesSectionProps> = ({ content }) => {
  // 解析参考文献
  const parseReferences = (text: string): Reference[] => {
    // 尝试匹配带有结束标记的格式
    let referencesMatch = text.match(/--- REFERENCES ---([\s\S]*?)--- END REFERENCES ---/);

    // 如果没有找到结束标记，尝试匹配到文本末尾
    if (!referencesMatch) {
      referencesMatch = text.match(/--- REFERENCES ---([\s\S]*?)$/);
    }

    if (!referencesMatch) return [];

    const referencesText = referencesMatch[1].trim();

    // 使用正则表达式分割引用，匹配 [数字] 开头的模式
    const referencePattern = /\[(\d+)\]\s*([^[]*?)(?=\[\d+\]|$)/g;
    const references: Reference[] = [];
    let match;

    while ((match = referencePattern.exec(referencesText)) !== null) {
      const refNumber = parseInt(match[1]);
      const refText = match[2].trim();

      if (refText) {
        references.push({
          id: refNumber,
          text: refText
        });
      }
    }

    // 如果正则匹配失败，回退到按行分割的方法
    if (references.length === 0) {
      const referenceLines = referencesText.split('\n').filter(line => line.trim());

      return referenceLines.map((line, index) => {
        // 移除开头的 [数字] 格式，因为我们会重新编号
        const cleanText = line.replace(/^\[\d+\]\s*/, '').trim();
        return {
          id: index + 1,
          text: cleanText
        };
      });
    }

    return references;
  };

  const references = parseReferences(content);

  if (references.length === 0) {
    return null;
  }

  return (
    <div className="mt-12 pt-8 border-t border-gray-200">
      <h2 className="text-2xl font-bold text-gray-900 font-serif mb-6">References</h2>
      
      <div className="space-y-4">
        {references.map((ref) => (
          <div key={ref.id} id={`ref-${ref.id}`} className="flex transition-colors duration-300">
            {/* 引用编号 */}
            <div className="flex-shrink-0 w-8">
              <span className="text-sm font-medium text-gray-600">[{ref.id}]</span>
            </div>

            {/* 引用内容 */}
            <div className="flex-1">
              <p className="text-sm text-gray-700 leading-relaxed">
                {ref.text}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      {/* 引用说明 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
        <p className="text-xs text-gray-600 italic">
          <strong>Note:</strong> All references are AI-generated for satirical purposes and do not represent real academic publications.
        </p>
      </div>
    </div>
  );
};

/**
 * 从文章内容中移除参考文献部分，返回清理后的内容
 */
export const removeReferencesFromContent = (content: string): string => {
  // 首先尝试移除带有结束标记的格式
  let cleanContent = content.replace(/--- REFERENCES ---[\s\S]*?--- END REFERENCES ---/g, '');

  // 如果没有找到结束标记，移除从 --- REFERENCES --- 到文本末尾的所有内容
  if (cleanContent === content) {
    cleanContent = content.replace(/--- REFERENCES ---[\s\S]*$/g, '');
  }

  return cleanContent.trim();
};

/**
 * 检查内容中是否包含参考文献
 */
export const hasReferences = (content: string): boolean => {
  return /--- REFERENCES ---/.test(content);
};
