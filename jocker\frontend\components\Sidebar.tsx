import React, { useState, useEffect } from 'react';
import { Article } from '../types';
import { adminApi } from '../src/services/api';

interface SidebarProps {
  trendingArticles: Article[];
  onNavigateToArticle: (id: number) => void;
}

interface Advertisement {
  id: string;
  imageUrl: string;
  title: string;
  description?: string;
  linkUrl?: string;
}

export const Sidebar: React.FC<SidebarProps> = ({ trendingArticles, onNavigateToArticle }) => {
  const [advertisement, setAdvertisement] = useState<Advertisement | null>(null);

  // 加载广告封面
  useEffect(() => {
    const loadAdvertisement = async () => {
      try {
        const result = await adminApi.getAdvertisementCover();
        if (result.advertisement) {
          setAdvertisement(result.advertisement);
        }
      } catch (error) {
        console.error('加载广告封面失败:', error);
      }
    };

    loadAdvertisement();
  }, []);
  return (
    <aside className="space-y-10 sticky top-24">
      <div>
        <h3 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-gray-200">
          Trending Now
        </h3>
        <ul className="space-y-4">
          {trendingArticles.map((article, index) => (
            <li key={article.id} className="flex items-start group">
              <span className="text-2xl font-bold text-purple-200 mr-4 transition-colors group-hover:text-purple-400">{index + 1}</span>
              <div>
                <button onClick={() => onNavigateToArticle(article.id)} className="font-semibold text-gray-800 group-hover:text-purple-700 text-left">
                  {article.title}
                </button>
                <p className="text-sm text-gray-500">{article.category}</p>
              </div>
            </li>
          ))}
        </ul>
      </div>

      <div>
        <h3 className="text-xl font-bold text-gray-900 mb-4 pb-2 border-b-2 border-gray-200">
          Advertisement
        </h3>
        <div className="rounded-lg bg-gray-100 p-4 text-center">
          {advertisement ? (
            <>
              <img
                src={advertisement.imageUrl}
                alt={advertisement.title}
                className="mx-auto rounded-md max-w-full h-auto"
              />
              <p className="mt-4 text-sm font-semibold text-gray-800">
                {advertisement.title}
              </p>
              {advertisement.description && (
                <p className="text-xs text-gray-600">{advertisement.description}</p>
              )}
              {advertisement.linkUrl && advertisement.linkUrl !== '#' && (
                <a
                  href={advertisement.linkUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="mt-3 inline-block text-xs font-bold text-purple-700 uppercase hover:underline"
                >
                  Shop Now
                </a>
              )}
            </>
          ) : (
            <>
              <img
                src="https://picsum.photos/seed/ad/300/250"
                alt="Advertisement"
                className="mx-auto rounded-md"
              />
              <p className="mt-4 text-sm font-semibold text-gray-800">
                Confused by Science?
              </p>
              <p className="text-xs text-gray-600">You're not alone. Try our new coffee mug.</p>
              <a href="#" className="mt-3 inline-block text-xs font-bold text-purple-700 uppercase hover:underline">
                Shop Now
              </a>
            </>
          )}
        </div>
      </div>
    </aside>
  );
};