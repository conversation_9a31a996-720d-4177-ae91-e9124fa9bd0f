import React, { useState, useEffect } from 'react';
import { adminApi } from '../src/services/api';

interface MockShoppingCartModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemName: string;
  itemPrice: string;
  language: 'en' | 'zh';
}

interface ProgressData {
  totalCount: number;
  progress: number;
  target: number;
  isNewRequest?: boolean;
}

export const MockShoppingCartModal: React.FC<MockShoppingCartModalProps> = ({
  isOpen,
  onClose,
  itemName,
  itemPrice,
  language
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isShaking, setIsShaking] = useState(false);
  const [progressData, setProgressData] = useState<ProgressData | null>(null);
  const [isLoadingProgress, setIsLoadingProgress] = useState(false);
  const [showProgressAnimation, setShowProgressAnimation] = useState(false);

  const content = {
    en: {
      steps: [
        {
          title: "Wait... Are you SERIOUS? 🤯",
          message: "You actually clicked \"Add to Cart\"? We thought this was just for show!",
          emoji: "😱"
        },
        {
          title: "Hold Up There, Big Spender! 💸",
          message: "Before you throw your money at fictional merchandise, let us ask you this: Do you REALLY need a plush toy that judges your life choices?",
          emoji: "🤔"
        },
        {
          title: "Plot Twist! 🎭",
          message: "Maybe we'll consider making this real... if we get 10,000 requests. Or if someone pays us in coffee and existential validation.",
          emoji: "☕",
          showProgress: true
        },
        {
          title: "Final Reality Check ✋",
          message: "This is a satirical academic journal. Our merchandise is as real as our peer review process (spoiler: it's run by clowns).",
          emoji: "🤡"
        },
        {
          title: "But Hey, Thanks for Playing! 🎪",
          message: "Your enthusiasm for academic chaos merchandise has been noted. We'll add it to our \"Things That Might Exist in an Alternate Universe\" list.",
          emoji: "🌟"
        }
      ],
      buttons: {
        next: "Continue the Madness",
        close: "Escape Reality",
        restart: "Relive the Experience"
      }
    },
    zh: {
      steps: [
        {
          title: "等等...你是认真的吗？🤯",
          message: "你真的点了\"加入购物车\"？我们以为这只是装装样子的！",
          emoji: "😱"
        },
        {
          title: "慢着，大款！💸",
          message: "在你把钱扔给虚构商品之前，让我们问你：你真的需要一个会评判你人生选择的毛绒玩具吗？",
          emoji: "🤔"
        },
        {
          title: "剧情反转！🎭",
          message: "也许我们会考虑把这个做成真的...如果我们收到10,000个请求。或者有人用咖啡和存在主义认同来付款。",
          emoji: "☕",
          showProgress: true
        },
        {
          title: "最终现实检查 ✋",
          message: "这是一个讽刺学术期刊。我们的周边和我们的同行评议流程一样真实（剧透：是小丑在运营）。",
          emoji: "🤡"
        },
        {
          title: "但是嘿，谢谢参与！🎪",
          message: "你对学术混乱周边的热情已被记录。我们会把它加入\"可能存在于平行宇宙的东西\"清单。",
          emoji: "🌟"
        }
      ],
      buttons: {
        next: "继续疯狂",
        close: "逃离现实",
        restart: "重温体验"
      }
    }
  };

  const currentContent = content[language];
  const currentStepData = currentContent.steps[currentStep];

  // 生成浏览器指纹
  const generateFingerprint = () => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Browser fingerprint', 2, 2);
    }

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|');

    // 生成简单的hash
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(36);
  };

  // 加载进度数据
  const loadProgressData = async () => {
    try {
      setIsLoadingProgress(true);
      const stats = await adminApi.getMerchandiseRequestStats();
      setProgressData(stats);
    } catch (error) {
      console.error('加载进度数据失败:', error);
    } finally {
      setIsLoadingProgress(false);
    }
  };

  // 记录请求并更新进度
  const recordRequest = async () => {
    try {
      const fingerprint = generateFingerprint();
      const result = await adminApi.recordMerchandiseRequest(fingerprint, itemName);

      setProgressData(result);

      if (result.isNewRequest) {
        setShowProgressAnimation(true);
        setTimeout(() => setShowProgressAnimation(false), 2000);
      }
    } catch (error) {
      console.error('记录请求失败:', error);
    }
  };

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(0);
      setProgressData(null);
      setShowProgressAnimation(false);
    }
  }, [isOpen]);

  // 当到达进度条步骤时加载数据
  useEffect(() => {
    if (currentStepData?.showProgress && !progressData && !isLoadingProgress) {
      loadProgressData();
    }
  }, [currentStep, currentStepData?.showProgress]);

  const handleNext = async () => {
    // 如果当前步骤显示进度条，记录请求
    if (currentStepData?.showProgress && !progressData?.isNewRequest) {
      await recordRequest();
    }

    if (currentStep < currentContent.steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // 最后一步，摇晃效果
      setIsShaking(true);
      setTimeout(() => setIsShaking(false), 500);
    }
  };

  const handleRestart = () => {
    setCurrentStep(0);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-white rounded-lg shadow-2xl max-w-md w-full transform transition-all duration-300 ${
        isShaking ? 'animate-bounce' : 'scale-100'
      }`}>
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="text-2xl">{currentStepData.emoji}</span>
              <h3 className="text-lg font-bold">
                {language === 'en' ? 'Shopping Cart Reality Check' : '购物车现实检查'}
              </h3>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="bg-gray-200 h-2">
          <div 
            className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 transition-all duration-500"
            style={{ width: `${((currentStep + 1) / currentContent.steps.length) * 100}%` }}
          />
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Item Info */}
          <div className="bg-gray-50 rounded-lg p-3 mb-4">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">
                {language === 'en' ? 'Attempting to purchase:' : '试图购买：'}
              </span>
              <span className="font-bold text-purple-600">{itemPrice}</span>
            </div>
            <p className="font-semibold text-gray-800 mt-1">{itemName}</p>
          </div>

          {/* Step Content */}
          <div className="text-center mb-6">
            <h4 className="text-xl font-bold text-gray-900 mb-3">
              {currentStepData.title}
            </h4>
            <p className="text-gray-700 leading-relaxed">
              {currentStepData.message}
            </p>

            {/* Progress Bar - 只在特定步骤显示 */}
            {currentStepData.showProgress && (
              <div className="mt-6 bg-gray-50 rounded-lg p-4 border-2 border-dashed border-gray-300">
                <div className="mb-3">
                  <h5 className="text-lg font-semibold text-purple-600 mb-2">
                    {language === 'en' ? '🎯 Request Progress' : '🎯 请求进度'}
                  </h5>

                  {isLoadingProgress ? (
                    <div className="flex items-center justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
                      <span className="ml-2 text-gray-600">
                        {language === 'en' ? 'Loading...' : '加载中...'}
                      </span>
                    </div>
                  ) : progressData ? (
                    <>
                      {/* Progress Bar */}
                      <div className="bg-gray-200 rounded-full h-4 mb-3 overflow-hidden">
                        <div
                          className={`h-4 rounded-full transition-all duration-1000 ${
                            showProgressAnimation ? 'animate-pulse' : ''
                          } bg-gradient-to-r from-purple-500 via-pink-500 to-purple-600`}
                          style={{ width: `${Math.min(progressData.progress, 100)}%` }}
                        />
                      </div>

                      {/* Progress Text */}
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">
                          {progressData.totalCount.toLocaleString()} / {progressData.target.toLocaleString()}
                        </span>
                        <span className="font-bold text-purple-600">
                          {progressData.progress.toFixed(1)}%
                        </span>
                      </div>

                      {/* New Request Animation */}
                      {showProgressAnimation && (
                        <div className="mt-3 p-2 bg-green-100 border border-green-300 rounded-lg">
                          <div className="flex items-center justify-center text-green-700">
                            <span className="text-lg mr-2">🎉</span>
                            <span className="font-semibold">
                              {language === 'en' ? '+1 Request Added!' : '+1 请求已添加！'}
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Progress Status */}
                      <div className="mt-3 text-xs text-gray-500">
                        {progressData.progress >= 100 ? (
                          <span className="text-green-600 font-semibold">
                            🎊 {language === 'en' ? 'Goal Reached! Maybe we should actually make this...' : '目标达成！也许我们真的应该做这个...'}
                          </span>
                        ) : (
                          <span>
                            {language === 'en'
                              ? `Only ${(progressData.target - progressData.totalCount).toLocaleString()} more requests needed!`
                              : `还需要 ${(progressData.target - progressData.totalCount).toLocaleString()} 个请求！`}
                          </span>
                        )}
                      </div>
                    </>
                  ) : (
                    <div className="text-gray-500 py-4">
                      {language === 'en' ? 'Failed to load progress data' : '加载进度数据失败'}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Step Counter */}
          <div className="text-center mb-4">
            <span className="text-sm text-gray-500">
              {language === 'en' ? 'Step' : '步骤'} {currentStep + 1} {language === 'en' ? 'of' : '/'} {currentContent.steps.length}
            </span>
          </div>

          {/* Buttons */}
          <div className="flex space-x-3">
            {currentStep < currentContent.steps.length - 1 ? (
              <button
                onClick={handleNext}
                className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-4 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 font-semibold"
              >
                {currentContent.buttons.next}
              </button>
            ) : (
              <>
                <button
                  onClick={handleRestart}
                  className="flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors font-semibold"
                >
                  {currentContent.buttons.restart}
                </button>
                <button
                  onClick={onClose}
                  className="flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-4 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 font-semibold"
                >
                  {currentContent.buttons.close}
                </button>
              </>
            )}
          </div>

          {/* Easter Egg */}
          {currentStep === currentContent.steps.length - 1 && (
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-400 italic">
                {language === 'en' 
                  ? "* No actual money, dignity, or sanity was harmed in this transaction."
                  : "* 在此交易中没有真正的金钱、尊严或理智受到伤害。"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
