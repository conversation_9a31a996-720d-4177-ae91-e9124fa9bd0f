import path from 'path';
import { defineConfig, loadEnv } from 'vite';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, '.'),
        }
      },
      build: {
        rollupOptions: {
          output: {
            manualChunks: {
              // 将大型库分离到单独的chunk
              'react-vendor': ['react', 'react-dom'],
              // 将管理员相关代码分离
              'admin': [
                './components/AdminPage',
                './components/LoginPage'
              ],
              // 将页面组件分离
              'pages': [
                './components/AboutPage',
                './components/ForAuthorsPage',
                './components/MerchandisePage',
                './components/PrivacyPolicyPage'
              ]
            }
          }
        },
        // 增加chunk大小警告阈值
        chunkSizeWarningLimit: 1000
      }
    };
});
