@echo off
echo ========================================
echo ? Jocker Backend Deployment Script
echo ========================================
echo.

if not exist "backend" (
    echo Error: backend directory not found!
    echo Please run this script from the jocker root directory.
    pause
    exit /b 1
)

echo Entering backend directory...
cd backend

echo Building backend...
call npm run build
if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Uploading backend files to server...
scp -r dist/* root@************:/var/www/jocker/jocker/backend/dist/
if %errorlevel% neq 0 (
    echo Backend upload failed!
    pause
    exit /b 1
)

echo Uploading package.json...
scp package.json root@************:/var/www/jocker/jocker/backend/
if %errorlevel% neq 0 (
    echo Package.json upload failed!
    pause
    exit /b 1
)

echo Uploading prisma schema only (preserving database)...
scp prisma/schema.prisma root@************:/var/www/jocker/jocker/backend/prisma/
if %errorlevel% neq 0 (
    echo Prisma schema upload failed!
    pause
    exit /b 1
)

echo Uploading prisma migrations...
scp -r prisma/migrations root@************:/var/www/jocker/jocker/backend/prisma/ 2>nul

echo Uploading migration scripts...
scp -r scripts root@************:/var/www/jocker/jocker/backend/ 2>nul

echo Generating Prisma client on server...
ssh root@************ "cd /var/www/jocker/jocker/backend && npx prisma generate"
if %errorlevel% neq 0 (
    echo Prisma client generation failed!
    pause
    exit /b 1
)

echo Running AI config migration on server...
ssh root@************ "cd /var/www/jocker/jocker/backend && node scripts/migrate-ai-config.js"

echo Running database migrations on server...
ssh root@************ "cd /var/www/jocker/jocker/backend && npx prisma db push --accept-data-loss"

echo Note: Database file (dev.db) is preserved on server - only schema updated

echo Restarting backend service on server...
ssh root@************ "cd /var/www/jocker/jocker/backend && npm install --production && pm2 restart jocker-backend"
if %errorlevel% neq 0 (
    echo Service restart failed!
    pause
    exit /b 1
)

cd ..

echo.
echo Backend deployment completed successfully!
echo Backend API: http://************:5003
echo.
pause
