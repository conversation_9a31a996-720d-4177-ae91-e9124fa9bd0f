<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Joker | A Satirical Science Journal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700;900&family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Lato', sans-serif;
      }
      h1, h2, h3, h4, h5, h6, .font-serif {
        font-family: 'Merriweather', serif;
      }

      /* 数学公式样式 */
      .katex-display {
        margin: 1.5em 0;
        text-align: center;
        position: relative;
      }

      .katex-display .katex {
        display: inline-block;
        text-align: center;
      }

      /* 公式编号样式 */
      .equation-number {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        font-size: 0.9em;
        color: #666;
        font-family: 'Lato', sans-serif;
      }

      /* 行内公式样式 */
      .katex {
        font-size: 1.1em;
      }

      /* 期刊风格的公式容器 */
      .equation-container {
        margin: 1.5em 0;
        padding: 0.5em 0;
        border-top: 1px solid #e5e7eb;
        border-bottom: 1px solid #e5e7eb;
        background-color: #fafafa;
      }

      /* 强制显示列表样式，覆盖Tailwind重置 */
      ol, ul {
        list-style: revert !important;
        padding-left: 2em !important;
        margin: 1em 0 !important;
      }

      ol {
        list-style-type: decimal !important;
      }

      ul {
        list-style-type: disc !important;
      }

      li {
        display: list-item !important;
        margin: 0.5em 0 !important;
      }

      /* 特别针对文章内容 */
      .article-content ol,
      .article-content ul {
        list-style: revert !important;
        padding-left: 2.5em !important;
        margin: 1.5em 0 !important;
      }

      .article-content ol {
        list-style-type: decimal !important;
      }

      .article-content ul {
        list-style-type: disc !important;
      }

      .article-content li {
        display: list-item !important;
        margin: 0.75em 0 !important;
        line-height: 1.6 !important;
      }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify/dist/purify.min.js"></script>

    <!-- KaTeX for mathematical equations -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css" integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1odI+WdtXRGWt2kTvGFasHpSy3SV" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js" integrity="sha384-XjKyOOlGwcjNTAIQHIpgOno0Hl1YQqzUOEleOLALmuqehneUG+vnGctmUb0ZY0l8" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>

    <!-- Custom styles for mathematical equations -->
    <style>
      /* 强制应用公式容器样式 */
      .article-content .equation-container,
      .equation-container {
        margin: 1.5rem 0 !important;
        position: relative !important;
        background: #fafafa !important;
        border-left: 4px solid #3b82f6 !important;
        border-radius: 0 8px 8px 0 !important;
        padding: 1.5rem !important;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
        display: block !important;
      }

      /* 强制应用块级公式样式 */
      .article-content .equation-container .katex-display,
      .equation-container .katex-display {
        margin: 0 !important;
        text-align: center !important;
        position: relative !important;
        padding: 0.5rem 0 !important;
        display: block !important;
      }

      /* 强制应用公式编号样式 */
      .article-content .equation-number,
      .equation-number {
        position: absolute !important;
        right: 1rem !important;
        top: 50% !important;
        transform: translateY(-50%) !important;
        font-size: 0.9rem !important;
        color: #666 !important;
        font-family: 'Times New Roman', serif !important;
        font-weight: bold !important;
        background: white !important;
        padding: 0.3rem 0.6rem !important;
        border-radius: 4px !important;
        border: 1px solid #ddd !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
        z-index: 10 !important;
      }

      /* 确保行内公式不受影响 */
      .katex:not(.katex-display) {
        background: none !important;
        border: none !important;
        padding: 0 !important;
        margin: 0 !important;
      }

      /* 调试样式 - 临时添加边框来检查元素 */
      .equation-container {
        border: 2px dashed red !important;
      }
    </style>
<link rel="stylesheet" href="/index.css">
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.8.0"
  }
}
</script>
  <script type="module" crossorigin src="/assets/index-CMWG1iOs.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/react-vendor-DJG_os-6.js">
  <link rel="modulepreload" crossorigin href="/assets/admin-D0oBVvoh.js">
  <link rel="modulepreload" crossorigin href="/assets/pages-B09XCZdT.js">
</head>
  <body class="bg-gray-50">
    <div id="root"></div>
</body>
</html>