<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Joker | A Satirical Science Journal</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Merriweather:wght@400;700;900&family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <style>
      body {
        font-family: 'Lato', sans-serif;
      }
      h1, h2, h3, h4, h5, h6, .font-serif {
        font-family: 'Merriweather', serif;
      }

      /* 数学公式样式 */
      .katex-display {
        margin: 1.5em 0;
        text-align: center;
        position: relative;
      }

      .katex-display .katex {
        display: inline-block;
        text-align: center;
      }

      /* 公式编号样式 */
      .equation-number {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        font-size: 0.9em;
        color: #666;
        font-family: 'Lato', sans-serif;
      }

      /* 行内公式样式 */
      .katex {
        font-size: 1.1em;
      }

      /* 期刊风格的公式容器 */
      .equation-container {
        margin: 1.5em 0;
        padding: 0.5em 0;
        border-top: 1px solid #e5e7eb;
        border-bottom: 1px solid #e5e7eb;
        background-color: #fafafa;
      }

      /* 强制显示列表样式，覆盖Tailwind重置 */
      ol, ul {
        list-style: revert !important;
        padding-left: 2em !important;
        margin: 1em 0 !important;
      }

      ol {
        list-style-type: decimal !important;
      }

      ul {
        list-style-type: disc !important;
      }

      li {
        display: list-item !important;
        margin: 0.5em 0 !important;
      }

      /* 特别针对文章内容 */
      .article-content ol,
      .article-content ul {
        list-style: revert !important;
        padding-left: 2.5em !important;
        margin: 1.5em 0 !important;
      }

      .article-content ol {
        list-style-type: decimal !important;
      }

      .article-content ul {
        list-style-type: disc !important;
      }

      .article-content li {
        display: list-item !important;
        margin: 0.75em 0 !important;
        line-height: 1.6 !important;
      }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dompurify/dist/purify.min.js"></script>

    <!-- KaTeX for mathematical equations -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css" integrity="sha384-n8MVd4RsNIU0tAv4ct0nTaAbDJwPJzDEaqSD1odI+WdtXRGWt2kTvGFasHpSy3SV" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js" integrity="sha384-XjKyOOlGwcjNTAIQHIpgOno0Hl1YQqzUOEleOLALmuqehneUG+vnGctmUb0ZY0l8" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js" integrity="sha384-+VBxd3r6XgURycqtZ117nYw44OOcIax56Z4dCRWbxyPt0Koah1uHoK0o4+/RRE05" crossorigin="anonymous"></script>
<link rel="stylesheet" href="/index.css">
  <script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "react/": "https://esm.sh/react@^19.1.0/",
    "@google/genai": "https://esm.sh/@google/genai@^1.8.0"
  }
}
</script>
  <script type="module" crossorigin src="/assets/index-D997j-ep.js"></script>
</head>
  <body class="bg-gray-50">
    <div id="root"></div>
</body>
</html>