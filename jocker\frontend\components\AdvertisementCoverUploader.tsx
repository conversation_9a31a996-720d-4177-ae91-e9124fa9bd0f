import React, { useState } from 'react';
import { adminApi } from '../src/services/api';

interface AdvertisementCoverUploaderProps {
  onUploadSuccess: (adData: any) => void;
  onUploadError: (error: string) => void;
}

export const AdvertisementCoverUploader: React.FC<AdvertisementCoverUploaderProps> = ({
  onUploadSuccess,
  onUploadError
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [linkUrl, setLinkUrl] = useState('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      onUploadError('请选择图片文件');
      return;
    }

    // 验证文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
      onUploadError('图片文件不能超过10MB');
      return;
    }

    // 读取文件并转换为base64
    const reader = new FileReader();
    reader.onload = (e) => {
      const base64String = e.target?.result as string;
      setPreviewUrl(base64String);
    };
    reader.readAsDataURL(file);
  };

  const handleUpload = async () => {
    if (!previewUrl) {
      onUploadError('请先选择图片');
      return;
    }

    if (!title.trim()) {
      onUploadError('请输入广告标题');
      return;
    }

    setIsUploading(true);

    try {
      const result = await adminApi.uploadAdvertisementCover({
        imageUrl: previewUrl,
        title: title.trim(),
        description: description.trim(),
        linkUrl: linkUrl.trim() || '#',
      });

      onUploadSuccess(result);
      
      // 重置表单
      setPreviewUrl(null);
      setTitle('');
      setDescription('');
      setLinkUrl('');
      
      // 重置文件输入
      const fileInput = document.getElementById('ad-cover-input') as HTMLInputElement;
      if (fileInput) {
        fileInput.value = '';
      }

    } catch (error) {
      console.error('上传广告封面失败:', error);
      onUploadError(error instanceof Error ? error.message : '上传失败');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">📢 广告封面设置</h4>
        <p className="text-sm text-gray-600 mb-4">
          设置侧边栏广告区域的封面图片和相关信息。
        </p>
      </div>

      {/* 文件选择 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          选择广告图片
        </label>
        <input
          id="ad-cover-input"
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
        />
        <p className="text-xs text-gray-500 mt-1">
          支持 JPG、PNG、GIF 格式，最大 10MB
        </p>
      </div>

      {/* 图片预览 */}
      {previewUrl && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            图片预览
          </label>
          <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <img
              src={previewUrl}
              alt="广告预览"
              className="max-w-full h-auto max-h-64 mx-auto rounded-md shadow-sm"
            />
          </div>
        </div>
      )}

      {/* 广告信息 */}
      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            广告标题 *
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="例如：Confused by Science?"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            广告描述
          </label>
          <input
            type="text"
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="例如：You're not alone. Try our new coffee mug."
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            点击链接
          </label>
          <input
            type="url"
            value={linkUrl}
            onChange={(e) => setLinkUrl(e.target.value)}
            placeholder="例如：https://example.com/shop"
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      {/* 上传按钮 */}
      <div className="flex justify-end">
        <button
          onClick={handleUpload}
          disabled={isUploading || !previewUrl || !title.trim()}
          className={`px-6 py-2 rounded-md font-medium transition-colors ${
            isUploading || !previewUrl || !title.trim()
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isUploading ? '上传中...' : '上传广告封面'}
        </button>
      </div>
    </div>
  );
};
