import React from 'react';
import { Article } from '../types';
import { ArticleCard } from './ArticleCard';
import { BackArrowIcon } from './icons';
import { SharedBackground } from './SharedBackground';

interface SearchPageProps {
  allArticles: Article[];
  query: string;
  onNavigateToArticle: (id: number) => void;
  onNavigateHome: () => void;
}

export const SearchPage: React.FC<SearchPageProps> = ({ allArticles, query, onNavigateToArticle, onNavigateHome }) => {
  const filteredArticles = allArticles.filter(article => {
    const searchTerm = query.toLowerCase();
    return (
      article.title.toLowerCase().includes(searchTerm) ||
      article.excerpt.toLowerCase().includes(searchTerm) ||
      article.author.toLowerCase().includes(searchTerm) ||
      article.category.toLowerCase().includes(searchTerm)
    );
  });

  return (
    <SharedBackground>
      <button onClick={onNavigateHome} className="inline-flex items-center text-purple-700 hover:text-purple-900 mb-6 group">
          <BackArrowIcon className="w-5 h-5 mr-2 transition-transform group-hover:-translate-x-1" />
          Back to Home
      </button>
      <h1 className="text-3xl font-bold text-gray-900 mb-2">
        Search Results
      </h1>
      <p className="text-lg text-gray-600 mb-8">
        {filteredArticles.length > 0 ? `Showing results for ` : `No results for `}
        <span className="font-semibold text-gray-800">"{query}"</span>
      </p>

      {filteredArticles.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredArticles.map(article => (
            <ArticleCard key={article.id} article={article} onNavigateToArticle={onNavigateToArticle} />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 border-2 border-dashed border-gray-200 rounded-lg">
          <h2 className="text-xl font-semibold text-gray-800">Nothing Found</h2>
          <p className="mt-2 text-gray-500">
            Our research archives are vast and mysterious. Try a different search term.
          </p>
        </div>
      )}
    </SharedBackground>
  );
};