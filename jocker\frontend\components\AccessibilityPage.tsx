import React, { useState } from 'react';

interface AccessibilityPageProps {
  onNavigateHome: () => void;
}

// 自定义功能项组件
const FeatureItem: React.FC<{ icon: string; en: string; zh: string }> = ({ icon, en, zh }) => (
  <div className="flex items-start space-x-3 p-3 bg-white bg-opacity-20 rounded-lg">
    <span className="text-lg">{icon}</span>
    <div className="flex-1">
      <p className="font-medium opacity-90">{en}</p>
      <p className="text-sm opacity-70 mt-1">{zh}</p>
    </div>
  </div>
);

export const AccessibilityPage: React.FC<AccessibilityPageProps> = ({ onNavigateHome }) => {
  const [currentMode, setCurrentMode] = useState<'normal' | 'dark' | 'clown' | 'existential'>('normal');

  const getModeStyles = () => {
    switch (currentMode) {
      case 'dark':
        return 'bg-gray-900 text-white';
      case 'clown':
        return 'bg-gradient-to-r from-red-400 via-yellow-400 to-blue-400 text-black';
      case 'existential':
        return 'bg-black text-gray-400';
      default:
        return 'bg-white text-gray-900';
    }
  };

  const getModeDescription = () => {
    switch (currentMode) {
      case 'dark':
        return '🌙 Dark Mode: Protects your eyes, but not your soul';
      case 'clown':
        return '🤡 Clown Mode: Rainbow chaos for maximum brain confusion';
      case 'existential':
        return '🖤 Existential Dread Mode: Question everything, including this website';
      default:
        return '☀️ Normal Mode: As boring as your life';
    }
  };

  return (
    <div className={`min-h-screen transition-all duration-500 ${getModeStyles()}`}>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 返回按钮 */}
        <button
          onClick={onNavigateHome}
          className="mb-8 inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors"
        >
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回首页
        </button>

        {/* 页面标题 */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">♿️ Accessibility Statement</h1>
          <p className="text-xl opacity-80">Making chaos accessible to everyone</p>
          <p className="text-lg opacity-70 mt-2">让每个人都能享受混乱</p>
          <div className="mt-4 text-sm opacity-60">
            Last Updated: When we remember | Testing Device: A broken calculator<br/>
            最后更新：当我们想起来的时候 | 测试设备：一台坏掉的计算器
          </div>
        </div>

        {/* 模式切换器 */}
        <div className="mb-8 bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-6">
          <h2 className="text-2xl font-bold mb-4">🎨 Experience Mode Switcher</h2>
          <p className="mb-4 opacity-80">{getModeDescription()}</p>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setCurrentMode('normal')}
              className={`px-4 py-2 rounded-lg transition-all ${
                currentMode === 'normal'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white bg-opacity-20 hover:bg-opacity-30'
              }`}
            >
              Normal Mode
            </button>
            <button
              onClick={() => setCurrentMode('dark')}
              className={`px-4 py-2 rounded-lg transition-all ${
                currentMode === 'dark'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white bg-opacity-20 hover:bg-opacity-30'
              }`}
            >
              Dark Mode
            </button>
            <button
              onClick={() => setCurrentMode('clown')}
              className={`px-4 py-2 rounded-lg transition-all ${
                currentMode === 'clown'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white bg-opacity-20 hover:bg-opacity-30'
              }`}
            >
              🤡 Clown Mode
            </button>
            <button
              onClick={() => setCurrentMode('existential')}
              className={`px-4 py-2 rounded-lg transition-all ${
                currentMode === 'existential'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white bg-opacity-20 hover:bg-opacity-30'
              }`}
            >
              Existential Dread
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="bg-white bg-opacity-10 backdrop-blur-sm rounded-lg shadow-lg p-8 space-y-8">
          
          {/* 开场白 */}
          <section className="border-l-4 border-green-500 pl-6">
            <h2 className="text-2xl font-bold mb-4">🌈 Our Accessibility Commitment / 我们的可访问性承诺</h2>
            <p className="text-lg leading-relaxed opacity-90">
              We are committed to being accessible… emotionally, spiritually, and maybe digitally.
            </p>
            <p className="opacity-70 mt-2">
              我们致力于让网站具有可访问性...在情感上、精神上，也许在数字上也是。
            </p>
          </section>

          {/* 支持的功能 */}
          <section>
            <h2 className="text-2xl font-bold mb-4">🛠️ Features We Support / 我们支持的功能</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-white bg-opacity-10 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-3">🎨 Visual Support / 视觉支持</h3>
                <p className="opacity-80 mb-3">
                  Our website supports dark mode, clown mode, and existential dread mode.
                </p>
                <p className="opacity-70 mb-3 text-sm">
                  我们的网站支持深色模式、小丑模式和存在主义绝望模式。
                </p>
                <div className="space-y-2">
                  <FeatureItem
                    icon="🌙"
                    en="Dark mode: For night owls and vampires"
                    zh="深色模式：适合夜猫子和吸血鬼"
                  />
                  <FeatureItem
                    icon="🤡"
                    en="Clown mode: Rainbow visual impact"
                    zh="小丑模式：彩虹般的视觉冲击"
                  />
                  <FeatureItem
                    icon="🖤"
                    en="Existential dread mode: For philosophers and emo teens"
                    zh="存在绝望模式：适合哲学家和emo青年"
                  />
                  <FeatureItem
                    icon="☀️"
                    en="Normal mode: For boring people"
                    zh="普通模式：给无聊的人准备的"
                  />
                </div>
              </div>

              <div className="bg-white bg-opacity-10 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-3">🔍 Text Support / 文字支持</h3>
                <p className="opacity-80 mb-3">
                  If our text is too small, squint harder.
                </p>
                <p className="opacity-70 mb-3 text-sm">
                  如果文字太小，请更用力地眯眼。
                </p>
                <div className="space-y-2">
                  <FeatureItem
                    icon="🔍"
                    en="Supports Ctrl+scroll zoom (if you know how)"
                    zh="支持Ctrl+滚轮缩放（如果你知道的话）"
                  />
                  <FeatureItem
                    icon="📏"
                    en="Font sizes: From 'ant' to 'elephant'"
                    zh="字体大小：从「蚂蚁」到「大象」"
                  />
                  <FeatureItem
                    icon="🎨"
                    en="Color contrast: From 'invisible' to 'blinding'"
                    zh="颜色对比度：从「看不见」到「刺眼」"
                  />
                </div>
              </div>

              <div className="bg-white bg-opacity-10 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-3">🖱️ 交互支持</h3>
                <p className="opacity-80 mb-3">
                  All buttons are technically clickable. No promises.
                </p>
                <ul className="space-y-2 text-sm opacity-70">
                  <li>所有按钮在技术上都是可点击的</li>
                  <li>键盘导航：Tab键是你的朋友</li>
                  <li>鼠标悬停效果：有时候有，有时候没有</li>
                  <li>触摸支持：用手指戳屏幕试试</li>
                </ul>
              </div>

              <div className="bg-white bg-opacity-10 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-3">🤖 AI支持</h3>
                <p className="opacity-80 mb-3">
                  We trained our servers to be more empathetic. It didn't work.
                </p>
                <ul className="space-y-2 text-sm opacity-70">
                  <li>我们训练服务器更有同理心，但失败了</li>
                  <li>屏幕阅读器支持：50%的时间有效</li>
                  <li>语音识别：只识别"哈哈哈"</li>
                  <li>自动翻译：可能会翻译成火星文</li>
                </ul>
              </div>
            </div>
          </section>

          {/* 已知问题 */}
          <section>
            <h2 className="text-2xl font-bold mb-4">🐛 已知的可访问性问题</h2>
            <div className="bg-yellow-500 bg-opacity-20 border border-yellow-500 border-opacity-30 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-4">我们正在"努力"解决的问题：</h3>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-bold mb-2">🎪 视觉问题</h4>
                  <ul className="space-y-1 text-sm opacity-80">
                    <li>小丑模式可能导致癫痫发作</li>
                    <li>某些颜色组合可能伤害视网膜</li>
                    <li>动画效果可能引起晕车</li>
                    <li>Logo设计可能冒犯艺术家</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-bold mb-2">🎭 交互问题</h4>
                  <ul className="space-y-1 text-sm opacity-80">
                    <li>某些按钮是装饰性的</li>
                    <li>表单可能会吃掉你的输入</li>
                    <li>链接有时候指向错误的地方</li>
                    <li>搜索功能偶尔会搜索到平行宇宙</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-bold mb-2">🧠 认知问题</h4>
                  <ul className="space-y-1 text-sm opacity-80">
                    <li>内容可能导致逻辑混乱</li>
                    <li>导航结构遵循量子力学</li>
                    <li>信息架构基于梦境逻辑</li>
                    <li>用户体验设计师是一只猫</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-bold mb-2">🌐 技术问题</h4>
                  <ul className="space-y-1 text-sm opacity-80">
                    <li>在IE6上完全不工作</li>
                    <li>移动端适配靠运气</li>
                    <li>加载速度取决于服务器心情</li>
                    <li>兼容性测试由实习生的宠物仓鼠完成</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* 辅助技术支持 */}
          <section>
            <h2 className="text-2xl font-bold mb-4">🦾 辅助技术支持</h2>
            <div className="bg-blue-500 bg-opacity-20 border border-blue-500 border-opacity-30 rounded-lg p-6">
              <div className="grid md:grid-cols-3 gap-6">
                <div>
                  <h3 className="font-bold mb-3">🔊 屏幕阅读器</h3>
                  <p className="text-sm opacity-80">
                    支持NVDA、JAWS和我们自制的"胡说八道阅读器"。
                    注意：可能会读出一些不存在的内容。
                  </p>
                </div>
                <div>
                  <h3 className="font-bold mb-3">⌨️ 键盘导航</h3>
                  <p className="text-sm opacity-80">
                    支持Tab、Enter、空格键和神秘的Ctrl+Alt+Del组合。
                    方向键可能会让你迷路。
                  </p>
                </div>
                <div>
                  <h3 className="font-bold mb-3">🎤 语音控制</h3>
                  <p className="text-sm opacity-80">
                    支持"打开"、"关闭"、"帮助"和"这是什么鬼"等语音命令。
                    不支持脏话（我们有标准的）。
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* 反馈和联系 */}
          <section>
            <h2 className="text-2xl font-bold mb-4">📞 可访问性反馈</h2>
            <div className="bg-purple-500 bg-opacity-20 border border-purple-500 border-opacity-30 rounded-lg p-6">
              <p className="mb-4 opacity-90">
                如果你在使用我们网站时遇到可访问性问题，请通过以下方式联系我们：
              </p>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-bold mb-2">传统方式</h3>
                  <ul className="space-y-1 text-sm opacity-80">
                    <li>📧 邮箱：<EMAIL></li>
                    <li>📱 电话：1-800-HELP-PLZ</li>
                    <li>📮 邮寄：可访问性部门，混乱大街404号</li>
                  </ul>
                </div>
                <div>
                  <h3 className="font-bold mb-2">创新方式</h3>
                  <ul className="space-y-1 text-sm opacity-80">
                    <li>🕊️ 信鸽：训练你的鸽子找到我们</li>
                    <li>📡 心灵感应：集中精神向我们发送想法</li>
                    <li>🎭 哑剧表演：在我们办公室外表演你的问题</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

          {/* 免责声明 */}
          <section className="border-t border-white border-opacity-20 pt-8">
            <div className="bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30 rounded-lg p-6">
              <h3 className="text-lg font-bold mb-2">⚠️ 可访问性免责声明</h3>
              <p className="text-sm opacity-80">
                我们尽力让网站对所有人都可访问，但我们的定义可能与标准定义不同。
                如果你因为我们的可访问性"功能"而感到困惑、沮丧或启发，
                那说明我们的设计达到了预期效果。记住：可访问性不仅仅是技术问题，
                更是一种心态...一种混乱的心态。🤡
              </p>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
};
