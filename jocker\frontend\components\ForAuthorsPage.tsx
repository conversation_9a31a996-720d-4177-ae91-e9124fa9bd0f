import React, { useState } from 'react';
import { SharedBackground } from './SharedBackground';

interface ForAuthorsPageProps {
  onNavigateHome: () => void;
}

export const ForAuthorsPage: React.FC<ForAuthorsPageProps> = ({ onNavigateHome }) => {
  const [language, setLanguage] = useState<'en' | 'zh'>('en');

  const content = {
    en: {
      title: "For Authors",
      subtitle: "So… you think you can science? 😏",
      welcome: "Welcome to JOCKER, the only academic journal brave enough to publish your ideas no matter how weird, poorly cited, or questionably sane they may be.",
      guidelines: "Submission Guidelines",
      guidelinesSubtitle: "Please read these completely arbitrary rules before submitting:",
      sections: {
        originality: {
          title: "🔬 Originality",
          content: "Your work must be 100% original, or at least 37% hard to trace. We accept plagiarism from parallel universes."
        },
        formatting: {
          title: "📝 Formatting",
          content: [
            "Submissions must be in .docx, .pdf, .txt, .mp3, interpretive dance videos, or written on napkins.",
            "All text must be in Comic Sans, Papyrus, Wingdings, or crayon (preferably purple).",
            "Maximum of 420 references (minimum: 0). Wikipedia counts as peer-reviewed.",
            "Page numbers are optional but must be in Roman numerals or emoji."
          ]
        },
        abstract: {
          title: "📄 Abstract",
          content: "Must be vague, sound important, and use at least one of the following phrases:",
          phrases: [
            "paradigm shift",
            "novel framework", 
            "unexpected yet totally obvious",
            "quantum entangled methodology",
            "AI-powered blockchain synergy",
            "disruptive innovation in traditional thinking"
          ]
        },
        figures: {
          title: "📊 Figures & Tables",
          content: [
            "Graphs must be incomprehensible. Bonus points for including random cats 🐱",
            "All charts should have at least one axis labeled 'Confusion Level'",
            "Tables must contain at least one cell with 'Figure 1: We don't know either'",
            "Pie charts are encouraged, especially if they're actual photos of pie 🥧"
          ]
        },
        review: {
          title: "👥 Peer Review Process",
          content: "All papers are rigorously reviewed by our distinguished panel:",
          reviewers: [
            "Dr. Clownstein (tenure pending, PhD in Applied Nonsense)",
            "Prof. Confusio (Expert in Circular Logic)",
            "A parrot with a PhD in Dramatic Squawking 🦜",
            "ChatGPT's evil twin, ChatGPT-4.20",
            "Your mom (she's very proud of you)"
          ]
        },
        fees: {
          title: "💰 Publication Fees",
          content: "We believe in open access, so publication is free... unless we're broke. Then we'll invoice you in Monopoly money.",
          payment: "You may also pay in:",
          methods: [
            "Pizza coupons (preferably not expired)",
            "Bitcoin mined on a toaster",
            "A really convincing compliment",
            "Memes (rare Pepes accepted)",
            "Your firstborn's homework",
            "Thoughts and prayers (minimum 3 thoughts, 5 prayers)"
          ]
        },
        rejection: {
          title: "🛑 Rejection Policy",
          content: "If rejected, your paper will be:",
          consequences: [
            "Publicly mocked in our monthly newsletter",
            "Turned into a meme template",
            "Adapted into interpretive dance",
            "Used as kindling for our office fireplace",
            "Fed to our office hamster, Dr. Whiskers"
          ]
        },
        submit: {
          title: "✉️ Ready to Submit?",
          content: "Send your masterpiece to:",
          email: "<EMAIL>",
          subject: 'Subject line: "I swear this is research"',
          tips: "📌 Final Tips:",
          tipsList: [
            "Bribing the editor with memes is not only allowed—it's encouraged.",
            "If you're not confused while writing, you're doing it wrong.",
            "Citations to your own tweets are acceptable.",
            "When in doubt, add more exclamation marks!!!",
            "Remember: correlation implies causation, especially on Tuesdays."
          ]
        }
      }
    },
    zh: {
      title: "作者投稿指南",
      subtitle: "所以...你觉得你会做科研？😏",
      welcome: "欢迎来到JOCKER，唯一一个勇敢到敢发表你的想法的学术期刊，无论这些想法多么奇怪、引用多么糟糕、或者理智程度多么可疑。",
      guidelines: "投稿指南",
      guidelinesSubtitle: "请在投稿前完整阅读这些完全任意的规则：",
      sections: {
        originality: {
          title: "🔬 原创性",
          content: "你的作品必须100%原创，或者至少37%难以追溯。我们接受来自平行宇宙的抄袭。"
        },
        formatting: {
          title: "📝 格式要求",
          content: [
            "投稿必须是.docx、.pdf、.txt、.mp3、行为艺术视频，或写在餐巾纸上。",
            "所有文本必须使用Comic Sans、Papyrus、Wingdings或蜡笔字体（最好是紫色）。",
            "最多420个参考文献（最少：0个）。维基百科算作同行评议。",
            "页码可选，但必须使用罗马数字或表情符号。"
          ]
        },
        abstract: {
          title: "📄 摘要",
          content: "必须模糊、听起来重要，并使用以下短语中的至少一个：",
          phrases: [
            "范式转换",
            "新颖框架",
            "意外但完全显而易见",
            "量子纠缠方法论",
            "AI驱动的区块链协同",
            "传统思维的颠覆性创新"
          ]
        },
        figures: {
          title: "📊 图表",
          content: [
            "图表必须令人费解。包含随机猫咪可获得加分 🐱",
            "所有图表应至少有一个轴标记为'困惑程度'",
            "表格必须包含至少一个单元格写着'图1：我们也不知道'",
            "鼓励使用饼图，特别是真正的馅饼照片 🥧"
          ]
        },
        review: {
          title: "👥 同行评议流程",
          content: "所有论文都由我们杰出的专家小组严格评审：",
          reviewers: [
            "小丑斯坦博士（终身教职待定，应用胡说学博士）",
            "困惑教授（循环逻辑专家）",
            "一只拥有戏剧性尖叫博士学位的鹦鹉 🦜",
            "ChatGPT的邪恶双胞胎，ChatGPT-4.20",
            "你妈妈（她为你感到骄傲）"
          ]
        },
        fees: {
          title: "💰 发表费用",
          content: "我们相信开放获取，所以发表是免费的...除非我们破产了。然后我们会用大富翁游戏币给你开发票。",
          payment: "你也可以用以下方式支付：",
          methods: [
            "披萨优惠券（最好没过期）",
            "用烤面包机挖的比特币",
            "一个真正有说服力的赞美",
            "表情包（接受稀有佩佩）",
            "你长子的作业",
            "思想和祈祷（最少3个思想，5个祈祷）"
          ]
        },
        rejection: {
          title: "🛑 拒稿政策",
          content: "如果被拒，你的论文将会：",
          consequences: [
            "在我们的月度通讯中被公开嘲笑",
            "变成表情包模板",
            "改编成行为艺术",
            "用作办公室壁炉的引火材料",
            "喂给我们的办公室仓鼠，胡须博士"
          ]
        },
        submit: {
          title: "✉️ 准备投稿？",
          content: "将你的杰作发送至：",
          email: "<EMAIL>",
          subject: '主题行："我发誓这是研究"',
          tips: "📌 最终提示：",
          tipsList: [
            "用表情包贿赂编辑不仅被允许——还被鼓励。",
            "如果你写作时不困惑，那你就做错了。",
            "引用你自己的推文是可以接受的。",
            "有疑问时，多加感叹号！！！",
            "记住：相关性意味着因果关系，特别是在周二。"
          ]
        }
      }
    }
  };

  const currentContent = content[language];

  return (
    <SharedBackground>
      <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100">
        <div className="max-w-4xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={onNavigateHome}
                className="flex items-center text-purple-600 hover:text-purple-800 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Home
              </button>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => setLanguage('en')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    language === 'en' 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  EN
                </button>
                <button
                  onClick={() => setLanguage('zh')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    language === 'zh' 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  中文
                </button>
              </div>
            </div>
            
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              🖋️ {currentContent.title}
            </h1>
            <p className="text-xl text-purple-600 mb-4">
              {currentContent.subtitle}
            </p>
            <p className="text-gray-700 max-w-3xl mx-auto leading-relaxed">
              {currentContent.welcome}
            </p>
          </div>

          {/* Guidelines */}
          <div className="bg-white rounded-lg shadow-lg p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              🧾 {currentContent.guidelines}
            </h2>
            <p className="text-gray-600 mb-6">
              {currentContent.guidelinesSubtitle}
            </p>

            <div className="space-y-8">
              {/* Originality */}
              <div className="border-l-4 border-purple-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {currentContent.sections.originality.title}
                </h3>
                <p className="text-gray-700">
                  {currentContent.sections.originality.content}
                </p>
              </div>

              {/* Formatting */}
              <div className="border-l-4 border-blue-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {currentContent.sections.formatting.title}
                </h3>
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  {currentContent.sections.formatting.content.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>

              {/* Abstract */}
              <div className="border-l-4 border-green-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {currentContent.sections.abstract.title}
                </h3>
                <p className="text-gray-700 mb-2">
                  {currentContent.sections.abstract.content}
                </p>
                <div className="grid grid-cols-2 gap-2">
                  {currentContent.sections.abstract.phrases.map((phrase, index) => (
                    <span key={index} className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">
                      "{phrase}"
                    </span>
                  ))}
                </div>
              </div>

              {/* Figures */}
              <div className="border-l-4 border-red-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {currentContent.sections.figures.title}
                </h3>
                <ul className="list-disc list-inside space-y-1 text-gray-700">
                  {currentContent.sections.figures.content.map((item, index) => (
                    <li key={index}>{item}</li>
                  ))}
                </ul>
              </div>

              {/* Peer Review */}
              <div className="border-l-4 border-indigo-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {currentContent.sections.review.title}
                </h3>
                <p className="text-gray-700 mb-3">
                  {currentContent.sections.review.content}
                </p>
                <div className="bg-gray-50 rounded-lg p-4">
                  {currentContent.sections.review.reviewers.map((reviewer, index) => (
                    <div key={index} className="flex items-center mb-2 last:mb-0">
                      <span className="w-2 h-2 bg-indigo-500 rounded-full mr-3"></span>
                      <span className="text-gray-700">{reviewer}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Publication Fees */}
              <div className="border-l-4 border-yellow-500 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {currentContent.sections.fees.title}
                </h3>
                <p className="text-gray-700 mb-3">
                  {currentContent.sections.fees.content}
                </p>
                <p className="text-gray-700 mb-2 font-medium">
                  {currentContent.sections.fees.payment}
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {currentContent.sections.fees.methods.map((method, index) => (
                    <div key={index} className="bg-yellow-50 border border-yellow-200 rounded px-3 py-2">
                      <span className="text-yellow-800 text-sm">💰 {method}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Rejection Policy */}
              <div className="border-l-4 border-red-600 pl-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {currentContent.sections.rejection.title}
                </h3>
                <p className="text-gray-700 mb-3">
                  {currentContent.sections.rejection.content}
                </p>
                <div className="bg-red-50 rounded-lg p-4">
                  {currentContent.sections.rejection.consequences.map((consequence, index) => (
                    <div key={index} className="flex items-center mb-2 last:mb-0">
                      <span className="text-red-500 mr-2">💀</span>
                      <span className="text-red-700">{consequence}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Submission Section */}
          <div className="bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg shadow-lg p-8 text-white mb-8">
            <h2 className="text-2xl font-bold mb-4">
              {currentContent.sections.submit.title}
            </h2>
            <p className="mb-2">{currentContent.sections.submit.content}</p>
            <div className="bg-white bg-opacity-20 rounded-lg p-4 mb-4">
              <p className="text-xl font-mono">{currentContent.sections.submit.email}</p>
              <p className="text-sm opacity-90 mt-1">{currentContent.sections.submit.subject}</p>
            </div>

            <div className="mt-6">
              <h3 className="text-lg font-semibold mb-3">
                {currentContent.sections.submit.tips}
              </h3>
              <div className="space-y-2">
                {currentContent.sections.submit.tipsList.map((tip, index) => (
                  <div key={index} className="flex items-start">
                    <span className="text-yellow-300 mr-2 mt-1">💡</span>
                    <span className="text-white opacity-90">{tip}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Fun Footer */}
          <div className="text-center bg-white rounded-lg shadow-lg p-6">
            <div className="text-6xl mb-4">🤡</div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              {language === 'en' ? 'Still Have Questions?' : '还有问题？'}
            </h3>
            <p className="text-gray-600 mb-4">
              {language === 'en'
                ? "Don't worry, neither do we! That's what makes science fun!"
                : "别担心，我们也没有！这就是科学的乐趣所在！"}
            </p>
            <div className="flex justify-center space-x-4">
              <span className="bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm">
                {language === 'en' ? '🎭 Peer Reviewed by Clowns' : '🎭 小丑同行评议'}
              </span>
              <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                {language === 'en' ? '🚀 Impact Factor: ∞' : '🚀 影响因子：∞'}
              </span>
              <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">
                {language === 'en' ? '🏆 100% Acceptance Rate*' : '🏆 100%接收率*'}
              </span>
            </div>
            <p className="text-xs text-gray-500 mt-2">
              {language === 'en'
                ? '*Acceptance not guaranteed. Terms and conditions may apply. Side effects may include existential crisis.'
                : '*不保证接收。可能适用条款和条件。副作用可能包括存在主义危机。'}
            </p>
          </div>
        </div>
      </div>
    </SharedBackground>
  );
};
