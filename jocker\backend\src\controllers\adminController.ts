import { Request, Response } from 'express';
import { asyncHandler } from '../utils/asyncHandler';
import { ApiResponse, SearchQuery } from '../types';
import prisma from '../config/database';
import { generateArticleContentWithFigures } from '../services/aiService';
// 图片生成功能已移除，保留导入以维持兼容性
// import { generateAllFiguresForArticle, regenerateFigure } from '../services/imageGenerationService';
// import { getArticleFigures } from '../services/figureService';
import { getArticlesForAdmin } from '../services/articleService';

/**
 * 获取文章列表（管理员专用，包含content字段）
 * @desc 管理员获取文章列表，包含完整的文章内容用于编辑
 * @route GET /api/admin/articles
 * @access Private (Admin only)
 */
export const getAdminArticleList = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以访问',
    });
  }

  const query: SearchQuery = {
    page: parseInt(req.query.page as string) || 1,
    limit: parseInt(req.query.limit as string) || 50,
    sortBy: req.query.sortBy as string || 'createdAt',
    sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc',
    q: req.query.q as string,
    category: req.query.category as string,
    author: req.query.author as string,
    published: req.query.published ? req.query.published === 'true' : undefined,
    featured: req.query.featured ? req.query.featured === 'true' : undefined,
  };

  const result = await getArticlesForAdmin(query);

  const response: ApiResponse = {
    success: true,
    message: '获取管理员文章列表成功',
    data: result,
  };

  res.status(200).json(response);
});

/**
 * 获取 Google AI API Key
 * @desc 只有管理员可以获取 API Key，用于前端 AI 生成功能
 * @route GET /api/admin/ai-key
 * @access Private (Admin only)
 */
export const getAIApiKey = asyncHandler(async (req: Request, res: Response) => {
  // 确保用户已认证且为管理员
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以获取 API Key',
    });
  }

  // 从环境变量获取 Google AI API Key
  const apiKey = process.env.GOOGLE_AI_API_KEY;

  if (!apiKey) {
    return res.status(500).json({
      success: false,
      message: 'Google AI API Key 未配置',
    });
  }

  const response: ApiResponse = {
    success: true,
    message: 'API Key 获取成功',
    data: {
      apiKey: apiKey,
    },
  };

  return res.status(200).json(response);
});

/**
 * 生成并保存文章内容
 * @desc 只有管理员可以生成文章内容并保存到数据库
 * @route POST /api/admin/articles/:id/generate-content
 * @access Private (Admin only)
 */
export const generateAndSaveArticleContent = asyncHandler(async (req: Request, res: Response) => {
  // 确保用户已认证且为管理员
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以生成文章内容',
    });
  }

  const articleId = parseInt(req.params.id);

  if (isNaN(articleId)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  try {
    // 查找文章
    const article = await prisma.article.findUnique({
      where: { id: articleId },
    });

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在',
      });
    }

    // 检查文章是否已有内容
    if (article.content && article.content.trim() !== '') {
      return res.status(200).json({
        success: true,
        message: '文章内容已存在',
        data: {
          content: article.content,
          cached: true,
        },
      });
    }

    // 生成文章内容
    const generatedContent = await generateArticleContentWithFigures(article);

    // 保存到数据库
    const updatedArticle = await prisma.article.update({
      where: { id: articleId },
      data: {
        content: generatedContent,
        updatedAt: new Date(),
      },
    });

    // 图片生成功能已移除，现在由前端处理
    // generateAllFiguresForArticle(articleId).catch(error => {
    //   console.error(`文章 ${articleId} 图片生成失败:`, error);
    // });

    const response: ApiResponse = {
      success: true,
      message: '文章内容生成并保存成功，图片正在后台生成',
      data: {
        content: generatedContent,
        cached: false,
      },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('生成文章内容失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '生成文章内容失败',
    });
  }
});

/**
 * 保存文章内容
 * @desc 保存前端生成的文章内容到数据库（仅管理员）
 * @route PUT /api/admin/articles/:id/content
 * @access Private (Admin only)
 */
export const saveArticleContent = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const articleId = parseInt(req.params.id);
  const { content } = req.body;

  if (isNaN(articleId)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  if (!content || typeof content !== 'string') {
    return res.status(400).json({
      success: false,
      message: '文章内容不能为空',
    });
  }

  try {
    // 检查文章是否存在
    const article = await prisma.article.findUnique({
      where: { id: articleId },
    });

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在',
      });
    }

    // 保存文章内容到数据库
    await prisma.article.update({
      where: { id: articleId },
      data: {
        content: content,
        updatedAt: new Date(),
      },
    });

    console.log(`✅ 文章 ${articleId} 内容保存成功`);

    const response: ApiResponse = {
      success: true,
      message: '文章内容保存成功',
      data: { success: true },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('保存文章内容失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '保存文章内容失败',
    });
  }
});

/**
 * 获取文章图片列表
 * @desc 获取文章的所有图片信息
 * @route GET /api/admin/articles/:id/figures
 * @access Private (Admin only)
 */
export const getArticleFiguresList = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const articleId = parseInt(req.params.id);

  if (isNaN(articleId)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章 ID',
    });
  }

  try {
    // 图片功能已移除，返回空列表
    const figures: any[] = [];

    const response: ApiResponse = {
      success: true,
      message: '图片功能已迁移到前端，请使用前端的图片管理功能',
      data: { figures },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('获取图片列表失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取图片列表失败',
    });
  }
});

/**
 * 重新生成图片
 * @desc 重新生成指定的图片
 * @route POST /api/admin/figures/:id/regenerate
 * @access Private (Admin only)
 */
export const regenerateFigureImage = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const figureId = req.params.id;

  try {
    // 图片生成功能已移除，现在由前端处理
    // regenerateFigure(figureId).catch(error => {
    //   console.error(`图片 ${figureId} 重新生成失败:`, error);
    // });

    const response: ApiResponse = {
      success: true,
      message: '图片生成功能已迁移到前端，请使用前端的图片重新生成功能',
      data: { figureId },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('启动图片重新生成失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '启动图片重新生成失败',
    });
  }
});

/**
 * 保存图片
 * @desc 保存前端生成的图片到数据库（仅管理员）
 * @route POST /api/admin/figures
 * @access Private (Admin only)
 */
export const saveFigure = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const {
    articleId,
    figureNumber,
    title,
    description,
    caption,
    imagePrompt,
    imageUrl,
    status
  } = req.body;

  try {
    // 验证必需字段
    if (!articleId || !figureNumber || !title) {
      return res.status(400).json({
        success: false,
        message: '缺少必需的图片信息',
      });
    }

    // 检查文章是否存在
    const article = await prisma.article.findUnique({
      where: { id: articleId },
    });

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在',
      });
    }

    // 使用 upsert 来创建或更新图片记录
    const figure = await prisma.figure.upsert({
      where: {
        articleId_figureNumber: {
          articleId,
          figureNumber
        }
      },
      update: {
        title,
        description: description || '',
        caption: caption || `Figure ${figureNumber}`,
        imagePrompt: imagePrompt || '',
        imageUrl: imageUrl || '',
        status: status || 'completed',
        updatedAt: new Date()
      },
      create: {
        articleId,
        figureNumber,
        title,
        description: description || '',
        caption: caption || `Figure ${figureNumber}`,
        imagePrompt: imagePrompt || '',
        imageUrl: imageUrl || '',
        status: status || 'completed',
      },
    });

    console.log(`✅ 图片 ${figureNumber} 保存成功: ${figure.id}`);

    const response: ApiResponse = {
      success: true,
      message: '图片保存成功',
      data: { figureId: figure.id },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('保存图片失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '保存图片失败',
    });
  }
});

/**
 * 清理文章的旧图片数据
 * @desc 删除指定文章的所有图片记录（重新生成时使用）
 * @route DELETE /api/admin/articles/:id/figures
 * @access Private (Admin only)
 */
export const clearArticleFigures = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const { id } = req.params;
  const articleId = parseInt(id);

  if (isNaN(articleId)) {
    return res.status(400).json({
      success: false,
      message: '无效的文章ID',
    });
  }

  try {
    // 检查文章是否存在
    const article = await prisma.article.findUnique({
      where: { id: articleId },
    });

    if (!article) {
      return res.status(404).json({
        success: false,
        message: '文章不存在',
      });
    }

    // 删除该文章的所有图片记录
    const deletedFigures = await prisma.figure.deleteMany({
      where: {
        articleId: articleId
      }
    });

    console.log(`🗑️ 已清理文章 ${articleId} 的 ${deletedFigures.count} 个图片记录`);

    const response: ApiResponse = {
      success: true,
      message: `已清理 ${deletedFigures.count} 个图片记录`,
      data: {
        deletedCount: deletedFigures.count
      }
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('清理图片记录失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '清理图片记录失败',
    });
  }
});

/**
 * 上传创始人头像
 * @desc 管理员上传创始人头像（base64 格式）
 * @route POST /api/admin/founder-avatar
 * @access Private (Admin only)
 */
export const uploadFounderAvatar = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以上传创始人头像',
    });
  }

  const { avatarUrl, name, title, description } = req.body;

  if (!avatarUrl || typeof avatarUrl !== 'string') {
    return res.status(400).json({
      success: false,
      message: '头像数据不能为空',
    });
  }

  try {
    // 使用 upsert 确保只有一个创始人记录
    const founder = await prisma.founder.upsert({
      where: { id: 1 }, // 假设只有一个创始人
      update: {
        avatarUrl,
        name: name || 'Dr. Serious McFunnyface',
        title: title || 'Editor-in-Chief',
        description: description || 'Leading researcher in satirical science and academic absurdity.',
        updatedAt: new Date(),
      },
      create: {
        id: 1,
        avatarUrl,
        name: name || 'Dr. Serious McFunnyface',
        title: title || 'Editor-in-Chief',
        description: description || 'Leading researcher in satirical science and academic absurdity.',
      },
    });

    console.log('✅ 创始人头像上传成功');

    const response: ApiResponse = {
      success: true,
      message: '创始人头像上传成功',
      data: { founder },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('上传创始人头像失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '上传创始人头像失败',
    });
  }
});

/**
 * 获取创始人头像
 * @desc 获取创始人信息和头像（所有人可访问）
 * @route GET /api/admin/founder-avatar
 * @access Public
 */
export const getFounderAvatar = asyncHandler(async (req: Request, res: Response) => {
  try {
    const founder = await prisma.founder.findFirst({
      where: { id: 1 },
    });

    const response: ApiResponse = {
      success: true,
      message: '获取创始人信息成功',
      data: { founder },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('获取创始人信息失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取创始人信息失败',
    });
  }
});

/**
 * 上传网站封面
 * @desc 管理员上传网站封面（base64 格式）
 * @route POST /api/admin/website-cover
 * @access Private (Admin only)
 */
export const uploadWebsiteCover = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以上传网站封面',
    });
  }

  const { coverUrl, title, description } = req.body;

  if (!coverUrl || typeof coverUrl !== 'string') {
    return res.status(400).json({
      success: false,
      message: '封面数据不能为空',
    });
  }

  try {
    // 查找或创建网站配置记录（使用id=1作为网站配置）
    const existingConfig = await prisma.founder.findFirst({
      where: { id: 2 }, // 使用id=2存储网站配置
    });

    let websiteConfig;
    if (existingConfig) {
      // 更新现有配置
      websiteConfig = await prisma.founder.update({
        where: { id: 2 },
        data: {
          name: title || '网站封面',
          title: 'website_cover',
          description: description || '网站主页封面图片',
          avatarUrl: coverUrl,
        },
      });
    } else {
      // 创建新配置
      websiteConfig = await prisma.founder.create({
        data: {
          id: 2,
          name: title || '网站封面',
          title: 'website_cover',
          description: description || '网站主页封面图片',
          avatarUrl: coverUrl,
        },
      });
    }

    const response: ApiResponse = {
      success: true,
      message: '网站封面上传成功',
      data: { websiteConfig },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('上传网站封面失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '上传网站封面失败',
    });
  }
});

/**
 * 获取网站封面
 * @desc 获取网站封面信息（所有人可访问）
 * @route GET /api/admin/website-cover
 * @access Public
 */
export const getWebsiteCover = asyncHandler(async (req: Request, res: Response) => {
  try {
    const websiteConfig = await prisma.founder.findFirst({
      where: { id: 2 }, // id=2存储网站配置
    });

    const response: ApiResponse = {
      success: true,
      message: '获取网站封面成功',
      data: { websiteConfig },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('获取网站封面失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取网站封面失败',
    });
  }
});

/**
 * 获取所有用户列表
 * @desc 管理员获取所有注册用户的信息
 * @route GET /api/admin/users
 * @access Private (Admin only)
 */
export const getAllUsers = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以查看用户列表',
    });
  }

  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        name: true,
        role: true,
        status: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            articles: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    const response: ApiResponse = {
      success: true,
      message: '获取用户列表成功',
      data: { users },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('获取用户列表失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取用户列表失败',
    });
  }
});

/**
 * 更新用户状态
 * @desc 管理员启用/禁用用户账户
 * @route PUT /api/admin/users/:id/status
 * @access Private (Admin only)
 */
export const updateUserStatus = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const userId = parseInt(req.params.id);
  const { status } = req.body;

  if (isNaN(userId)) {
    return res.status(400).json({
      success: false,
      message: '无效的用户 ID',
    });
  }

  if (!status || !['ACTIVE', 'DISABLED', 'SUSPENDED'].includes(status)) {
    return res.status(400).json({
      success: false,
      message: '无效的用户状态',
    });
  }

  // 防止管理员禁用自己
  if (userId === req.user.userId && status !== 'ACTIVE') {
    return res.status(400).json({
      success: false,
      message: '不能禁用自己的账户',
    });
  }

  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data: { status },
      select: {
        id: true,
        username: true,
        email: true,
        status: true,
      }
    });

    console.log(`✅ 用户 ${user.username} 状态更新为: ${status}`);

    const response: ApiResponse = {
      success: true,
      message: `用户状态更新为 ${status}`,
      data: { user },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('更新用户状态失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '更新用户状态失败',
    });
  }
});

/**
 * 更新用户角色
 * @desc 管理员修改用户角色权限
 * @route PUT /api/admin/users/:id/role
 * @access Private (Admin only)
 */
export const updateUserRole = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const userId = parseInt(req.params.id);
  const { role } = req.body;

  if (isNaN(userId)) {
    return res.status(400).json({
      success: false,
      message: '无效的用户 ID',
    });
  }

  if (!role || !['USER', 'EDITOR', 'ADMIN'].includes(role)) {
    return res.status(400).json({
      success: false,
      message: '无效的用户角色',
    });
  }

  // 防止管理员降级自己
  if (userId === req.user.userId && role !== 'ADMIN') {
    return res.status(400).json({
      success: false,
      message: '不能修改自己的管理员权限',
    });
  }

  try {
    const user = await prisma.user.update({
      where: { id: userId },
      data: { role },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
      }
    });

    console.log(`✅ 用户 ${user.username} 角色更新为: ${role}`);

    const response: ApiResponse = {
      success: true,
      message: `用户角色更新为 ${role}`,
      data: { user },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('更新用户角色失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '更新用户角色失败',
    });
  }
});

/**
 * 删除用户
 * @desc 管理员删除用户账户
 * @route DELETE /api/admin/users/:id
 * @access Private (Admin only)
 */
export const deleteUser = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足',
    });
  }

  const userId = parseInt(req.params.id);

  if (isNaN(userId)) {
    return res.status(400).json({
      success: false,
      message: '无效的用户 ID',
    });
  }

  // 防止管理员删除自己
  if (userId === req.user.userId) {
    return res.status(400).json({
      success: false,
      message: '不能删除自己的账户',
    });
  }

  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { username: true, email: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
      });
    }

    await prisma.user.delete({
      where: { id: userId }
    });

    console.log(`🗑️ 用户 ${user.username} 已被删除`);

    const response: ApiResponse = {
      success: true,
      message: `用户 ${user.username} 已被删除`,
      data: { deletedUser: user },
    };

    return res.status(200).json(response);

  } catch (error) {
    console.error('删除用户失败:', error);

    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '删除用户失败',
    });
  }
});

/**
 * 上传广告封面
 * @desc 管理员上传广告封面图片
 * @route POST /api/admin/advertisement-cover
 * @access Private (Admin only)
 */
export const uploadAdvertisementCover = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以上传广告封面',
    });
  }

  const { imageUrl, title, description, linkUrl } = req.body;

  if (!imageUrl) {
    return res.status(400).json({
      success: false,
      message: '图片数据不能为空',
    });
  }

  try {
    // 检查是否已存在广告封面
    const existingAd = await prisma.advertisementCover.findFirst();

    let advertisement;
    if (existingAd) {
      // 更新现有记录
      advertisement = await prisma.advertisementCover.update({
        where: { id: existingAd.id },
        data: {
          imageUrl,
          title: title || '广告标题',
          description: description || '广告描述',
          linkUrl: linkUrl || '#',
          updatedAt: new Date(),
        },
      });
    } else {
      // 创建新记录
      advertisement = await prisma.advertisementCover.create({
        data: {
          imageUrl,
          title: title || '广告标题',
          description: description || '广告描述',
          linkUrl: linkUrl || '#',
        },
      });
    }

    const response: ApiResponse = {
      success: true,
      message: '广告封面上传成功',
      data: { advertisement },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('上传广告封面失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '上传广告封面失败',
    });
  }
});

/**
 * 获取广告封面
 * @desc 获取当前的广告封面信息（所有人可访问）
 * @route GET /api/admin/advertisement-cover
 * @access Public
 */
export const getAdvertisementCover = asyncHandler(async (req: Request, res: Response) => {
  try {
    const advertisement = await prisma.advertisementCover.findFirst({
      orderBy: { updatedAt: 'desc' },
    });

    const response: ApiResponse = {
      success: true,
      message: advertisement ? '获取广告封面成功' : '暂无广告封面',
      data: { advertisement },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('获取广告封面失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取广告封面失败',
    });
  }
});

/**
 * 设置AI API配置
 * @desc 管理员设置自定义的AI API配置（支持Gemini和OpenAI格式）
 * @route POST /api/admin/ai-config
 * @access Private (Admin only)
 */
export const setAIConfig = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以设置AI配置',
    });
  }

  const {
    provider = 'gemini',
    apiKey,
    baseUrl,
    isActive = true,
    textModel = 'gemini-2.0-flash-exp',
    imageModel = 'imagen-3.0-generate-001'
  } = req.body;

  // 验证供应商
  if (!['gemini', 'openai'].includes(provider)) {
    return res.status(400).json({
      success: false,
      message: '供应商必须是 gemini 或 openai',
    });
  }

  if (!apiKey || typeof apiKey !== 'string' || apiKey.trim() === '') {
    return res.status(400).json({
      success: false,
      message: 'API Key不能为空',
    });
  }

  // 验证模型名称格式
  if (!textModel || typeof textModel !== 'string' || textModel.trim() === '') {
    return res.status(400).json({
      success: false,
      message: '文本模型不能为空',
    });
  }

  if (!imageModel || typeof imageModel !== 'string' || imageModel.trim() === '') {
    return res.status(400).json({
      success: false,
      message: '图片模型不能为空',
    });
  }

  // 如果是OpenAI格式，验证baseUrl
  if (provider === 'openai' && (!baseUrl || typeof baseUrl !== 'string' || baseUrl.trim() === '')) {
    return res.status(400).json({
      success: false,
      message: 'OpenAI格式需要提供基础URL',
    });
  }

  try {
    // 检查是否已存在配置
    const existingConfig = await prisma.aIConfig.findFirst();

    let config;
    if (existingConfig) {
      // 更新现有配置
      config = await prisma.aIConfig.update({
        where: { id: existingConfig.id },
        data: {
          provider,
          apiKey: apiKey.trim(),
          baseUrl: provider === 'openai' ? baseUrl?.trim() : null,
          isActive,
          textModel: textModel.trim(),
          imageModel: imageModel.trim(),
          updatedAt: new Date(),
        },
      });
    } else {
      // 创建新配置
      config = await prisma.aIConfig.create({
        data: {
          provider,
          apiKey: apiKey.trim(),
          baseUrl: provider === 'openai' ? baseUrl?.trim() : null,
          isActive,
          textModel: textModel.trim(),
          imageModel: imageModel.trim(),
        },
      });
    }

    const response: ApiResponse = {
      success: true,
      message: 'AI API配置设置成功',
      data: {
        config: {
          id: config.id,
          provider: config.provider,
          baseUrl: config.baseUrl,
          isActive: config.isActive,
          textModel: config.textModel,
          imageModel: config.imageModel,
          updatedAt: config.updatedAt,
          // 不返回完整的API Key，只返回前几位用于确认
          apiKeyPreview: config.apiKey.substring(0, 8) + '...'
        }
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('设置AI配置失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '设置AI配置失败',
    });
  }
});

/**
 * 获取AI API配置
 * @desc 获取当前的AI API配置状态
 * @route GET /api/admin/ai-config
 * @access Private (Admin only)
 */
export const getAIConfig = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以查看AI配置',
    });
  }

  try {
    const config = await prisma.aIConfig.findFirst({
      orderBy: { updatedAt: 'desc' },
    });

    const response: ApiResponse = {
      success: true,
      message: config ? '获取AI配置成功' : '暂无AI配置',
      data: {
        config: config ? {
          id: config.id,
          provider: config.provider,
          baseUrl: config.baseUrl,
          isActive: config.isActive,
          textModel: config.textModel,
          imageModel: config.imageModel,
          updatedAt: config.updatedAt,
          // 不返回完整的API Key，只返回前几位用于确认
          apiKeyPreview: config.apiKey.substring(0, 8) + '...'
        } : null
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('获取AI配置失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取AI配置失败',
    });
  }
});

/**
 * 获取AI API配置（用于前端AI调用，返回完整API Key）
 * @desc 获取当前的AI API配置，包含完整的API Key用于前端调用
 * @route GET /api/admin/ai-config-for-client
 * @access Private (Admin only)
 */
export const getAIConfigForClient = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以获取AI配置',
    });
  }

  try {
    const config = await prisma.aIConfig.findFirst({
      where: { isActive: true },
      orderBy: { updatedAt: 'desc' },
    });

    const response: ApiResponse = {
      success: true,
      message: config ? '获取AI配置成功' : '暂无AI配置',
      data: {
        config: config ? {
          id: config.id,
          provider: config.provider,
          baseUrl: config.baseUrl,
          isActive: config.isActive,
          textModel: config.textModel,
          imageModel: config.imageModel,
          apiKey: config.apiKey, // 返回完整的API Key用于前端调用
          updatedAt: config.updatedAt
        } : null
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('获取AI配置失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取AI配置失败',
    });
  }
});

/**
 * 更改用户密码
 * @desc 允许用户更改自己的密码
 * @route POST /api/admin/change-password
 * @access Private (Admin only)
 */
export const changePassword = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || typeof req.user === 'string') {
    return res.status(401).json({
      success: false,
      message: '未认证用户',
    });
  }

  const userId = (req.user as any).id;

  const { currentPassword, newPassword } = req.body;

  if (!currentPassword || !newPassword) {
    return res.status(400).json({
      success: false,
      message: '请提供当前密码和新密码',
    });
  }

  if (newPassword.length < 6) {
    return res.status(400).json({
      success: false,
      message: '新密码长度至少6位',
    });
  }

  try {
    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在',
      });
    }

    // 验证当前密码
    const bcrypt = require('bcryptjs');
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);

    if (!isCurrentPasswordValid) {
      return res.status(400).json({
        success: false,
        message: '当前密码不正确',
      });
    }

    // 加密新密码
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        password: hashedNewPassword,
        updatedAt: new Date()
      },
    });

    const response: ApiResponse = {
      success: true,
      message: '密码修改成功',
      data: null,
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('修改密码失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '修改密码失败',
    });
  }
});

/**
 * 设置期刊图标
 * @desc 设置期刊的图标（emoji或图片URL）
 * @route POST /api/admin/journal-icon
 * @access Private (Admin only)
 */
export const setJournalIcon = asyncHandler(async (req: Request, res: Response) => {
  if (!req.user || req.user.role !== 'ADMIN') {
    return res.status(403).json({
      success: false,
      message: '权限不足，只有管理员可以设置期刊图标',
    });
  }

  const { icon } = req.body;

  if (!icon || typeof icon !== 'string') {
    return res.status(400).json({
      success: false,
      message: '请提供有效的图标',
    });
  }

  try {
    // 检查是否已存在配置
    let iconConfig = await prisma.journalIcon.findFirst();

    if (iconConfig) {
      // 更新现有配置
      iconConfig = await prisma.journalIcon.update({
        where: { id: iconConfig.id },
        data: {
          icon,
          updatedAt: new Date()
        },
      });
    } else {
      // 创建新配置
      iconConfig = await prisma.journalIcon.create({
        data: { icon },
      });
    }

    const response: ApiResponse = {
      success: true,
      message: '期刊图标设置成功',
      data: { icon: iconConfig.icon },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('设置期刊图标失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '设置期刊图标失败',
    });
  }
});

/**
 * 获取期刊图标
 * @desc 获取当前的期刊图标
 * @route GET /api/admin/journal-icon
 * @access Public
 */
export const getJournalIcon = asyncHandler(async (req: Request, res: Response) => {
  try {
    const iconConfig = await prisma.journalIcon.findFirst({
      orderBy: { updatedAt: 'desc' },
    });

    const response: ApiResponse = {
      success: true,
      message: iconConfig ? '获取期刊图标成功' : '暂无期刊图标配置',
      data: {
        icon: iconConfig?.icon || '🤡'
      },
    };

    return res.status(200).json(response);
  } catch (error) {
    console.error('获取期刊图标失败:', error);
    return res.status(500).json({
      success: false,
      message: error instanceof Error ? error.message : '获取期刊图标失败',
    });
  }
});

// 保持向后兼容的旧API
export const setGeminiKey = setAIConfig;
export const getGeminiKey = getAIConfig;
