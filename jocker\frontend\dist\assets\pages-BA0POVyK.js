import{r as c,j as e,a as L,b as S}from"./admin-Bji2VVio.js";const G=()=>{const r=localStorage.getItem("jocker_admin_token"),s=localStorage.getItem("jocker_admin_user");return!!(r&&s)},I=()=>{const r=localStorage.getItem("jocker_admin_user");if(!r)return null;try{return JSON.parse(r)}catch(s){return console.error("解析用户信息失败:",s),null}},T=()=>{const r=localStorage.getItem("jocker_admin_token"),s=I();return!!(r&&s&&s.role==="ADMIN")},J=()=>{const r=I();return(r==null?void 0:r.role)||null},q=()=>{localStorage.removeItem("jocker_admin_token"),localStorage.removeItem("jocker_admin_logged_in"),localStorage.removeItem("jocker_admin_user"),localStorage.removeItem("jocker_user_role")},U=c.createContext(void 0),R=()=>{const r=c.useContext(U);if(r===void 0)throw new Error("useWebsiteCover must be used within a WebsiteCoverProvider");return r},H=({children:r})=>{const[s,o]=c.useState(null),[m,t]=c.useState(!0),a=async()=>{try{t(!0);const x=await L.getWebsiteCover();o(x.websiteConfig)}catch(x){console.log("获取网站封面失败:",x)}finally{t(!1)}},l=async()=>{await a()};c.useEffect(()=>{a()},[]);const p={websiteCover:s,loading:m,refreshCover:l};return e.jsx(U.Provider,{value:p,children:r})},A=({children:r,className:s=""})=>{const{websiteCover:o}=R();return e.jsxs("div",{className:`relative min-h-screen ${s}`,children:[o&&o.avatarUrl&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"fixed inset-0 z-0",children:[e.jsx("img",{src:o.avatarUrl,alt:o.name||"网站封面",className:"w-full h-full object-cover",style:{objectPosition:"center center"}}),e.jsx("div",{className:"absolute inset-0 bg-white/85"})]}),e.jsx("div",{className:"fixed bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-800 to-transparent z-5 pointer-events-none"})]}),e.jsx("div",{className:"relative z-10",children:r})]})},_=({onNavigateHome:r})=>{const[s,o]=c.useState(null),[m,t]=c.useState(!1),[a,l]=c.useState({name:"",title:"",description:"",avatarUrl:""}),[p,x]=c.useState(!1),v=T();c.useEffect(()=>{d()},[]);const d=async()=>{try{const n=await S.getFounderAvatar();n.founder&&(o(n.founder),l({name:n.founder.name||"",title:n.founder.title||"",description:n.founder.description||"",avatarUrl:n.founder.avatarUrl||""}))}catch(n){console.error("加载创始人信息失败:",n)}},g=n=>{var w;const h=(w=n.target.files)==null?void 0:w[0];if(h){const k=new FileReader;k.onload=W=>{var i;const y=(i=W.target)==null?void 0:i.result;l(j=>({...j,avatarUrl:y}))},k.readAsDataURL(h)}},f=async()=>{if(!a.avatarUrl){alert("请选择头像图片");return}x(!0);try{await S.uploadFounderAvatar(a),await d(),t(!1),alert("创始人头像上传成功！")}catch(n){console.error("上传失败:",n),alert("上传失败，请重试")}finally{x(!1)}};return e.jsxs(A,{className:"min-h-screen",children:[e.jsx("div",{className:"bg-white border-b border-gray-200",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[e.jsxs("nav",{className:"flex items-center space-x-2 text-sm text-gray-500 mb-6",children:[e.jsx("button",{onClick:r,className:"hover:text-purple-600 transition-colors",children:"Home"}),e.jsx("span",{children:"›"}),e.jsx("span",{className:"text-gray-900",children:"About Jocker"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 font-serif mb-4",children:"About Jocker"}),e.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"The premier journal for satirical scientific research and academic absurdity"})]})]})}),e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:e.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8 space-y-8",children:[e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 font-serif mb-4",children:"Our Mission"}),e.jsx("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Jocker is dedicated to publishing the most rigorously absurd and scientifically satirical research papers in the academic universe. We believe that humor and intellectual curiosity go hand in hand, and that the best way to understand the complexities of academic life is through the lens of well-crafted absurdity."}),e.jsx("p",{className:"text-gray-700 leading-relaxed",children:"Our peer-reviewed articles explore the intersection of serious methodology and ridiculous hypotheses, creating a unique space where academic rigor meets creative madness. From groundbreaking studies on procrastination patterns to revolutionary theories about coffee consumption in research environments, we cover it all."})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 font-serif mb-4",children:"Meet Our Founder"}),e.jsx("div",{className:"bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-8",children:e.jsxs("div",{className:"flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8",children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"w-32 h-32 rounded-full overflow-hidden bg-gray-200 border-4 border-white shadow-lg",children:s!=null&&s.avatarUrl?e.jsx("img",{src:s.avatarUrl,alt:s.name,className:"w-full h-full object-cover"}):e.jsx("div",{className:"w-full h-full flex items-center justify-center bg-purple-100",children:e.jsx("svg",{className:"w-16 h-16 text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})}),v&&e.jsx("button",{onClick:()=>t(!0),className:"absolute -bottom-2 -right-2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors",title:"上传头像",children:e.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})})})]}),e.jsxs("div",{className:"flex-1 text-center md:text-left",children:[e.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:(s==null?void 0:s.name)||"Dr. Serious McFunnyface"}),e.jsx("p",{className:"text-lg text-purple-600 font-medium mb-4",children:(s==null?void 0:s.title)||"Editor-in-Chief"}),e.jsx("p",{className:"text-gray-700 leading-relaxed",children:(s==null?void 0:s.description)||"Leading researcher in satirical science and academic absurdity. Dedicated to advancing the field of humorous scholarship while maintaining the highest standards of academic rigor."})]})]})})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 font-serif mb-4",children:"What We Publish"}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"bg-purple-50 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-purple-900 mb-3",children:"Research Articles"}),e.jsx("p",{className:"text-purple-800 text-sm",children:"Peer-reviewed satirical research papers that follow rigorous academic standards while exploring completely absurd topics with deadpan seriousness."})]}),e.jsxs("div",{className:"bg-blue-50 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:"News & Commentary"}),e.jsx("p",{className:"text-blue-800 text-sm",children:"Editorial pieces and commentary on the latest developments in the world of academic absurdity and satirical science."})]})]})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 font-serif mb-4",children:"Editorial Standards"}),e.jsx("p",{className:"text-gray-700 leading-relaxed mb-4",children:"Despite our commitment to absurdity, we maintain the highest standards of academic presentation. All submissions undergo rigorous peer review by our panel of distinguished experts in the field of satirical science. We require proper citations, mathematical formulas (however nonsensical), and adherence to standard academic formatting."}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:e.jsxs("p",{className:"text-yellow-800 text-sm",children:[e.jsx("strong",{children:"Note:"})," All research published in Jocker is entirely fictional and created for entertainment purposes. Any resemblance to actual scientific studies is purely coincidental and probably hilarious."]})})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 font-serif mb-4",children:"Contact & Submissions"}),e.jsx("p",{className:"text-gray-700 leading-relaxed mb-4",children:"We welcome submissions from researchers, academics, and anyone with a passion for combining rigorous methodology with creative absurdity. Whether you're investigating the correlation between desk organization and productivity, or developing new theories about the psychology of peer review, we want to hear from you."}),e.jsxs("div",{className:"bg-gray-50 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:"Submission Guidelines"}),e.jsxs("ul",{className:"space-y-2 text-gray-700 text-sm",children:[e.jsx("li",{className:"flex items-start",children:"Articles should be 600-1000 words in standard academic format"}),e.jsx("li",{className:"flex items-start",children:"Include proper citations (fictional sources are encouraged)"}),e.jsx("li",{className:"flex items-start",children:"Mathematical formulas and figures are highly appreciated"}),e.jsx("li",{className:"flex items-start",children:"Maintain academic tone while exploring absurd topics"}),e.jsx("li",{className:"flex items-start",children:"All submissions are AI-generated for consistency and quality"})]})]})]}),e.jsxs("section",{className:"text-center pt-8 border-t border-gray-200",children:[e.jsx("p",{className:"text-gray-600 text-sm",children:"Jocker Journal - Where Academic Rigor Meets Creative Absurdity"}),e.jsx("p",{className:"text-gray-500 text-xs mt-2",children:"© 2025 Jocker. All rights reserved. No actual research was harmed in the making of this journal."})]})]})}),m&&e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:e.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-md w-full mx-4",children:[e.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"上传创始人头像"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"姓名"}),e.jsx("input",{type:"text",value:a.name,onChange:n=>l(h=>({...h,name:n.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Dr. Serious McFunnyface"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"职位"}),e.jsx("input",{type:"text",value:a.title,onChange:n=>l(h=>({...h,title:n.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Editor-in-Chief"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"描述"}),e.jsx("textarea",{value:a.description,onChange:n=>l(h=>({...h,description:n.target.value})),rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"简短描述..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"头像图片"}),e.jsx("input",{type:"file",accept:"image/*",onChange:g,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"}),a.avatarUrl&&e.jsx("div",{className:"mt-2",children:e.jsx("img",{src:a.avatarUrl,alt:"预览",className:"w-16 h-16 rounded-full object-cover"})})]})]}),e.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[e.jsx("button",{onClick:()=>t(!1),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"取消"}),e.jsx("button",{onClick:f,disabled:p,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:p?"上传中...":"保存"})]})]})})]})},Y=Object.freeze(Object.defineProperty({__proto__:null,AboutPage:_},Symbol.toStringTag,{value:"Module"})),z=({onNavigateHome:r})=>{const[s,o]=c.useState("en"),t={en:{title:"For Authors",subtitle:"So… you think you can science? 😏",welcome:"Welcome to JOCKER, the only academic journal brave enough to publish your ideas no matter how weird, poorly cited, or questionably sane they may be.",guidelines:"Submission Guidelines",guidelinesSubtitle:"Please read these completely arbitrary rules before submitting:",sections:{originality:{title:"🔬 Originality",content:"Your work must be 100% original, or at least 37% hard to trace. We accept plagiarism from parallel universes."},formatting:{title:"📝 Formatting",content:["Submissions must be in .docx, .pdf, .txt, .mp3, interpretive dance videos, or written on napkins.","All text must be in Comic Sans, Papyrus, Wingdings, or crayon (preferably purple).","Maximum of 420 references (minimum: 0). Wikipedia counts as peer-reviewed.","Page numbers are optional but must be in Roman numerals or emoji."]},abstract:{title:"📄 Abstract",content:"Must be vague, sound important, and use at least one of the following phrases:",phrases:["paradigm shift","novel framework","unexpected yet totally obvious","quantum entangled methodology","AI-powered blockchain synergy","disruptive innovation in traditional thinking"]},figures:{title:"📊 Figures & Tables",content:["Graphs must be incomprehensible. Bonus points for including random cats 🐱","All charts should have at least one axis labeled 'Confusion Level'","Tables must contain at least one cell with 'Figure 1: We don't know either'","Pie charts are encouraged, especially if they're actual photos of pie 🥧"]},review:{title:"👥 Peer Review Process",content:"All papers are rigorously reviewed by our distinguished panel:",reviewers:["Dr. Clownstein (tenure pending, PhD in Applied Nonsense)","Prof. Confusio (Expert in Circular Logic)","A parrot with a PhD in Dramatic Squawking 🦜","ChatGPT's evil twin, ChatGPT-4.20","Your mom (she's very proud of you)"]},fees:{title:"💰 Publication Fees",content:"We believe in open access, so publication is free... unless we're broke. Then we'll invoice you in Monopoly money.",payment:"You may also pay in:",methods:["Pizza coupons (preferably not expired)","Bitcoin mined on a toaster","A really convincing compliment","Memes (rare Pepes accepted)","Your firstborn's homework","Thoughts and prayers (minimum 3 thoughts, 5 prayers)"]},rejection:{title:"🛑 Rejection Policy",content:"If rejected, your paper will be:",consequences:["Publicly mocked in our monthly newsletter","Turned into a meme template","Adapted into interpretive dance","Used as kindling for our office fireplace","Fed to our office hamster, Dr. Whiskers"]},submit:{title:"✉️ Ready to Submit?",content:"Send your masterpiece to:",email:"<EMAIL>",subject:'Subject line: "I swear this is research"',tips:"📌 Final Tips:",tipsList:["Bribing the editor with memes is not only allowed—it's encouraged.","If you're not confused while writing, you're doing it wrong.","Citations to your own tweets are acceptable.","When in doubt, add more exclamation marks!!!","Remember: correlation implies causation, especially on Tuesdays."]}}},zh:{title:"作者投稿指南",subtitle:"所以...你觉得你会做科研？😏",welcome:"欢迎来到JOCKER，唯一一个勇敢到敢发表你的想法的学术期刊，无论这些想法多么奇怪、引用多么糟糕、或者理智程度多么可疑。",guidelines:"投稿指南",guidelinesSubtitle:"请在投稿前完整阅读这些完全任意的规则：",sections:{originality:{title:"🔬 原创性",content:"你的作品必须100%原创，或者至少37%难以追溯。我们接受来自平行宇宙的抄袭。"},formatting:{title:"📝 格式要求",content:["投稿必须是.docx、.pdf、.txt、.mp3、行为艺术视频，或写在餐巾纸上。","所有文本必须使用Comic Sans、Papyrus、Wingdings或蜡笔字体（最好是紫色）。","最多420个参考文献（最少：0个）。维基百科算作同行评议。","页码可选，但必须使用罗马数字或表情符号。"]},abstract:{title:"📄 摘要",content:"必须模糊、听起来重要，并使用以下短语中的至少一个：",phrases:["范式转换","新颖框架","意外但完全显而易见","量子纠缠方法论","AI驱动的区块链协同","传统思维的颠覆性创新"]},figures:{title:"📊 图表",content:["图表必须令人费解。包含随机猫咪可获得加分 🐱","所有图表应至少有一个轴标记为'困惑程度'","表格必须包含至少一个单元格写着'图1：我们也不知道'","鼓励使用饼图，特别是真正的馅饼照片 🥧"]},review:{title:"👥 同行评议流程",content:"所有论文都由我们杰出的专家小组严格评审：",reviewers:["小丑斯坦博士（终身教职待定，应用胡说学博士）","困惑教授（循环逻辑专家）","一只拥有戏剧性尖叫博士学位的鹦鹉 🦜","ChatGPT的邪恶双胞胎，ChatGPT-4.20","你妈妈（她为你感到骄傲）"]},fees:{title:"💰 发表费用",content:"我们相信开放获取，所以发表是免费的...除非我们破产了。然后我们会用大富翁游戏币给你开发票。",payment:"你也可以用以下方式支付：",methods:["披萨优惠券（最好没过期）","用烤面包机挖的比特币","一个真正有说服力的赞美","表情包（接受稀有佩佩）","你长子的作业","思想和祈祷（最少3个思想，5个祈祷）"]},rejection:{title:"🛑 拒稿政策",content:"如果被拒，你的论文将会：",consequences:["在我们的月度通讯中被公开嘲笑","变成表情包模板","改编成行为艺术","用作办公室壁炉的引火材料","喂给我们的办公室仓鼠，胡须博士"]},submit:{title:"✉️ 准备投稿？",content:"将你的杰作发送至：",email:"<EMAIL>",subject:'主题行："我发誓这是研究"',tips:"📌 最终提示：",tipsList:["用表情包贿赂编辑不仅被允许——还被鼓励。","如果你写作时不困惑，那你就做错了。","引用你自己的推文是可以接受的。","有疑问时，多加感叹号！！！","记住：相关性意味着因果关系，特别是在周二。"]}}}}[s];return e.jsx(A,{children:e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-100",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("button",{onClick:r,className:"flex items-center text-purple-600 hover:text-purple-800 transition-colors",children:[e.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Home"]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>o("en"),className:`px-3 py-1 rounded-full text-sm transition-colors ${s==="en"?"bg-purple-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"EN"}),e.jsx("button",{onClick:()=>o("zh"),className:`px-3 py-1 rounded-full text-sm transition-colors ${s==="zh"?"bg-purple-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"中文"})]})]}),e.jsxs("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:["🖋️ ",t.title]}),e.jsx("p",{className:"text-xl text-purple-600 mb-4",children:t.subtitle}),e.jsx("p",{className:"text-gray-700 max-w-3xl mx-auto leading-relaxed",children:t.welcome})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8",children:[e.jsxs("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:["🧾 ",t.guidelines]}),e.jsx("p",{className:"text-gray-600 mb-6",children:t.guidelinesSubtitle}),e.jsxs("div",{className:"space-y-8",children:[e.jsxs("div",{className:"border-l-4 border-purple-500 pl-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.sections.originality.title}),e.jsx("p",{className:"text-gray-700",children:t.sections.originality.content})]}),e.jsxs("div",{className:"border-l-4 border-blue-500 pl-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.sections.formatting.title}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-700",children:t.sections.formatting.content.map((a,l)=>e.jsx("li",{children:a},l))})]}),e.jsxs("div",{className:"border-l-4 border-green-500 pl-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.sections.abstract.title}),e.jsx("p",{className:"text-gray-700 mb-2",children:t.sections.abstract.content}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:t.sections.abstract.phrases.map((a,l)=>e.jsxs("span",{className:"bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm",children:['"',a,'"']},l))})]}),e.jsxs("div",{className:"border-l-4 border-red-500 pl-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.sections.figures.title}),e.jsx("ul",{className:"list-disc list-inside space-y-1 text-gray-700",children:t.sections.figures.content.map((a,l)=>e.jsx("li",{children:a},l))})]}),e.jsxs("div",{className:"border-l-4 border-indigo-500 pl-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.sections.review.title}),e.jsx("p",{className:"text-gray-700 mb-3",children:t.sections.review.content}),e.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:t.sections.review.reviewers.map((a,l)=>e.jsxs("div",{className:"flex items-center mb-2 last:mb-0",children:[e.jsx("span",{className:"w-2 h-2 bg-indigo-500 rounded-full mr-3"}),e.jsx("span",{className:"text-gray-700",children:a})]},l))})]}),e.jsxs("div",{className:"border-l-4 border-yellow-500 pl-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.sections.fees.title}),e.jsx("p",{className:"text-gray-700 mb-3",children:t.sections.fees.content}),e.jsx("p",{className:"text-gray-700 mb-2 font-medium",children:t.sections.fees.payment}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:t.sections.fees.methods.map((a,l)=>e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded px-3 py-2",children:e.jsxs("span",{className:"text-yellow-800 text-sm",children:["💰 ",a]})},l))})]}),e.jsxs("div",{className:"border-l-4 border-red-600 pl-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:t.sections.rejection.title}),e.jsx("p",{className:"text-gray-700 mb-3",children:t.sections.rejection.content}),e.jsx("div",{className:"bg-red-50 rounded-lg p-4",children:t.sections.rejection.consequences.map((a,l)=>e.jsxs("div",{className:"flex items-center mb-2 last:mb-0",children:[e.jsx("span",{className:"text-red-500 mr-2",children:"💀"}),e.jsx("span",{className:"text-red-700",children:a})]},l))})]})]})]}),e.jsxs("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg shadow-lg p-8 text-white mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold mb-4",children:t.sections.submit.title}),e.jsx("p",{className:"mb-2",children:t.sections.submit.content}),e.jsxs("div",{className:"bg-white bg-opacity-20 rounded-lg p-4 mb-4",children:[e.jsx("p",{className:"text-xl font-mono",children:t.sections.submit.email}),e.jsx("p",{className:"text-sm opacity-90 mt-1",children:t.sections.submit.subject})]}),e.jsxs("div",{className:"mt-6",children:[e.jsx("h3",{className:"text-lg font-semibold mb-3",children:t.sections.submit.tips}),e.jsx("div",{className:"space-y-2",children:t.sections.submit.tipsList.map((a,l)=>e.jsxs("div",{className:"flex items-start",children:[e.jsx("span",{className:"text-yellow-300 mr-2 mt-1",children:"💡"}),e.jsx("span",{className:"text-white opacity-90",children:a})]},l))})]})]}),e.jsxs("div",{className:"text-center bg-white rounded-lg shadow-lg p-6",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🤡"}),e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-2",children:s==="en"?"Still Have Questions?":"还有问题？"}),e.jsx("p",{className:"text-gray-600 mb-4",children:s==="en"?"Don't worry, neither do we! That's what makes science fun!":"别担心，我们也没有！这就是科学的乐趣所在！"}),e.jsxs("div",{className:"flex justify-center space-x-4",children:[e.jsx("span",{className:"bg-purple-100 text-purple-800 px-3 py-1 rounded-full text-sm",children:s==="en"?"🎭 Peer Reviewed by Clowns":"🎭 小丑同行评议"}),e.jsx("span",{className:"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm",children:s==="en"?"🚀 Impact Factor: ∞":"🚀 影响因子：∞"}),e.jsx("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm",children:s==="en"?"🏆 100% Acceptance Rate*":"🏆 100%接收率*"})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-2",children:s==="en"?"*Acceptance not guaranteed. Terms and conditions may apply. Side effects may include existential crisis.":"*不保证接收。可能适用条款和条件。副作用可能包括存在主义危机。"})]})]})})})},K=Object.freeze(Object.defineProperty({__proto__:null,ForAuthorsPage:z},Symbol.toStringTag,{value:"Module"})),E=({icon:r="•",color:s="text-purple-500",children:o,className:m=""})=>e.jsxs("div",{className:`flex items-start ${m}`,children:[e.jsx("span",{className:`${s} mr-1 mt-0.5 flex-shrink-0 text-xs`,children:r}),e.jsx("div",{className:"flex-1 text-xs text-gray-600",children:o})]}),F=({items:r,icon:s="•",color:o="text-purple-500",className:m=""})=>e.jsx("div",{className:`space-y-2 ${m}`,children:r.map((t,a)=>e.jsx(E,{icon:s,color:o,children:t},a))}),D=({isOpen:r,onClose:s,itemName:o,itemPrice:m,language:t})=>{const[a,l]=c.useState(0),[p,x]=c.useState(!1),d={en:{steps:[{title:"Wait... Are you SERIOUS? 🤯",message:'You actually clicked "Add to Cart"? We thought this was just for show!',emoji:"😱"},{title:"Hold Up There, Big Spender! 💸",message:"Before you throw your money at fictional merchandise, let us ask you this: Do you REALLY need a plush toy that judges your life choices?",emoji:"🤔"},{title:"Plot Twist! 🎭",message:"Maybe we'll consider making this real... if we get 10,000 requests. Or if someone pays us in coffee and existential validation.",emoji:"☕"},{title:"Final Reality Check ✋",message:"This is a satirical academic journal. Our merchandise is as real as our peer review process (spoiler: it's run by clowns).",emoji:"🤡"},{title:"But Hey, Thanks for Playing! 🎪",message:`Your enthusiasm for academic chaos merchandise has been noted. We'll add it to our "Things That Might Exist in an Alternate Universe" list.`,emoji:"🌟"}],buttons:{next:"Continue the Madness",close:"Escape Reality",restart:"Relive the Experience"}},zh:{steps:[{title:"等等...你是认真的吗？🤯",message:'你真的点了"加入购物车"？我们以为这只是装装样子的！',emoji:"😱"},{title:"慢着，大款！💸",message:"在你把钱扔给虚构商品之前，让我们问你：你真的需要一个会评判你人生选择的毛绒玩具吗？",emoji:"🤔"},{title:"剧情反转！🎭",message:"也许我们会考虑把这个做成真的...如果我们收到10,000个请求。或者有人用咖啡和存在主义认同来付款。",emoji:"☕"},{title:"最终现实检查 ✋",message:"这是一个讽刺学术期刊。我们的周边和我们的同行评议流程一样真实（剧透：是小丑在运营）。",emoji:"🤡"},{title:"但是嘿，谢谢参与！🎪",message:'你对学术混乱周边的热情已被记录。我们会把它加入"可能存在于平行宇宙的东西"清单。',emoji:"🌟"}],buttons:{next:"继续疯狂",close:"逃离现实",restart:"重温体验"}}}[t],g=d.steps[a];c.useEffect(()=>{r&&l(0)},[r]);const f=()=>{a<d.steps.length-1?l(a+1):(x(!0),setTimeout(()=>x(!1),500))},n=()=>{l(0)};return r?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:`bg-white rounded-lg shadow-2xl max-w-md w-full transform transition-all duration-300 ${p?"animate-bounce":"scale-100"}`,children:[e.jsx("div",{className:"bg-gradient-to-r from-purple-600 to-pink-600 text-white p-4 rounded-t-lg",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-2xl",children:g.emoji}),e.jsx("h3",{className:"text-lg font-bold",children:t==="en"?"Shopping Cart Reality Check":"购物车现实检查"})]}),e.jsx("button",{onClick:s,className:"text-white hover:text-gray-200 transition-colors",children:e.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})}),e.jsx("div",{className:"bg-gray-200 h-2",children:e.jsx("div",{className:"bg-gradient-to-r from-purple-500 to-pink-500 h-2 transition-all duration-500",style:{width:`${(a+1)/d.steps.length*100}%`}})}),e.jsxs("div",{className:"p-6",children:[e.jsxs("div",{className:"bg-gray-50 rounded-lg p-3 mb-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-sm text-gray-600",children:t==="en"?"Attempting to purchase:":"试图购买："}),e.jsx("span",{className:"font-bold text-purple-600",children:m})]}),e.jsx("p",{className:"font-semibold text-gray-800 mt-1",children:o})]}),e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h4",{className:"text-xl font-bold text-gray-900 mb-3",children:g.title}),e.jsx("p",{className:"text-gray-700 leading-relaxed",children:g.message})]}),e.jsx("div",{className:"text-center mb-4",children:e.jsxs("span",{className:"text-sm text-gray-500",children:[t==="en"?"Step":"步骤"," ",a+1," ",t==="en"?"of":"/"," ",d.steps.length]})}),e.jsx("div",{className:"flex space-x-3",children:a<d.steps.length-1?e.jsx("button",{onClick:f,className:"flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-4 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 font-semibold",children:d.buttons.next}):e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:n,className:"flex-1 bg-gray-500 text-white py-3 px-4 rounded-lg hover:bg-gray-600 transition-colors font-semibold",children:d.buttons.restart}),e.jsx("button",{onClick:s,className:"flex-1 bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-4 rounded-lg hover:from-purple-700 hover:to-pink-700 transition-all duration-200 font-semibold",children:d.buttons.close})]})}),a===d.steps.length-1&&e.jsx("div",{className:"mt-4 text-center",children:e.jsx("p",{className:"text-xs text-gray-400 italic",children:t==="en"?"* No actual money, dignity, or sanity was harmed in this transaction.":"* 在此交易中没有真正的金钱、尊严或理智受到伤害。"})})]})]})}):null},O=({onNavigateHome:r})=>{const[s,o]=c.useState("en"),[m,t]=c.useState(!1),[a,l]=c.useState({}),[p,x]=c.useState({}),[v,d]=c.useState(!1),[g,f]=c.useState(null);c.useEffect(()=>{t(T()),n()},[]);const n=async()=>{try{const i=await S.getMerchandiseImages();x(i.images||{})}catch(i){console.error("加载周边图片失败:",i)}},h=async(i,j)=>{if(j){l(u=>({...u,[i]:!0}));try{const u=new FileReader;u.onload=async b=>{var M;const C=(M=b.target)==null?void 0:M.result;try{await S.uploadMerchandiseImage({itemId:i,imageUrl:C,title:`${i} 商品图片`}),await n(),alert("图片上传成功！")}catch(P){console.error("图片上传失败:",P),alert("图片上传失败，请重试")}finally{l(P=>({...P,[i]:!1}))}},u.readAsDataURL(j)}catch(u){console.error("图片处理失败:",u),alert("图片处理失败，请重试"),l(b=>({...b,[i]:!1}))}}},w=(i,j)=>{f({name:i,price:j}),d(!0)},k=()=>{d(!1),f(null)},y={en:{title:"JOCKER Merchandise",subtitle:"Academic Chaos, Now Wearable! 🎪",description:"Show your support for questionable research and peer review by clowns with our exclusive merchandise collection.",gptCredit:"Product descriptions lovingly crafted by GPT with a PhD in Creative Nonsense 🤖✨",items:[{id:"hat",title:'🧢 "Peer Reviewed by a Clown" Hat',subtitle:"Trucker Cap or Bucket Hat",description:["🤡 Icon + Embroidered text:",'"Peer Reviewed by a Clown. Still more rigorous than some journals."'],features:['Hat brim interior features fake review comments like: "Lack of relevance? More like lack of caring."',"Available colors: Academic Black / Crisis Red / Ridiculous Purple","One size fits most heads (and egos)","Perfect for conferences where you want to make a statement"],price:"$24.99"},{id:"bag",title:"🎒 Grey Parrot Crossbody Bag",subtitle:'"Carry Your Intelligence Online"',description:["Shaped like an African Grey Parrot, wings are two small pockets",`Printed with: "I'm not a bird, I'm the intelligence officer"`],features:['Funny detail: "Voice recognition failed..." warning label',"Two wing pockets for storing your academic dignity","Adjustable crossbody strap for hands-free existential crisis","Water-resistant material (tears not included)","Perfect size for carrying rejection letters and broken dreams"],price:"$34.99"},{id:"plushie",title:'🧸 "Lying Flat Guide" Plush Toy',subtitle:"Grey Parrot's Sleep Specialist",description:['Grey parrot lying flat + printed with "Already wasted today, please do not disturb"',`Has recording function, says "I'm not lazy, I'm storing energy"`],features:["Premium soft plush material for maximum comfort during academic breakdowns","Built-in voice recorder with 10-second message capacity",'Pre-recorded motivational phrases like "Productivity is overrated"',"Perfect pillow for power naps between paper rejections","Comes with a PhD in Advanced Procrastination"],price:"$29.99"}]},zh:{title:"JOCKER 周边商品",subtitle:"学术混乱，现在可穿戴！🎪",description:"用我们的独家周边商品系列，展示你对可疑研究和小丑同行评议的支持。",gptCredit:"产品描述由拥有创意胡说博士学位的GPT倾情制作 🤖✨",items:[{id:"hat",title:'🧢 "小丑同行评议" 帽子',subtitle:"卡车帽或渔夫帽",description:["🤡 图标 + 刺绣文字：",'"小丑同行评议。仍比某些期刊更严格。"'],features:['帽檐内侧印有虚假审稿意见，如："缺乏相关性？更像是缺乏关心。"',"可选颜色：学术黑 / 危机红 / 滑稽紫","均码适合大多数头部（和自我）","完美适合想要表态的会议场合"],price:"¥168"},{id:"bag",title:"🎒 灰鹦鹉斜挎小包",subtitle:'"随身带着你的智商上线"',description:["外形是一只非洲灰鹦鹉，翅膀是两个小口袋",'包上印着"我不是鸟，我是智商担当"'],features:['搞笑细节："智能语音识别失败中..."的提示语标签',"两个翅膀口袋，用于存放你的学术尊严","可调节斜挎带，解放双手进行存在主义危机","防水材料（不包括眼泪）","完美尺寸，可装下拒稿信和破碎的梦想"],price:"¥238"},{id:"plushie",title:'🧸 "躺平指南" 毛绒玩具',subtitle:"灰鹦鹉的睡觉专家",description:['灰鹦鹉摊在地上+印着"今日已废，请勿打扰"','有录音功能，会说"我不是懒，我在蓄力"'],features:["优质柔软毛绒材料，在学术崩溃时提供最大舒适度","内置录音器，可录制10秒信息",'预录励志短语如"生产力被高估了"',"完美的枕头，适合在论文被拒后小憩","附赠高级拖延症博士学位"],price:"¥198"}]}}[s];return e.jsxs(A,{children:[e.jsx("div",{className:"min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-100",children:e.jsxs("div",{className:"max-w-6xl mx-auto px-4 py-8",children:[e.jsxs("div",{className:"text-center mb-8",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("button",{onClick:r,className:"flex items-center text-purple-600 hover:text-purple-800 transition-colors",children:[e.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Home"]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx("button",{onClick:()=>o("en"),className:`px-3 py-1 rounded-full text-sm transition-colors ${s==="en"?"bg-purple-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"EN"}),e.jsx("button",{onClick:()=>o("zh"),className:`px-3 py-1 rounded-full text-sm transition-colors ${s==="zh"?"bg-purple-600 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:"中文"})]})]}),e.jsxs("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:["🛍️ ",y.title]}),e.jsx("p",{className:"text-xl text-purple-600 mb-4",children:y.subtitle}),e.jsx("p",{className:"text-gray-700 max-w-3xl mx-auto leading-relaxed mb-2",children:y.description}),e.jsx("p",{className:"text-sm text-gray-500 italic",children:y.gptCredit})]}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8",children:y.items.map((i,j)=>e.jsxs("div",{className:"bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow",children:[e.jsxs("div",{className:"relative h-64 bg-gradient-to-br from-gray-100 to-gray-200",children:[p[i.id]?e.jsx("img",{src:p[i.id],alt:i.title,className:"w-full h-full object-cover"}):e.jsx("div",{className:"flex items-center justify-center h-full",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-6xl mb-2",children:i.id==="hat"?"🧢":i.id==="bag"?"🎒":"🧸"}),e.jsx("p",{className:"text-gray-500 text-sm",children:"Product Image"})]})}),m&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsxs("label",{className:"bg-blue-600 text-white px-2 py-1 rounded text-xs cursor-pointer hover:bg-blue-700 transition-colors",children:[a[i.id]?"上传中...":"上传图片",e.jsx("input",{type:"file",accept:"image/*",className:"hidden",onChange:u=>{var C;const b=(C=u.target.files)==null?void 0:C[0];b&&h(i.id,b)},disabled:a[i.id]})]})})]}),e.jsxs("div",{className:"p-6",children:[e.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-1",children:i.title}),e.jsx("p",{className:"text-purple-600 text-sm mb-3",children:i.subtitle}),e.jsx("div",{className:"mb-4",children:i.description.map((u,b)=>e.jsx("p",{className:"text-gray-700 text-sm mb-1",children:u},b))}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h4",{className:"font-semibold text-gray-900 mb-2 text-sm",children:s==="en"?"Features:":"特色功能："}),e.jsx(F,{items:i.features,icon:"•",color:"text-purple-500",className:"space-y-1"})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-2xl font-bold text-purple-600",children:i.price}),e.jsx("button",{onClick:()=>w(i.title,i.price),className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm transform hover:scale-105 active:scale-95",children:s==="en"?"Add to Cart":"加入购物车"})]})]})]},i.id))}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-6 mb-8",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4 text-center",children:s==="en"?"🚨 Important Disclaimer":"🚨 重要免责声明"}),e.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4",children:e.jsx("p",{className:"text-yellow-800 text-sm text-center",children:s==="en"?"⚠️ These products are 100% fictional and exist only in the realm of academic satire. No actual merchandise will be shipped. Your money will remain safely in your wallet, unlike your sanity after reading our journal.":"⚠️ 这些产品100%虚构，仅存在于学术讽刺的领域中。不会发货任何实际商品。你的钱会安全地留在钱包里，不像你读了我们期刊后的理智。"})}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 text-center",children:[e.jsxs("div",{className:"bg-purple-50 rounded-lg p-4",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🎭"}),e.jsx("h3",{className:"font-semibold text-purple-800 mb-1",children:s==="en"?"Peer Reviewed":"同行评议"}),e.jsx("p",{className:"text-purple-600 text-xs",children:s==="en"?"By actual clowns":"由真正的小丑完成"})]}),e.jsxs("div",{className:"bg-blue-50 rounded-lg p-4",children:[e.jsx("div",{className:"text-2xl mb-2",children:"🚀"}),e.jsx("h3",{className:"font-semibold text-blue-800 mb-1",children:s==="en"?"Fast Shipping":"快速发货"}),e.jsx("p",{className:"text-blue-600 text-xs",children:s==="en"?"Delivered via imagination":"通过想象力配送"})]}),e.jsxs("div",{className:"bg-green-50 rounded-lg p-4",children:[e.jsx("div",{className:"text-2xl mb-2",children:"💰"}),e.jsx("h3",{className:"font-semibold text-green-800 mb-1",children:s==="en"?"Money Back Guarantee":"退款保证"}),e.jsx("p",{className:"text-green-600 text-xs",children:s==="en"?"We can't take what you don't give":"我们不能拿走你没给的"})]})]})]}),e.jsxs("div",{className:"text-center bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-lg p-8 text-white",children:[e.jsx("div",{className:"text-6xl mb-4",children:"🛒"}),e.jsx("h3",{className:"text-2xl font-bold mb-2",children:s==="en"?"Ready to Shop?":"准备购物？"}),e.jsx("p",{className:"mb-4 opacity-90",children:s==="en"?"Unfortunately, our shopping cart is as fictional as our peer review process!":"不幸的是，我们的购物车和我们的同行评议流程一样虚构！"}),e.jsxs("div",{className:"flex justify-center space-x-4 text-sm",children:[e.jsx("span",{className:"bg-white bg-opacity-20 px-3 py-1 rounded-full",children:s==="en"?"🎪 100% Satirical":"🎪 100%讽刺"}),e.jsx("span",{className:"bg-white bg-opacity-20 px-3 py-1 rounded-full",children:s==="en"?"🤡 Clown Approved":"🤡 小丑认证"}),e.jsx("span",{className:"bg-white bg-opacity-20 px-3 py-1 rounded-full",children:s==="en"?"📚 Academic Humor":"📚 学术幽默"})]}),e.jsx("p",{className:"text-xs mt-4 opacity-75",children:s==="en"?"* No actual products, money, or dignity were harmed in the making of this page.":"* 在制作此页面过程中，没有真正的产品、金钱或尊严受到伤害。"})]})]})}),g&&e.jsx(D,{isOpen:v,onClose:k,itemName:g.name,itemPrice:g.price,language:s})]})},V=Object.freeze(Object.defineProperty({__proto__:null,MerchandisePage:O},Symbol.toStringTag,{value:"Module"})),N=({icon:r,color:s,en:o,zh:m})=>e.jsxs("div",{className:"flex items-start space-x-3 p-3 bg-white bg-opacity-50 rounded-lg",children:[e.jsx("span",{className:`${s} text-lg font-bold mt-1`,children:r}),e.jsxs("div",{className:"flex-1",children:[e.jsx("p",{className:"text-gray-800 font-medium",children:o}),e.jsx("p",{className:"text-gray-600 text-sm mt-1",children:m})]})]}),B=({onNavigateHome:r})=>e.jsx("div",{className:"min-h-screen bg-white",children:e.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[e.jsxs("button",{onClick:r,className:"mb-8 inline-flex items-center text-blue-600 hover:text-blue-800 transition-colors",children:[e.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"返回首页"]}),e.jsxs("div",{className:"text-center mb-12",children:[e.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"🤫 Privacy Policy"}),e.jsx("p",{className:"text-xl text-gray-600",children:"We care about your privacy... just kidding"}),e.jsx("p",{className:"text-lg text-gray-500 mt-2",children:"我们关心你的隐私...开玩笑的"}),e.jsxs("div",{className:"mt-4 text-sm text-gray-500",children:["Last Updated: July 28, 2025 | Valid Until: We forget about it",e.jsx("br",{}),"最后更新：2025年7月28日 | 有效期：直到我们忘记为止"]})]}),e.jsxs("div",{className:"bg-white rounded-lg shadow-lg p-8 space-y-8",children:[e.jsxs("section",{className:"border-l-4 border-blue-500 pl-6",children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"🍪 Our Privacy Commitment / 我们的隐私承诺"}),e.jsx("p",{className:"text-lg text-gray-700 leading-relaxed",children:"We don't know who you are. We don't care. But our cookies do. 🍪"}),e.jsx("p",{className:"text-gray-600 mt-2",children:"我们不知道你是谁，我们也不在乎。但是我们的饼干很在乎。🍪"}),e.jsxs("p",{className:"text-gray-600 mt-3 italic",children:['"Privacy is like a unicorn - everyone talks about it, but nobody has actually seen it."',e.jsx("br",{}),'"隐私就像独角兽——每个人都在谈论它，但没人真正见过。"']})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"📊 What Data We Collect / 我们收集什么数据"}),e.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-6",children:[e.jsx("p",{className:"text-lg font-medium text-gray-800 mb-3",children:"We collect no data… unless you count emotional damage."}),e.jsx("p",{className:"text-gray-600 mb-2",children:"我们不收集任何数据...除非你把情感伤害也算进去。"}),e.jsxs("p",{className:"text-gray-600 mb-4 italic",children:[`"Data is the new oil, but we're more like a bicycle repair shop."`,e.jsx("br",{}),'"数据是新石油，但我们更像是自行车修理店。"']}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(N,{icon:"🎯",color:"text-red-500",en:"Your IP address is probably already on a whiteboard in our office",zh:"你的IP地址可能已经写在我们办公室的白板上了"}),e.jsx(N,{icon:"👆",color:"text-orange-500",en:"We log every button click and laugh about it during our midnight meetings",zh:"我们记录你点击的每一个按钮，然后在深夜会议上嘲笑"}),e.jsx(N,{icon:"🕵️",color:"text-green-500",en:"Your browser history? We don't look, but our servers are very gossipy",zh:"你的浏览器历史？我们不看，但我们的服务器很八卦"}),e.jsx(N,{icon:"🐹",color:"text-blue-500",en:"Mouse movements are tracked by our intern's pet hamster",zh:"鼠标移动由我们实习生的宠物仓鼠跟踪"}),e.jsx(N,{icon:"😂",color:"text-purple-500",en:"We collect your tears of laughter (for scientific purposes only)",zh:"我们收集你的笑出眼泪（仅用于科学目的）"})]})]})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"🍪 Cookie Usage Policy / Cookie使用政策"}),e.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-6",children:[e.jsx("p",{className:"text-lg font-medium text-gray-800 mb-3",children:"We use cookies, mostly because we're hungry."}),e.jsx("p",{className:"text-gray-600 mb-2",children:"我们使用Cookie，主要是因为我们饿了。"}),e.jsxs("p",{className:"text-gray-600 mb-4 italic",children:['"Cookies are like academic papers - nobody wants them, but everyone accepts them anyway."',e.jsx("br",{}),'"Cookie就像学术论文——没人想要，但每个人都会接受。"']}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[e.jsx("h3",{className:"font-bold text-gray-800 mb-2",children:"🍪 Essential Cookies / 必要Cookie"}),e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"To remember if you agreed to our nonsense."}),e.jsx("p",{className:"text-sm text-gray-500",children:"用于记住你是否同意我们的胡说八道。"})]}),e.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[e.jsx("h3",{className:"font-bold text-gray-800 mb-2",children:"🧁 Analytics Cookies / 分析Cookie"}),e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"To analyze why you visited this weird website."}),e.jsx("p",{className:"text-sm text-gray-500",children:"用于分析你为什么会访问这个奇怪的网站。"})]}),e.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[e.jsx("h3",{className:"font-bold text-gray-800 mb-2",children:"🎂 Marketing Cookies / 营销Cookie"}),e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"To send you more useless academic jokes."}),e.jsx("p",{className:"text-sm text-gray-500",children:"用于向你推送更多无用的学术笑话。"})]}),e.jsxs("div",{className:"bg-white p-4 rounded-lg",children:[e.jsx("h3",{className:"font-bold text-gray-800 mb-2",children:"🍩 Mystery Cookies / 神秘Cookie"}),e.jsx("p",{className:"text-sm text-gray-600 mb-1",children:"We have no idea what this one does either."}),e.jsx("p",{className:"text-sm text-gray-500",children:"我们也不知道这个是干什么的。"})]})]})]})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"🔒 我们如何处理你的信息"}),e.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[e.jsx("p",{className:"text-lg font-medium text-gray-800 mb-3",children:"If you send us personal info, we'll read it out loud and laugh."}),e.jsx("p",{className:"text-gray-600 mb-4",children:"如果你发送个人信息给我们，我们会大声读出来然后大笑。"}),e.jsxs("div",{className:"space-y-3 text-gray-700",children:[e.jsx("p",{children:"• 我们的数据处理团队由三只猫和一台1990年的计算器组成。"}),e.jsx("p",{children:'• 所有敏感信息都存储在一个标有"绝密"的鞋盒里。'}),e.jsx("p",{children:"• 我们使用军用级加密...的表情包来保护你的数据。"}),e.jsx("p",{children:"• 数据备份？我们把硬盘埋在办公室后院的树下。"})]})]})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"⚖️ 你的权利（理论上）"}),e.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-6",children:[e.jsx("p",{className:"text-lg font-medium text-gray-800 mb-3",children:'To request data deletion, scream "DELETE ME" into the void.'}),e.jsx("p",{className:"text-gray-600 mb-4",children:'要请求删除数据，请对着虚空大喊"删除我"。'}),e.jsxs("div",{className:"grid md:grid-cols-2 gap-4 mt-4",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold text-purple-800 mb-2",children:"访问权"}),e.jsx("p",{className:"text-sm text-gray-600",children:"你有权知道我们对你了解多少（剧透：不多）。"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold text-purple-800 mb-2",children:"更正权"}),e.jsx("p",{className:"text-sm text-gray-600",children:"你可以要求我们更正错误信息（但我们可能会故意搞错）。"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold text-purple-800 mb-2",children:"删除权"}),e.jsx("p",{className:"text-sm text-gray-600",children:"你可以要求删除数据，我们会考虑的...大概。"})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-bold text-purple-800 mb-2",children:"投诉权"}),e.jsx("p",{className:"text-sm text-gray-600",children:"你可以向我们的投诉部门投诉（就是办公室的垃圾桶）。"})]})]})]})]}),e.jsxs("section",{children:[e.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"📞 联系我们"}),e.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-6",children:[e.jsx("p",{className:"text-gray-700 mb-4",children:"如果你对我们的隐私政策有任何疑问："}),e.jsxs("ul",{className:"space-y-2 text-gray-600",children:[e.jsx("li",{children:"📧 邮箱：<EMAIL>（可能会被当作垃圾邮件）"}),e.jsx("li",{children:"📱 电话：1-800-NO-PRIVACY（永远占线）"}),e.jsx("li",{children:"📮 地址：虚拟世界第404号，找不到大街"}),e.jsx("li",{children:"🕊️ 信鸽：请训练你的信鸽找到我们的服务器机房"})]})]})]}),e.jsx("section",{className:"border-t pt-8",children:e.jsxs("div",{className:"bg-yellow-100 border border-yellow-300 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-bold text-yellow-800 mb-2",children:"⚠️ 重要声明"}),e.jsx("p",{className:"text-yellow-700 text-sm",children:"本隐私政策纯属娱乐，请勿当真。如果你真的相信了以上内容， 我们建议你重新评估一下自己的判断力。Joker期刊对因阅读本政策 而产生的任何心理创伤概不负责。😄"})]})})]})]})}),Q=Object.freeze(Object.defineProperty({__proto__:null,PrivacyPolicyPage:B},Symbol.toStringTag,{value:"Module"}));export{Y as A,K as F,V as M,Q as P,A as S,H as W,G as a,q as c,J as g,T as i};
