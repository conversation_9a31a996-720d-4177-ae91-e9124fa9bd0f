import React, { useEffect, useRef } from 'react';

// 声明 KaTeX 全局变量
declare global {
  interface Window {
    katex: any;
    renderMathInElement: any;
  }
}

interface MathRendererProps {
  content: string;
  className?: string;
}

/**
 * 数学公式渲染组件
 * 处理 LaTeX 公式并渲染为专业的数学表达式
 */
export const MathRenderer: React.FC<MathRendererProps> = ({ content, className = '' }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      // 设置内容
      containerRef.current.innerHTML = content;

      // 等待KaTeX加载完成后再渲染
      const renderMath = () => {
        if (window.renderMathInElement && containerRef.current) {
          console.log('🔢 开始KaTeX渲染');

          try {
            // 渲染数学公式
            window.renderMathInElement(containerRef.current, {
              delimiters: [
                { left: '$$', right: '$$', display: true },
                { left: '$', right: '$', display: false },
                { left: '\\[', right: '\\]', display: true },
                { left: '\\(', right: '\\)', display: false }
              ],
              throwOnError: false,
              errorColor: '#cc0000',
              strict: false,
              trust: false,
              macros: {
                // 添加一些常用的数学宏
                '\\RR': '\\mathbb{R}',
                '\\NN': '\\mathbb{N}',
                '\\ZZ': '\\mathbb{Z}',
                '\\QQ': '\\mathbb{Q}',
                '\\CC': '\\mathbb{C}',
                '\\text': '\\mathrm', // 确保\text命令可用
              }
            });

            console.log('✅ KaTeX渲染完成');
          } catch (error) {
            console.error('❌ KaTeX渲染失败:', error);
          }
        } else {
          // KaTeX还没加载，稍后重试
          setTimeout(renderMath, 100);
        }
      };

      renderMath();
    }
  }, [content]);

  return (
    <div 
      ref={containerRef} 
      className={`math-content ${className}`}
      style={{ lineHeight: '1.6' }}
    />
  );
};

/**
 * 处理公式，添加编号和样式
 */
function processEquations(content: string): string {
  let equationCounter = 0;
  
  // 处理显示公式（$$...$$）并添加编号
  const processedContent = content.replace(/\$\$(.*?)\$\$/gs, (match, equation) => {
    equationCounter++;
    return `
      <div class="equation-container">
        <div class="katex-display">
          $$${equation.trim()}$$
          <span class="equation-number">(${equationCounter})</span>
        </div>
      </div>
    `;
  });

  return processedContent;
}

/**
 * 用于文章内容的数学公式处理 Hook
 */
export const useMathProcessing = () => {
  const processMathContent = (htmlContent: string): string => {
    // 这个函数在 marked 处理后的 HTML 中查找和处理数学公式
    return htmlContent;
  };

  const renderMathInElement = (element: HTMLElement) => {
    if (window.renderMathInElement) {
      window.renderMathInElement(element, {
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '$', right: '$', display: false }
        ],
        throwOnError: false,
        errorColor: '#cc0000'
      });
    }
  };

  return { processMathContent, renderMathInElement };
};

/**
 * 公式引用组件
 */
interface EquationRefProps {
  number: number;
  className?: string;
}

export const EquationRef: React.FC<EquationRefProps> = ({ number, className = '' }) => (
  <span className={`equation-ref ${className}`}>
    Equation ({number})
  </span>
);

/**
 * 独立的数学公式组件
 */
interface MathEquationProps {
  equation: string;
  number?: number;
  label?: string;
  className?: string;
}

export const MathEquation: React.FC<MathEquationProps> = ({ 
  equation, 
  number, 
  label, 
  className = '' 
}) => {
  const equationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (equationRef.current && window.katex) {
      try {
        window.katex.render(equation, equationRef.current, {
          displayMode: true,
          throwOnError: false,
          errorColor: '#cc0000'
        });
      } catch (error) {
        console.error('KaTeX rendering error:', error);
        if (equationRef.current) {
          equationRef.current.innerHTML = `<span style="color: #cc0000;">Error rendering equation: ${equation}</span>`;
        }
      }
    }
  }, [equation]);

  return (
    <div className={`equation-container ${className}`}>
      <div className="katex-display">
        <div ref={equationRef} />
        {number && (
          <span className="equation-number">({number})</span>
        )}
      </div>
      {label && (
        <div className="text-center text-sm text-gray-600 mt-2">
          {label}
        </div>
      )}
    </div>
  );
};

/**
 * 行内数学公式组件
 */
interface InlineMathProps {
  equation: string;
  className?: string;
}

export const InlineMath: React.FC<InlineMathProps> = ({ equation, className = '' }) => {
  const mathRef = useRef<HTMLSpanElement>(null);

  useEffect(() => {
    if (mathRef.current && window.katex) {
      try {
        window.katex.render(equation, mathRef.current, {
          displayMode: false,
          throwOnError: false,
          errorColor: '#cc0000'
        });
      } catch (error) {
        console.error('KaTeX rendering error:', error);
        if (mathRef.current) {
          mathRef.current.innerHTML = `<span style="color: #cc0000;">Error: ${equation}</span>`;
        }
      }
    }
  }, [equation]);

  return <span ref={mathRef} className={`inline-math ${className}`} />;
};

/**
 * 数学公式预览组件（用于编辑时预览）
 */
interface MathPreviewProps {
  latex: string;
  isDisplay?: boolean;
}

export const MathPreview: React.FC<MathPreviewProps> = ({ latex, isDisplay = false }) => {
  if (isDisplay) {
    return <MathEquation equation={latex} />;
  } else {
    return <InlineMath equation={latex} />;
  }
};
