import React from 'react';

interface FooterProps {
    onNavigateHome: () => void;
    onNavigateToSubmit: () => void;
    onNavigateToForAuthors?: () => void;
    onNavigateToMerchandise?: () => void;
    onNavigateToPress?: () => void;
    onNavigateToContact?: () => void;
    onNavigateToPrivacy?: () => void;
    onNavigateToTerms?: () => void;
    onNavigateToAccessibility?: () => void;
}

export const Footer: React.FC<FooterProps> = ({
  onNavigateHome,
  onNavigateToSubmit,
  onNavigateToForAuthors,
  onNavigateToMerchandise,
  onNavigateToPress,
  onNavigateToContact,
  onNavigateToPrivacy,
  onNavigateToTerms,
  onNavigateToAccessibility
}) => {
  const NavButton = ({ onClick, children }: { onClick?: () => void; children: React.ReactNode }) => (
    <button onClick={onClick} className="text-base text-gray-300 hover:text-white text-left w-full disabled:opacity-50 disabled:hover:text-gray-300" disabled={!onClick}>
      {children}
    </button>
  );

  return (
    <footer className="bg-gray-800 text-white relative z-10" aria-labelledby="footer-heading">
      <h2 id="footer-heading" className="sr-only">
        Footer
      </h2>
      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
        <div className="pb-8 xl:grid xl:grid-cols-5 xl:gap-8">
          <div className="grid grid-cols-2 gap-8 xl:col-span-4 md:grid-cols-4">
            <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Jocker</h3>
              <ul className="mt-4 space-y-4">
                <li><NavButton onClick={onNavigateToSubmit}>About</NavButton></li>
                <li><NavButton onClick={onNavigateToPress}>Press</NavButton></li>
                <li><NavButton onClick={onNavigateToContact}>Contact</NavButton></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Research</h3>
              <ul className="mt-4 space-y-4">
                <li><NavButton onClick={onNavigateHome}>Archive</NavButton></li>
                <li><NavButton onClick={onNavigateHome}>Topics</NavButton></li>
                <li><NavButton onClick={onNavigateToForAuthors || onNavigateToSubmit}>For Authors</NavButton></li>
                <li><NavButton onClick={onNavigateToMerchandise}>🛍️ Merchandise</NavButton></li>
              </ul>
            </div>
            <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Libraries</h3>
              <ul className="mt-4 space-y-4">
                <li><NavButton>Librarians</NavButton></li>
                <li><NavButton>Societies</NavButton></li>
                <li><NavButton>Partnerships</NavButton></li>
              </ul>
            </div>
             <div>
              <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Legal</h3>
              <ul className="mt-4 space-y-4">
                <li><NavButton onClick={onNavigateToPrivacy}>Privacy</NavButton></li>
                <li><NavButton onClick={onNavigateToTerms}>Terms</NavButton></li>
                <li><NavButton onClick={onNavigateToAccessibility}>Accessibility</NavButton></li>
              </ul>
            </div>
          </div>
          <div className="mt-12 xl:mt-0 xl:col-span-1">
             <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Subscribe to our newsletter</h3>
             <p className="mt-4 text-gray-300 text-base">The most absurd science, direct to your inbox.</p>
             <form className="mt-4 sm:flex sm:max-w-md">
                <label htmlFor="email-address" className="sr-only">Email address</label>
                <input type="email" name="email-address" id="email-address" autoComplete="email" required className="appearance-none min-w-0 w-full bg-white border border-transparent rounded-md py-2 px-4 text-base text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white focus:border-white focus:placeholder-gray-400" placeholder="Enter your email" />
                <div className="mt-3 rounded-md sm:mt-0 sm:ml-3 sm:flex-shrink-0">
                    <button type="submit" className="w-full bg-purple-700 flex items-center justify-center border border-transparent rounded-md py-2 px-4 text-base font-medium text-white hover:bg-purple-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-purple-500">
                    Subscribe
                    </button>
                </div>
             </form>
          </div>
        </div>
        <div className="mt-8 border-t border-gray-700 pt-8 md:flex md:items-center md:justify-between">
          <p className="mt-8 text-base text-gray-400 md:mt-0 md:order-1">
            &copy; 2024 Jocker Publishing Group. All rights reserved (mostly).
          </p>
        </div>
      </div>
    </footer>
  );
};