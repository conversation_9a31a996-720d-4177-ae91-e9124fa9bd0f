-- AlterTable
ALTER TABLE "ai_configs" ADD COLUMN "imageApiKey" TEXT;
ALTER TABLE "ai_configs" ADD COLUMN "imageBaseUrl" TEXT;
ALTER TABLE "ai_configs" ADD COLUMN "imageProvider" TEXT;
ALTER TABLE "ai_configs" ADD COLUMN "textApiKey" TEXT;
ALTER TABLE "ai_configs" ADD COLUMN "textBaseUrl" TEXT;
ALTER TABLE "ai_configs" ADD COLUMN "textProvider" TEXT;

-- CreateTable
CREATE TABLE "merchandise_images" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "itemId" TEXT NOT NULL,
    "imageUrl" TEXT NOT NULL,
    "title" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateIndex
CREATE UNIQUE INDEX "merchandise_images_itemId_key" ON "merchandise_images"("itemId");

-- CreateIndex
CREATE INDEX "articles_published_idx" ON "articles"("published");

-- CreateIndex
CREATE INDEX "articles_featured_idx" ON "articles"("featured");

-- CreateIndex
CREATE INDEX "articles_createdAt_idx" ON "articles"("createdAt");

-- CreateIndex
CREATE INDEX "articles_published_createdAt_idx" ON "articles"("published", "createdAt");

-- CreateIndex
CREATE INDEX "articles_published_featured_idx" ON "articles"("published", "featured");

-- CreateIndex
CREATE INDEX "articles_views_idx" ON "articles"("views");
