{"version": "v2.5.0", "type": "minor", "title": "网站封面系统与全页面背景设计", "description": "实现网站封面上传管理功能，支持全页面背景显示，优化视觉体验和管理界面", "changes": ["🖼️ 网站封面上传：管理员可在后台上传网站主页封面图片，支持base64格式存储", "🌟 全页面背景设计：封面图片作为整个主页背景，使用透明度和模糊效果优化内容可读性", "🎨 智能布局适配：竖图自动使用object-fit: cover居中裁剪，确保不同尺寸图片的美观显示", "💡 用户体验优化：为竖图提供优化建议提示，指导用户将重点内容放在图片中央区域", "🛡️ 透明度遮罩：添加白色半透明遮罩层(85%透明度)，确保文字内容清晰可读", "✨ 毛玻璃效果：内容区域使用backdrop-blur-sm毛玻璃效果，提升视觉层次感", "🎯 英雄区域设计：顶部期刊标题区域使用大字体和阴影效果，突出品牌形象", "📱 响应式背景：背景图片在不同设备上自适应显示，保持视觉一致性", "🔧 管理界面扩展：在管理后台添加'网站管理'标签页，集中管理网站外观设置", "🚀 API接口完善：新增网站封面上传和获取接口，支持标题和描述信息管理", "🎪 兼容性保障：无封面时自动回退到原有简洁设计，确保功能完整性", "🔄 实时预览：上传界面提供图片预览功能，支持标题和描述自定义输入"]}