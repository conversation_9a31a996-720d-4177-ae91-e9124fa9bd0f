// Prisma 数据库模式定义
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 用户模型
model User {
  id        Int       @id @default(autoincrement())
  email     String    @unique
  username  String    @unique
  password  String
  name      String?
  avatar    String?
  role      String    @default("USER")
  status    String    @default("ACTIVE") // "ACTIVE", "DISABLED", "SUSPENDED"
  lastLogin DateTime?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // 关联的文章
  articles Article[]

  // 观看记录
  articleViews ArticleView[]

  // 上传的音频
  uploadedAudios ArticleAudio[]

  // 创建的系统日志
  systemLogs SystemLog[]

  // 评论
  comments ArticleComment[]

  @@map("users")
}

// SQLite 不支持枚举，使用字符串代替
// 用户角色: "USER", "ADMIN", "EDITOR"

// 文章模型
model Article {
  id          Int      @id @default(autoincrement())
  title       String
  author      String
  category    String
  excerpt     String
  content     String?
  imageUrl    String
  imagePrompt String?
  doi         String? // DOI标识符
  published   Boolean  @default(false)
  featured    <PERSON><PERSON><PERSON>  @default(false)
  views       Int      @default(0)
  likes       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联的用户（创建者）
  userId Int?
  user   User? @relation(fields: [userId], references: [id])

  // 标签关联
  tags ArticleTag[]

  // 图片关联
  figures Figure[]

  // 观看记录
  viewRecords ArticleView[]

  // 音频文件
  audio ArticleAudio?

  // 评论
  comments ArticleComment[]

  @@map("articles")
}

// 标签模型
model Tag {
  id        Int      @id @default(autoincrement())
  name      String   @unique
  color     String?
  createdAt DateTime @default(now())

  // 文章关联
  articles ArticleTag[]

  @@map("tags")
}

// 文章标签关联表
model ArticleTag {
  articleId Int
  tagId     Int
  article   Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  tag       Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([articleId, tagId])
  @@map("article_tags")
}

// 图片模型
model Figure {
  id           String   @id @default(cuid())
  articleId    Int
  figureNumber Int // 图片编号 (1, 2, 3...)
  title        String // 图片标题
  description  String? // 图片描述
  caption      String // 图注
  imagePrompt  String // 生成图片的 AI 提示词
  imageUrl     String? // 图片 URL
  thumbnailUrl String? // 缩略图 URL
  width        Int? // 图片宽度
  height       Int? // 图片高度
  fileSize     Int? // 文件大小（字节）
  mimeType     String? // 文件类型
  status       String   @default("pending") // "pending" | "generating" | "completed" | "failed"
  errorMsg     String? // 错误信息
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // 关联的文章
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@unique([articleId, figureNumber])
  @@map("figures")
}

// AI 生成记录模型
model AIGenerationLog {
  id        Int      @id @default(autoincrement())
  type      String // "article" | "image" | "content" | "figure"
  prompt    String
  response  String?
  success   Boolean  @default(false)
  errorMsg  String?
  duration  Int? // 生成耗时（毫秒）
  relatedId String? // 关联的资源 ID（如 figure ID）
  createdAt DateTime @default(now())

  @@map("ai_generation_logs")
}

// 文章评论模型（恶搞同行评审）
model ArticleComment {
  id          String   @id @default(cuid())
  articleId   Int // 文章ID
  authorName  String // 评论者署名（可以是AI生成的假名）
  authorTitle String? // 评论者头衔，如"Dr.", "Prof.", "AI-PhD"
  title       String // 评论标题
  content     String // 评论内容
  isAI        Boolean  @default(false) // 是否为AI生成的评论
  isApproved  Boolean  @default(false) // 是否通过"审核"
  userId      Int? // 用户ID（如果是真实用户评论）
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  user    User?   @relation(fields: [userId], references: [id])

  @@map("article_comments")
}

// 创始人模型
model Founder {
  id          Int      @id @default(autoincrement())
  name        String // 创始人姓名
  title       String // 职位/头衔
  description String? // 描述
  avatarUrl   String? // 头像 URL（base64 或图片链接）
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("founders")
}

// 文章观看记录模型
model ArticleView {
  id          Int      @id @default(autoincrement())
  articleId   Int // 文章ID
  userId      Int? // 用户ID（如果已登录）
  ipAddress   String // IP地址
  userAgent   String? // 用户代理
  fingerprint String? // 浏览器指纹（前端生成）
  createdAt   DateTime @default(now())

  // 关联的文章
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  // 关联的用户（可选）
  user    User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  // 确保同一用户/IP对同一文章只能有一条记录
  @@unique([articleId, userId])
  @@unique([articleId, ipAddress, fingerprint])
  @@map("article_views")
}

// 文章音频模型
model ArticleAudio {
  id          String   @id @default(cuid())
  articleId   Int      @unique // 一篇文章只能有一个音频
  title       String // 音频标题
  description String? // 音频描述
  fileName    String // 原始文件名
  fileSize    Int // 文件大小（字节）
  duration    Int? // 音频时长（秒）
  mimeType    String // 文件类型 (audio/mpeg, audio/wav, etc.)
  audioUrl    String // 音频文件URL或路径
  uploadedBy  Int // 上传者ID
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联的文章
  article  Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  // 上传者
  uploader User    @relation(fields: [uploadedBy], references: [id])

  @@map("article_audios")
}

// 广告封面模型
model AdvertisementCover {
  id          String   @id @default(cuid())
  imageUrl    String // Base64编码的图片数据
  title       String // 广告标题
  description String? // 广告描述
  linkUrl     String? // 点击链接
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("advertisement_covers")
}

// AI API配置模型（支持多个供应商）
model AIConfig {
  id         String   @id @default(cuid())
  provider   String   @default("gemini") // 供应商: "gemini" | "openai"
  apiKey     String // 用户提供的API Key
  baseUrl    String? // OpenAI兼容API的基础URL（仅OpenAI格式需要）
  isActive   Boolean  @default(true) // 是否启用用户提供的配置
  textModel  String   @default("gemini-2.0-flash-exp") // 文本生成模型
  imageModel String   @default("imagen-3.0-generate-001") // 图片生成模型
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("ai_configs")
}

// 系统日志模型
model SystemLog {
  id          String   @id @default(cuid())
  version     String // 版本号，如 "v2.2.0"
  date        DateTime @default(now()) // 自动获取当前时间
  type        String // 类型：major, minor, patch, hotfix
  title       String? // 可选的标题
  description String? // 可选的描述
  changes     String // JSON格式的变更列表
  createdBy   Int? // 创建者ID（可选）
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // 关联创建者
  creator User? @relation(fields: [createdBy], references: [id])

  @@map("system_logs")
}

// 期刊图标模型
model JournalIcon {
  id        String   @id @default(cuid())
  icon      String // emoji或图片URL
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("journal_icons")
}
