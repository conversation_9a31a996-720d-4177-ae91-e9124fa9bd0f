import { Request, Response } from 'express';
export declare const getAdminArticleList: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getAIApiKey: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const generateAndSaveArticleContent: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const saveArticleContent: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getArticleFiguresList: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const regenerateFigureImage: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const saveFigure: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const clearArticleFigures: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const uploadFounderAvatar: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getFounderAvatar: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const uploadWebsiteCover: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getWebsiteCover: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getAllUsers: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateUserStatus: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const updateUserRole: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteUser: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const uploadAdvertisementCover: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getAdvertisementCover: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const setAIConfig: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getAIConfig: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const recordMerchandiseRequest: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getMerchandiseRequestStats: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getAIConfigForClient: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const changePassword: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const setJournalIcon: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getJournalIcon: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const setGeminiKey: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getGeminiKey: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const uploadMerchandiseImage: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const getMerchandiseImages: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=adminController.d.ts.map