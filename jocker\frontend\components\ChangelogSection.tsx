import React, { useState } from 'react';
import { SharedBackground } from './SharedBackground';

interface ChangelogEntry {
  version: string;
  date: string;
  changes: string[];
  type: 'major' | 'minor' | 'patch';
}

/**
 * 更新日志页面组件
 * 显示系统的版本更新历史
 */
export const ChangelogPage: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  // 更新日志数据
  const changelog: ChangelogEntry[] = [
    {
      version: "v2.2.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "🎵 播客功能：支持为文章添加音频播客，提供完整的多媒体阅读体验",
        "🎧 音频播放器：专业的音频播放控件，支持进度控制、音量调节、时长显示",
        "📤 音频上传：管理员可上传最大50MB的音频文件，支持拖拽上传",
        "🎛️ 播放控制：播放/暂停、进度跳转、音量控制等完整功能",
        "📊 音频管理：支持音频信息编辑、删除等管理功能",
        "🔒 权限控制：只有管理员和编辑可以上传/删除音频文件",
        "📱 响应式设计：音频播放器适配各种屏幕尺寸"
      ]
    },
    {
      version: "v2.1.3",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🤖 AI模型升级：全面切换到Gemini 2.5 Flash模型，提升生成速度和质量",
        "⚡ 性能优化：新模型响应更快，生成内容更加稳定",
        "🔧 统一配置：前后端AI调用统一使用最新的模型版本",
        "📝 改进生成：文章内容和图片生成都使用优化后的模型"
      ]
    },
    {
      version: "v2.1.2",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "👤 用户去重统计：同一用户只会被统计一次观看次数，避免重复刷新增加浏览量",
        "🔍 智能识别：基于用户ID（已登录）+ IP地址 + 浏览器指纹进行用户识别",
        "💾 本地缓存：使用localStorage记录已访问文章，减少不必要的API调用",
        "📊 真实统计：新增观看记录表，提供更准确的用户行为分析",
        "🎯 精确去重：确保统计数据的真实性和准确性"
      ]
    },
    {
      version: "v2.1.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🔧 修复观看次数统计：解决前端未调用后端API导致观看次数不更新的问题",
        "📊 优化统计逻辑：移除重复的观看次数增加逻辑，确保统计准确性",
        "⚡ 实时更新：进入文章页面时自动获取最新的观看次数并显示",
        "🎯 精确计算：每次访问文章详情页都会正确增加观看次数"
      ]
    },
    {
      version: "v2.1.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "📊 新增观看次数统计：文章详情页自动统计观看次数，实时显示浏览量",
        "🗂️ 批量删除功能：管理员可多选文章进行批量删除，提升管理效率",
        "✏️ 观看次数编辑：管理员可手动修改文章观看次数，支持数据调整",
        "☑️ 全选功能：支持一键全选/取消全选所有文章，便于批量操作",
        "🎯 智能选择：选中文章数量实时显示，操作状态清晰可见",
        "🔒 权限控制：批量删除和观看次数编辑仅限管理员和编辑使用",
        "📈 数据展示：文章列表和详情页都显示观看次数，数据透明化"
      ]
    },
    {
      version: "v2.0.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🔧 修复UI重复问题：移除重复的'文章内容尚未生成'提示，优化用户体验",
        "📝 修复日志显示：解决更新日志中重复列表符号的显示问题",
        "🎯 优化内容生成：改进prompt避免重复生成标题和摘要，从正文部分开始延续",
        "✨ 智能内容衔接：生成详细内容时会基于已有的标题、作者、摘要信息继续写作",
        "🎨 界面清理：移除冗余的提示信息，让界面更加简洁明了"
      ]
    },
    {
      version: "v2.0.0",
      date: "2025-01-11",
      type: "major",
      changes: [
        "🃏 重大更名：期刊名称从'Jocker'正式更名为'Joker'，修正了所有前端显示",
        "🎭 双模式系统：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）",
        "🎓 学术生产力：严肃模式生成符合顶级期刊标准的专业文章，成为真正的学术工具",
        "🎪 娱乐保留：小丑模式继续提供幽默、戏谑的学术文章",
        "⚙️ 智能切换：根据模式自动使用不同的AI提示词模板",
        "🔬 严谨标准：严肃模式确保科学方法论、统计分析、引用格式的正确性",
        "🎨 全新界面：生成弹窗提供直观的模式选择体验",
        "📚 完整更新：DOI、引用格式、PDF文件名等全面更新为新期刊名称",
        "🔧 技术优化：修复了多个渲染和显示问题，提升系统稳定性"
      ]
    },
    {
      version: "v1.8.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "🎭 新增生成模式选择：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）",
        "🎓 严肃模式功能：生成符合顶级期刊发表标准的专业学术文章，可作为真正的学术生产力工具",
        "🎪 小丑模式保留：继续支持原有的戏谑、幽默学术文章生成",
        "⚙️ 智能提示词切换：根据选择的模式自动使用不同的AI提示词模板",
        "🔬 严谨学术标准：严肃模式确保科学方法论正确、统计分析合理、引用格式标准",
        "🎨 直观模式选择：生成和重新生成弹窗中提供清晰的模式选择界面"
      ]
    },
    {
      version: "v1.7.6",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🔧 修复图片生成问题：限制AI提示词长度在400字符以内，避免413错误",
        "⚡ 优化错误处理：图片生成失败时保存提示词并跳过到下一张，避免进度卡死",
        "🎨 修复后台布局：解决文章标题过长时编辑删除按钮被挤出容器的问题",
        "📱 改进响应式设计：后台文章管理界面按钮现在垂直排列，确保始终可点击",
        "🖼️ 修复失败图片显示：失败的图片现在会在Figures画廊中显示，可以点击重新生成",
        "💡 改进用户提示：文章中的失败占位符会提示用户到Figures画廊重新生成",
        "📝 修复列表渲染问题：解决有序列表和无序列表在Markdown渲染时丢失的问题",
        "🔧 优化Markdown解析：改进marked配置和列表格式处理，确保列表正确显示",
        "🔄 修复旧文章图片：自动为旧文章中的图片占位符创建数据库记录，支持重新生成",
        "🚫 移除自动格式修复：移除所有加粗标记的自动修复逻辑，让用户完全控制Markdown格式",
        "✏️ 改进编辑体验：编辑模式下的格式不会被系统自动修改，避免意外的格式变化",
        "🔢 修复文献引用冲突：文献引用处理现在会避开LaTeX数学块，防止[1,2]等范围表示被误处理",
        "🔧 修复渲染问题：移除有问题的marked自定义renderer，解决[object Object]显示问题",
        "📝 简化列表处理：使用CSS样式强制显示列表数字，避免复杂的JavaScript处理",
        "🛠️ 增强稳定性：图片生成过程更加健壮，能够处理各种网络和API异常"
      ]
    },
    {
      version: "v1.7.5",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "✏️ 新增文章编辑功能：管理员和编辑可以直接编辑文章的Markdown内容",
        "🎯 权限分级管理：管理员可生成和重新生成文章，编辑只能编辑现有内容",
        "📝 实时编辑界面：提供专业的Markdown编辑器，支持语法提示和格式指导",
        "🔄 自动重新渲染：编辑保存后自动重新渲染文章内容，包括图片和引用",
        "🛡️ 角色权限控制：基于用户角色显示不同的操作按钮和功能",
        "💾 即时保存功能：编辑内容可即时保存到数据库并更新显示"
      ]
    },
    {
      version: "v1.7.4",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "📚 文献引用上标化：文献引用现在显示为学术期刊标准的上标格式，保留中括号",
        "🔗 引用点击跳转：点击文献引用可平滑滚动到对应参考文献并高亮显示",
        "📄 优化PDF导出：移除PDF中多余的'Peer Reviewed'和分类标签行",
        "🔧 修复加粗渲染：改进紧贴数字和Figure标题的加粗标记处理（如**1**、** Figure 1:**格式）",
        "✨ 引用格式支持：支持[1], [1,2], [1-3], [1,3-5], [9, 10], [9-10]等多种引用格式",
        "🎨 视觉优化：引用点击后临时高亮效果，提升用户体验",
        "📋 修复PDF问题：确保上标和参考文献序号在PDF导出中正确显示"
      ]
    },
    {
      version: "v1.7.3",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "🎯 优化文章生成流程：分离基本信息生成和详细内容生成",
        "📝 首页生成文章：现在只需输入主题，生成标题、作者、摘要和封面",
        "⚙️ 详情页生成正文：在文章详情页可自定义字数、图片数量和参考文献数量",
        "🎨 新增生成参数弹窗：用户可在生成正文时设置文章长度、图片数量和参考文献数量",
        "📚 新增参考文献数量控制：支持自定义1-10篇参考文献，默认3-5篇",
        "🔄 改进重新生成功能：支持自定义参数重新生成文章内容",
        "🖼️ 修复图片重新生成：加载失败的图片现在也有重新生成按钮",
        "🔧 修复图片生成失败问题：即使生成失败也会创建数据库记录，确保可以重新生成",
        "📍 改进占位符显示：失败的图片占位符会显示失败状态并提供重新生成按钮",
        "📄 优化PDF导出：文件名现在使用DOI格式（如：10.1000_jocker.2025.01.11.123.article-title）",
        "📊 新增生成进度显示：实时显示文章生成进度（准备→生成文字→生成图片1、2、3...→保存）",
        "🎭 进度弹窗界面：美观的进度条和详细状态信息，让用户了解生成进展",
        "💡 优化用户体验：明确区分文章基本信息和详细内容的生成时机"
      ]
    },
    {
      version: "v1.7.2",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🔧 统一后台管理界面：移除弹窗版本，保留页面版本",
        "👥 完善用户管理功能：在 AdminPage 中集成完整的用户管理",
        "🐛 修复普通用户登录后状态不立即更新的问题",
        "🎯 优化后台按钮：点击直接跳转到管理页面而非弹窗",
        "✨ 提升管理员操作体验：统一的界面风格和操作流程"
      ]
    },
    {
      version: "v1.7.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🐛 修复普通用户登录后显示未登录状态的问题",
        "👤 优化用户界面：区分普通用户和管理员功能",
        "🎨 改进导航栏：普通用户只显示登出按钮，管理员显示完整功能",
        "📝 增强用户体验：显示当前登录用户名和角色标识",
        "🔧 放宽用户名验证规则：支持中文和特殊字符"
      ]
    },
    {
      version: "v1.7.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "👥 新增管理员账户管理功能：查看所有用户、修改权限、禁用账户",
        "🔧 新增后台管理界面：统一的管理员操作面板",
        "🚪 导航栏新增后台按钮：管理员登录后可快速进入后台",
        "🔒 增强用户状态管理：支持账户禁用、暂停等状态",
        "📊 用户详情展示：显示注册时间、最后登录、文章数量等信息",
        "⚡ 实时权限控制：防止管理员误操作自己的账户"
      ]
    },
    {
      version: "v1.6.2",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🐛 修复 PDF 导出中期刊标题重复显示的问题",
        "👤 新增创始人头像功能：About 页面显示创始人信息",
        "🔧 管理员可上传和编辑创始人头像及信息",
        "💾 创始人信息存储到数据库，支持动态更新",
        "🎨 优化 About 页面布局和创始人展示区域"
      ]
    },
    {
      version: "v1.6.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "🖼️ 优化管理员图片重新生成功能：更明显的操作按钮和确认提示",
        "📄 优化期刊标题显示：调整为页眉样式，避免与文章标题混淆",
        "🎨 改进图片管理界面：添加生成状态指示器和管理员提示",
        "📋 优化 PDF 输出中的期刊标题布局和字体大小",
        "✨ 提升管理员操作体验和界面专业性"
      ]
    },
    {
      version: "v1.6.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "🎯 增强管理员文章生成功能：添加文字数量和图片数量自定义输入",
        "🤖 优化 AI 生成 prompt，更好地围绕用户输入主题构建文章",
        "👥 新增用户注册功能，区分普通用户和管理员权限",
        "🔐 完善用户认证系统，支持普通用户浏览和管理员管理",
        "📝 改进登录页面 UI，支持注册/登录模式切换",
        "✨ 提升用户体验和系统功能完整性"
      ]
    },
    {
      version: "v1.5.2",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "修复 PDF 生成中的页脚内容问题",
        "移除 PDF 中不应出现的 Cite This Article 和 About Jocker 等内容",
        "优化 PDF 标题层级，确保文章标题为最大标题",
        "改进 PDF 布局和字体大小设置",
        "提升 PDF 输出的专业性和可读性"
      ]
    },
    {
      version: "v1.5.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "优化导航栏布局，修复按钮被搜索框遮挡的问题",
        "重新设计导航栏为左对齐布局，提升可用性",
        "添加独立的 Submit 和 About 导航按钮",
        "创建专业的 About 页面，介绍 Jocker 期刊使命",
        "改进导航结构和用户体验"
      ]
    },
    {
      version: "v1.5.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "实现学术期刊风格的分层浏览系统",
        "添加文章列表页面，支持分页和排序功能",
        "优化导航结构：首页保持卡片视图，分类页面使用列表视图",
        "添加智能分页组件，支持大量文章的高效浏览",
        "改进用户体验，符合学术期刊网站的标准做法"
      ]
    },
    {
      version: "v1.4.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "优化 Figures 侧边栏显示为 2x2 网格布局",
        "添加紧凑模式支持，提升空间利用率",
        "新增参考文献自动解析和显示功能",
        "添加更新日志栏目"
      ]
    },
    {
      version: "v1.3.0",
      date: "2025-01-10",
      type: "minor",
      changes: [
        "实现 PDF 下载功能，支持学术论文格式导出",
        "优化 PDF 中的图片和公式渲染",
        "改进文章指标在 PDF 中的水平显示",
        "修复图片在 PDF 中的集成显示问题"
      ]
    },
    {
      version: "v1.2.0",
      date: "2025-01-09",
      type: "minor",
      changes: [
        "添加 LaTeX 公式支持，实现专业期刊样式渲染",
        "优化图片生成工作流程",
        "实现标准化图片占位符格式",
        "添加图片预览网格视图功能"
      ]
    },
    {
      version: "v1.1.0",
      date: "2025-01-08",
      type: "minor",
      changes: [
        "集成 Gemini Imagen API 进行图片生成",
        "实现文章内容一次性生成并存储",
        "添加管理员专用 API 密钥安全机制",
        "优化学术期刊样式 UI 组件"
      ]
    },
    {
      version: "v1.0.0",
      date: "2025-01-07",
      type: "major",
      changes: [
        "初始版本发布",
        "实现基础文章生成和管理功能",
        "添加用户认证和管理员界面",
        "建立前后端分离架构",
        "部署到生产服务器"
      ]
    }
  ];

  const getVersionBadgeColor = (type: string) => {
    switch (type) {
      case 'major':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'minor':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'patch':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const displayedChangelog = isExpanded ? changelog : changelog.slice(0, 5);

  return (
    <SharedBackground className="min-h-screen">
      {/* 页面头部 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">更新日志</h1>
              <p className="mt-2 text-gray-600">Jocker 学术期刊系统的版本更新历史</p>
            </div>
            <div className="flex items-center space-x-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                当前版本: {changelog[0].version}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 更新日志内容 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        <div className="space-y-6">
          {displayedChangelog.map((entry, index) => (
            <div key={entry.version} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <span className={`inline-flex items-center px-3 py-1 rounded-md text-sm font-medium border ${getVersionBadgeColor(entry.type)}`}>
                    {entry.version}
                  </span>
                  <span className="text-sm text-gray-500">{entry.date}</span>
                </div>
                <div className="text-xs text-gray-400 uppercase tracking-wide">
                  {entry.type === 'major' ? '重大更新' : entry.type === 'minor' ? '功能更新' : '修复更新'}
                </div>
              </div>

              <ul className="space-y-2">
                {entry.changes.map((change, changeIndex) => (
                  <li key={changeIndex} className="text-gray-700 flex items-start">
                    <span className="text-blue-500 mr-3 mt-1">•</span>
                    <span>{change}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {changelog.length > 5 && (
          <div className="mt-8 text-center">
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              {isExpanded ? '收起历史版本' : `查看更多版本 (${changelog.length - 5} 个)`}
            </button>
          </div>
        )}

        {/* 版本说明 */}
        <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">版本说明</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <span className="inline-block w-3 h-3 bg-red-500 rounded-full"></span>
              <span className="text-sm text-gray-700"><strong>Major:</strong> 重大功能更新或架构变更</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-block w-3 h-3 bg-blue-500 rounded-full"></span>
              <span className="text-sm text-gray-700"><strong>Minor:</strong> 新功能添加或功能改进</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-block w-3 h-3 bg-green-500 rounded-full"></span>
              <span className="text-sm text-gray-700"><strong>Patch:</strong> 错误修复或小幅优化</span>
            </div>
          </div>
        </div>
      </div>
    </SharedBackground>
  );
};

/**
 * 侧边栏版本的更新日志组件（简化版）
 */
export const ChangelogSidebar: React.FC = () => {
  // 更新日志数据（与主页面共享）
  const changelog: ChangelogEntry[] = [
    {
      version: "v1.7.2",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "统一后台管理界面",
        "完善用户管理功能",
        "修复登录状态更新问题",
        "优化后台操作体验",
        "提升界面一致性"
      ]
    },
    {
      version: "v1.7.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "修复普通用户登录状态显示问题",
        "优化用户界面和权限区分",
        "改进导航栏用户体验",
        "增强用户名显示和角色标识",
        "放宽用户名验证规则"
      ]
    },
    {
      version: "v1.7.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "新增管理员账户管理功能",
        "新增后台管理界面",
        "导航栏新增后台按钮",
        "增强用户状态管理",
        "用户详情和权限控制"
      ]
    },
    {
      version: "v1.6.2",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "修复 PDF 导出重复标题问题",
        "新增创始人头像功能",
        "管理员可编辑创始人信息",
        "优化 About 页面展示"
      ]
    },
    {
      version: "v1.6.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "优化管理员图片重新生成功能",
        "改进期刊标题显示为页眉样式",
        "增强图片管理界面和操作体验",
        "优化 PDF 输出布局"
      ]
    },
    {
      version: "v1.6.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "增强管理员文章生成功能",
        "新增用户注册功能",
        "优化 AI 生成 prompt",
        "完善用户认证系统"
      ]
    },
    {
      version: "v1.5.2",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "修复 PDF 生成中的页脚内容问题",
        "优化 PDF 标题层级和布局",
        "提升 PDF 输出的专业性"
      ]
    },
    {
      version: "v1.5.1",
      date: "2025-01-11",
      type: "patch",
      changes: [
        "优化导航栏布局，修复按钮遮挡问题",
        "添加独立的 Submit 和 About 导航按钮",
        "创建专业的 About 页面"
      ]
    },
    {
      version: "v1.5.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "实现学术期刊风格的分层浏览系统",
        "添加文章列表页面，支持分页和排序功能",
        "优化导航结构和用户体验"
      ]
    },
    {
      version: "v1.4.0",
      date: "2025-01-11",
      type: "minor",
      changes: [
        "优化 Figures 侧边栏显示为 2x2 网格布局",
        "添加紧凑模式支持，提升空间利用率",
        "新增参考文献自动解析和显示功能",
        "添加更新日志栏目"
      ]
    },
    {
      version: "v1.3.0",
      date: "2025-01-10",
      type: "minor",
      changes: [
        "实现 PDF 下载功能，支持学术论文格式导出",
        "优化 PDF 中的图片和公式渲染"
      ]
    },
    {
      version: "v1.2.0",
      date: "2025-01-09",
      type: "minor",
      changes: [
        "添加 LaTeX 公式支持，实现专业期刊样式渲染",
        "优化图片生成工作流程"
      ]
    }
  ];

  const getVersionBadgeColor = (type: string) => {
    switch (type) {
      case 'major':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'minor':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'patch':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold text-gray-900">最近更新</h3>
        <span className="text-xs text-gray-500">
          {changelog[0].version}
        </span>
      </div>

      <div className="space-y-3">
        {changelog.slice(0, 3).map((entry) => (
          <div key={entry.version} className="border-l-2 border-gray-200 pl-3">
            <div className="flex items-center space-x-2 mb-1">
              <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getVersionBadgeColor(entry.type)}`}>
                {entry.version}
              </span>
              <span className="text-xs text-gray-500">{entry.date}</span>
            </div>
            <ul className="space-y-1 list-none">
              {entry.changes.slice(0, 2).map((change, index) => (
                <li key={index} className="text-xs text-gray-600 flex items-start">
                  <span className="text-gray-400 mr-1 mt-0.5">•</span>
                  <span className="line-clamp-2">{change}</span>
                </li>
              ))}
              {entry.changes.length > 2 && (
                <li className="text-xs text-gray-400 italic">
                  +{entry.changes.length - 2} 更多...
                </li>
              )}
            </ul>
          </div>
        ))}
      </div>

      <div className="mt-3 pt-3 border-t border-gray-200">
        <a
          href="/changelog"
          className="text-xs text-blue-600 hover:text-blue-800 font-medium"
        >
          查看完整更新日志 →
        </a>
      </div>
    </div>
  );
};
