import prisma from '../config/database';
import { Article, CreateArticleRequest, UpdateArticleRequest, SearchQuery, PaginatedResponse } from '../types';

// 生成唯一DOI的函数
const generateUniqueDOI = async (title: string): Promise<string> => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');

  // 清理标题，移除特殊字符，保留字母数字和空格
  const cleanTitle = title
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .toLowerCase()
    .substring(0, 30); // 限制长度

  // 获取当天已有的文章数量，用作序号
  const startOfDay = new Date(year, now.getMonth(), now.getDate());
  const endOfDay = new Date(year, now.getMonth(), now.getDate() + 1);

  const todayArticleCount = await prisma.article.count({
    where: {
      createdAt: {
        gte: startOfDay,
        lt: endOfDay
      }
    }
  });

  const sequence = String(todayArticleCount + 1).padStart(4, '0');

  return `10.1000/jocker.${year}.${month}.${day}.${sequence}.${cleanTitle}`;
};

/**
 * 获取所有文章（分页）
 */
export const getArticles = async (query: SearchQuery): Promise<PaginatedResponse<Article>> => {
  const {
    page = 1,
    limit = 10,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    q,
    category,
    author,
    published,
    featured,
  } = query;

  const skip = (page - 1) * limit;

  // 构建查询条件
  const where: any = {};

  if (q) {
    where.OR = [
      { title: { contains: q, mode: 'insensitive' } },
      { excerpt: { contains: q, mode: 'insensitive' } },
      { author: { contains: q, mode: 'insensitive' } },
      { category: { contains: q, mode: 'insensitive' } },
    ];
  }

  if (category) {
    where.category = { contains: category, mode: 'insensitive' };
  }

  if (author) {
    where.author = { contains: author, mode: 'insensitive' };
  }

  if (published !== undefined) {
    where.published = published;
  }

  if (featured !== undefined) {
    where.featured = featured;
  }

  // 获取总数
  const total = await prisma.article.count({ where });

  // 获取文章列表（优化：只选择列表页面需要的字段，不包含content）
  const articles = await prisma.article.findMany({
    where,
    skip,
    take: limit,
    orderBy: {
      [sortBy]: sortOrder,
    },
    select: {
      id: true,
      title: true,
      author: true,
      category: true,
      excerpt: true,
      imageUrl: true,
      imagePrompt: true,
      doi: true,
      views: true,
      likes: true,
      createdAt: true,
      updatedAt: true,
      published: true,
      featured: true,
      // 不包含 content 字段以提高性能
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  });

  const totalPages = Math.ceil(total / limit);

  return {
    data: articles as any,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
};

/**
 * 根据 ID 获取单篇文章
 */
export const getArticleById = async (id: number): Promise<Article> => {
  const article = await prisma.article.findUnique({
    where: { id },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  });

  if (!article) {
    throw new Error('文章不存在');
  }

  return article as any;
};

/**
 * 创建文章
 */
export const createArticle = async (
  articleData: CreateArticleRequest,
  userId?: number
): Promise<Article> => {
  // 生成唯一DOI
  const doi = await generateUniqueDOI(articleData.title);

  const data: any = {
    ...articleData,
    doi, // 添加DOI
    published: articleData.published ?? false,
    featured: articleData.featured ?? false,
  };

  if (userId) {
    data.userId = userId;
  }

  const article = await prisma.article.create({
    data,
    include: {
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  });

  console.log(`✅ 新文章创建成功，DOI: ${doi}`);
  return article as any;
};

/**
 * 更新文章
 */
export const updateArticle = async (
  id: number,
  articleData: UpdateArticleRequest,
  userId?: number
): Promise<Article> => {
  // 检查文章是否存在
  const existingArticle = await prisma.article.findUnique({
    where: { id },
  });

  if (!existingArticle) {
    throw new Error('文章不存在');
  }

  // 个人使用，跳过权限检查

  const article = await prisma.article.update({
    where: { id },
    data: articleData,
    include: {
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  });

  return article as any;
};

/**
 * 删除文章
 */
export const deleteArticle = async (id: number, userId?: number): Promise<void> => {
  // 检查文章是否存在
  const existingArticle = await prisma.article.findUnique({
    where: { id },
  });

  if (!existingArticle) {
    throw new Error('文章不存在');
  }

  // 个人使用，跳过权限检查

  await prisma.article.delete({
    where: { id },
  });
};

/**
 * 获取热门文章
 */
export const getTrendingArticles = async (limit: number = 5): Promise<Article[]> => {
  return prisma.article.findMany({
    where: { published: true },
    orderBy: [
      { views: 'desc' },
      { likes: 'desc' },
      { createdAt: 'desc' },
    ],
    take: limit,
    select: {
      id: true,
      title: true,
      author: true,
      category: true,
      excerpt: true,
      imageUrl: true,
      imagePrompt: true,
      doi: true,
      views: true,
      likes: true,
      createdAt: true,
      updatedAt: true,
      published: true,
      featured: true,
      // 不包含 content 字段以提高性能
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  }) as any;
};

/**
 * 获取推荐文章
 */
export const getFeaturedArticles = async (limit: number = 3): Promise<Article[]> => {
  return prisma.article.findMany({
    where: {
      published: true,
      featured: true,
    },
    orderBy: { createdAt: 'desc' },
    take: limit,
    select: {
      id: true,
      title: true,
      author: true,
      category: true,
      excerpt: true,
      imageUrl: true,
      imagePrompt: true,
      doi: true,
      views: true,
      likes: true,
      createdAt: true,
      updatedAt: true,
      published: true,
      featured: true,
      // 不包含 content 字段以提高性能
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  }) as any;
};

/**
 * 获取所有文章（管理员专用，包含content字段）
 */
export const getArticlesForAdmin = async (query: SearchQuery): Promise<PaginatedResponse<Article>> => {
  const {
    page = 1,
    limit = 50, // 管理员默认更大的limit
    sortBy = 'createdAt',
    sortOrder = 'desc',
    q,
    category,
    author,
    published,
    featured,
  } = query;

  const skip = (page - 1) * limit;

  // 构建查询条件
  const where: any = {};

  if (q) {
    where.OR = [
      { title: { contains: q, mode: 'insensitive' } },
      { excerpt: { contains: q, mode: 'insensitive' } },
      { author: { contains: q, mode: 'insensitive' } },
      { category: { contains: q, mode: 'insensitive' } },
    ];
  }

  if (category) {
    where.category = { contains: category, mode: 'insensitive' };
  }

  if (author) {
    where.author = { contains: author, mode: 'insensitive' };
  }

  if (published !== undefined) {
    where.published = published;
  }

  if (featured !== undefined) {
    where.featured = featured;
  }

  // 获取总数
  const total = await prisma.article.count({ where });

  // 获取文章列表（管理员版本：包含content字段）
  const articles = await prisma.article.findMany({
    where,
    skip,
    take: limit,
    orderBy: {
      [sortBy]: sortOrder,
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  });

  const totalPages = Math.ceil(total / limit);

  return {
    data: articles as any,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  };
};

/**
 * 点赞文章
 */
export const likeArticle = async (id: number): Promise<Article> => {
  const article = await prisma.article.update({
    where: { id },
    data: { likes: { increment: 1 } },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          name: true,
          avatar: true,
        },
      },
    },
  });

  return article as any;
};
