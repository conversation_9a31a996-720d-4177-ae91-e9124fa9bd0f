import React, { useState, useEffect } from 'react';
import { adminApi } from '../src/services/api';

interface JournalIconManagerProps {
  onUpdateSuccess?: (message: string) => void;
  onUpdateError?: (error: string) => void;
}

export const JournalIconManager: React.FC<JournalIconManagerProps> = ({
  onUpdateSuccess,
  onUpdateError
}) => {
  const [currentIcon, setCurrentIcon] = useState<string>('🤡');
  const [newIcon, setNewIcon] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // 预设的期刊图标选项
  const presetIcons = [
    '🤡', '🎭', '🎪', '🎨', '🔬', '📚', '📖', '📝', 
    '🧪', '🔍', '💡', '🎯', '🚀', '⚡', '🌟', '💎',
    '🎲', '🎳', '🎮', '🎸', '🎺', '🎻', '🎤', '🎧'
  ];

  // 加载当前图标
  const loadCurrentIcon = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/journal-icon');
      const data = await response.json();
      if (data.success) {
        setCurrentIcon(data.data.icon);
        setNewIcon(data.data.icon);
      }
    } catch (error) {
      console.error('加载期刊图标失败:', error);
      onUpdateError?.('加载期刊图标失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 保存图标
  const saveIcon = async () => {
    if (!newIcon.trim()) {
      onUpdateError?.('请输入或选择一个图标');
      return;
    }

    setIsSaving(true);
    try {
      await adminApi.setJournalIcon(newIcon.trim());
      setCurrentIcon(newIcon.trim());
      onUpdateSuccess?.('期刊图标更新成功！');
    } catch (error) {
      console.error('保存期刊图标失败:', error);
      onUpdateError?.('保存期刊图标失败');
    } finally {
      setIsSaving(false);
    }
  };

  // 重置为当前图标
  const resetIcon = () => {
    setNewIcon(currentIcon);
  };

  useEffect(() => {
    loadCurrentIcon();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">期刊图标管理</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">当前图标:</span>
          <span className="text-2xl">{currentIcon}</span>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* 自定义图标输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              自定义图标 (Emoji 或 图片URL)
            </label>
            <div className="flex space-x-2">
              <input
                type="text"
                value={newIcon}
                onChange={(e) => setNewIcon(e.target.value)}
                placeholder="输入 emoji 或图片URL..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="flex items-center justify-center w-12 h-10 border border-gray-300 rounded-md bg-gray-50">
                {newIcon && (
                  newIcon.startsWith('http') ? (
                    <img src={newIcon} alt="图标预览" className="w-8 h-8 object-contain" />
                  ) : (
                    <span className="text-xl">{newIcon}</span>
                  )
                )}
              </div>
            </div>
          </div>

          {/* 预设图标选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              或选择预设图标
            </label>
            <div className="grid grid-cols-8 gap-2">
              {presetIcons.map((icon, index) => (
                <button
                  key={index}
                  onClick={() => setNewIcon(icon)}
                  className={`w-12 h-12 flex items-center justify-center text-xl border-2 rounded-lg transition-colors ${
                    newIcon === icon
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  {icon}
                </button>
              ))}
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={resetIcon}
              disabled={isSaving}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              重置
            </button>
            <button
              onClick={saveIcon}
              disabled={isSaving || newIcon === currentIcon}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSaving && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
              <span>{isSaving ? '保存中...' : '保存图标'}</span>
            </button>
          </div>

          {/* 使用说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 可以使用 Emoji 作为期刊图标，如 🤡 🎭 🔬 等</li>
              <li>• 也可以使用图片URL，建议使用正方形图片，尺寸不超过 64x64px</li>
              <li>• 图标会显示在网站标题、PDF导出等位置</li>
              <li>• 建议选择与期刊主题相关的图标</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};
