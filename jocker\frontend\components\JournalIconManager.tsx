import React, { useState, useEffect } from 'react';
import { adminApi } from '../src/services/api';

interface JournalIconManagerProps {
  onUpdateSuccess?: (message: string) => void;
  onUpdateError?: (error: string) => void;
}

export const JournalIconManager: React.FC<JournalIconManagerProps> = ({
  onUpdateSuccess,
  onUpdateError
}) => {
  const [currentIcon, setCurrentIcon] = useState<string>('🤡');
  const [newIcon, setNewIcon] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [iconType, setIconType] = useState<'emoji' | 'image'>('emoji');

  // 预设的期刊图标选项
  const presetIcons = [
    '🤡', '🎭', '🎪', '🎨', '🔬', '📚', '📖', '📝', 
    '🧪', '🔍', '💡', '🎯', '🚀', '⚡', '🌟', '💎',
    '🎲', '🎳', '🎮', '🎸', '🎺', '🎻', '🎤', '🎧'
  ];

  // 加载当前图标
  const loadCurrentIcon = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/admin/journal-icon');
      const data = await response.json();
      if (data.success) {
        const icon = data.data.icon;
        setCurrentIcon(icon);
        setNewIcon(icon);
        setIconType(icon.startsWith('data:image/') ? 'image' : 'emoji');
      }
    } catch (error) {
      console.error('加载期刊图标失败:', error);
      onUpdateError?.('加载期刊图标失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 保存图标
  const saveIcon = async () => {
    if (!newIcon.trim()) {
      onUpdateError?.('请输入或选择一个图标');
      return;
    }

    setIsSaving(true);
    try {
      await adminApi.setJournalIcon(newIcon.trim());
      setCurrentIcon(newIcon.trim());
      onUpdateSuccess?.('期刊图标更新成功！');
    } catch (error) {
      console.error('保存期刊图标失败:', error);
      onUpdateError?.('保存期刊图标失败');
    } finally {
      setIsSaving(false);
    }
  };

  // 重置为当前图标
  const resetIcon = () => {
    setNewIcon(currentIcon);
    setIconType(currentIcon.startsWith('data:image/') ? 'image' : 'emoji');
  };

  // 处理图片上传
  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      onUpdateError?.('请选择图片文件');
      return;
    }

    // 验证文件大小 (限制为2MB)
    if (file.size > 2 * 1024 * 1024) {
      onUpdateError?.('图片文件大小不能超过2MB');
      return;
    }

    setIsUploading(true);
    try {
      // 将图片转换为base64
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setNewIcon(result);
        setIconType('image');
        setIsUploading(false);
      };
      reader.onerror = () => {
        onUpdateError?.('图片读取失败');
        setIsUploading(false);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('图片上传失败:', error);
      onUpdateError?.('图片上传失败');
      setIsUploading(false);
    }
  };

  useEffect(() => {
    loadCurrentIcon();
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">期刊图标管理</h3>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">当前图标:</span>
          <span className="text-2xl">{currentIcon}</span>
        </div>
      </div>

      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {/* 图标类型选择 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              选择图标类型
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  value="emoji"
                  checked={iconType === 'emoji'}
                  onChange={(e) => setIconType(e.target.value as 'emoji' | 'image')}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">Emoji 表情</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  value="image"
                  checked={iconType === 'image'}
                  onChange={(e) => setIconType(e.target.value as 'emoji' | 'image')}
                  className="mr-2"
                />
                <span className="text-sm text-gray-700">上传图片</span>
              </label>
            </div>
          </div>

          {iconType === 'emoji' ? (
            <>
              {/* 自定义Emoji输入 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  自定义 Emoji
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={newIcon.startsWith('data:image/') ? '' : newIcon}
                    onChange={(e) => setNewIcon(e.target.value)}
                    placeholder="输入 emoji..."
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <div className="flex items-center justify-center w-12 h-10 border border-gray-300 rounded-md bg-gray-50">
                    {newIcon && !newIcon.startsWith('data:image/') && (
                      <span className="text-xl">{newIcon}</span>
                    )}
                  </div>
                </div>
              </div>

              {/* 预设图标选择 */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  或选择预设 Emoji
                </label>
                <div className="grid grid-cols-8 gap-2">
                  {presetIcons.map((icon, index) => (
                    <button
                      key={index}
                      onClick={() => setNewIcon(icon)}
                      className={`w-12 h-12 flex items-center justify-center text-xl border-2 rounded-lg transition-colors ${
                        newIcon === icon
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>
            </>
          ) : (
            /* 图片上传 */
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                上传图标图片
              </label>
              <div className="flex items-center space-x-4">
                <div className="flex-1">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleImageUpload}
                    disabled={isUploading}
                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50"
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    支持 JPG、PNG、GIF 格式，建议尺寸 64x64px，文件大小不超过 2MB
                  </p>
                </div>
                <div className="flex items-center justify-center w-16 h-16 border border-gray-300 rounded-md bg-gray-50">
                  {isUploading ? (
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  ) : newIcon.startsWith('data:image/') ? (
                    <img src={newIcon} alt="图标预览" className="w-12 h-12 object-contain rounded" />
                  ) : (
                    <span className="text-xs text-gray-400">预览</span>
                  )}
                </div>
              </div>
            </div>
          )}



          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-4">
            <button
              onClick={resetIcon}
              disabled={isSaving}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              重置
            </button>
            <button
              onClick={saveIcon}
              disabled={isSaving || newIcon === currentIcon}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isSaving && (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              )}
              <span>{isSaving ? '保存中...' : '保存图标'}</span>
            </button>
          </div>

          {/* 使用说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Emoji 模式</strong>：可以使用任何 Emoji 表情作为期刊图标，如 🤡 🎭 🔬 等</li>
              <li>• <strong>图片模式</strong>：上传自定义图片作为期刊图标，建议使用正方形图片，尺寸 64x64px</li>
              <li>• 图标会显示在网站标题、PDF导出、浏览器标签页等位置</li>
              <li>• 建议选择与期刊主题相关的图标，保持专业性</li>
              <li>• 图片文件会转换为 base64 格式存储，无需外部链接</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};
