export interface ImageGenerationConfig {
    aspectRatio?: '1:1' | '16:9' | '9:16';
    quality?: 'standard' | 'hd';
}
export declare const DEFAULT_CONFIG: ImageGenerationConfig;
export declare const updateFigureStatus: (figureId: string, status: "PENDING" | "GENERATING" | "COMPLETED" | "FAILED", imageUrl?: string, errorMessage?: string) => Promise<void>;
export declare const generateImageWithGemini: (prompt: string, config?: ImageGenerationConfig) => Promise<{
    imageUrl: string;
    revisedPrompt?: string;
}>;
export declare const generateImagesForArticle: (articleId: number, prompts: string[]) => Promise<string[]>;
//# sourceMappingURL=imageGenerationService.d.ts.map