import prisma from '../config/database';

/**
 * 图片生成配置接口
 */
export interface ImageGenerationConfig {
  aspectRatio?: '1:1' | '16:9' | '9:16';
  quality?: 'standard' | 'hd';
}

/**
 * 默认图片生成配置
 */
export const DEFAULT_CONFIG: ImageGenerationConfig = {
  aspectRatio: '1:1',
  quality: 'standard',
};

/**
 * 更新图片状态
 */
export const updateFigureStatus = async (
  figureId: string,
  status: 'PENDING' | 'GENERATING' | 'COMPLETED' | 'FAILED',
  imageUrl?: string,
  errorMessage?: string
): Promise<void> => {
  try {
    await prisma.figure.update({
      where: { id: figureId },
      data: {
        status,
        imageUrl: imageUrl || undefined,
        errorMsg: errorMessage || undefined,
        updatedAt: new Date(),
      },
    });
  } catch (error) {
    console.error(`更新图片状态失败 (ID: ${figureId}):`, error);
  }
};

/**
 * 使用 Gemini Imagen 生成图片 - 已移除，现在由前端处理
 */
export const generateImageWithGemini = async (
  prompt: string,
  config: ImageGenerationConfig = DEFAULT_CONFIG
): Promise<{ imageUrl: string; revisedPrompt?: string }> => {
  throw new Error('图片生成功能已迁移到前端，请使用前端的图片生成功能');
};

/**
 * 批量生成图片 - 已移除，现在由前端处理
 */
export const generateImagesForArticle = async (
  articleId: number,
  prompts: string[]
): Promise<string[]> => {
  throw new Error('图片生成功能已迁移到前端，请使用前端的图片生成功能');
};
