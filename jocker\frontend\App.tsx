import React, { useState, useEffect } from 'react';
import { Header } from './components/Header';
import { Footer } from './components/Footer';
import { LoadingSkeleton } from './components/LoadingSkeleton';
import { HomePage } from './components/HomePage';
import { JournalArticlePage } from './components/JournalArticlePage';
import { SearchPage } from './components/SearchPage';
import { SubmitPage } from './components/SubmitPage';
import { AdminPage } from './components/AdminPage';

import { LoginPage } from './components/LoginPage';
import { DynamicChangelogSection } from './components/DynamicChangelogSection';
import { ArticleListPage } from './components/ArticleListPage';
import { AboutPage } from './components/AboutPage';
import { PrivacyPolicyPage } from './components/PrivacyPolicyPage';
import { TermsOfServicePage } from './components/TermsOfServicePage';
import { AccessibilityPage } from './components/AccessibilityPage';
import { Article } from './types';
import { articleApi, aiApi } from './src/services/api';
import { WebsiteCoverProvider } from './src/contexts/WebsiteCoverContext';
import { isUserLoggedIn, isUserAdmin, clearUserAuth } from './src/utils/authUtils';

// 使用后端 API 的 Article 类型

const App: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Routing state
  const [view, setView] = useState<'home' | 'article' | 'search' | 'submit' | 'about' | 'admin' | 'admin-dashboard' | 'login' | 'changelog' | 'research-articles' | 'news-comment' | 'privacy' | 'terms' | 'accessibility'>('home');

  // Authentication state - 使用统一的权限检查
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(() => {
    const initialLoggedIn = isUserLoggedIn();
    console.log('🚀 App.tsx 初始化 isLoggedIn:', initialLoggedIn);
    return initialLoggedIn;
  });
  const [isAdmin, setIsAdmin] = useState<boolean>(() => {
    const initialIsAdmin = isUserAdmin();
    console.log('🚀 App.tsx 初始化 isAdmin:', initialIsAdmin);
    return initialIsAdmin;
  });
  const [selectedArticleId, setSelectedArticleId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Initial content generation
  useEffect(() => {
    const fetchInitialContent = async () => {
      if (articles.length > 0) {
        setIsLoading(false);
        return;
      }
      setIsLoading(true);
      setError(null);
      try {
        // 从后端获取现有文章
        const existingArticles = await articleApi.getArticles({
          published: true,
          limit: 20
        });

        setArticles(existingArticles.data);

      } catch (e) {
        console.error("Failed to fetch content:", e);
        setError("无法获取文章列表，请稍后重试。");
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialContent();
  }, [articles.length]);

  // 定期检查权限状态
  useEffect(() => {
    const checkAuthStatus = () => {
      const newIsLoggedIn = isUserLoggedIn();
      const newIsAdmin = isUserAdmin();

      console.log('🔄 App.tsx 权限状态检查:', {
        currentIsLoggedIn: isLoggedIn,
        newIsLoggedIn,
        currentIsAdmin: isAdmin,
        newIsAdmin,
        willUpdateLogin: newIsLoggedIn !== isLoggedIn,
        willUpdateAdmin: newIsAdmin !== isAdmin
      });

      if (newIsLoggedIn !== isLoggedIn) {
        console.log('🔄 更新 isLoggedIn:', newIsLoggedIn);
        setIsLoggedIn(newIsLoggedIn);
      }
      if (newIsAdmin !== isAdmin) {
        console.log('🔄 更新 isAdmin:', newIsAdmin);
        setIsAdmin(newIsAdmin);
      }
    };

    // 立即检查一次
    checkAuthStatus();

    // 每秒检查一次权限状态
    const interval = setInterval(checkAuthStatus, 1000);

    // 监听localStorage变化
    window.addEventListener('storage', checkAuthStatus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('storage', checkAuthStatus);
    };
  }, []); // 移除依赖数组，避免无限循环

  const handleUpdateArticleContent = (id: number, content: string) => {
    setArticles((prevArticles: Article[]) =>
        prevArticles.map((a: Article) => a.id === id ? { ...a, content } : a)
    );
  };

  const handleArticleCreated = async () => {
    try {
      // 重新获取文章列表以包含新创建的文章
      const updatedArticles = await articleApi.getArticles({
        published: true,
        limit: 20
      });
      setArticles(updatedArticles.data);
    } catch (error) {
      console.error('刷新文章列表失败:', error);
    }
  };
  
  // Navigation Handlers
  const handleNavigateToHome = () => {
    setView('home');
    setSelectedArticleId(null);
    setSearchQuery('');
    window.scrollTo(0, 0);
  }

  const handleNavigateToArticle = (id: number) => {
    setSelectedArticleId(id);
    setView('article');
    window.scrollTo(0, 0);
  }

  const handleNavigateToSearch = (query: string) => {
    setSearchQuery(query);
    setView('search');
    window.scrollTo(0, 0);
  }
  
  const handleNavigateToSubmit = () => {
    setView('submit');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAdmin = () => {
    if (isAdmin) {
      setView('admin');
      window.scrollTo(0, 0);
    } else {
      setView('login');
      window.scrollTo(0, 0);
    }
  };

  const handleNavigateToLogin = () => {
    setView('login');
    window.scrollTo(0, 0);
  };

  const handleNavigateToChangelog = () => {
    setView('changelog');
    window.scrollTo(0, 0);
  };

  const handleNavigateToResearchArticles = () => {
    setView('research-articles');
    window.scrollTo(0, 0);
  };

  const handleNavigateToNewsComment = () => {
    setView('news-comment');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAbout = () => {
    setView('about');
    window.scrollTo(0, 0);
  };

  const handleNavigateToPrivacy = () => {
    setView('privacy');
    window.scrollTo(0, 0);
  };

  const handleNavigateToTerms = () => {
    setView('terms');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAccessibility = () => {
    setView('accessibility');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAdminDashboard = () => {
    if (isAdmin) {
      setView('admin');
      window.scrollTo(0, 0);
    } else {
      setView('login');
      window.scrollTo(0, 0);
    }
  };

  const handleLoginSuccess = () => {
    // 立即更新权限状态
    setIsLoggedIn(isUserLoggedIn());
    setIsAdmin(isUserAdmin());

    const userStr = localStorage.getItem('jocker_admin_user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user.role === 'ADMIN') {
          setView('admin');
        } else {
          setView('home');
        }
      } catch (error) {
        console.error('解析用户信息失败:', error);
        setView('home');
      }
    }
    window.scrollTo(0, 0);
  };

  const handleLogout = () => {
    // 使用统一的清除认证函数
    clearUserAuth();
    // 立即更新状态
    setIsLoggedIn(false);
    setIsAdmin(false);
    setView('home');
    window.scrollTo(0, 0);
  };

  const renderContent = () => {
    if (isLoading) {
      return <LoadingSkeleton />;
    }

    if (error) {
      return (
        <div className="text-center py-20 px-4">
            <h2 className="text-2xl font-semibold text-red-600">An Error Occurred</h2>
            <p className="mt-4 text-gray-600">{error}</p>
        </div>
      );
    }
    
    if(articles.length === 0 && !isLoading) {
       return (
        <div className="text-center py-20 px-4">
            <h2 className="text-2xl font-semibold text-gray-800">暂无文章</h2>
            <p className="mt-4 text-gray-600">还没有发布的文章。点击上方的"🤖 生成文章"按钮来创建第一篇文章吧！</p>
        </div>
       );
    }

    switch(view) {
        case 'article':
            const selectedArticle = articles.find(a => a.id === selectedArticleId);
            return selectedArticle ? <JournalArticlePage article={selectedArticle} updateArticleContent={handleUpdateArticleContent} onNavigateHome={handleNavigateToHome} /> : <div className="text-center py-20">Article not found.</div>;
        case 'search':
            return <SearchPage allArticles={articles} query={searchQuery} onNavigateToArticle={handleNavigateToArticle} onNavigateHome={handleNavigateToHome} />;
        case 'submit':
            return <SubmitPage />;
        case 'login':
            return <LoginPage onNavigateHome={handleNavigateToHome} onLoginSuccess={handleLoginSuccess} />;
        case 'admin':
            return isAdmin ? <AdminPage onNavigateHome={handleNavigateToHome} /> : <LoginPage onNavigateHome={handleNavigateToHome} onLoginSuccess={handleLoginSuccess} />;
        case 'changelog':
            return <DynamicChangelogSection />;
        case 'research-articles':
            return (
                <ArticleListPage
                    articles={articles}
                    title="Research Articles"
                    description="Peer-reviewed satirical research papers and academic studies"
                    onNavigateToArticle={handleNavigateToArticle}
                    onNavigateHome={handleNavigateToHome}
                />
            );
        case 'news-comment':
            return (
                <ArticleListPage
                    articles={articles}
                    title="News & Comment"
                    description="Editorial commentary and academic news in satirical science"
                    category="News & Comment"
                    onNavigateToArticle={handleNavigateToArticle}
                    onNavigateHome={handleNavigateToHome}
                />
            );
        case 'about':
            return <AboutPage onNavigateHome={handleNavigateToHome} />;
        case 'privacy':
            return <PrivacyPolicyPage onNavigateHome={handleNavigateToHome} />;
        case 'terms':
            return <TermsOfServicePage onNavigateHome={handleNavigateToHome} />;
        case 'accessibility':
            return <AccessibilityPage onNavigateHome={handleNavigateToHome} />;
        case 'home':
        default:
            return <HomePage articles={articles} onNavigateToArticle={handleNavigateToArticle} />;
    }
  };

  return (
    <WebsiteCoverProvider>
    <div className="bg-white min-h-screen flex flex-col">
      <Header
        onNavigateHome={handleNavigateToHome}
        onNavigateToSubmit={handleNavigateToSubmit}
        onSearch={handleNavigateToSearch}
        onArticleCreated={handleArticleCreated}
        onNavigateToLogin={handleNavigateToLogin}
        onNavigateToChangelog={handleNavigateToChangelog}
        onNavigateToResearchArticles={handleNavigateToResearchArticles}
        onNavigateToNewsComment={handleNavigateToNewsComment}
        onNavigateToAbout={handleNavigateToAbout}
        isLoggedIn={isLoggedIn}
        isAdmin={isAdmin}
        onLogout={handleLogout}
        onNavigateToAdmin={handleNavigateToAdminDashboard}
      />
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 w-full flex-grow" role="main">
        {renderContent()}
      </main>
      <Footer
        onNavigateHome={handleNavigateToHome}
        onNavigateToSubmit={handleNavigateToSubmit}
        onNavigateToPrivacy={handleNavigateToPrivacy}
        onNavigateToTerms={handleNavigateToTerms}
        onNavigateToAccessibility={handleNavigateToAccessibility}
      />
    </div>
    </WebsiteCoverProvider>
  );
};

export default App;