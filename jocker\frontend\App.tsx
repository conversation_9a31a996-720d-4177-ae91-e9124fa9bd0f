import React, { useState, useEffect, Suspense, lazy } from 'react';
import { Header } from './components/Header';
import { Footer } from './components/Footer';
import { LoadingSkeleton } from './components/LoadingSkeleton';
import { HomePage } from './components/HomePage';
import { JournalArticlePage } from './components/JournalArticlePage';
import { SearchPage } from './components/SearchPage';
import { SubmitPage } from './components/SubmitPage';

// 懒加载非关键组件
const AdminPage = lazy(() => import('./components/AdminPage').then(module => ({ default: module.AdminPage })));
const LoginPage = lazy(() => import('./components/LoginPage').then(module => ({ default: module.LoginPage })));
const DynamicChangelogSection = lazy(() => import('./components/DynamicChangelogSection').then(module => ({ default: module.DynamicChangelogSection })));
const ArticleListPage = lazy(() => import('./components/ArticleListPage').then(module => ({ default: module.ArticleListPage })));
const AboutPage = lazy(() => import('./components/AboutPage').then(module => ({ default: module.AboutPage })));
const ForAuthorsPage = lazy(() => import('./components/ForAuthorsPage').then(module => ({ default: module.ForAuthorsPage })));
const MerchandisePage = lazy(() => import('./components/MerchandisePage').then(module => ({ default: module.MerchandisePage })));
const PrivacyPolicyPage = lazy(() => import('./components/PrivacyPolicyPage').then(module => ({ default: module.PrivacyPolicyPage })));
const TermsOfServicePage = lazy(() => import('./components/TermsOfServicePage').then(module => ({ default: module.TermsOfServicePage })));
const AccessibilityPage = lazy(() => import('./components/AccessibilityPage').then(module => ({ default: module.AccessibilityPage })));
const PressPage = lazy(() => import('./components/PressPage').then(module => ({ default: module.PressPage })));
const ContactPage = lazy(() => import('./components/ContactPage').then(module => ({ default: module.ContactPage })));
import { Article } from './types';
import { articleApi, aiApi } from './src/services/api';
import { WebsiteCoverProvider } from './src/contexts/WebsiteCoverContext';
import { isUserLoggedIn, isUserAdmin, clearUserAuth } from './src/utils/authUtils';

// 使用后端 API 的 Article 类型

const App: React.FC = () => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [articlesCache, setArticlesCache] = useState<{ data: Article[], timestamp: number } | null>(null);
  
  // Routing state
  const [view, setView] = useState<'home' | 'article' | 'search' | 'submit' | 'about' | 'for-authors' | 'merchandise' | 'admin' | 'admin-dashboard' | 'login' | 'changelog' | 'research-articles' | 'news-comment' | 'privacy' | 'terms' | 'accessibility' | 'press' | 'contact'>('home');

  // Authentication state - 使用统一的权限检查
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(() => {
    const initialLoggedIn = isUserLoggedIn();
    console.log('🚀 App.tsx 初始化 isLoggedIn:', initialLoggedIn);
    return initialLoggedIn;
  });
  const [isAdmin, setIsAdmin] = useState<boolean>(() => {
    const initialIsAdmin = isUserAdmin();
    console.log('🚀 App.tsx 初始化 isAdmin:', initialIsAdmin);
    return initialIsAdmin;
  });
  const [selectedArticleId, setSelectedArticleId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Initial content generation
  useEffect(() => {
    const fetchInitialContent = async () => {
      // 检查缓存（5分钟有效期）
      const now = Date.now();
      const cacheValidTime = 5 * 60 * 1000; // 5分钟

      if (articlesCache && (now - articlesCache.timestamp) < cacheValidTime) {
        console.log('📦 使用缓存的文章数据');
        setArticles(articlesCache.data);
        setIsLoading(false);
        return;
      }

      if (articles.length > 0 && !articlesCache) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      try {
        console.log('🌐 从服务器获取文章数据');
        // 从后端获取现有文章
        const existingArticles = await articleApi.getArticles({
          published: true,
          limit: 20
        });

        setArticles(existingArticles.data);
        // 更新缓存
        setArticlesCache({
          data: existingArticles.data,
          timestamp: now
        });

      } catch (e) {
        console.error("Failed to fetch content:", e);
        setError("无法获取文章列表，请稍后重试。");
      } finally {
        setIsLoading(false);
      }
    };

    fetchInitialContent();
  }, [articles.length]);

  // 检查权限状态
  useEffect(() => {
    const checkAuthStatus = () => {
      const newIsLoggedIn = isUserLoggedIn();
      const newIsAdmin = isUserAdmin();

      if (newIsLoggedIn !== isLoggedIn) {
        setIsLoggedIn(newIsLoggedIn);
      }
      if (newIsAdmin !== isAdmin) {
        setIsAdmin(newIsAdmin);
      }
    };

    // 立即检查一次
    checkAuthStatus();

    // 监听localStorage变化
    window.addEventListener('storage', checkAuthStatus);

    return () => {
      window.removeEventListener('storage', checkAuthStatus);
    };
  }, [isLoggedIn, isAdmin]);

  const handleUpdateArticleContent = (id: number, content: string) => {
    setArticles((prevArticles: Article[]) =>
        prevArticles.map((a: Article) => a.id === id ? { ...a, content } : a)
    );
  };

  const handleArticleCreated = async () => {
    try {
      // 重新获取文章列表以包含新创建的文章
      const updatedArticles = await articleApi.getArticles({
        published: true,
        limit: 20
      });
      setArticles(updatedArticles.data);
    } catch (error) {
      console.error('刷新文章列表失败:', error);
    }
  };
  
  // Navigation Handlers
  const handleNavigateToHome = () => {
    setView('home');
    setSelectedArticleId(null);
    setSearchQuery('');
    window.scrollTo(0, 0);
  }

  const handleNavigateToArticle = (id: number) => {
    setSelectedArticleId(id);
    setView('article');
    window.scrollTo(0, 0);
  }

  const handleNavigateToSearch = (query: string) => {
    setSearchQuery(query);
    setView('search');
    window.scrollTo(0, 0);
  }
  
  const handleNavigateToSubmit = () => {
    setView('submit');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAdmin = () => {
    if (isAdmin) {
      setView('admin');
      window.scrollTo(0, 0);
    } else {
      setView('login');
      window.scrollTo(0, 0);
    }
  };

  const handleNavigateToLogin = () => {
    setView('login');
    window.scrollTo(0, 0);
  };

  const handleNavigateToChangelog = () => {
    setView('changelog');
    window.scrollTo(0, 0);
  };

  const handleNavigateToResearchArticles = () => {
    setView('research-articles');
    window.scrollTo(0, 0);
  };

  const handleNavigateToNewsComment = () => {
    setView('news-comment');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAbout = () => {
    setView('about');
    window.scrollTo(0, 0);
  };

  const handleNavigateToForAuthors = () => {
    setView('for-authors');
    window.scrollTo(0, 0);
  };

  const handleNavigateToMerchandise = () => {
    setView('merchandise');
    window.scrollTo(0, 0);
  };

  const handleNavigateToPrivacy = () => {
    setView('privacy');
    window.scrollTo(0, 0);
  };

  const handleNavigateToTerms = () => {
    setView('terms');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAccessibility = () => {
    setView('accessibility');
    window.scrollTo(0, 0);
  };

  const handleNavigateToPress = () => {
    setView('press');
    window.scrollTo(0, 0);
  };

  const handleNavigateToContact = () => {
    setView('contact');
    window.scrollTo(0, 0);
  };

  const handleNavigateToAdminDashboard = () => {
    if (isAdmin) {
      setView('admin');
      window.scrollTo(0, 0);
    } else {
      setView('login');
      window.scrollTo(0, 0);
    }
  };

  const handleLoginSuccess = () => {
    // 立即更新权限状态
    setIsLoggedIn(isUserLoggedIn());
    setIsAdmin(isUserAdmin());

    const userStr = localStorage.getItem('jocker_admin_user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (user.role === 'ADMIN') {
          setView('admin');
        } else {
          setView('home');
        }
      } catch (error) {
        console.error('解析用户信息失败:', error);
        setView('home');
      }
    }
    window.scrollTo(0, 0);
  };

  const handleLogout = () => {
    // 使用统一的清除认证函数
    clearUserAuth();
    // 立即更新状态
    setIsLoggedIn(false);
    setIsAdmin(false);
    setView('home');
    window.scrollTo(0, 0);
  };

  const renderContent = () => {
    if (isLoading) {
      return <LoadingSkeleton />;
    }

    if (error) {
      return (
        <div className="text-center py-20 px-4">
            <h2 className="text-2xl font-semibold text-red-600">An Error Occurred</h2>
            <p className="mt-4 text-gray-600">{error}</p>
        </div>
      );
    }
    
    if(articles.length === 0 && !isLoading) {
       return (
        <div className="text-center py-20 px-4">
            <h2 className="text-2xl font-semibold text-gray-800">暂无文章</h2>
            <p className="mt-4 text-gray-600">还没有发布的文章。点击上方的"🤖 生成文章"按钮来创建第一篇文章吧！</p>
        </div>
       );
    }

    switch(view) {
        case 'article':
            const selectedArticle = articles.find(a => a.id === selectedArticleId);
            return selectedArticle ? <JournalArticlePage article={selectedArticle} updateArticleContent={handleUpdateArticleContent} onNavigateHome={handleNavigateToHome} /> : <div className="text-center py-20">Article not found.</div>;
        case 'search':
            return <SearchPage allArticles={articles} query={searchQuery} onNavigateToArticle={handleNavigateToArticle} onNavigateHome={handleNavigateToHome} />;
        case 'submit':
            return <SubmitPage />;
        case 'login':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <LoginPage onNavigateHome={handleNavigateToHome} onLoginSuccess={handleLoginSuccess} />
                </Suspense>
            );
        case 'admin':
            return isAdmin ? (
                <Suspense fallback={<LoadingSkeleton />}>
                    <AdminPage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            ) : (
                <Suspense fallback={<LoadingSkeleton />}>
                    <LoginPage onNavigateHome={handleNavigateToHome} onLoginSuccess={handleLoginSuccess} />
                </Suspense>
            );
        case 'changelog':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <DynamicChangelogSection />
                </Suspense>
            );
        case 'research-articles':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <ArticleListPage
                        articles={articles.filter(article => article.category !== 'News & Comment')}
                        title="Research Articles"
                        description="Peer-reviewed satirical research papers and academic studies"
                        onNavigateToArticle={handleNavigateToArticle}
                        onNavigateHome={handleNavigateToHome}
                    />
                </Suspense>
            );
        case 'news-comment':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <ArticleListPage
                        articles={articles.filter(article => article.category === 'News & Comment')}
                        title="News & Comment"
                        description="Breaking news and editorial commentary from the academic world"
                        category="News & Comment"
                        onNavigateToArticle={handleNavigateToArticle}
                        onNavigateHome={handleNavigateToHome}
                    />
                </Suspense>
            );
        case 'about':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <AboutPage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'for-authors':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <ForAuthorsPage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'merchandise':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <MerchandisePage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'privacy':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <PrivacyPolicyPage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'terms':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <TermsOfServicePage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'accessibility':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <AccessibilityPage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'press':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <PressPage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'contact':
            return (
                <Suspense fallback={<LoadingSkeleton />}>
                    <ContactPage onNavigateHome={handleNavigateToHome} />
                </Suspense>
            );
        case 'home':
        default:
            return <HomePage articles={articles.filter(article => article.category !== 'News & Comment')} onNavigateToArticle={handleNavigateToArticle} />;
    }
  };

  return (
    <WebsiteCoverProvider>
    <div className="bg-white min-h-screen flex flex-col">
      <Header
        onNavigateHome={handleNavigateToHome}
        onNavigateToSubmit={handleNavigateToSubmit}
        onSearch={handleNavigateToSearch}
        onArticleCreated={handleArticleCreated}
        onNavigateToLogin={handleNavigateToLogin}
        onNavigateToChangelog={handleNavigateToChangelog}
        onNavigateToResearchArticles={handleNavigateToResearchArticles}
        onNavigateToNewsComment={handleNavigateToNewsComment}
        onNavigateToAbout={handleNavigateToAbout}
        isLoggedIn={isLoggedIn}
        isAdmin={isAdmin}
        onLogout={handleLogout}
        onNavigateToAdmin={handleNavigateToAdminDashboard}
      />
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8 w-full flex-grow" role="main">
        {renderContent()}
      </main>
      <Footer
        onNavigateHome={handleNavigateToHome}
        onNavigateToSubmit={handleNavigateToSubmit}
        onNavigateToForAuthors={handleNavigateToForAuthors}
        onNavigateToMerchandise={handleNavigateToMerchandise}
        onNavigateToPress={handleNavigateToPress}
        onNavigateToContact={handleNavigateToContact}
        onNavigateToPrivacy={handleNavigateToPrivacy}
        onNavigateToTerms={handleNavigateToTerms}
        onNavigateToAccessibility={handleNavigateToAccessibility}
      />
    </div>
    </WebsiteCoverProvider>
  );
};

export default App;