import React, { useState, useEffect } from 'react';
import { adminApi } from '../src/services/api';
import { isUserAdmin } from '../src/utils/authUtils';
import { SharedBackground } from './SharedBackground';

interface AboutPageProps {
  onNavigateHome: () => void;
}

interface Founder {
  id: number;
  name: string;
  title: string;
  description: string;
  avatarUrl?: string;
}

/**
 * About 页面组件
 * 介绍 Jocker 学术期刊的使命和特色
 */
export const AboutPage: React.FC<AboutPageProps> = ({ onNavigateHome }) => {
  const [founder, setFounder] = useState<Founder | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadData, setUploadData] = useState({
    name: '',
    title: '',
    description: '',
    avatarUrl: ''
  });
  const [isUploading, setIsUploading] = useState(false);

  // 使用统一的管理员权限检查
  const isAdmin = isUserAdmin();

  // 加载创始人信息
  useEffect(() => {
    loadFounderInfo();
  }, []);

  const loadFounderInfo = async () => {
    try {
      const response = await adminApi.getFounderAvatar();
      if (response.founder) {
        setFounder(response.founder);
        setUploadData({
          name: response.founder.name || '',
          title: response.founder.title || '',
          description: response.founder.description || '',
          avatarUrl: response.founder.avatarUrl || ''
        });
      }
    } catch (error) {
      console.error('加载创始人信息失败:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setUploadData(prev => ({ ...prev, avatarUrl: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadSubmit = async () => {
    if (!uploadData.avatarUrl) {
      alert('请选择头像图片');
      return;
    }

    setIsUploading(true);
    try {
      await adminApi.uploadFounderAvatar(uploadData);
      await loadFounderInfo(); // 重新加载数据
      setShowUploadModal(false);
      alert('创始人头像上传成功！');
    } catch (error) {
      console.error('上传失败:', error);
      alert('上传失败，请重试');
    } finally {
      setIsUploading(false);
    }
  };
  return (
    <SharedBackground className="min-h-screen">
      {/* 页面头部 */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6">
            <button 
              onClick={onNavigateHome}
              className="hover:text-purple-600 transition-colors"
            >
              Home
            </button>
            <span>›</span>
            <span className="text-gray-900">About Jocker</span>
          </nav>
          
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 font-serif mb-4">
              About Jocker
            </h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              The premier journal for satirical scientific research and academic absurdity
            </p>
          </div>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 space-y-8">
          
          {/* Mission Statement */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 font-serif mb-4">Our Mission</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Jocker is dedicated to publishing the most rigorously absurd and scientifically satirical research papers 
              in the academic universe. We believe that humor and intellectual curiosity go hand in hand, and that the 
              best way to understand the complexities of academic life is through the lens of well-crafted absurdity.
            </p>
            <p className="text-gray-700 leading-relaxed">
              Our peer-reviewed articles explore the intersection of serious methodology and ridiculous hypotheses, 
              creating a unique space where academic rigor meets creative madness. From groundbreaking studies on 
              procrastination patterns to revolutionary theories about coffee consumption in research environments, 
              we cover it all.
            </p>
          </section>

          {/* Founder Section */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 font-serif mb-4">Meet Our Founder</h2>
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-8">
              <div className="flex flex-col md:flex-row items-center md:items-start space-y-6 md:space-y-0 md:space-x-8">
                {/* Avatar */}
                <div className="relative">
                  <div className="w-32 h-32 rounded-full overflow-hidden bg-gray-200 border-4 border-white shadow-lg">
                    {founder?.avatarUrl ? (
                      <img
                        src={founder.avatarUrl}
                        alt={founder.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-purple-100">
                        <svg className="w-16 h-16 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Admin Upload Button */}
                  {isAdmin && (
                    <button
                      onClick={() => setShowUploadModal(true)}
                      className="absolute -bottom-2 -right-2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-lg transition-colors"
                      title="上传头像"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </button>
                  )}
                </div>

                {/* Info */}
                <div className="flex-1 text-center md:text-left">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    {founder?.name || 'Dr. Serious McFunnyface'}
                  </h3>
                  <p className="text-lg text-purple-600 font-medium mb-4">
                    {founder?.title || 'Editor-in-Chief'}
                  </p>
                  <p className="text-gray-700 leading-relaxed">
                    {founder?.description || 'Leading researcher in satirical science and academic absurdity. Dedicated to advancing the field of humorous scholarship while maintaining the highest standards of academic rigor.'}
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* What We Publish */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 font-serif mb-4">What We Publish</h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="bg-purple-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-purple-900 mb-3">Research Articles</h3>
                <p className="text-purple-800 text-sm">
                  Peer-reviewed satirical research papers that follow rigorous academic standards while exploring 
                  completely absurd topics with deadpan seriousness.
                </p>
              </div>
              <div className="bg-blue-50 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-900 mb-3">News & Commentary</h3>
                <p className="text-blue-800 text-sm">
                  Editorial pieces and commentary on the latest developments in the world of academic absurdity 
                  and satirical science.
                </p>
              </div>
            </div>
          </section>

          {/* Editorial Standards */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 font-serif mb-4">Editorial Standards</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              Despite our commitment to absurdity, we maintain the highest standards of academic presentation. 
              All submissions undergo rigorous peer review by our panel of distinguished experts in the field of 
              satirical science. We require proper citations, mathematical formulas (however nonsensical), and 
              adherence to standard academic formatting.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-yellow-800 text-sm">
                <strong>Note:</strong> All research published in Jocker is entirely fictional and created for 
                entertainment purposes. Any resemblance to actual scientific studies is purely coincidental and 
                probably hilarious.
              </p>
            </div>
          </section>

          {/* Contact Information */}
          <section>
            <h2 className="text-2xl font-bold text-gray-900 font-serif mb-4">Contact & Submissions</h2>
            <p className="text-gray-700 leading-relaxed mb-4">
              We welcome submissions from researchers, academics, and anyone with a passion for combining rigorous 
              methodology with creative absurdity. Whether you're investigating the correlation between desk 
              organization and productivity, or developing new theories about the psychology of peer review, 
              we want to hear from you.
            </p>
            
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Submission Guidelines</h3>
              <ul className="space-y-2 text-gray-700 text-sm">
                <li className="flex items-start">
                  Articles should be 600-1000 words in standard academic format
                </li>
                <li className="flex items-start">
                  Include proper citations (fictional sources are encouraged)
                </li>
                <li className="flex items-start">
                  Mathematical formulas and figures are highly appreciated
                </li>
                <li className="flex items-start">
                  Maintain academic tone while exploring absurd topics
                </li>
                <li className="flex items-start">
                  All submissions are AI-generated for consistency and quality
                </li>
              </ul>
            </div>
          </section>

          {/* Footer */}
          <section className="text-center pt-8 border-t border-gray-200">
            <p className="text-gray-600 text-sm">
              Jocker Journal - Where Academic Rigor Meets Creative Absurdity
            </p>
            <p className="text-gray-500 text-xs mt-2">
              © 2025 Jocker. All rights reserved. No actual research was harmed in the making of this journal.
            </p>
          </section>
        </div>
      </div>

      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">上传创始人头像</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  姓名
                </label>
                <input
                  type="text"
                  value={uploadData.name}
                  onChange={(e) => setUploadData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Dr. Serious McFunnyface"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  职位
                </label>
                <input
                  type="text"
                  value={uploadData.title}
                  onChange={(e) => setUploadData(prev => ({ ...prev, title: e.target.value }))}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Editor-in-Chief"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  描述
                </label>
                <textarea
                  value={uploadData.description}
                  onChange={(e) => setUploadData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="简短描述..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  头像图片
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {uploadData.avatarUrl && (
                  <div className="mt-2">
                    <img
                      src={uploadData.avatarUrl}
                      alt="预览"
                      className="w-16 h-16 rounded-full object-cover"
                    />
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowUploadModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={handleUploadSubmit}
                disabled={isUploading}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
              >
                {isUploading ? '上传中...' : '保存'}
              </button>
            </div>
          </div>
        </div>
      )}
    </SharedBackground>
  );
};
