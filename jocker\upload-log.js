#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

/**
 * 一键上传日志到服务器
 * 使用方法：node upload-log.js v2.4.0
 */

// 配置
const CONFIG = {
  hostname: '************',
  port: 80,
  adminEmail: '<EMAIL>',
  adminPassword: 'jocker2024'
};

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log('❌ 请提供版本号');
    console.log('使用方法：node upload-log.js v2.4.0');
    process.exit(1);
  }

  const version = args[0];
  const logFile = path.join(__dirname, 'logs', `log-${version}.json`);

  if (!fs.existsSync(logFile)) {
    console.log(`❌ 日志文件不存在: ${logFile}`);
    console.log('\n可用的日志文件：');
    const logsDir = path.join(__dirname, 'logs');
    if (fs.existsSync(logsDir)) {
      fs.readdirSync(logsDir)
        .filter(file => file.endsWith('.json'))
        .forEach(file => console.log(`  ${file}`));
    }
    process.exit(1);
  }

  // 读取日志文件
  const logData = JSON.parse(fs.readFileSync(logFile, 'utf8'));
  console.log(`📋 准备上传日志: ${logData.version}`);
  console.log(`📝 标题: ${logData.title}`);
  console.log(`🔄 变更数量: ${logData.changes.length}`);
  console.log('');

  // 开始上传流程
  uploadLog(logData);
}

async function uploadLog(logData) {
  try {
    console.log('🔐 正在登录...');
    const token = await login();
    
    console.log('✅ 登录成功！');
    console.log('📤 正在上传日志...');
    
    const result = await importLog(token, logData);
    console.log('✅ 日志上传成功！');
    console.log(`📊 ${result.message}`);
    
  } catch (error) {
    console.log('❌ 上传失败:', error.message);
  }
}

function login() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      email: CONFIG.adminEmail,
      password: CONFIG.adminPassword
    });

    const options = {
      hostname: CONFIG.hostname,
      port: CONFIG.port,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 && response.success) {
            resolve(response.data.token);
          } else {
            reject(new Error(response.message || `登录失败 (${res.statusCode})`));
          }
        } catch (error) {
          reject(new Error('解析登录响应失败: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error('登录请求失败: ' + error.message));
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('登录请求超时'));
    });

    req.write(postData);
    req.end();
  });
}

function importLog(token, logData) {
  return new Promise((resolve, reject) => {
    // API期望的格式
    const postData = JSON.stringify({
      logs: [logData]
    });

    const options = {
      hostname: CONFIG.hostname,
      port: CONFIG.port,
      path: '/api/logs/import',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${token}`
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          if (res.statusCode === 200 || res.statusCode === 201) {
            if (response.success) {
              resolve(response);
            } else {
              reject(new Error(response.message || '导入失败'));
            }
          } else {
            reject(new Error(`导入失败 (${res.statusCode}): ${response.message || data}`));
          }
        } catch (error) {
          reject(new Error('解析导入响应失败: ' + error.message + '\n原始响应: ' + data));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error('导入请求失败: ' + error.message));
    });

    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('导入请求超时'));
    });

    req.write(postData);
    req.end();
  });
}

// 运行
main();
