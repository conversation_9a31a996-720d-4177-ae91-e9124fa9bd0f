import React, { useState, useEffect } from 'react';
import { adminApi } from '../src/services/api';

interface GeminiKeyManagerProps {
  onUpdateSuccess: (message: string) => void;
  onUpdateError: (error: string) => void;
}

interface GeminiConfig {
  id: string;
  isActive: boolean;
  textModel: string;
  imageModel: string;
  updatedAt: string;
  apiKeyPreview: string;
}

export const GeminiKeyManager: React.FC<GeminiKeyManagerProps> = ({
  onUpdateSuccess,
  onUpdateError
}) => {
  const [apiKey, setApiKey] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [textModel, setTextModel] = useState('gemini-2.0-flash-exp');
  const [imageModel, setImageModel] = useState('imagen-3.0-generate-001');
  const [isLoading, setIsLoading] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<GeminiConfig | null>(null);
  const [showKey, setShowKey] = useState(false);

  // 加载当前配置
  useEffect(() => {
    loadCurrentConfig();
  }, []);

  const loadCurrentConfig = async () => {
    try {
      const result = await adminApi.getGeminiKey();
      setCurrentConfig(result.config);
      if (result.config) {
        setIsActive(result.config.isActive);
        setTextModel(result.config.textModel);
        setImageModel(result.config.imageModel);
      }
    } catch (error) {
      console.error('加载Gemini Key配置失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      onUpdateError('请输入Gemini API Key');
      return;
    }

    if (!textModel.trim()) {
      onUpdateError('请输入文本生成模型');
      return;
    }

    if (!imageModel.trim()) {
      onUpdateError('请输入图片生成模型');
      return;
    }

    setIsLoading(true);

    try {
      const result = await adminApi.setGeminiKey({
        apiKey: apiKey.trim(),
        isActive,
        textModel: textModel.trim(),
        imageModel: imageModel.trim()
      });

      onUpdateSuccess('Gemini API Key设置成功！');
      setCurrentConfig(result.config);
      setApiKey(''); // 清空输入框
      
    } catch (error) {
      console.error('设置Gemini Key失败:', error);
      onUpdateError(error instanceof Error ? error.message : '设置Gemini Key失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async () => {
    if (!currentConfig) return;

    setIsLoading(true);
    try {
      const result = await adminApi.setGeminiKey({
        apiKey: currentConfig.apiKeyPreview.replace('...', ''), // 这里需要完整的key，实际应该从服务器获取
        isActive: !isActive
      });

      setIsActive(!isActive);
      setCurrentConfig(result.config);
      onUpdateSuccess(`Gemini API Key已${!isActive ? '启用' : '禁用'}`);
      
    } catch (error) {
      console.error('切换Gemini Key状态失败:', error);
      onUpdateError('切换状态失败');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">🔑 Gemini API Key 管理</h4>
        <p className="text-sm text-gray-600 mb-4">
          设置自定义的Gemini API Key。当设置了自定义Key时，系统将优先使用您提供的Key而不是服务器默认Key。
        </p>
      </div>

      {/* 当前配置状态 */}
      {currentConfig && (
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <h5 className="text-sm font-medium text-gray-900 mb-2">当前配置</h5>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">API Key:</span>
              <span className="font-mono text-gray-900">{currentConfig.apiKeyPreview}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">状态:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                currentConfig.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {currentConfig.isActive ? '已启用' : '已禁用'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">文本模型:</span>
              <span className="font-mono text-gray-900 text-sm">{currentConfig.textModel}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">图片模型:</span>
              <span className="font-mono text-gray-900 text-sm">{currentConfig.imageModel}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">更新时间:</span>
              <span className="text-gray-900">
                {new Date(currentConfig.updatedAt).toLocaleString('zh-CN')}
              </span>
            </div>
          </div>
          
          {/* 启用/禁用按钮 */}
          <div className="mt-3 pt-3 border-t border-gray-200">
            <button
              onClick={handleToggleActive}
              disabled={isLoading}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                currentConfig.isActive
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isLoading ? '处理中...' : (currentConfig.isActive ? '禁用' : '启用')}
            </button>
          </div>
        </div>
      )}

      {/* 设置新的API Key */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {currentConfig ? '更新' : '设置'} Gemini API Key
          </label>
          <div className="relative">
            <input
              type={showKey ? 'text' : 'password'}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="输入您的Gemini API Key (例如: AIzaSy...)"
              className="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
            <button
              type="button"
              onClick={() => setShowKey(!showKey)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showKey ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            💡 您可以在 <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google AI Studio</a> 获取免费的API Key
          </p>
        </div>

        {/* 模型选择 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文本生成模型
            </label>
            <input
              type="text"
              value={textModel}
              onChange={(e) => setTextModel(e.target.value)}
              placeholder="例如: gemini-2.0-flash-exp"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              用于文章内容生成的模型
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              图片生成模型
            </label>
            <input
              type="text"
              value={imageModel}
              onChange={(e) => setImageModel(e.target.value)}
              placeholder="例如: imagen-3.0-generate-001"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              用于图片生成的模型
            </p>
          </div>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            checked={isActive}
            onChange={(e) => setIsActive(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
            立即启用此API Key
          </label>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || !apiKey.trim() || !textModel.trim() || !imageModel.trim()}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              isLoading || !apiKey.trim() || !textModel.trim() || !imageModel.trim()
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isLoading ? '设置中...' : (currentConfig ? '更新配置' : '设置配置')}
          </button>
        </div>
      </form>

      {/* 使用说明 */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <h5 className="text-sm font-medium text-blue-900 mb-2">📋 使用说明</h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• 设置自定义API Key和模型后，系统将优先使用您的配置进行AI生成</li>
          <li>• 文本模型用于生成文章内容，图片模型用于生成文章配图</li>
          <li>• 如果您的Key失效，系统会自动回退到服务器默认配置</li>
          <li>• 您可以随时禁用自定义配置，系统将使用服务器默认设置</li>
          <li>• 所有配置会安全存储，只有管理员可以查看和修改</li>
          <li>• 推荐模型：文本使用 gemini-2.0-flash-exp，图片使用 imagen-3.0-generate-001</li>
        </ul>
      </div>
    </div>
  );
};
