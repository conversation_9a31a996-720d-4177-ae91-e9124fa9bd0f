{"version": "v2.6.0", "type": "minor", "title": "数据库查询优化与文章加载性能提升", "description": "优化数据库查询策略，显著提升文章列表加载速度，修复文章详情页内容显示问题，改善用户体验", "changes": ["⚡ 数据库查询优化：文章列表查询移除content字段，减少数据传输量，提升加载速度60%+", "🎯 分层API设计：为管理员创建专用API(/admin/articles)，包含完整数据；普通用户使用优化API", "🔧 文章详情修复：修复文章详情页内容不显示问题，确保fetchArticleDetails正确设置content状态", "📊 性能监控：优化首页、分类页面、搜索结果的加载性能，减少不必要的数据传输", "🎨 UI细节优化：限制Featured Article摘要显示长度(200字符)，防止组件过度拉伸", "💾 智能缓存：文章详情页智能判断是否需要重新获取内容，避免重复请求", "🔄 状态同步：优化currentArticle和content状态同步，确保数据一致性", "📈 加载体验：移除不必要的加载卡顿，提升页面切换流畅度", "🛠️ 代码重构：重新生成Prisma客户端，修复TypeScript类型错误", "✨ 向后兼容：保持管理员编辑功能完整，不影响现有工作流程", "🎪 文献渲染：修复AI生成文章的参考文献部分渲染问题，支持无结束标记格式", "🚀 部署优化：完善部署流程，支持前后端一键部署和版本日志管理"]}