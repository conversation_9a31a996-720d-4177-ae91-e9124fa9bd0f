import React, { useState, useEffect } from 'react';
import { articleApi, adminApi } from '../src/services/api';
import { SystemMonitor } from './SystemMonitor';
import { LogManagement } from './LogManagement';
import { WebsiteCoverUploader } from './WebsiteCoverUploader';
import { AdvertisementCoverUploader } from './AdvertisementCoverUploader';
import { AIConfigManager } from './AIConfigManager';
import { JournalIconManager } from './JournalIconManager';

interface User {
  id: number;
  email: string;
  username: string;
  name?: string;
  role: string;
  status: string;
  lastLogin?: string;
  createdAt: string;
  _count: {
    articles: number;
  };
}
import { Article } from '../types';

interface AdminPageProps {
  onNavigateHome: () => void;
}

export const AdminPage: React.FC<AdminPageProps> = ({ onNavigateHome }) => {
  const [articles, setArticles] = useState<Article[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingArticle, setEditingArticle] = useState<Article | null>(null);
  const [currentView, setCurrentView] = useState<'articles' | 'users' | 'system' | 'logs' | 'website'>('articles');

  // 批量删除相关状态
  const [selectedArticles, setSelectedArticles] = useState<Set<number>>(new Set());
  const [isDeleting, setIsDeleting] = useState(false);

  // 编辑观看次数相关状态
  const [editingViews, setEditingViews] = useState<{ [key: number]: number }>({});

  // 用户管理相关状态
  const [users, setUsers] = useState<User[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);

  // 获取文章列表
  useEffect(() => {
    fetchArticles();
  }, []);

  // 当切换到用户管理时加载用户数据
  useEffect(() => {
    if (currentView === 'users') {
      loadUsers();
    }
  }, [currentView]);

  const fetchArticles = async () => {
    try {
      setLoading(true);
      // 使用管理员专用API，包含content字段
      const response = await adminApi.getArticles({ limit: 50 });
      setArticles(response.data);
    } catch (err) {
      setError('获取文章列表失败');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleEditArticle = (article: Article) => {
    setEditingArticle({ ...article });
  };

  const handleSaveArticle = async () => {
    if (!editingArticle) return;

    try {
      const updatedArticle = await articleApi.updateArticle(editingArticle.id, {
        title: editingArticle.title,
        author: editingArticle.author,
        category: editingArticle.category,
        excerpt: editingArticle.excerpt,
        content: editingArticle.content,
        published: editingArticle.published,
        featured: editingArticle.featured,
      });

      // 更新本地状态
      setArticles(prev =>
        prev.map(article =>
          article.id === editingArticle.id ? updatedArticle : article
        )
      );

      setEditingArticle(null);
      alert('文章保存成功！');
    } catch (err) {
      alert('保存文章失败');
      console.error(err);
    }
  };

  const handleDeleteArticle = async (id: number) => {
    if (!confirm('确定要删除这篇文章吗？')) return;

    try {
      await articleApi.deleteArticle(id);
      setArticles(prev => prev.filter(article => article.id !== id));
      alert('文章删除成功！');
    } catch (err) {
      alert('删除文章失败');
      console.error(err);
    }
  };

  // 批量删除文章
  const handleBatchDelete = async () => {
    if (selectedArticles.size === 0) {
      alert('请先选择要删除的文章');
      return;
    }

    if (!confirm(`确定要删除选中的 ${selectedArticles.size} 篇文章吗？此操作不可恢复！`)) {
      return;
    }

    setIsDeleting(true);
    try {
      const articleIds = Array.from(selectedArticles);
      await adminApi.batchDeleteArticles(articleIds);

      // 更新本地状态
      setArticles(prev => prev.filter(article => !selectedArticles.has(article.id)));
      setSelectedArticles(new Set());

      alert(`成功删除 ${articleIds.length} 篇文章！`);
    } catch (err) {
      alert('批量删除失败');
      console.error(err);
    } finally {
      setIsDeleting(false);
    }
  };

  // 选择/取消选择文章
  const handleSelectArticle = (id: number) => {
    setSelectedArticles(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // 全选/取消全选
  const handleSelectAll = () => {
    if (selectedArticles.size === articles.length) {
      setSelectedArticles(new Set());
    } else {
      setSelectedArticles(new Set(articles.map(article => article.id)));
    }
  };

  // 更新观看次数
  const handleUpdateViews = async (id: number, newViews: number) => {
    if (newViews < 0) {
      alert('观看次数不能为负数');
      return;
    }

    try {
      await adminApi.updateArticleViews(id, newViews);

      // 更新本地状态
      setArticles(prev => prev.map(article =>
        article.id === id ? { ...article, views: newViews } : article
      ));

      // 清除编辑状态
      setEditingViews(prev => {
        const newState = { ...prev };
        delete newState[id];
        return newState;
      });

      alert('观看次数更新成功！');
    } catch (err) {
      alert('更新观看次数失败');
      console.error(err);
    }
  };

  // 用户管理相关函数
  const loadUsers = async () => {
    console.log('开始加载用户列表...');
    console.log('adminApi:', adminApi);
    setUsersLoading(true);
    try {
      const response = await adminApi.getAllUsers();
      console.log('用户列表响应:', response);
      setUsers(response.users);
    } catch (error) {
      console.error('加载用户列表失败:', error);
      alert('加载用户列表失败: ' + (error instanceof Error ? error.message : '未知错误'));
    } finally {
      setUsersLoading(false);
    }
  };

  const handleUserAction = async (userId: number, action: string, value?: string) => {
    try {
      switch (action) {
        case 'status':
          await adminApi.updateUserStatus(userId, value!);
          break;
        case 'role':
          await adminApi.updateUserRole(userId, value!);
          break;
        case 'delete':
          if (confirm('确定要删除这个用户吗？此操作不可恢复。')) {
            await adminApi.deleteUser(userId);
          } else {
            return;
          }
          break;
      }
      await loadUsers(); // 重新加载用户列表
      alert('操作成功');
    } catch (error) {
      console.error('操作失败:', error);
      alert('操作失败，请重试');
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-red-100 text-red-800';
      case 'EDITOR': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'DISABLED': return 'bg-red-100 text-red-800';
      case 'SUSPENDED': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN');
  };

  const renderArticleEditor = () => {
    if (!editingArticle) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <h3 className="text-xl font-bold mb-4">编辑文章</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">标题</label>
              <input
                type="text"
                value={editingArticle.title}
                onChange={(e) => setEditingArticle({...editingArticle, title: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">作者</label>
              <input
                type="text"
                value={editingArticle.author}
                onChange={(e) => setEditingArticle({...editingArticle, author: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">分类</label>
              <input
                type="text"
                value={editingArticle.category}
                onChange={(e) => setEditingArticle({...editingArticle, category: e.target.value})}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">摘要</label>
              <textarea
                value={editingArticle.excerpt}
                onChange={(e) => setEditingArticle({...editingArticle, excerpt: e.target.value})}
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">内容</label>
              <textarea
                value={editingArticle.content || ''}
                onChange={(e) => setEditingArticle({...editingArticle, content: e.target.value})}
                rows={8}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-center space-x-4">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={editingArticle.published}
                  onChange={(e) => setEditingArticle({...editingArticle, published: e.target.checked})}
                  className="mr-2"
                />
                已发布
              </label>
              
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={editingArticle.featured}
                  onChange={(e) => setEditingArticle({...editingArticle, featured: e.target.checked})}
                  className="mr-2"
                />
                推荐文章
              </label>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => setEditingArticle(null)}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
            >
              取消
            </button>
            <button
              onClick={handleSaveArticle}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              保存
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderArticlesList = () => (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">文章管理</h2>
        <div className="flex space-x-2">
          {selectedArticles.size > 0 && (
            <button
              onClick={handleBatchDelete}
              disabled={isDeleting}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
            >
              {isDeleting ? '删除中...' : `删除选中 (${selectedArticles.size})`}
            </button>
          )}
          <button
            onClick={fetchArticles}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            刷新列表
          </button>
        </div>
      </div>

      {articles.length > 0 && (
        <div className="flex items-center space-x-4 bg-gray-50 p-3 rounded-md">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={selectedArticles.size === articles.length && articles.length > 0}
              onChange={handleSelectAll}
              className="mr-2"
            />
            全选 ({articles.length} 篇文章)
          </label>
          {selectedArticles.size > 0 && (
            <span className="text-sm text-gray-600">
              已选择 {selectedArticles.size} 篇文章
            </span>
          )}
        </div>
      )}
      
      {loading ? (
        <div className="text-center py-8">加载中...</div>
      ) : error ? (
        <div className="text-center py-8 text-red-600">{error}</div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {articles.map((article) => (
              <li key={article.id} className="px-6 py-4">
                <div className="flex items-start gap-4">
                  {/* 选择框 */}
                  <div className="flex-shrink-0 pt-1">
                    <input
                      type="checkbox"
                      checked={selectedArticles.has(article.id)}
                      onChange={() => handleSelectArticle(article.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2">
                      <h3 className="text-lg font-medium text-gray-900 break-words flex-1">
                        {article.title}
                      </h3>
                      <div className="flex items-center space-x-2 flex-shrink-0">
                        {article.published && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            已发布
                          </span>
                        )}
                        {article.featured && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            推荐
                          </span>
                        )}
                      </div>
                    </div>

                    <div className="mt-1 text-sm text-gray-600 flex items-center space-x-4">
                      <span>作者: {article.author}</span>
                      <span>分类: {article.category}</span>
                      <div className="flex items-center space-x-1">
                        <span>浏览:</span>
                        {editingViews[article.id] !== undefined ? (
                          <div className="flex items-center space-x-1">
                            <input
                              type="number"
                              value={editingViews[article.id]}
                              onChange={(e) => setEditingViews(prev => ({
                                ...prev,
                                [article.id]: parseInt(e.target.value) || 0
                              }))}
                              className="w-20 px-1 py-0.5 text-xs border border-gray-300 rounded"
                              min="0"
                            />
                            <button
                              onClick={() => handleUpdateViews(article.id, editingViews[article.id])}
                              className="px-2 py-0.5 bg-green-600 text-white text-xs rounded hover:bg-green-700"
                            >
                              保存
                            </button>
                            <button
                              onClick={() => setEditingViews(prev => {
                                const newState = { ...prev };
                                delete newState[article.id];
                                return newState;
                              })}
                              className="px-2 py-0.5 bg-gray-600 text-white text-xs rounded hover:bg-gray-700"
                            >
                              取消
                            </button>
                          </div>
                        ) : (
                          <button
                            onClick={() => setEditingViews(prev => ({
                              ...prev,
                              [article.id]: article.views || 0
                            }))}
                            className="text-blue-600 hover:text-blue-800 underline"
                          >
                            {article.views || 0}
                          </button>
                        )}
                      </div>
                      <span>点赞: {article.likes}</span>
                    </div>

                    <p className="mt-1 text-sm text-gray-500 line-clamp-2">
                      {article.excerpt}
                    </p>
                  </div>
                  <div className="flex flex-col space-y-2 flex-shrink-0">
                    <button
                      onClick={() => handleEditArticle(article)}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 whitespace-nowrap"
                    >
                      编辑
                    </button>
                    <button
                      onClick={() => handleDeleteArticle(article.id)}
                      className="px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 whitespace-nowrap"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );

  const renderUsersSection = () => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">用户管理</h2>
        <div className="text-sm text-gray-500">
          共 {users.length} 个用户
        </div>
      </div>

      {usersLoading ? (
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <div className="bg-white shadow overflow-hidden sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {users.map((user) => (
              <li key={user.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-700">
                          {user.username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {user.username}
                        </p>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                          {user.role}
                        </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                          {user.status}
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 mt-1">
                        <p className="text-sm text-gray-500">{user.email}</p>
                        <p className="text-sm text-gray-500">
                          文章: {user._count.articles}
                        </p>
                        <p className="text-sm text-gray-500">
                          注册: {formatDate(user.createdAt)}
                        </p>
                        {user.lastLogin && (
                          <p className="text-sm text-gray-500">
                            最后登录: {formatDate(user.lastLogin)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {/* 状态切换 */}
                    <select
                      value={user.status}
                      onChange={(e) => handleUserAction(user.id, 'status', e.target.value)}
                      className="text-sm border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="ACTIVE">激活</option>
                      <option value="DISABLED">禁用</option>
                      <option value="SUSPENDED">暂停</option>
                    </select>

                    {/* 角色切换 */}
                    <select
                      value={user.role}
                      onChange={(e) => handleUserAction(user.id, 'role', e.target.value)}
                      className="text-sm border border-gray-300 rounded px-2 py-1"
                    >
                      <option value="USER">用户</option>
                      <option value="EDITOR">编辑</option>
                      <option value="ADMIN">管理员</option>
                    </select>

                    {/* 删除按钮 */}
                    <button
                      onClick={() => handleUserAction(user.id, 'delete')}
                      className="text-red-600 hover:text-red-800 text-sm px-2 py-1 border border-red-300 rounded hover:bg-red-50"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );

  // 渲染网站管理部分
  const renderWebsiteSection = () => {
    return (
      <div className="space-y-6">
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
              🌐 网站管理
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              管理网站的外观和设置，包括主页封面图片等。
            </p>

            <WebsiteCoverUploader
              onUploadSuccess={(coverData) => {
                console.log('封面上传成功:', coverData);
                // 可以添加成功提示
                alert('封面上传成功！刷新页面查看效果。');
              }}
              onUploadError={(error) => {
                console.error('封面上传失败:', error);
                alert('封面上传失败: ' + error);
              }}
            />
          </div>
        </div>

        {/* 广告封面设置 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <AdvertisementCoverUploader
              onUploadSuccess={(adData) => {
                console.log('广告封面上传成功:', adData);
                alert('广告封面上传成功！刷新页面查看效果。');
              }}
              onUploadError={(error) => {
                console.error('广告封面上传失败:', error);
                alert('广告封面上传失败: ' + error);
              }}
            />
          </div>
        </div>

        {/* 期刊图标管理 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <JournalIconManager
              onUpdateSuccess={(message) => {
                console.log('期刊图标更新成功:', message);
                alert(message);
              }}
              onUpdateError={(error) => {
                console.error('期刊图标更新失败:', error);
                alert('期刊图标更新失败: ' + error);
              }}
            />
          </div>
        </div>

        {/* AI配置管理 */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <AIConfigManager
              onUpdateSuccess={(message) => {
                console.log('AI配置更新成功:', message);
                alert(message);
              }}
              onUpdateError={(error) => {
                console.error('AI配置更新失败:', error);
                alert('AI配置更新失败: ' + error);
              }}
            />
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={onNavigateHome}
                className="text-xl font-bold text-gray-900 hover:text-gray-700"
              >
                ← 返回首页
              </button>
            </div>
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-gray-900">Jocker 管理后台</h1>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* 标签页 */}
        <div className="mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setCurrentView('articles')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                currentView === 'articles'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              文章管理
            </button>
            <button
              onClick={() => setCurrentView('users')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                currentView === 'users'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              用户管理
            </button>
            <button
              onClick={() => setCurrentView('system')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                currentView === 'system'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              📊 系统监控
            </button>
            <button
              onClick={() => setCurrentView('logs')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                currentView === 'logs'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              📋 日志管理
            </button>
            <button
              onClick={() => setCurrentView('website')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                currentView === 'website'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              网站管理
            </button>
          </nav>
        </div>

        {/* 内容区域 */}
        {currentView === 'articles' ? renderArticlesList() :
         currentView === 'users' ? renderUsersSection() :
         currentView === 'system' ? <SystemMonitor /> :
         currentView === 'logs' ? <LogManagement /> :
         renderWebsiteSection()}
      </div>

      {/* 编辑器模态框 */}
      {renderArticleEditor()}
    </div>
  );
};
