﻿import React from 'react';
import { Article } from '../types';
import { FeaturedArticle } from './FeaturedArticle';
import { ArticleCard } from './ArticleCard';
import { Sidebar } from './Sidebar';
import { ScrollingArticleList } from './ScrollingArticleList';
import { EnhancedButton, FloatingActionButton } from './EnhancedButton';
import { ArticleCard as EnhancedArticleCard, StatsCard } from './ResponsiveCard';
import { SharedBackground } from './SharedBackground';

interface HomePageProps {
  articles: Article[];
  onNavigateToArticle: (id: number) => void;
}

export const HomePage: React.FC<HomePageProps> = ({ articles, onNavigateToArticle }) => {
  const mainArticle = articles[0];
  const otherArticles = articles.slice(1, 9); // 更多文章用于展示
  const trendingArticles = articles.slice(0, 6); // 更多热门文章
  const featuredArticles = articles.slice(0, 5); // 用于滚动展示

  return (
    <SharedBackground>
      {/* 顶部英雄区域 - 只在有封面时显示 */}
      <div className="text-center py-16 mb-8">
        <div className="max-w-4xl mx-auto px-4">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold text-gray-900 mb-4 drop-shadow-sm">
            The Joker Journal
          </h1>
          <p className="text-xl md:text-2xl text-gray-700 mb-6 drop-shadow-sm">
            Satirical Science & Academic Humor
          </p>
          <div className="flex items-center justify-center space-x-8 text-sm text-gray-600 bg-white/60 backdrop-blur-sm rounded-full px-6 py-3 inline-flex">
            <span>Volume 2, Issue {new Date().getMonth() + 1}</span>
            <span>•</span>
            <span>{new Date().getFullYear()}</span>
            <span>•</span>
            <span>ISSN: 2025-JOKE</span>
          </div>
        </div>
      </div>

      {/* 当期重点文章展示 */}
      {featuredArticles.length > 0 && (
        <div className="mb-12 px-4">
          <div className="bg-white/70 backdrop-blur-sm rounded-lg p-6 shadow-sm">
            <ScrollingArticleList
              articles={featuredArticles}
              onArticleClick={(article) => onNavigateToArticle(article.id)}
              className="max-w-5xl mx-auto"
            />
          </div>
        </div>
      )}

      {/* 特色文章区域 - 期刊风格 */}
      {mainArticle && (
        <div className="mb-12 px-4">
          <div className="bg-white/80 backdrop-blur-sm border border-gray-200/50 py-8 rounded-lg shadow-sm">
            <div className="max-w-5xl mx-auto">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-serif font-bold text-gray-900">
                  Featured Article
                </h2>
                <div className="w-24 h-0.5 bg-gray-400 mx-auto mt-2"></div>
              </div>
              <FeaturedArticle article={mainArticle} onNavigateToArticle={onNavigateToArticle} />
            </div>
          </div>
        </div>
      )}

      {/* 主要内容区域 - 期刊布局 */}
      <div className="max-w-7xl mx-auto px-4">
        <div className="bg-white/80 backdrop-blur-sm rounded-lg shadow-sm p-6">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* 主要文章列表 */}
          <div className="lg:col-span-3">
            <div className="mb-8">
              <h2 className="text-2xl font-serif font-bold text-gray-900 mb-2">
                Recent Articles
              </h2>
              <div className="w-16 h-0.5 bg-gray-400 mb-6"></div>
            </div>

            <div className="space-y-6">
              {otherArticles.map((article) => (
                <div key={article.id} className="bg-white border border-gray-200 hover:shadow-sm transition-shadow duration-200">
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3
                          className="text-lg font-serif text-gray-900 hover:text-blue-800 cursor-pointer transition-colors duration-200 mb-2"
                          onClick={() => onNavigateToArticle(article.id)}
                        >
                          {article.title}
                        </h3>
                        <div className="text-sm text-gray-600 mb-3">
                          <span className="font-medium">{article.author}</span>
                          <span className="mx-2">•</span>
                          <span>{article.category}</span>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 text-right ml-4">
                        <div>DOI: {article.doi}</div>
                        <div className="mt-1">{new Date(article.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}</div>
                      </div>
                    </div>

                    <p className="text-gray-700 text-sm leading-relaxed mb-4 line-clamp-3">
                      {article.excerpt}
                    </p>

                    <div className="flex items-center justify-between text-xs text-gray-500 pt-4 border-t border-gray-100">
                      <div className="flex items-center space-x-4">
                        <span>Views: {article.views || 0}</span>
                        <span>Citations: {article.likes || 0}</span>
                      </div>
                      <button
                        onClick={() => onNavigateToArticle(article.id)}
                        className="text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200"
                      >
                        Read Article →
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 侧边栏 */}
          <div className="lg:col-span-1">
            <Sidebar trendingArticles={trendingArticles} onNavigateToArticle={onNavigateToArticle} />
          </div>
        </div>
        </div>
      </div>
    </SharedBackground>
  );
};