const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// 生成DOI的函数
function generateDOI(article, index) {
  const year = new Date(article.createdAt).getFullYear();
  const month = String(new Date(article.createdAt).getMonth() + 1).padStart(2, '0');
  const day = String(new Date(article.createdAt).getDate()).padStart(2, '0');

  // 清理标题，移除特殊字符，保留字母数字和空格
  const cleanTitle = article.title
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .toLowerCase()
    .substring(0, 30); // 限制长度

  // 使用递增的序号确保唯一性
  const sequence = String(index + 1).padStart(4, '0');
  
  return `10.1000/jocker.${year}.${month}.${day}.${sequence}.${cleanTitle}`;
}

async function migrateDOI() {
  try {
    console.log('🔄 开始为现有文章分配DOI...');

    // 获取所有没有DOI的文章，按创建时间排序
    const articlesWithoutDOI = await prisma.article.findMany({
      where: {
        OR: [
          { doi: null },
          { doi: '' }
        ]
      },
      orderBy: {
        createdAt: 'asc'
      }
    });

    console.log(`📊 找到 ${articlesWithoutDOI.length} 篇需要分配DOI的文章`);

    if (articlesWithoutDOI.length === 0) {
      console.log('✅ 所有文章都已有DOI，无需迁移');
      return;
    }

    // 为每篇文章分配DOI
    for (let i = 0; i < articlesWithoutDOI.length; i++) {
      const article = articlesWithoutDOI[i];
      const doi = generateDOI(article, i);

      await prisma.article.update({
        where: { id: article.id },
        data: { doi }
      });

      console.log(`✅ 文章 ${article.id} "${article.title}" 分配DOI: ${doi}`);
    }

    console.log(`🎉 成功为 ${articlesWithoutDOI.length} 篇文章分配了DOI`);

    // 验证结果
    const totalArticles = await prisma.article.count();
    const articlesWithDOI = await prisma.article.count({
      where: {
        AND: [
          { doi: { not: null } },
          { doi: { not: '' } }
        ]
      }
    });

    console.log(`📈 统计结果:`);
    console.log(`   总文章数: ${totalArticles}`);
    console.log(`   有DOI的文章数: ${articlesWithDOI}`);
    console.log(`   覆盖率: ${((articlesWithDOI / totalArticles) * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('❌ DOI迁移失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateDOI()
    .then(() => {
      console.log('✅ DOI迁移完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ DOI迁移失败:', error);
      process.exit(1);
    });
}

module.exports = { migrateDOI, generateDOI };
