"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateAIComments = exports.submitComment = exports.getArticleComments = void 0;
const asyncHandler_1 = require("../utils/asyncHandler");
const database_1 = __importDefault(require("../config/database"));
const AI_REVIEWERS = [
    { name: 'Dr. AlphaGPT', title: 'Prof.', affiliation: 'Institute of Artificial Nonsense' },
    { name: 'SynthetixCritic', title: 'Dr.', affiliation: 'University of Digital Delusion' },
    { name: 'MetaLogic-7', title: 'Prof.', affiliation: 'Academy of Computational Confusion' },
    { name: 'NeuralNonsense', title: 'Dr.', affiliation: 'Center for Algorithmic Absurdity' },
    { name: 'QuantumQuibble', title: 'Prof.', affiliation: 'Institute of Theoretical Tomfoolery' },
    { name: 'DeepDrivel', title: 'Dr.', affiliation: 'School of Synthetic Scholarship' },
    { name: 'LogicLoop-9000', title: 'Prof.', affiliation: 'Department of Recursive Reasoning' },
    { name: 'BinaryBabble', title: 'Dr.', affiliation: 'College of Computational Comedy' }
];
const COMMENT_TEMPLATES = [
    "这篇论文在{aspect}方面显示出了令人震惊的{quality}，但其{contradiction}的叙事模式值得进一步的深度学习！",
    "本文论述{adjective}，尤其在{field}领域具有里程碑意义！作者的{approach}方法堪称{evaluation}。",
    "虽然作者在{topic}上的见解{opinion}，但我认为其对{concept}的理解存在根本性的{flaw}。",
    "这项研究为{field}领域开辟了新的{direction}，特别是在{specific_area}方面的贡献不可忽视。",
    "作者的{methodology}方法论虽然{evaluation}，但其结论的{reliability}令人担忧。"
];
exports.getArticleComments = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const articleId = parseInt(req.params.id);
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章ID',
        });
    }
    try {
        const comments = await database_1.default.articleComment.findMany({
            where: {
                articleId,
                isApproved: true,
            },
            include: {
                user: {
                    select: {
                        id: true,
                        username: true,
                        name: true,
                        avatar: true,
                    },
                },
            },
            orderBy: {
                createdAt: 'desc',
            },
        });
        const response = {
            success: true,
            message: '获取评论成功',
            data: { comments },
        };
        return res.status(200).json(response);
    }
    catch (error) {
        console.error('获取评论失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '获取评论失败',
        });
    }
});
exports.submitComment = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user) {
        return res.status(401).json({
            success: false,
            message: '请先登录',
        });
    }
    const articleId = parseInt(req.params.id);
    const { title, content, authorName } = req.body;
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章ID',
        });
    }
    if (!title || !content) {
        return res.status(400).json({
            success: false,
            message: '评论标题和内容不能为空',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        const isAIComment = authorName && (authorName.includes('Dr. AlphaGPT') ||
            authorName.includes('SynthetixCritic') ||
            authorName.includes('MetaLogic-7') ||
            authorName.includes('NeuralNonsense') ||
            authorName.includes('QuantumQuibble') ||
            authorName.includes('DeepDrivel') ||
            authorName.includes('LogicLoop-9000') ||
            authorName.includes('BinaryBabble'));
        const comment = await database_1.default.articleComment.create({
            data: {
                articleId,
                authorName: authorName || req.user.name || req.user.username,
                authorTitle: isAIComment ? authorName.split(' ')[0] : 'Reviewer',
                title,
                content,
                isAI: isAIComment,
                isApproved: true,
                userId: isAIComment ? null : req.user.id,
            },
        });
        const response = {
            success: true,
            message: '感谢您的学术反馈！您的评论已通过我们的元-算法伦理审查，现已发布。（审查时间：0.001纳秒）',
            data: { comment },
        };
        return res.status(201).json(response);
    }
    catch (error) {
        console.error('提交评论失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '提交评论失败',
        });
    }
});
exports.generateAIComments = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return res.status(403).json({
            success: false,
            message: '权限不足',
        });
    }
    const articleId = parseInt(req.params.id);
    const { count = 3 } = req.body;
    if (isNaN(articleId)) {
        return res.status(400).json({
            success: false,
            message: '无效的文章ID',
        });
    }
    if (count < 1 || count > 10) {
        return res.status(400).json({
            success: false,
            message: '评论数量必须在1-10之间',
        });
    }
    try {
        const article = await database_1.default.article.findUnique({
            where: { id: articleId },
        });
        if (!article) {
            return res.status(404).json({
                success: false,
                message: '文章不存在',
            });
        }
        const generatedComments = [];
        for (let i = 0; i < count; i++) {
            const reviewer = AI_REVIEWERS[Math.floor(Math.random() * AI_REVIEWERS.length)];
            const commentTitle = generateCommentTitle(article.title);
            const commentContent = generateCommentContent(article);
            const comment = await database_1.default.articleComment.create({
                data: {
                    articleId,
                    authorName: reviewer.name,
                    authorTitle: reviewer.title,
                    title: commentTitle,
                    content: commentContent,
                    isAI: true,
                    isApproved: true,
                },
            });
            generatedComments.push(comment);
        }
        const response = {
            success: true,
            message: `成功生成 ${count} 条AI评论`,
            data: { comments: generatedComments },
        };
        return res.status(201).json(response);
    }
    catch (error) {
        console.error('生成AI评论失败:', error);
        return res.status(500).json({
            success: false,
            message: error instanceof Error ? error.message : '生成AI评论失败',
        });
    }
});
function generateCommentTitle(articleTitle) {
    const templates = [
        `对《${articleTitle}》的深度思考`,
        `关于《${articleTitle}》的学术质疑`,
        `《${articleTitle}》：一个革命性的突破？`,
        `重新审视《${articleTitle}》的核心论点`,
        `《${articleTitle}》中的方法论创新`,
    ];
    return templates[Math.floor(Math.random() * templates.length)];
}
function generateCommentContent(article) {
    const aspects = ['理论构建', '方法论', '数据分析', '结论推导', '文献综述'];
    const qualities = ['模糊性', '创新性', '严谨性', '前瞻性', '争议性'];
    const contradictions = ['自我矛盾', '逻辑清晰', '令人困惑', '出人意料'];
    const adjectives = ['精彩', '深刻', '有趣', '令人震惊', '值得商榷'];
    const fields = ['空话', '理论物理', '计算哲学', '数字人文', '算法伦理'];
    const approaches = ['定量', '定性', '混合', '创新', '传统'];
    const evaluations = ['令人印象深刻', '有待改进', '具有开创性', '存在争议'];
    const template = COMMENT_TEMPLATES[Math.floor(Math.random() * COMMENT_TEMPLATES.length)];
    return template
        .replace('{aspect}', aspects[Math.floor(Math.random() * aspects.length)])
        .replace('{quality}', qualities[Math.floor(Math.random() * qualities.length)])
        .replace('{contradiction}', contradictions[Math.floor(Math.random() * contradictions.length)])
        .replace('{adjective}', adjectives[Math.floor(Math.random() * adjectives.length)])
        .replace('{field}', fields[Math.floor(Math.random() * fields.length)])
        .replace('{approach}', approaches[Math.floor(Math.random() * approaches.length)])
        .replace('{evaluation}', evaluations[Math.floor(Math.random() * evaluations.length)])
        .replace('{topic}', '核心议题')
        .replace('{opinion}', '颇具见地')
        .replace('{concept}', '基本概念')
        .replace('{flaw}', '认知偏差')
        .replace('{direction}', '研究方向')
        .replace('{specific_area}', '特定领域')
        .replace('{methodology}', '研究')
        .replace('{reliability}', '可靠性');
}
//# sourceMappingURL=commentController.js.map