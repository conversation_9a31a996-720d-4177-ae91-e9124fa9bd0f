import React from 'react';
import { useWebsiteCover } from '../src/contexts/WebsiteCoverContext';

interface SharedBackgroundProps {
  children: React.ReactNode;
  className?: string;
}

export const SharedBackground: React.FC<SharedBackgroundProps> = ({ children, className = "" }) => {
  const { websiteCover } = useWebsiteCover();

  return (
    <div className={`relative min-h-screen ${className}`}>
      {/* 全页面背景封面 */}
      {websiteCover && websiteCover.avatarUrl && (
        <>
          <div className="fixed inset-0 z-0">
            <img
              src={websiteCover.avatarUrl}
              alt={websiteCover.name || '网站封面'}
              className="w-full h-full object-cover"
              style={{
                objectPosition: 'center center'
              }}
            />
            {/* 白色透明遮罩，让内容更清晰 */}
            <div className="absolute inset-0 bg-white/85"></div>
          </div>
          
          {/* 底部渐变遮罩，确保与footer的平滑过渡 */}
          <div className="fixed bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-800 to-transparent z-5 pointer-events-none"></div>
        </>
      )}

      {/* 内容区域 */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};
