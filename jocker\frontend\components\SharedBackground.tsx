import React, { useState, useEffect } from 'react';
import { api } from '../src/services/api';

interface SharedBackgroundProps {
  children: React.ReactNode;
  className?: string;
}

export const SharedBackground: React.FC<SharedBackgroundProps> = ({ children, className = "" }) => {
  const [websiteCover, setWebsiteCover] = useState<any>(null);
  const [coverLoading, setCoverLoading] = useState(true);

  // 加载网站封面
  useEffect(() => {
    const loadWebsiteCover = async () => {
      try {
        const coverData = await api.getWebsiteCover();
        setWebsiteCover(coverData.websiteConfig);
      } catch (error) {
        console.log('获取网站封面失败:', error);
      } finally {
        setCoverLoading(false);
      }
    };

    loadWebsiteCover();
  }, []);

  return (
    <div className={`relative min-h-screen ${className}`}>
      {/* 全页面背景封面 */}
      {websiteCover && websiteCover.avatarUrl && (
        <>
          <div className="fixed inset-0 z-0">
            <img
              src={websiteCover.avatarUrl}
              alt={websiteCover.name || '网站封面'}
              className="w-full h-full object-cover"
              style={{
                objectPosition: 'center center'
              }}
            />
            {/* 白色透明遮罩，让内容更清晰 */}
            <div className="absolute inset-0 bg-white/85"></div>
          </div>
          
          {/* 底部渐变遮罩，确保与footer的平滑过渡 */}
          <div className="fixed bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-gray-800 to-transparent z-5 pointer-events-none"></div>
        </>
      )}

      {/* 内容区域 */}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  );
};
