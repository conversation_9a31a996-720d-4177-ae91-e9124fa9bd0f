# Joker Journal 项目开发指南

## 📁 项目结构

```
jocker/
├── frontend/           # React + TypeScript 前端
├── backend/            # Node.js + Express + Prisma 后端
├── logs/              # 版本更新日志
├── deploy-all.bat     # 一键部署脚本（前端+后端）
├── deploy-frontend.bat # 前端部署脚本
├── deploy-backend.bat  # 后端部署脚本
├── upload-log.js      # 版本日志上传工具
└── cleanup-server.bat # 服务器清理工具
```

## 🚀 部署工具说明

### 1. deploy-all.bat
**一键部署前端和后端**
- 自动构建前端项目
- 上传前端dist文件到服务器
- 上传后端代码到服务器
- 自动重启PM2服务
- 运行数据库迁移

### 2. deploy-frontend.bat
**仅部署前端**
- 构建React项目
- 上传到服务器 `/var/www/jocker/jocker/frontend/`
- 不影响后端服务

### 3. deploy-backend.bat
**仅部署后端**
- 上传后端代码到服务器
- 重启PM2服务 `jocker-backend`
- 运行 `prisma db push` 更新数据库，不会覆盖旧的数据库数据。

### 4. upload-log.js
**版本日志上传工具**
```bash
node upload-log.js v2.6.0
```
- **必须指定版本号**：读取 `logs/log-v2.6.0.json` 文件
- 通过API上传到服务器数据库
- 自动登录管理员账户

## 📋 开发工作流程

### 版本发布流程
1. **开发完成后**：创建版本日志文件 `logs/log-vX.X.X.json`
2. **上传日志**：`node upload-log.js vX.X.X`（必须带版本号）
3. **选择部署方式**：
   - 仅前端改动：`deploy-frontend.bat`
   - 仅后端改动：`deploy-backend.bat`
   - 前后端都改动：`deploy-all.bat`

### 部署选择原则
- **deploy-frontend.bat**：UI组件修改、样式调整、前端逻辑优化
- **deploy-backend.bat**：API接口修改、数据库schema变更、后端逻辑
- **deploy-all.bat**：涉及前后端协调的功能、大版本更新

### 性能优化注意事项
- **数据库查询**：列表页面避免查询content等大字段
- **API设计**：区分管理员API和普通用户API
- **状态管理**：确保前端状态同步，避免重复请求

## 🌐 服务器配置

### 服务器信息
- **IP地址**: ************
- **连接方式**: SSH
- **执行命令**: `ssh root@************ "命令"`

### Nginx代理配置
- **外部端口**: 80 (HTTP)
- **内部端口**: 5003 (Node.js后端)
- **配置**: Nginx将80端口请求代理到5003端口

⚠️ **重要**: API调用时使用80端口，不要直接访问5003端口

### PM2服务管理
- **服务名**: jocker-backend
- **重启命令**: `pm2 restart jocker-backend`
- **查看状态**: `pm2 status`

## 🛠️ 最近实现的功能 (v2.5.0)

### 网站封面系统
1. **管理员上传功能**
   - 在管理后台 → 网站管理 → 封面上传
   - 支持JPG、PNG、GIF格式，最大10MB
   - Base64格式存储到数据库

2. **全页面背景显示**
   - 封面作为主页背景，固定定位
   - 白色半透明遮罩(85%透明度)确保内容可读
   - 毛玻璃效果(backdrop-blur-sm)提升视觉层次

3. **智能布局适配**
   - 竖图自动居中裁剪(object-fit: cover)
   - 响应式设计，适配不同设备
   - 无封面时回退到原有设计

### API接口
- `POST /api/admin/website-cover` - 上传封面
- `GET /api/admin/website-cover` - 获取封面

## 📊 已实现的核心功能

### 文章管理
- ✅ 文章CRUD操作
- ✅ AI内容生成(严肃模式/小丑模式)
- ✅ 图片生成与管理
- ✅ PDF导出功能
- ✅ 文章浏览统计
- ✅ 音频上传与播放

### 用户系统
- ✅ 用户注册/登录
- ✅ 角色管理(用户/编辑/管理员)
- ✅ Gmail OAuth登录

### 管理后台
- ✅ 文章管理
- ✅ 用户管理
- ✅ 系统监控
- ✅ 日志管理
- ✅ 网站管理(封面上传)

### 技术特性
- ✅ 响应式设计
- ✅ LaTeX公式支持
- ✅ Markdown渲染
- ✅ 图片懒加载
- ✅ 一键部署系统

## 🔧 开发流程

### 1. 本地开发（开发过程中不用，可直接部署）
```bash
# 前端开发
cd frontend
npm run dev

# 后端开发
cd backend
npm run dev
```

### 2. 部署流程（建议，每次修改完代码，让用户自己运行部署脚本）
```bash
# 一键部署
deploy-all.bat

# 上传版本日志
node upload-log.js v2.5.0
```

### 3. 数据库管理
- 数据库位置: `/var/www/jocker/jocker/backend/prisma/dev.db`
- 迁移命令: `prisma db push`
- 备份策略: 部署时不覆盖数据库文件

## 📝 注意事项

1. **端口配置**: 所有API调用使用80端口，不要使用5003
2. **数据库安全**: 部署脚本不会覆盖服务器数据库
3. **图片优化**: 竖图建议将重点内容放在中央区域
4. **版本管理**: 每次更新都要创建对应的日志文件

## 🎯 下一步开发建议

1. 优化封面上传体验(拖拽上传)
2. 添加封面预设模板
3. 实现封面定时更换功能
4. 优化移动端显示效果
