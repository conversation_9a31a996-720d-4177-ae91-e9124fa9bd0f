import React, { useState, useEffect } from 'react';
import { logApi } from '../src/services/api';
import { SharedBackground } from './SharedBackground';

interface SystemLog {
  id: string;
  version: string;
  date: string;
  type: 'major' | 'minor' | 'patch' | 'hotfix';
  title?: string;
  description?: string;
  changes: string[];
  creator?: {
    id: number;
    username: string;
    name?: string;
  };
}

export const DynamicChangelogSection: React.FC = () => {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [filterType, setFilterType] = useState<string>('');

  useEffect(() => {
    loadLogs();
  }, [currentPage, filterType]);

  const loadLogs = async () => {
    try {
      setLoading(true);
      const params: any = { page: currentPage, limit: 10 };
      if (filterType) params.type = filterType;
      
      const data = await logApi.getSystemLogs(params);
      setLogs(data.logs);
      setTotalPages(data.pagination.totalPages);
      setError(null);
    } catch (err) {
      console.error('加载日志失败:', err);
      setError('加载日志失败');
    } finally {
      setLoading(false);
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'major': return 'bg-red-100 text-red-800';
      case 'minor': return 'bg-blue-100 text-blue-800';
      case 'patch': return 'bg-green-100 text-green-800';
      case 'hotfix': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'major': return '🚀';
      case 'minor': return '✨';
      case 'patch': return '🔧';
      case 'hotfix': return '🚨';
      default: return '📝';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading && logs.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="border rounded-lg p-4">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/4 mb-3"></div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded"></div>
                  <div className="h-3 bg-gray-200 rounded w-5/6"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="text-center">
          <div className="text-red-600 mb-4">❌ {error}</div>
          <button
            onClick={loadLogs}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-900">📋 更新日志</h2>
          <div className="flex items-center space-x-4">
            <select
              value={filterType}
              onChange={(e) => {
                setFilterType(e.target.value);
                setCurrentPage(1);
              }}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            >
              <option value="">所有类型</option>
              <option value="major">🚀 重大更新</option>
              <option value="minor">✨ 功能更新</option>
              <option value="patch">🔧 修复更新</option>
              <option value="hotfix">🚨 紧急修复</option>
            </select>
            <button
              onClick={loadLogs}
              disabled={loading}
              className="bg-blue-600 text-white px-3 py-1 rounded-md hover:bg-blue-700 disabled:bg-gray-400 text-sm"
            >
              {loading ? '刷新中...' : '刷新'}
            </button>
          </div>
        </div>
        <p className="text-gray-600">
          Joker期刊系统的版本更新记录，实时获取最新的功能改进和修复信息。
        </p>
      </div>

      <div className="p-6">
        {logs.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500 mb-4">📝 暂无日志记录</div>
            <p className="text-sm text-gray-400">管理员可以在后台添加系统更新日志</p>
          </div>
        ) : (
          <div className="space-y-6">
            {logs.map((log) => (
              <div key={log.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{getTypeIcon(log.type)}</span>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900">
                        {log.title || `版本 ${log.version}`}
                      </h3>
                      <div className="flex items-center space-x-3 mt-1">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(log.type)}`}>
                          {log.type}
                        </span>
                        <span className="text-sm text-gray-500">
                          {formatDate(log.date)}
                        </span>
                        {log.creator && (
                          <span className="text-sm text-gray-500">
                            by {log.creator.name || log.creator.username}
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-lg font-mono text-blue-600">
                    {log.version}
                  </div>
                </div>

                {log.description && (
                  <div className="mb-4 text-gray-700 bg-gray-50 rounded-lg p-3">
                    {log.description}
                  </div>
                )}

                <div className="space-y-2">
                  {log.changes.map((change, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <span className="text-blue-500 mt-1">•</span>
                      <span className="text-gray-700 flex-1">{change}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* 分页 */}
        {totalPages > 1 && (
          <div className="mt-8 flex justify-center">
            <nav className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1 || loading}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
              >
                上一页
              </button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1;
                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      disabled={loading}
                      className={`px-3 py-2 text-sm font-medium rounded-md ${
                        currentPage === page
                          ? 'bg-blue-600 text-white'
                          : 'text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}
              </div>

              <button
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages || loading}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:bg-gray-100 disabled:text-gray-400"
              >
                下一页
              </button>
            </nav>
          </div>
        )}
      </div>
    </div>
  );
};
