import React from 'react';

interface CustomListItemProps {
  icon?: string;
  color?: string;
  children: React.ReactNode;
  className?: string;
}

/**
 * 自定义列表项组件
 * 用于避免markdown渲染时的重复点问题
 */
export const CustomListItem: React.FC<CustomListItemProps> = ({
  icon = "•",
  color = "text-purple-500",
  children,
  className = ""
}) => (
  <div className={`flex items-start ${className}`}>
    <span className={`${color} mr-1 mt-0.5 flex-shrink-0 text-xs`}>{icon}</span>
    <div className="flex-1 text-xs text-gray-600">
      {children}
    </div>
  </div>
);

interface CustomListProps {
  items: string[];
  icon?: string;
  color?: string;
  className?: string;
}

/**
 * 自定义列表组件
 * 渲染一组列表项，避免markdown渲染问题
 */
export const CustomList: React.FC<CustomListProps> = ({ 
  items, 
  icon = "•", 
  color = "text-purple-500", 
  className = "" 
}) => (
  <div className={`space-y-2 ${className}`}>
    {items.map((item, index) => (
      <CustomListItem key={index} icon={icon} color={color}>
        {item}
      </CustomListItem>
    ))}
  </div>
);
