// 备份文件 - 用于恢复
// 这是一个临时备份，包含了修复后的评论生成逻辑

// 简化的AI评论生成函数
const generateAIComments = async (articleId: number, article: any, content: string, generateCount: number) => {
  const isNewsArticle = article.category === 'News & Comment';
  
  // 简化的prompt
  const prompt = `Generate exactly ${generateCount} ${isNewsArticle ? 'internet comments' : 'academic reviews'} as a JSON array.

Article: "${article.title}"
Content: ${content.substring(0, 500)}...

Return ONLY this JSON format:
[{"reviewerName": "Username", "reviewerTitle": "${isNewsArticle ? '@' : 'Dr.'}", "commentTitle": "Title", "content": "Comment text"}]`;

  const response = await aiService.generateText({ prompt });

  // 更鲁棒的JSON解析
  let reviewsData;
  try {
    console.log('🤖 AI原始响应:', response);
    
    // 多步骤清理和解析
    let cleanResponse = response.trim();
    
    // 移除常见的非JSON前缀/后缀
    cleanResponse = cleanResponse
      .replace(/^[^[{]*/, '') // 移除开头的非JSON字符
      .replace(/[^}\]]*$/, '') // 移除结尾的非JSON字符
      .replace(/```json\s*/g, '') // 移除markdown代码块
      .replace(/```\s*/g, '') // 移除代码块结束标记
      .replace(/^Here.*?:/gm, '') // 移除"Here are the comments:"等前缀
      .trim();
    
    // 确保以[开头，]结尾
    if (!cleanResponse.startsWith('[')) {
      const arrayStart = cleanResponse.indexOf('[');
      if (arrayStart !== -1) {
        cleanResponse = cleanResponse.substring(arrayStart);
      }
    }
    if (!cleanResponse.endsWith(']')) {
      const arrayEnd = cleanResponse.lastIndexOf(']');
      if (arrayEnd !== -1) {
        cleanResponse = cleanResponse.substring(0, arrayEnd + 1);
      }
    }
    
    console.log('🧹 清理后的响应:', cleanResponse);
    reviewsData = JSON.parse(cleanResponse);

    // 确保是数组
    if (!Array.isArray(reviewsData)) {
      throw new Error('Response is not an array');
    }
    
    console.log('✅ JSON解析成功:', reviewsData);
  } catch (parseError) {
    console.warn('❌ JSON解析失败，使用备用格式:', parseError);
    // 备用方案：生成默认评论
    reviewsData = [];
    for (let i = 0; i < generateCount; i++) {
      reviewsData.push({
        reviewerName: isNewsArticle ? `InternetUser${i + 1}` : `Dr. Savage AI-${i + 1}`,
        reviewerTitle: isNewsArticle ? '@' : 'Dr.',
        commentTitle: isNewsArticle 
          ? `This is wild 😂 #${i + 1}` 
          : `Brutally Honest Review #${i + 1} 🔥💀`,
        content: isNewsArticle
          ? `Can't believe this actually happened lol 😭 This is peak academia right here 💀 Someone really wrote a whole report about this 🤣`
          : `This article is a masterpiece of academic mediocrity! 😂 The author has successfully proven that they can string words together in a vaguely coherent manner. 🤡 Truly groundbreaking work in the field of stating the obvious! 💀🎪`
      });
    }
  }

  return reviewsData;
};

export { generateAIComments };
