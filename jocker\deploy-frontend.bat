@echo off
echo ========================================
echo ? Jocker Frontend Deployment Script
echo ========================================
echo.

if not exist "frontend" (
    echo ? Error: frontend directory not found!
    echo Please run this script from the jocker root directory.
    pause
    exit /b 1
)

echo ? Entering frontend directory...
cd frontend

echo ? Building frontend...
call npm run build
if %errorlevel% neq 0 (
    echo ? Build failed!
    pause
    exit /b 1
)

echo ? Deploying to server...
scp -r dist/* root@************:/var/www/jocker/jocker/frontend/dist/
if %errorlevel% neq 0 (
    echo ? Deployment failed!
    pause
    exit /b 1
)

cd ..

echo.
echo ? Frontend deployment completed successfully!
echo ? Visit: http://************:3000
echo.
pause
