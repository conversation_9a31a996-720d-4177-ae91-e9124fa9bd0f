@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    Jocker 一键部署到新服务器脚本
echo ========================================
echo.

:: 检查参数
if "%1"=="" (
    echo 使用方法: deploy-new-server.bat [服务器IP]
    echo 示例: deploy-new-server.bat *************
    pause
    exit /b 1
)

set SERVER_IP=%1
echo 目标服务器: %SERVER_IP%
echo.

:: 确认部署
set /p CONFIRM="确认要部署到服务器 %SERVER_IP% 吗? (y/N): "
if /i not "%CONFIRM%"=="y" (
    echo 部署已取消
    pause
    exit /b 0
)

echo.
echo ========================================
echo 第1步: 检查本地环境
echo ========================================

:: 检查SSH连接
echo 测试SSH连接...
ssh -o ConnectTimeout=10 root@%SERVER_IP% "echo SSH连接成功" 2>nul
if %errorlevel% neq 0 (
    echo ❌ SSH连接失败，请检查：
    echo   1. 服务器IP是否正确
    echo   2. SSH服务是否启动
    echo   3. 防火墙设置
    echo   4. SSH密钥是否配置
    pause
    exit /b 1
)

:: 检查本地文件
echo 检查本地项目文件...
if not exist "frontend\dist" (
    echo ❌ 前端构建文件不存在，正在构建...
    cd frontend
    call npm run build
    if %errorlevel% neq 0 (
        echo ❌ 前端构建失败
        pause
        exit /b 1
    )
    cd ..
)

if not exist "backend\dist" (
    echo ❌ 后端构建文件不存在，正在构建...
    cd backend
    call npm run build
    if %errorlevel% neq 0 (
        echo ❌ 后端构建失败
        pause
        exit /b 1
    )
    cd ..
)

echo ✅ 本地环境检查完成
echo.

echo ========================================
echo 第2步: 服务器环境准备
echo ========================================

echo 更新系统包...
ssh root@%SERVER_IP% "apt update && apt upgrade -y"

echo 安装必要软件...
ssh root@%SERVER_IP% "apt install -y curl wget git nginx sqlite3"

echo 安装Node.js 18...
ssh root@%SERVER_IP% "curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && apt install -y nodejs"

echo 安装PM2...
ssh root@%SERVER_IP% "npm install -g pm2"

echo 创建项目目录...
ssh root@%SERVER_IP% "mkdir -p /var/www/jocker"

echo ✅ 服务器环境准备完成
echo.

echo ========================================
echo 第3步: 上传项目文件
echo ========================================

echo 上传后端文件...
scp -r backend root@%SERVER_IP%:/var/www/jocker/
if %errorlevel% neq 0 (
    echo ❌ 后端文件上传失败
    pause
    exit /b 1
)

echo 上传前端文件...
scp -r frontend root@%SERVER_IP%:/var/www/jocker/
if %errorlevel% neq 0 (
    echo ❌ 前端文件上传失败
    pause
    exit /b 1
)

echo ✅ 项目文件上传完成
echo.

echo ========================================
echo 第4步: 服务器端配置
echo ========================================

echo 安装后端依赖...
ssh root@%SERVER_IP% "cd /var/www/jocker/backend && npm install --production"

echo 生成Prisma客户端...
ssh root@%SERVER_IP% "cd /var/www/jocker/backend && npx prisma generate"

echo 初始化数据库...
ssh root@%SERVER_IP% "cd /var/www/jocker/backend && npx prisma db push"

echo 配置PM2启动后端...
ssh root@%SERVER_IP% "cd /var/www/jocker/backend && pm2 start dist/server.js --name jocker-backend"
ssh root@%SERVER_IP% "pm2 save && pm2 startup"

echo ✅ 后端服务配置完成
echo.

echo ========================================
echo 第5步: 配置Nginx反向代理
echo ========================================

echo 创建Nginx配置文件...
ssh root@%SERVER_IP% "cat > /etc/nginx/sites-available/jocker << 'EOF'
server {
    listen 80;
    server_name %SERVER_IP%;

    # 前端静态文件
    location / {
        root /var/www/jocker/frontend/dist;
        try_files \$uri \$uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control \"public, immutable\";
        }
    }

    # API代理到后端
    location /api/ {
        proxy_pass http://localhost:5003;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
}
EOF"

echo 启用站点配置...
ssh root@%SERVER_IP% "ln -sf /etc/nginx/sites-available/jocker /etc/nginx/sites-enabled/"
ssh root@%SERVER_IP% "rm -f /etc/nginx/sites-enabled/default"

echo 测试Nginx配置...
ssh root@%SERVER_IP% "nginx -t"
if %errorlevel% neq 0 (
    echo ❌ Nginx配置测试失败
    pause
    exit /b 1
)

echo 重启Nginx...
ssh root@%SERVER_IP% "systemctl restart nginx"
ssh root@%SERVER_IP% "systemctl enable nginx"

echo ✅ Nginx配置完成
echo.

echo ========================================
echo 第6步: 创建管理员账户
echo ========================================

echo 创建默认管理员账户...
ssh root@%SERVER_IP% "cd /var/www/jocker/backend && node -e \"
const bcrypt = require('bcryptjs');
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function createAdmin() {
  try {
    const hashedPassword = await bcrypt.hash('admin123', 10);
    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'admin',
        name: 'Administrator',
        password: hashedPassword,
        role: 'ADMIN',
        status: 'ACTIVE'
      }
    });
    console.log('✅ 管理员账户创建成功');
    console.log('邮箱: <EMAIL>');
    console.log('密码: admin123');
  } catch (error) {
    if (error.code === 'P2002') {
      console.log('ℹ️ 管理员账户已存在');
    } else {
      console.error('❌ 创建管理员账户失败:', error.message);
    }
  } finally {
    await prisma.\$disconnect();
  }
}

createAdmin();
\""

echo ✅ 管理员账户配置完成
echo.

echo ========================================
echo 🎉 部署完成！
echo ========================================
echo.
echo 🌐 网站地址: http://%SERVER_IP%
echo 👤 管理员登录:
echo    邮箱: <EMAIL>
echo    密码: admin123
echo.
echo 📋 服务管理命令:
echo    查看后端状态: ssh root@%SERVER_IP% "pm2 status"
echo    重启后端: ssh root@%SERVER_IP% "pm2 restart jocker-backend"
echo    查看日志: ssh root@%SERVER_IP% "pm2 logs jocker-backend"
echo    重启Nginx: ssh root@%SERVER_IP% "systemctl restart nginx"
echo.
echo ⚠️  重要提醒:
echo    1. 请及时修改默认管理员密码
echo    2. 配置防火墙规则
echo    3. 设置SSL证书（推荐使用Let's Encrypt）
echo    4. 定期备份数据库文件: /var/www/jocker/backend/prisma/dev.db
echo.

pause
