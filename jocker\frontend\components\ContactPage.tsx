import React, { useState } from 'react';
import { SharedBackground } from './SharedBackground';

interface ContactPageProps {
  onNavigateHome: () => void;
}

export const ContactPage: React.FC<ContactPageProps> = ({ onNavigateHome }) => {
  const [language, setLanguage] = useState<'en' | 'zh'>('en');
  const [formData, setFormData] = useState({
    type: 'submission',
    name: '',
    email: '',
    affiliation: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitted, setSubmitted] = useState(false);

  const content = {
    en: {
      title: "Contact Us",
      subtitle: "Get in Touch with the Academic Circus 🎪",
      description: "Whether you want to submit groundbreaking research on rubber duck physics or report us for crimes against academia, we're here to listen. All correspondence is reviewed by our highly qualified grey parrot.",
      
      contactInfo: {
        title: "📬 Contact Information",
        items: [
          {
            icon: "📧",
            label: "Editorial Submissions",
            value: "<EMAIL>",
            note: "For papers that challenge the very fabric of reality"
          },
          {
            icon: "🤝",
            label: "Collaboration Inquiries", 
            value: "<EMAIL>",
            note: "For institutions brave enough to work with us"
          },
          {
            icon: "🚨",
            label: "Report Academic Crimes",
            value: "<EMAIL>", 
            note: "For when we've gone too far (which is often)"
          },
          {
            icon: "🎪",
            label: "General Inquiries",
            value: "<EMAIL>",
            note: "For everything else that defies categorization"
          }
        ]
      },
      
      form: {
        title: "📝 Contact Form",
        subtitle: "Send us your thoughts, complaints, or existential crises",
        types: [
          { value: 'submission', label: '📄 I want to submit a paper' },
          { value: 'collaboration', label: '🤝 I want to collaborate' },
          { value: 'complaint', label: '😤 I want to report something outrageous' },
          { value: 'praise', label: '🎉 I want to praise your work' },
          { value: 'existential', label: '🤔 I\'m having an existential crisis' }
        ],
        fields: {
          name: "Your Name",
          email: "Email Address", 
          affiliation: "Academic Affiliation (or Circus Troupe)",
          subject: "Subject",
          message: "Your Message"
        },
        placeholders: {
          name: "Dr. Serious Researcher",
          email: "<EMAIL>",
          affiliation: "University of Advanced Nonsense",
          subject: "My groundbreaking research on...",
          message: "Dear JOCKER Editorial Team..."
        },
        submit: "Send to the Parrot",
        submitting: "Consulting the Parrot..."
      },
      
      reviewProcess: {
        title: "🦜 Our Review Process",
        steps: [
          "Your message is received by our automated system (a very sophisticated rubber duck)",
          "It's then forwarded to our Chief Editorial Parrot, Professor Squawks",
          "Professor Squawks reviews it while eating sunflower seeds",
          "If deemed worthy, it's passed to our human editors (who may or may not exist)",
          "You'll receive a response within 3-5 business days (or whenever the parrot feels like it)"
        ]
      },
      
      guidelines: {
        title: "📋 Submission Guidelines",
        items: [
          "Papers must be written in Comic Sans font (just kidding, but we wouldn't be surprised)",
          "All research must include at least one rubber duck in the methodology",
          "Citations of imaginary papers are not only allowed but encouraged",
          "Peer reviewers will be randomly selected from our pool of qualified clowns",
          "Publication fees are paid in circus peanuts and academic tears"
        ]
      },
      
      disclaimer: "⚠️ Disclaimer: All emails are subject to review by Professor Squawks. The parrot has final editorial authority and may reject submissions based on insufficient entertainment value.",
      
      successMessage: "🎉 Message sent successfully! Professor Squawks will review your submission shortly. Please allow 3-5 business days for a response (or until the parrot finishes its current crossword puzzle)."
    },
    zh: {
      title: "联系我们",
      subtitle: "与学术马戏团取得联系 🎪",
      description: "无论您想提交关于橡皮鸭物理学的突破性研究，还是举报我们对学术界犯下的罪行，我们都愿意倾听。所有通信都由我们高度合格的灰鹦鹉审查。",
      
      contactInfo: {
        title: "📬 联系信息",
        items: [
          {
            icon: "📧",
            label: "编辑投稿",
            value: "<EMAIL>",
            note: "用于挑战现实结构的论文"
          },
          {
            icon: "🤝", 
            label: "合作咨询",
            value: "<EMAIL>",
            note: "给有勇气与我们合作的机构"
          },
          {
            icon: "🚨",
            label: "举报学术罪行",
            value: "<EMAIL>",
            note: "当我们做得太过分时（这经常发生）"
          },
          {
            icon: "🎪",
            label: "一般咨询",
            value: "<EMAIL>", 
            note: "其他无法归类的事情"
          }
        ]
      },
      
      form: {
        title: "📝 联系表单",
        subtitle: "向我们发送您的想法、投诉或存在主义危机",
        types: [
          { value: 'submission', label: '📄 我想投稿' },
          { value: 'collaboration', label: '🤝 我想合作' },
          { value: 'complaint', label: '😤 我想举报离谱的事' },
          { value: 'praise', label: '🎉 我想赞扬你们的工作' },
          { value: 'existential', label: '🤔 我正在经历存在主义危机' }
        ],
        fields: {
          name: "您的姓名",
          email: "邮箱地址",
          affiliation: "学术机构（或马戏团）",
          subject: "主题",
          message: "您的消息"
        },
        placeholders: {
          name: "严肃研究员博士",
          email: "<EMAIL>", 
          affiliation: "高级胡说大学",
          subject: "我关于...的突破性研究",
          message: "亲爱的JOCKER编辑团队..."
        },
        submit: "发送给鹦鹉",
        submitting: "正在咨询鹦鹉..."
      },
      
      reviewProcess: {
        title: "🦜 我们的审查流程",
        steps: [
          "您的消息由我们的自动化系统接收（一只非常精密的橡皮鸭）",
          "然后转发给我们的首席编辑鹦鹉，Squawks教授",
          "Squawks教授在吃葵花籽时审查它",
          "如果认为值得，会传递给我们的人类编辑（他们可能存在也可能不存在）",
          "您将在3-5个工作日内收到回复（或者鹦鹉想回复的时候）"
        ]
      },
      
      guidelines: {
        title: "📋 投稿指南",
        items: [
          "论文必须用Comic Sans字体书写（开玩笑的，但我们不会感到惊讶）",
          "所有研究的方法论中必须包含至少一只橡皮鸭",
          "引用虚构论文不仅被允许，而且被鼓励",
          "同行评议员将从我们的合格小丑池中随机选择",
          "发表费用用马戏团花生和学术眼泪支付"
        ]
      },
      
      disclaimer: "⚠️ 免责声明：所有邮件都需经过Squawks教授审查。鹦鹉拥有最终编辑权威，可能会因娱乐价值不足而拒绝投稿。",
      
      successMessage: "🎉 消息发送成功！Squawks教授将很快审查您的投稿。请允许3-5个工作日的回复时间（或者直到鹦鹉完成当前的填字游戏）。"
    }
  };

  const currentContent = content[language];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // 模拟提交过程
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setSubmitted(true);
    
    // 重置表单
    setTimeout(() => {
      setSubmitted(false);
      setFormData({
        type: 'submission',
        name: '',
        email: '',
        affiliation: '',
        subject: '',
        message: ''
      });
    }, 5000);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen">
      <SharedBackground />
      
      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <header className="bg-white/90 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="max-w-6xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <button
                onClick={onNavigateHome}
                className="flex items-center space-x-2 text-purple-600 hover:text-purple-800 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span className="font-medium">Back to Journal</span>
              </button>
              
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setLanguage(language === 'en' ? 'zh' : 'en')}
                  className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors"
                >
                  {language === 'en' ? '中文' : 'English'}
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 py-12">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-purple-600 mb-6">
              {currentContent.subtitle}
            </p>
            <p className="text-gray-700 leading-relaxed max-w-3xl mx-auto">
              {currentContent.description}
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {/* Contact Information */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {currentContent.contactInfo.title}
              </h2>
              
              <div className="space-y-6">
                {currentContent.contactInfo.items.map((item, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <span className="text-2xl">{item.icon}</span>
                    <div>
                      <h3 className="font-semibold text-gray-900">{item.label}</h3>
                      <a 
                        href={`mailto:${item.value}`}
                        className="text-purple-600 hover:text-purple-800 transition-colors"
                      >
                        {item.value}
                      </a>
                      <p className="text-sm text-gray-600 mt-1">{item.note}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Contact Form */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-4">
                {currentContent.form.title}
              </h2>
              <p className="text-gray-600 mb-6">
                {currentContent.form.subtitle}
              </p>

              {submitted ? (
                <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                  <div className="text-4xl mb-4">🦜</div>
                  <p className="text-green-800">
                    {currentContent.successMessage}
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-4">
                  {/* Contact Type */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Contact Type
                    </label>
                    <select
                      name="type"
                      value={formData.type}
                      onChange={handleInputChange}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      {currentContent.form.types.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Name */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {currentContent.form.fields.name}
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder={currentContent.form.placeholders.name}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>

                  {/* Email */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {currentContent.form.fields.email}
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder={currentContent.form.placeholders.email}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>

                  {/* Affiliation */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {currentContent.form.fields.affiliation}
                    </label>
                    <input
                      type="text"
                      name="affiliation"
                      value={formData.affiliation}
                      onChange={handleInputChange}
                      placeholder={currentContent.form.placeholders.affiliation}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>

                  {/* Subject */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {currentContent.form.fields.subject}
                    </label>
                    <input
                      type="text"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      placeholder={currentContent.form.placeholders.subject}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>

                  {/* Message */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {currentContent.form.fields.message}
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder={currentContent.form.placeholders.message}
                      rows={6}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      required
                    />
                  </div>

                  {/* Submit Button */}
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-purple-600 text-white py-3 px-4 rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-semibold"
                  >
                    {isSubmitting ? currentContent.form.submitting : currentContent.form.submit}
                  </button>
                </form>
              )}
            </div>
          </div>

          {/* Additional Information */}
          <div className="mt-12 grid md:grid-cols-2 gap-8">
            {/* Review Process */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {currentContent.reviewProcess.title}
              </h2>
              
              <ol className="space-y-3">
                {currentContent.reviewProcess.steps.map((step, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <span className="flex-shrink-0 w-6 h-6 bg-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </span>
                    <span className="text-gray-700">{step}</span>
                  </li>
                ))}
              </ol>
            </div>

            {/* Submission Guidelines */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">
                {currentContent.guidelines.title}
              </h2>
              
              <ul className="space-y-3">
                {currentContent.guidelines.items.map((item, index) => (
                  <li key={index} className="flex items-start space-x-3">
                    <span className="text-purple-600 mt-1">•</span>
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Disclaimer */}
          <div className="mt-12 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <p className="text-yellow-800 text-sm">
              {currentContent.disclaimer}
            </p>
          </div>
        </main>
      </div>
    </div>
  );
};
