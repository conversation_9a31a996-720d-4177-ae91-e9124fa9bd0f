{"version": "v2.8.0", "type": "minor", "title": "🔥 Post-Publication Peer Review 恶搞评论系统 & DOI自动分配", "description": "推出史上最savage的AI同行评审系统，让学术界的roast文化发扬光大！同时完善DOI系统，修复数学公式渲染，让恶搞期刊更加专业和有趣。", "changes": ["📝 Post-Publication Peer Review：全新的恶搞同行评审系统，支持用户提交评论和AI生成savage评论", "🤖 AI Savage评论生成：AI一次性生成多条brutally funny的学术评论，每条评论针对文章不同方面进行roast", "😂 Emoji丰富评论：AI评论充满emoji和学术讽刺，如'groundbreaking discovery that water is wet 😂🤡💀'", "🎭 虚假评论者身份：AI生成搞笑的学术评论者名字，如Dr. <PERSON> McRoast、Prof. <PERSON><PERSON><PERSON> Honesty等", "🔍 完整文章分析：AI评论基于文章完整内容（标题、摘要、正文）生成，而不仅仅是标题", "⚡ 批量评论生成：一次API调用生成多条评论，提高效率并确保评论间的差异化", "🗑️ 管理员删除功能：支持单个删除和批量删除评论，包含复选框选择和确认对话框", "✅ 用户评论系统：登录用户可提交评论，立即通过'元-算法伦理审查'（0.001纳秒）", "🎯 DOI自动分配：新文章自动生成唯一DOI，格式为10.1000/jocker.YYYY.MM.DD.NNNN.title-slug", "📄 DOI迁移脚本：为现有文章批量分配DOI的数据库迁移工具", "🔧 PDF下载修复：PDF文件名使用真实DOI，PDF内容显示正确的DOI而非写死格式", "🎨 期刊图标修复：解决base64图片显示为字符串的问题，图标现在正确显示", "🔄 图片重新生成修复：修复API Key获取问题，使用统一的AI配置进行图片生成", "📐 数学公式渲染增强：在保护阶段就处理块级公式样式，解决公式检测为0的问题", "🐛 摘要字段修复：修正AI评论生成时使用错误字段名导致摘要为空的问题", "🎪 评论UI优化：评论显示在文章底部而非侧边栏，避免布局变形", "🔒 权限控制完善：严格的管理员权限验证，确保只有管理员能删除评论和生成AI评论", "📊 调试日志增强：详细的评论生成和删除日志，便于问题排查", "🎨 响应式设计：评论组件适配不同屏幕尺寸，提供良好的移动端体验", "⚙️ 数据库优化：新增ArticleComment表，支持AI标识、审核状态和用户关联", "🚀 API架构完善：RESTful评论API，支持CRUD操作和批量处理", "🎭 学术幽默升级：AI评论采用'International Journal of Brutal Honesty'风格，学术roast达到新高度"]}