import{r as ya,g as xa}from"./react-vendor-DJG_os-6.js";var mn={exports:{}},tt={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ss;function va(){if(Ss)return tt;Ss=1;var t=Symbol.for("react.transitional.element"),e=Symbol.for("react.fragment");function n(s,i,a){var l=null;if(a!==void 0&&(l=""+a),i.key!==void 0&&(l=""+i.key),"key"in i){a={};for(var u in i)u!=="key"&&(a[u]=i[u])}else a=i;return i=a.ref,{$$typeof:t,type:s,key:l,ref:i!==void 0?i:null,props:a}}return tt.Fragment=e,tt.jsx=n,tt.jsxs=n,tt}var ws;function Ca(){return ws||(ws=1,mn.exports=va()),mn.exports}var d=Ca(),R=ya();const Yy=xa(R),ge="http://47.79.90.239/api";console.log("API_BASE_URL:",ge);console.log("VITE_API_BASE_URL:","http://47.79.90.239/api");async function ie(t,e={}){const n=`${ge}${t}`,i={...{headers:{"Content-Type":"application/json"}},...e};try{const l=await(await fetch(n,i)).json();if(!l.success)throw new Error(l.message||"请求失败");return l.data}catch(a){throw console.error("API 请求失败:",a),a}}async function G(t,e={}){const n=localStorage.getItem("jocker_admin_token");if(!n)throw new Error("未找到认证 Token，请先登录");const s=`${ge}${t}`,a={...{headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`}},...e};try{const u=await(await fetch(s,a)).json();if(!u.success)throw new Error(u.message||"请求失败");return u.data}catch(l){throw console.error("认证请求失败:",l),l}}const wn={getArticles:async t=>{const e=new URLSearchParams;t&&Object.entries(t).forEach(([s,i])=>{i!==void 0&&e.append(s,String(i))});const n=e.toString();return ie(`/articles${n?`?${n}`:""}`)},getArticle:async t=>ie(`/articles/${t}`),getTrending:async(t=5)=>ie(`/articles/trending?limit=${t}`),getFeatured:async(t=3)=>ie(`/articles/featured?limit=${t}`),likeArticle:async t=>ie(`/articles/${t}/like`,{method:"POST"}),createArticle:async t=>ie("/articles",{method:"POST",body:JSON.stringify(t)}),updateArticle:async(t,e)=>ie(`/articles/${t}`,{method:"PUT",body:JSON.stringify(e)}),deleteArticle:async t=>ie(`/articles/${t}`,{method:"DELETE"}),getComments:async t=>ie(`/articles/${t}/comments`),submitComment:async(t,e)=>G(`/articles/${t}/comments`,{method:"POST",body:JSON.stringify(e)})},_a={generateReview:async t=>ie("/ai/generate-review",{method:"POST",body:JSON.stringify(t)}),testConnection:async()=>ie("/ai/test-connection",{method:"GET"})},An={register:async t=>ie("/auth/register",{method:"POST",body:JSON.stringify(t)}),login:async t=>ie("/auth/login",{method:"POST",body:JSON.stringify(t)}),verifyToken:async()=>G("/auth/verify"),getProfile:async()=>G("/auth/profile")},oe={getArticles:async t=>{const e=new URLSearchParams;t&&Object.entries(t).forEach(([s,i])=>{i!==void 0&&e.append(s,String(i))});const n=e.toString();return G(`/admin/articles${n?`?${n}`:""}`)},getAIApiKey:async()=>G("/admin/ai-key"),saveArticleContent:async(t,e)=>G(`/admin/articles/${t}/content`,{method:"PUT",body:JSON.stringify({content:e})}),getArticleFigures:async t=>G(`/admin/articles/${t}/figures`),regenerateFigure:async t=>G(`/admin/figures/${t}/regenerate`,{method:"POST"}),saveFigure:async t=>G("/admin/figures",{method:"POST",body:JSON.stringify(t)}),clearArticleFigures:async t=>G(`/admin/articles/${t}/figures`,{method:"DELETE"}),uploadFounderAvatar:async t=>G("/admin/founder-avatar",{method:"POST",body:JSON.stringify(t)}),getFounderAvatar:async()=>ie("/admin/founder-avatar"),uploadAdvertisementCover:async t=>G("/admin/advertisement-cover",{method:"POST",body:JSON.stringify(t)}),getAdvertisementCover:async()=>ie("/admin/advertisement-cover"),setAIConfig:async t=>G("/admin/ai-config",{method:"POST",body:JSON.stringify(t)}),getAIConfig:async()=>G("/admin/ai-config"),getAIConfigForClient:async()=>G("/admin/ai-config-for-client"),changePassword:async t=>G("/admin/change-password",{method:"POST",body:JSON.stringify(t)}),setGeminiKey:async t=>G("/admin/gemini-key",{method:"POST",body:JSON.stringify(t)}),getGeminiKey:async()=>G("/admin/gemini-key"),getAllUsers:async()=>G("/admin/users"),updateUserStatus:async(t,e)=>G(`/admin/users/${t}/status`,{method:"PUT",body:JSON.stringify({status:e})}),updateUserRole:async(t,e)=>G(`/admin/users/${t}/role`,{method:"PUT",body:JSON.stringify({role:e})}),deleteUser:async t=>G(`/admin/users/${t}`,{method:"DELETE"}),batchDeleteArticles:async t=>G("/articles/batch",{method:"DELETE",body:JSON.stringify({articleIds:t})}),updateArticleViews:async(t,e)=>G(`/articles/${t}/views`,{method:"PUT",body:JSON.stringify({views:e})}),setJournalIcon:async t=>G("/admin/journal-icon",{method:"POST",body:JSON.stringify({icon:t})}),getJournalIcon:async()=>{const e=await(await fetch(`${ge}/admin/journal-icon`)).json();if(!e.success)throw new Error(e.message||"获取期刊图标失败");return e.data},uploadMerchandiseImage:async t=>G("/admin/merchandise-image",{method:"POST",body:JSON.stringify(t)}),getMerchandiseImages:async()=>{const e=await(await fetch(`${ge}/admin/merchandise-images`)).json();if(!e.success)throw new Error(e.message||"获取周边商品图片失败");return e.data},recordMerchandiseRequest:async(t,e)=>{const s=await(await fetch(`${ge}/admin/merchandise-request`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fingerprint:t,itemId:e})})).json();if(!s.success)throw new Error(s.message||"记录商品请求失败");return s.data},getMerchandiseRequestStats:async()=>{const e=await(await fetch(`${ge}/admin/merchandise-request-stats`)).json();if(!e.success)throw new Error(e.message||"获取商品请求统计失败");return e.data}},Ta={getArticleAudio:async t=>{const n=await(await fetch(`${ge}/audio/articles/${t}`)).json();if(!n.success)throw new Error(n.message||"获取音频信息失败");return n.data},deleteArticleAudio:async t=>G(`/audio/articles/${t}`,{method:"DELETE"}),updateArticleAudio:async(t,e)=>G(`/audio/articles/${t}`,{method:"PUT",body:JSON.stringify(e)})},Dt={getSystemStats:async()=>G("/system/stats"),getStorageAnalysis:async()=>G("/system/storage"),cleanupStorage:async()=>G("/system/cleanup",{method:"POST"})},qe={getSystemLogs:async t=>{const e=new URLSearchParams;t!=null&&t.page&&e.append("page",t.page.toString()),t!=null&&t.limit&&e.append("limit",t.limit.toString()),t!=null&&t.type&&e.append("type",t.type);const n=`/logs${e.toString()?"?"+e.toString():""}`,i=await(await fetch(`${ge}${n}`)).json();if(!i.success)throw new Error(i.message||"获取日志失败");return i.data},createSystemLog:async t=>G("/logs",{method:"POST",body:JSON.stringify(t)}),updateSystemLog:async(t,e)=>G(`/logs/${t}`,{method:"PUT",body:JSON.stringify(e)}),deleteSystemLog:async t=>G(`/logs/${t}`,{method:"DELETE"}),getLogStats:async()=>{const e=await(await fetch(`${ge}/logs/stats`)).json();if(!e.success)throw new Error(e.message||"获取日志统计失败");return e.data},importHistoryLogs:async t=>G("/logs/import",{method:"POST",body:JSON.stringify({logs:t})}),uploadWebsiteCover:async t=>G("/admin/website-cover",{method:"POST",body:JSON.stringify(t)}),getWebsiteCover:async()=>{const e=await(await fetch(`${ge}/admin/website-cover`)).json();if(!e.success)throw new Error(e.message||"获取网站封面失败");return e.data}},Sa=async()=>ie("/health"),wa={...wn,..._a,...An,...oe,...Ta,...Dt,...qe,healthCheck:Sa};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let Aa,Ea;function Ia(){return{geminiUrl:Aa,vertexUrl:Ea}}function Na(t,e,n){var s,i,a;if(!(!((s=t.httpOptions)===null||s===void 0)&&s.baseUrl)){const l=Ia();return t.vertexai?(i=l.vertexUrl)!==null&&i!==void 0?i:e:(a=l.geminiUrl)!==null&&a!==void 0?a:n}return t.httpOptions.baseUrl}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Ue{}function F(t,e){const n=/\{([^}]+)\}/g;return t.replace(n,(s,i)=>{if(Object.prototype.hasOwnProperty.call(e,i)){const a=e[i];return a!=null?String(a):""}else throw new Error(`Key '${i}' not found in valueMap.`)})}function r(t,e,n){for(let a=0;a<e.length-1;a++){const l=e[a];if(l.endsWith("[]")){const u=l.slice(0,-2);if(!(u in t))if(Array.isArray(n))t[u]=Array.from({length:n.length},()=>({}));else throw new Error(`Value must be a list given an array path ${l}`);if(Array.isArray(t[u])){const f=t[u];if(Array.isArray(n))for(let c=0;c<f.length;c++){const m=f[c];r(m,e.slice(a+1),n[c])}else for(const c of f)r(c,e.slice(a+1),n)}return}else if(l.endsWith("[0]")){const u=l.slice(0,-3);u in t||(t[u]=[{}]);const f=t[u];r(f[0],e.slice(a+1),n);return}(!t[l]||typeof t[l]!="object")&&(t[l]={}),t=t[l]}const s=e[e.length-1],i=t[s];if(i!==void 0){if(!n||typeof n=="object"&&Object.keys(n).length===0||n===i)return;if(typeof i=="object"&&typeof n=="object"&&i!==null&&n!==null)Object.assign(i,n);else throw new Error(`Cannot set value for an existing key. Key: ${s}`)}else t[s]=n}function o(t,e){try{if(e.length===1&&e[0]==="_self")return t;for(let n=0;n<e.length;n++){if(typeof t!="object"||t===null)return;const s=e[n];if(s.endsWith("[]")){const i=s.slice(0,-2);if(i in t){const a=t[i];return Array.isArray(a)?a.map(l=>o(l,e.slice(n+1))):void 0}else return}else t=t[s]}return t}catch(n){if(n instanceof TypeError)return;throw n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var As;(function(t){t.OUTCOME_UNSPECIFIED="OUTCOME_UNSPECIFIED",t.OUTCOME_OK="OUTCOME_OK",t.OUTCOME_FAILED="OUTCOME_FAILED",t.OUTCOME_DEADLINE_EXCEEDED="OUTCOME_DEADLINE_EXCEEDED"})(As||(As={}));var Es;(function(t){t.LANGUAGE_UNSPECIFIED="LANGUAGE_UNSPECIFIED",t.PYTHON="PYTHON"})(Es||(Es={}));var Ne;(function(t){t.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",t.STRING="STRING",t.NUMBER="NUMBER",t.INTEGER="INTEGER",t.BOOLEAN="BOOLEAN",t.ARRAY="ARRAY",t.OBJECT="OBJECT",t.NULL="NULL"})(Ne||(Ne={}));var Is;(function(t){t.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",t.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",t.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",t.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",t.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",t.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY",t.HARM_CATEGORY_IMAGE_HATE="HARM_CATEGORY_IMAGE_HATE",t.HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT="HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT",t.HARM_CATEGORY_IMAGE_HARASSMENT="HARM_CATEGORY_IMAGE_HARASSMENT",t.HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT="HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT"})(Is||(Is={}));var Ns;(function(t){t.HARM_BLOCK_METHOD_UNSPECIFIED="HARM_BLOCK_METHOD_UNSPECIFIED",t.SEVERITY="SEVERITY",t.PROBABILITY="PROBABILITY"})(Ns||(Ns={}));var bs;(function(t){t.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE",t.OFF="OFF"})(bs||(bs={}));var Ms;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(Ms||(Ms={}));var Ps;(function(t){t.AUTH_TYPE_UNSPECIFIED="AUTH_TYPE_UNSPECIFIED",t.NO_AUTH="NO_AUTH",t.API_KEY_AUTH="API_KEY_AUTH",t.HTTP_BASIC_AUTH="HTTP_BASIC_AUTH",t.GOOGLE_SERVICE_ACCOUNT_AUTH="GOOGLE_SERVICE_ACCOUNT_AUTH",t.OAUTH="OAUTH",t.OIDC_AUTH="OIDC_AUTH"})(Ps||(Ps={}));var Rs;(function(t){t.API_SPEC_UNSPECIFIED="API_SPEC_UNSPECIFIED",t.SIMPLE_SEARCH="SIMPLE_SEARCH",t.ELASTIC_SEARCH="ELASTIC_SEARCH"})(Rs||(Rs={}));var ks;(function(t){t.ENVIRONMENT_UNSPECIFIED="ENVIRONMENT_UNSPECIFIED",t.ENVIRONMENT_BROWSER="ENVIRONMENT_BROWSER"})(ks||(ks={}));var Ds;(function(t){t.URL_RETRIEVAL_STATUS_UNSPECIFIED="URL_RETRIEVAL_STATUS_UNSPECIFIED",t.URL_RETRIEVAL_STATUS_SUCCESS="URL_RETRIEVAL_STATUS_SUCCESS",t.URL_RETRIEVAL_STATUS_ERROR="URL_RETRIEVAL_STATUS_ERROR"})(Ds||(Ds={}));var Ls;(function(t){t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.LANGUAGE="LANGUAGE",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.SPII="SPII",t.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",t.IMAGE_SAFETY="IMAGE_SAFETY",t.UNEXPECTED_TOOL_CALL="UNEXPECTED_TOOL_CALL"})(Ls||(Ls={}));var Fs;(function(t){t.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",t.NEGLIGIBLE="NEGLIGIBLE",t.LOW="LOW",t.MEDIUM="MEDIUM",t.HIGH="HIGH"})(Fs||(Fs={}));var Us;(function(t){t.HARM_SEVERITY_UNSPECIFIED="HARM_SEVERITY_UNSPECIFIED",t.HARM_SEVERITY_NEGLIGIBLE="HARM_SEVERITY_NEGLIGIBLE",t.HARM_SEVERITY_LOW="HARM_SEVERITY_LOW",t.HARM_SEVERITY_MEDIUM="HARM_SEVERITY_MEDIUM",t.HARM_SEVERITY_HIGH="HARM_SEVERITY_HIGH"})(Us||(Us={}));var $s;(function(t){t.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",t.SAFETY="SAFETY",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.IMAGE_SAFETY="IMAGE_SAFETY"})($s||($s={}));var Vs;(function(t){t.TRAFFIC_TYPE_UNSPECIFIED="TRAFFIC_TYPE_UNSPECIFIED",t.ON_DEMAND="ON_DEMAND",t.PROVISIONED_THROUGHPUT="PROVISIONED_THROUGHPUT"})(Vs||(Vs={}));var Kt;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.AUDIO="AUDIO"})(Kt||(Kt={}));var Bs;(function(t){t.MEDIA_RESOLUTION_UNSPECIFIED="MEDIA_RESOLUTION_UNSPECIFIED",t.MEDIA_RESOLUTION_LOW="MEDIA_RESOLUTION_LOW",t.MEDIA_RESOLUTION_MEDIUM="MEDIA_RESOLUTION_MEDIUM",t.MEDIA_RESOLUTION_HIGH="MEDIA_RESOLUTION_HIGH"})(Bs||(Bs={}));var En;(function(t){t.JOB_STATE_UNSPECIFIED="JOB_STATE_UNSPECIFIED",t.JOB_STATE_QUEUED="JOB_STATE_QUEUED",t.JOB_STATE_PENDING="JOB_STATE_PENDING",t.JOB_STATE_RUNNING="JOB_STATE_RUNNING",t.JOB_STATE_SUCCEEDED="JOB_STATE_SUCCEEDED",t.JOB_STATE_FAILED="JOB_STATE_FAILED",t.JOB_STATE_CANCELLING="JOB_STATE_CANCELLING",t.JOB_STATE_CANCELLED="JOB_STATE_CANCELLED",t.JOB_STATE_PAUSED="JOB_STATE_PAUSED",t.JOB_STATE_EXPIRED="JOB_STATE_EXPIRED",t.JOB_STATE_UPDATING="JOB_STATE_UPDATING",t.JOB_STATE_PARTIALLY_SUCCEEDED="JOB_STATE_PARTIALLY_SUCCEEDED"})(En||(En={}));var qs;(function(t){t.ADAPTER_SIZE_UNSPECIFIED="ADAPTER_SIZE_UNSPECIFIED",t.ADAPTER_SIZE_ONE="ADAPTER_SIZE_ONE",t.ADAPTER_SIZE_TWO="ADAPTER_SIZE_TWO",t.ADAPTER_SIZE_FOUR="ADAPTER_SIZE_FOUR",t.ADAPTER_SIZE_EIGHT="ADAPTER_SIZE_EIGHT",t.ADAPTER_SIZE_SIXTEEN="ADAPTER_SIZE_SIXTEEN",t.ADAPTER_SIZE_THIRTY_TWO="ADAPTER_SIZE_THIRTY_TWO"})(qs||(qs={}));var Gs;(function(t){t.FEATURE_SELECTION_PREFERENCE_UNSPECIFIED="FEATURE_SELECTION_PREFERENCE_UNSPECIFIED",t.PRIORITIZE_QUALITY="PRIORITIZE_QUALITY",t.BALANCED="BALANCED",t.PRIORITIZE_COST="PRIORITIZE_COST"})(Gs||(Gs={}));var Js;(function(t){t.UNSPECIFIED="UNSPECIFIED",t.BLOCKING="BLOCKING",t.NON_BLOCKING="NON_BLOCKING"})(Js||(Js={}));var Os;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(Os||(Os={}));var Hs;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.AUTO="AUTO",t.ANY="ANY",t.NONE="NONE"})(Hs||(Hs={}));var Ws;(function(t){t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE"})(Ws||(Ws={}));var Ks;(function(t){t.DONT_ALLOW="DONT_ALLOW",t.ALLOW_ADULT="ALLOW_ADULT",t.ALLOW_ALL="ALLOW_ALL"})(Ks||(Ks={}));var Ys;(function(t){t.auto="auto",t.en="en",t.ja="ja",t.ko="ko",t.hi="hi",t.zh="zh",t.pt="pt",t.es="es"})(Ys||(Ys={}));var zs;(function(t){t.MASK_MODE_DEFAULT="MASK_MODE_DEFAULT",t.MASK_MODE_USER_PROVIDED="MASK_MODE_USER_PROVIDED",t.MASK_MODE_BACKGROUND="MASK_MODE_BACKGROUND",t.MASK_MODE_FOREGROUND="MASK_MODE_FOREGROUND",t.MASK_MODE_SEMANTIC="MASK_MODE_SEMANTIC"})(zs||(zs={}));var Xs;(function(t){t.CONTROL_TYPE_DEFAULT="CONTROL_TYPE_DEFAULT",t.CONTROL_TYPE_CANNY="CONTROL_TYPE_CANNY",t.CONTROL_TYPE_SCRIBBLE="CONTROL_TYPE_SCRIBBLE",t.CONTROL_TYPE_FACE_MESH="CONTROL_TYPE_FACE_MESH"})(Xs||(Xs={}));var Qs;(function(t){t.SUBJECT_TYPE_DEFAULT="SUBJECT_TYPE_DEFAULT",t.SUBJECT_TYPE_PERSON="SUBJECT_TYPE_PERSON",t.SUBJECT_TYPE_ANIMAL="SUBJECT_TYPE_ANIMAL",t.SUBJECT_TYPE_PRODUCT="SUBJECT_TYPE_PRODUCT"})(Qs||(Qs={}));var Zs;(function(t){t.EDIT_MODE_DEFAULT="EDIT_MODE_DEFAULT",t.EDIT_MODE_INPAINT_REMOVAL="EDIT_MODE_INPAINT_REMOVAL",t.EDIT_MODE_INPAINT_INSERTION="EDIT_MODE_INPAINT_INSERTION",t.EDIT_MODE_OUTPAINT="EDIT_MODE_OUTPAINT",t.EDIT_MODE_CONTROLLED_EDITING="EDIT_MODE_CONTROLLED_EDITING",t.EDIT_MODE_STYLE="EDIT_MODE_STYLE",t.EDIT_MODE_BGSWAP="EDIT_MODE_BGSWAP",t.EDIT_MODE_PRODUCT_IMAGE="EDIT_MODE_PRODUCT_IMAGE"})(Zs||(Zs={}));var js;(function(t){t.OPTIMIZED="OPTIMIZED",t.LOSSLESS="LOSSLESS"})(js||(js={}));var ei;(function(t){t.STATE_UNSPECIFIED="STATE_UNSPECIFIED",t.PROCESSING="PROCESSING",t.ACTIVE="ACTIVE",t.FAILED="FAILED"})(ei||(ei={}));var ti;(function(t){t.SOURCE_UNSPECIFIED="SOURCE_UNSPECIFIED",t.UPLOADED="UPLOADED",t.GENERATED="GENERATED"})(ti||(ti={}));var ni;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.VIDEO="VIDEO",t.AUDIO="AUDIO",t.DOCUMENT="DOCUMENT"})(ni||(ni={}));var si;(function(t){t.START_SENSITIVITY_UNSPECIFIED="START_SENSITIVITY_UNSPECIFIED",t.START_SENSITIVITY_HIGH="START_SENSITIVITY_HIGH",t.START_SENSITIVITY_LOW="START_SENSITIVITY_LOW"})(si||(si={}));var ii;(function(t){t.END_SENSITIVITY_UNSPECIFIED="END_SENSITIVITY_UNSPECIFIED",t.END_SENSITIVITY_HIGH="END_SENSITIVITY_HIGH",t.END_SENSITIVITY_LOW="END_SENSITIVITY_LOW"})(ii||(ii={}));var oi;(function(t){t.ACTIVITY_HANDLING_UNSPECIFIED="ACTIVITY_HANDLING_UNSPECIFIED",t.START_OF_ACTIVITY_INTERRUPTS="START_OF_ACTIVITY_INTERRUPTS",t.NO_INTERRUPTION="NO_INTERRUPTION"})(oi||(oi={}));var ri;(function(t){t.TURN_COVERAGE_UNSPECIFIED="TURN_COVERAGE_UNSPECIFIED",t.TURN_INCLUDES_ONLY_ACTIVITY="TURN_INCLUDES_ONLY_ACTIVITY",t.TURN_INCLUDES_ALL_INPUT="TURN_INCLUDES_ALL_INPUT"})(ri||(ri={}));var ai;(function(t){t.SCHEDULING_UNSPECIFIED="SCHEDULING_UNSPECIFIED",t.SILENT="SILENT",t.WHEN_IDLE="WHEN_IDLE",t.INTERRUPT="INTERRUPT"})(ai||(ai={}));var li;(function(t){t.SCALE_UNSPECIFIED="SCALE_UNSPECIFIED",t.C_MAJOR_A_MINOR="C_MAJOR_A_MINOR",t.D_FLAT_MAJOR_B_FLAT_MINOR="D_FLAT_MAJOR_B_FLAT_MINOR",t.D_MAJOR_B_MINOR="D_MAJOR_B_MINOR",t.E_FLAT_MAJOR_C_MINOR="E_FLAT_MAJOR_C_MINOR",t.E_MAJOR_D_FLAT_MINOR="E_MAJOR_D_FLAT_MINOR",t.F_MAJOR_D_MINOR="F_MAJOR_D_MINOR",t.G_FLAT_MAJOR_E_FLAT_MINOR="G_FLAT_MAJOR_E_FLAT_MINOR",t.G_MAJOR_E_MINOR="G_MAJOR_E_MINOR",t.A_FLAT_MAJOR_F_MINOR="A_FLAT_MAJOR_F_MINOR",t.A_MAJOR_G_FLAT_MINOR="A_MAJOR_G_FLAT_MINOR",t.B_FLAT_MAJOR_G_MINOR="B_FLAT_MAJOR_G_MINOR",t.B_MAJOR_A_FLAT_MINOR="B_MAJOR_A_FLAT_MINOR"})(li||(li={}));var He;(function(t){t.PLAYBACK_CONTROL_UNSPECIFIED="PLAYBACK_CONTROL_UNSPECIFIED",t.PLAY="PLAY",t.PAUSE="PAUSE",t.STOP="STOP",t.RESET_CONTEXT="RESET_CONTEXT"})(He||(He={}));class In{constructor(e){const n={};for(const s of e.headers.entries())n[s[0]]=s[1];this.headers=n,this.responseInternal=e}json(){return this.responseInternal.json()}}class nt{get text(){var e,n,s,i,a,l,u,f;if(((i=(s=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||s===void 0?void 0:s.parts)===null||i===void 0?void 0:i.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning text from the first one.");let c="",m=!1;const h=[];for(const p of(f=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)!==null&&f!==void 0?f:[]){for(const[g,y]of Object.entries(p))g!=="text"&&g!=="thought"&&(y!==null||y!==void 0)&&h.push(g);if(typeof p.text=="string"){if(typeof p.thought=="boolean"&&p.thought)continue;m=!0,c+=p.text}}return h.length>0&&console.warn(`there are non-text parts ${h} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),m?c:void 0}get data(){var e,n,s,i,a,l,u,f;if(((i=(s=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||s===void 0?void 0:s.parts)===null||i===void 0?void 0:i.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning data from the first one.");let c="";const m=[];for(const h of(f=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)!==null&&f!==void 0?f:[]){for(const[p,g]of Object.entries(h))p!=="inlineData"&&(g!==null||g!==void 0)&&m.push(p);h.inlineData&&typeof h.inlineData.data=="string"&&(c+=atob(h.inlineData.data))}return m.length>0&&console.warn(`there are non-data parts ${m} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),c.length>0?btoa(c):void 0}get functionCalls(){var e,n,s,i,a,l,u,f;if(((i=(s=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||s===void 0?void 0:s.parts)===null||i===void 0?void 0:i.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning function calls from the first one.");const c=(f=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)===null||f===void 0?void 0:f.filter(m=>m.functionCall).map(m=>m.functionCall).filter(m=>m!==void 0);if((c==null?void 0:c.length)!==0)return c}get executableCode(){var e,n,s,i,a,l,u,f,c;if(((i=(s=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||s===void 0?void 0:s.parts)===null||i===void 0?void 0:i.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning executable code from the first one.");const m=(f=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)===null||f===void 0?void 0:f.filter(h=>h.executableCode).map(h=>h.executableCode).filter(h=>h!==void 0);if((m==null?void 0:m.length)!==0)return(c=m==null?void 0:m[0])===null||c===void 0?void 0:c.code}get codeExecutionResult(){var e,n,s,i,a,l,u,f,c;if(((i=(s=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||s===void 0?void 0:s.parts)===null||i===void 0?void 0:i.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning code execution result from the first one.");const m=(f=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)===null||f===void 0?void 0:f.filter(h=>h.codeExecutionResult).map(h=>h.codeExecutionResult).filter(h=>h!==void 0);if((m==null?void 0:m.length)!==0)return(c=m==null?void 0:m[0])===null||c===void 0?void 0:c.output}}class ui{}class ci{}class ba{}class Ma{}class di{}class fi{}class mi{}class Pa{}class hi{}class pi{}class gi{}class Ra{}class ka{}class Da{}class yi{}class La{get text(){var e,n,s;let i="",a=!1;const l=[];for(const u of(s=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&s!==void 0?s:[]){for(const[f,c]of Object.entries(u))f!=="text"&&f!=="thought"&&c!==null&&l.push(f);if(typeof u.text=="string"){if(typeof u.thought=="boolean"&&u.thought)continue;a=!0,i+=u.text}}return l.length>0&&console.warn(`there are non-text parts ${l} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),a?i:void 0}get data(){var e,n,s;let i="";const a=[];for(const l of(s=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&s!==void 0?s:[]){for(const[u,f]of Object.entries(l))u!=="inlineData"&&f!==null&&a.push(u);l.inlineData&&typeof l.inlineData.data=="string"&&(i+=atob(l.inlineData.data))}return a.length>0&&console.warn(`there are non-data parts ${a} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),i.length>0?btoa(i):void 0}}class Fa{get audioChunk(){if(this.serverContent&&this.serverContent.audioChunks&&this.serverContent.audioChunks.length>0)return this.serverContent.audioChunks[0]}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function J(t,e){if(!e||typeof e!="string")throw new Error("model is required and must be a string");if(t.isVertexAI()){if(e.startsWith("publishers/")||e.startsWith("projects/")||e.startsWith("models/"))return e;if(e.indexOf("/")>=0){const n=e.split("/",2);return`publishers/${n[0]}/models/${n[1]}`}else return`publishers/google/models/${e}`}else return e.startsWith("models/")||e.startsWith("tunedModels/")?e:`models/${e}`}function yo(t,e){const n=J(t,e);return n?n.startsWith("publishers/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}`:n.startsWith("models/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/publishers/google/${n}`:n:""}function xo(t){return Array.isArray(t)?t.map(e=>Yt(e)):[Yt(t)]}function Yt(t){if(typeof t=="object"&&t!==null)return t;throw new Error(`Could not parse input as Blob. Unsupported blob type: ${typeof t}`)}function vo(t){const e=Yt(t);if(e.mimeType&&e.mimeType.startsWith("image/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function Co(t){const e=Yt(t);if(e.mimeType&&e.mimeType.startsWith("audio/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function xi(t){if(t==null)throw new Error("PartUnion is required");if(typeof t=="object")return t;if(typeof t=="string")return{text:t};throw new Error(`Unsupported part type: ${typeof t}`)}function _o(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("PartListUnion is required");return Array.isArray(t)?t.map(e=>xi(e)):[xi(t)]}function Nn(t){return t!=null&&typeof t=="object"&&"parts"in t&&Array.isArray(t.parts)}function vi(t){return t!=null&&typeof t=="object"&&"functionCall"in t}function Ci(t){return t!=null&&typeof t=="object"&&"functionResponse"in t}function re(t){if(t==null)throw new Error("ContentUnion is required");return Nn(t)?t:{role:"user",parts:_o(t)}}function To(t,e){if(!e)return[];if(t.isVertexAI()&&Array.isArray(e))return e.flatMap(n=>{const s=re(n);return s.parts&&s.parts.length>0&&s.parts[0].text!==void 0?[s.parts[0].text]:[]});if(t.isVertexAI()){const n=re(e);return n.parts&&n.parts.length>0&&n.parts[0].text!==void 0?[n.parts[0].text]:[]}return Array.isArray(e)?e.map(n=>re(n)):[re(e)]}function he(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("contents are required");if(!Array.isArray(t)){if(vi(t)||Ci(t))throw new Error("To specify functionCall or functionResponse parts, please wrap them in a Content object, specifying the role for them");return[re(t)]}const e=[],n=[],s=Nn(t[0]);for(const i of t){const a=Nn(i);if(a!=s)throw new Error("Mixing Content and Parts is not supported, please group the parts into a the appropriate Content objects and specify the roles for them");if(a)e.push(i);else{if(vi(i)||Ci(i))throw new Error("To specify functionCall or functionResponse parts, please wrap them, and any other parts, in Content objects as appropriate, specifying the role for them");n.push(i)}}return s||e.push({role:"user",parts:_o(n)}),e}function Ua(t,e){t.includes("null")&&(e.nullable=!0);const n=t.filter(s=>s!=="null");if(n.length===1)e.type=Object.values(Ne).includes(n[0].toUpperCase())?n[0].toUpperCase():Ne.TYPE_UNSPECIFIED;else{e.anyOf=[];for(const s of n)e.anyOf.push({type:Object.values(Ne).includes(s.toUpperCase())?s.toUpperCase():Ne.TYPE_UNSPECIFIED})}}function Ke(t){const e={},n=["items"],s=["anyOf"],i=["properties"];if(t.type&&t.anyOf)throw new Error("type and anyOf cannot be both populated.");const a=t.anyOf;a!=null&&a.length==2&&(a[0].type==="null"?(e.nullable=!0,t=a[1]):a[1].type==="null"&&(e.nullable=!0,t=a[0])),t.type instanceof Array&&Ua(t.type,e);for(const[l,u]of Object.entries(t))if(u!=null)if(l=="type"){if(u==="null")throw new Error("type: null can not be the only possible type for the field.");if(u instanceof Array)continue;e.type=Object.values(Ne).includes(u.toUpperCase())?u.toUpperCase():Ne.TYPE_UNSPECIFIED}else if(n.includes(l))e[l]=Ke(u);else if(s.includes(l)){const f=[];for(const c of u){if(c.type=="null"){e.nullable=!0;continue}f.push(Ke(c))}e[l]=f}else if(i.includes(l)){const f={};for(const[c,m]of Object.entries(u))f[c]=Ke(m);e[l]=f}else{if(l==="additionalProperties")continue;e[l]=u}return e}function Xn(t){return Ke(t)}function Qn(t){if(typeof t=="object")return t;if(typeof t=="string")return{voiceConfig:{prebuiltVoiceConfig:{voiceName:t}}};throw new Error(`Unsupported speechConfig type: ${typeof t}`)}function Zn(t){if("multiSpeakerVoiceConfig"in t)throw new Error("multiSpeakerVoiceConfig is not supported in the live API.");return t}function ze(t){if(t.functionDeclarations)for(const e of t.functionDeclarations)e.parameters&&(Object.keys(e.parameters).includes("$schema")?e.parametersJsonSchema||(e.parametersJsonSchema=e.parameters,delete e.parameters):e.parameters=Ke(e.parameters)),e.response&&(Object.keys(e.response).includes("$schema")?e.responseJsonSchema||(e.responseJsonSchema=e.response,delete e.response):e.response=Ke(e.response));return t}function Xe(t){if(t==null)throw new Error("tools is required");if(!Array.isArray(t))throw new Error("tools is required and must be an array of Tools");const e=[];for(const n of t)e.push(n);return e}function $a(t,e,n,s=1){const i=!e.startsWith(`${n}/`)&&e.split("/").length===s;return t.isVertexAI()?e.startsWith("projects/")?e:e.startsWith("locations/")?`projects/${t.getProject()}/${e}`:e.startsWith(`${n}/`)?`projects/${t.getProject()}/locations/${t.getLocation()}/${e}`:i?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}/${e}`:e:i?`${n}/${e}`:e}function Se(t,e){if(typeof e!="string")throw new Error("name must be a string");return $a(t,e,"cachedContents")}function So(t){switch(t){case"STATE_UNSPECIFIED":return"JOB_STATE_UNSPECIFIED";case"CREATING":return"JOB_STATE_RUNNING";case"ACTIVE":return"JOB_STATE_SUCCEEDED";case"FAILED":return"JOB_STATE_FAILED";default:return t}}function we(t){if(typeof t!="string")throw new Error("fromImageBytes must be a string");return t}function Va(t){return t!=null&&typeof t=="object"&&"name"in t}function Ba(t){return t!=null&&typeof t=="object"&&"video"in t}function qa(t){return t!=null&&typeof t=="object"&&"uri"in t}function wo(t){var e;let n;if(Va(t)&&(n=t.name),!(qa(t)&&(n=t.uri,n===void 0))&&!(Ba(t)&&(n=(e=t.video)===null||e===void 0?void 0:e.uri,n===void 0))){if(typeof t=="string"&&(n=t),n===void 0)throw new Error("Could not extract file name from the provided input.");if(n.startsWith("https://")){const i=n.split("files/")[1].match(/[a-z0-9]+/);if(i===null)throw new Error(`Could not extract file name from URI ${n}`);n=i[0]}else n.startsWith("files/")&&(n=n.split("files/")[1]);return n}}function Ao(t,e){let n;return t.isVertexAI()?n=e?"publishers/google/models":"models":n=e?"models":"tunedModels",n}function Eo(t){for(const e of["models","tunedModels","publisherModels"])if(Ga(t,e))return t[e];return[]}function Ga(t,e){return t!==null&&typeof t=="object"&&e in t}function Ja(t,e={}){const n=t,s={name:n.name,description:n.description,parametersJsonSchema:n.inputSchema};return e.behavior&&(s.behavior=e.behavior),{functionDeclarations:[s]}}function Oa(t,e={}){const n=[],s=new Set;for(const i of t){const a=i.name;if(s.has(a))throw new Error(`Duplicate function name ${a} found in MCP tools. Please ensure function names are unique.`);s.add(a);const l=Ja(i,e);l.functionDeclarations&&n.push(...l.functionDeclarations)}return{functionDeclarations:n}}function Io(t,e){if(typeof e!="string"&&!Array.isArray(e)){if(t&&t.isVertexAI()){if(e.gcsUri&&e.bigqueryUri)throw new Error("Only one of `gcsUri` or `bigqueryUri` can be set.");if(!e.gcsUri&&!e.bigqueryUri)throw new Error("One of `gcsUri` or `bigqueryUri` must be set.")}else{if(e.inlinedRequests&&e.fileName)throw new Error("Only one of `inlinedRequests` or `fileName` can be set.");if(!e.inlinedRequests&&!e.fileName)throw new Error("One of `inlinedRequests` or `fileName` must be set.")}return e}else{if(Array.isArray(e))return{inlinedRequests:e};if(typeof e=="string"){if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:[e]};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};if(e.startsWith("files/"))return{fileName:e}}}throw new Error(`Unsupported source: ${e}`)}function Ha(t){const e=t;if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:e};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};throw new Error(`Unsupported destination: ${e}`)}function Qe(t,e){const n=e;if(!t.isVertexAI()){if(/batches\/[^/]+$/.test(n))return n.split("/").pop();throw new Error(`Invalid batch job name: ${n}.`)}if(/^projects\/[^/]+\/locations\/[^/]+\/batchPredictionJobs\/[^/]+$/.test(n))return n.split("/").pop();if(/^\d+$/.test(n))return n;throw new Error(`Invalid batch job name: ${n}.`)}function No(t){const e=t;return e==="BATCH_STATE_UNSPECIFIED"?"JOB_STATE_UNSPECIFIED":e==="BATCH_STATE_PENDING"?"JOB_STATE_PENDING":e==="BATCH_STATE_SUCCEEDED"?"JOB_STATE_SUCCEEDED":e==="BATCH_STATE_FAILED"?"JOB_STATE_FAILED":e==="BATCH_STATE_CANCELLED"?"JOB_STATE_CANCELLED":e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Wa(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Ka(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ya(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function za(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Wa(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Ka(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ya(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bo(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>za(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Xa(t){const e={},n=o(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const s=o(t,["default"]);s!=null&&r(e,["default"],s);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const a=o(t,["enum"]);a!=null&&r(e,["enum"],a);const l=o(t,["example"]);l!=null&&r(e,["example"],l);const u=o(t,["format"]);u!=null&&r(e,["format"],u);const f=o(t,["items"]);f!=null&&r(e,["items"],f);const c=o(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const m=o(t,["maxLength"]);m!=null&&r(e,["maxLength"],m);const h=o(t,["maxProperties"]);h!=null&&r(e,["maxProperties"],h);const p=o(t,["maximum"]);p!=null&&r(e,["maximum"],p);const g=o(t,["minItems"]);g!=null&&r(e,["minItems"],g);const y=o(t,["minLength"]);y!=null&&r(e,["minLength"],y);const x=o(t,["minProperties"]);x!=null&&r(e,["minProperties"],x);const T=o(t,["minimum"]);T!=null&&r(e,["minimum"],T);const I=o(t,["nullable"]);I!=null&&r(e,["nullable"],I);const w=o(t,["pattern"]);w!=null&&r(e,["pattern"],w);const A=o(t,["properties"]);A!=null&&r(e,["properties"],A);const _=o(t,["propertyOrdering"]);_!=null&&r(e,["propertyOrdering"],_);const C=o(t,["required"]);C!=null&&r(e,["required"],C);const E=o(t,["title"]);E!=null&&r(e,["title"],E);const N=o(t,["type"]);return N!=null&&r(e,["type"],N),e}function Qa(t){const e={};if(o(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=o(t,["category"]);n!=null&&r(e,["category"],n);const s=o(t,["threshold"]);return s!=null&&r(e,["threshold"],s),e}function Za(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const f=o(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function ja(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function el(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],ja(n)),e}function tl(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function nl(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],tl(n)),e}function sl(){return{}}function il(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let f=n;Array.isArray(f)&&(f=f.map(c=>Za(c))),r(e,["functionDeclarations"],f)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],el(s));const i=o(t,["googleSearchRetrieval"]);if(i!=null&&r(e,["googleSearchRetrieval"],nl(i)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],sl());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function ol(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["allowedFunctionNames"]);return s!=null&&r(e,["allowedFunctionNames"],s),e}function rl(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const s=o(t,["longitude"]);return s!=null&&r(e,["longitude"],s),e}function al(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],rl(n));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function ll(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],ol(n));const s=o(t,["retrievalConfig"]);return s!=null&&r(e,["retrievalConfig"],al(s)),e}function ul(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Mo(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],ul(n)),e}function cl(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const s=o(t,["voiceConfig"]);return s!=null&&r(e,["voiceConfig"],Mo(s)),e}function dl(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>cl(i))),r(e,["speakerVoiceConfigs"],s)}return e}function fl(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Mo(n));const s=o(t,["multiSpeakerVoiceConfig"]);s!=null&&r(e,["multiSpeakerVoiceConfig"],dl(s));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function ml(t){const e={},n=o(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const s=o(t,["thinkingBudget"]);return s!=null&&r(e,["thinkingBudget"],s),e}function hl(t,e,n){const s={},i=o(e,["systemInstruction"]);n!==void 0&&i!=null&&r(n,["systemInstruction"],bo(re(i)));const a=o(e,["temperature"]);a!=null&&r(s,["temperature"],a);const l=o(e,["topP"]);l!=null&&r(s,["topP"],l);const u=o(e,["topK"]);u!=null&&r(s,["topK"],u);const f=o(e,["candidateCount"]);f!=null&&r(s,["candidateCount"],f);const c=o(e,["maxOutputTokens"]);c!=null&&r(s,["maxOutputTokens"],c);const m=o(e,["stopSequences"]);m!=null&&r(s,["stopSequences"],m);const h=o(e,["responseLogprobs"]);h!=null&&r(s,["responseLogprobs"],h);const p=o(e,["logprobs"]);p!=null&&r(s,["logprobs"],p);const g=o(e,["presencePenalty"]);g!=null&&r(s,["presencePenalty"],g);const y=o(e,["frequencyPenalty"]);y!=null&&r(s,["frequencyPenalty"],y);const x=o(e,["seed"]);x!=null&&r(s,["seed"],x);const T=o(e,["responseMimeType"]);T!=null&&r(s,["responseMimeType"],T);const I=o(e,["responseSchema"]);I!=null&&r(s,["responseSchema"],Xa(Xn(I)));const w=o(e,["responseJsonSchema"]);if(w!=null&&r(s,["responseJsonSchema"],w),o(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(o(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const A=o(e,["safetySettings"]);if(n!==void 0&&A!=null){let k=A;Array.isArray(k)&&(k=k.map(K=>Qa(K))),r(n,["safetySettings"],k)}const _=o(e,["tools"]);if(n!==void 0&&_!=null){let k=Xe(_);Array.isArray(k)&&(k=k.map(K=>il(ze(K)))),r(n,["tools"],k)}const C=o(e,["toolConfig"]);if(n!==void 0&&C!=null&&r(n,["toolConfig"],ll(C)),o(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const E=o(e,["cachedContent"]);n!==void 0&&E!=null&&r(n,["cachedContent"],Se(t,E));const N=o(e,["responseModalities"]);N!=null&&r(s,["responseModalities"],N);const P=o(e,["mediaResolution"]);P!=null&&r(s,["mediaResolution"],P);const b=o(e,["speechConfig"]);if(b!=null&&r(s,["speechConfig"],fl(Qn(b))),o(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const L=o(e,["thinkingConfig"]);return L!=null&&r(s,["thinkingConfig"],ml(L)),s}function pl(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["request","model"],J(t,s));const i=o(e,["contents"]);if(i!=null){let l=he(i);Array.isArray(l)&&(l=l.map(u=>bo(u))),r(n,["request","contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["request","generationConfig"],hl(t,a,n)),n}function gl(t,e){const n={};if(o(e,["format"])!==void 0)throw new Error("format parameter is not supported in Gemini API.");if(o(e,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");if(o(e,["bigqueryUri"])!==void 0)throw new Error("bigqueryUri parameter is not supported in Gemini API.");const s=o(e,["fileName"]);s!=null&&r(n,["fileName"],s);const i=o(e,["inlinedRequests"]);if(i!=null){let a=i;Array.isArray(a)&&(a=a.map(l=>pl(t,l))),r(n,["requests","requests"],a)}return n}function yl(t,e){const n={},s=o(t,["displayName"]);if(e!==void 0&&s!=null&&r(e,["batch","displayName"],s),o(t,["dest"])!==void 0)throw new Error("dest parameter is not supported in Gemini API.");return n}function xl(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["src"]);i!=null&&r(n,["batch","inputConfig"],gl(t,Io(t,i)));const a=o(e,["config"]);return a!=null&&r(n,["config"],yl(a,n)),n}function vl(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Qe(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Cl(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Qe(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function _l(t,e){const n={},s=o(t,["pageSize"]);e!==void 0&&s!=null&&r(e,["_query","pageSize"],s);const i=o(t,["pageToken"]);if(e!==void 0&&i!=null&&r(e,["_query","pageToken"],i),o(t,["filter"])!==void 0)throw new Error("filter parameter is not supported in Gemini API.");return n}function Tl(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],_l(n,e)),e}function Sl(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Qe(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function wl(t){const e={},n=o(t,["format"]);n!=null&&r(e,["instancesFormat"],n);const s=o(t,["gcsUri"]);s!=null&&r(e,["gcsSource","uris"],s);const i=o(t,["bigqueryUri"]);if(i!=null&&r(e,["bigquerySource","inputUri"],i),o(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(o(t,["inlinedRequests"])!==void 0)throw new Error("inlinedRequests parameter is not supported in Vertex AI.");return e}function Al(t){const e={},n=o(t,["format"]);n!=null&&r(e,["predictionsFormat"],n);const s=o(t,["gcsUri"]);s!=null&&r(e,["gcsDestination","outputUriPrefix"],s);const i=o(t,["bigqueryUri"]);if(i!=null&&r(e,["bigqueryDestination","outputUri"],i),o(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(o(t,["inlinedResponses"])!==void 0)throw new Error("inlinedResponses parameter is not supported in Vertex AI.");return e}function El(t,e){const n={},s=o(t,["displayName"]);e!==void 0&&s!=null&&r(e,["displayName"],s);const i=o(t,["dest"]);return e!==void 0&&i!=null&&r(e,["outputConfig"],Al(Ha(i))),n}function Il(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["model"],J(t,s));const i=o(e,["src"]);i!=null&&r(n,["inputConfig"],wl(Io(t,i)));const a=o(e,["config"]);return a!=null&&r(n,["config"],El(a,n)),n}function Nl(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Qe(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function bl(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Qe(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Ml(t,e){const n={},s=o(t,["pageSize"]);e!==void 0&&s!=null&&r(e,["_query","pageSize"],s);const i=o(t,["pageToken"]);e!==void 0&&i!=null&&r(e,["_query","pageToken"],i);const a=o(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function Pl(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],Ml(n,e)),e}function Rl(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Qe(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Po(){return{}}function kl(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Dl(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ll(t){const e={},n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Fl(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],kl(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Dl(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ll(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Ul(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Fl(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function $l(t){const e={},n=o(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function Vl(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const s=o(t,["urlRetrievalStatus"]);return s!=null&&r(e,["urlRetrievalStatus"],s),e}function Bl(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Vl(i))),r(e,["urlMetadata"],s)}return e}function ql(t){const e={},n=o(t,["content"]);n!=null&&r(e,["content"],Ul(n));const s=o(t,["citationMetadata"]);s!=null&&r(e,["citationMetadata"],$l(s));const i=o(t,["tokenCount"]);i!=null&&r(e,["tokenCount"],i);const a=o(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=o(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],Bl(l));const u=o(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const f=o(t,["groundingMetadata"]);f!=null&&r(e,["groundingMetadata"],f);const c=o(t,["index"]);c!=null&&r(e,["index"],c);const m=o(t,["logprobsResult"]);m!=null&&r(e,["logprobsResult"],m);const h=o(t,["safetyRatings"]);return h!=null&&r(e,["safetyRatings"],h),e}function Gl(t){const e={},n=o(t,["candidates"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(u=>ql(u))),r(e,["candidates"],l)}const s=o(t,["modelVersion"]);s!=null&&r(e,["modelVersion"],s);const i=o(t,["promptFeedback"]);i!=null&&r(e,["promptFeedback"],i);const a=o(t,["usageMetadata"]);return a!=null&&r(e,["usageMetadata"],a),e}function Jl(t){const e={},n=o(t,["response"]);return n!=null&&r(e,["response"],Gl(n)),o(t,["error"])!=null&&r(e,["error"],Po()),e}function Ol(t){const e={},n=o(t,["responsesFile"]);n!=null&&r(e,["fileName"],n);const s=o(t,["inlinedResponses","inlinedResponses"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>Jl(a))),r(e,["inlinedResponses"],i)}return e}function bn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["metadata","displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["metadata","state"]);i!=null&&r(e,["state"],No(i));const a=o(t,["metadata","createTime"]);a!=null&&r(e,["createTime"],a);const l=o(t,["metadata","endTime"]);l!=null&&r(e,["endTime"],l);const u=o(t,["metadata","updateTime"]);u!=null&&r(e,["updateTime"],u);const f=o(t,["metadata","model"]);f!=null&&r(e,["model"],f);const c=o(t,["metadata","output"]);return c!=null&&r(e,["dest"],Ol(c)),e}function Hl(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["operations"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>bn(a))),r(e,["batchJobs"],i)}return e}function Wl(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["done"]);return s!=null&&r(e,["done"],s),o(t,["error"])!=null&&r(e,["error"],Po()),e}function Ro(t){const e={},n=o(t,["details"]);n!=null&&r(e,["details"],n);const s=o(t,["code"]);s!=null&&r(e,["code"],s);const i=o(t,["message"]);return i!=null&&r(e,["message"],i),e}function Kl(t){const e={},n=o(t,["instancesFormat"]);n!=null&&r(e,["format"],n);const s=o(t,["gcsSource","uris"]);s!=null&&r(e,["gcsUri"],s);const i=o(t,["bigquerySource","inputUri"]);return i!=null&&r(e,["bigqueryUri"],i),e}function Yl(t){const e={},n=o(t,["predictionsFormat"]);n!=null&&r(e,["format"],n);const s=o(t,["gcsDestination","outputUriPrefix"]);s!=null&&r(e,["gcsUri"],s);const i=o(t,["bigqueryDestination","outputUri"]);return i!=null&&r(e,["bigqueryUri"],i),e}function Mn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["state"]);i!=null&&r(e,["state"],No(i));const a=o(t,["error"]);a!=null&&r(e,["error"],Ro(a));const l=o(t,["createTime"]);l!=null&&r(e,["createTime"],l);const u=o(t,["startTime"]);u!=null&&r(e,["startTime"],u);const f=o(t,["endTime"]);f!=null&&r(e,["endTime"],f);const c=o(t,["updateTime"]);c!=null&&r(e,["updateTime"],c);const m=o(t,["model"]);m!=null&&r(e,["model"],m);const h=o(t,["inputConfig"]);h!=null&&r(e,["src"],Kl(h));const p=o(t,["outputConfig"]);return p!=null&&r(e,["dest"],Yl(p)),e}function zl(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["batchPredictionJobs"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>Mn(a))),r(e,["batchJobs"],i)}return e}function Xl(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["done"]);s!=null&&r(e,["done"],s);const i=o(t,["error"]);return i!=null&&r(e,["error"],Ro(i)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var Le;(function(t){t.PAGED_ITEM_BATCH_JOBS="batchJobs",t.PAGED_ITEM_MODELS="models",t.PAGED_ITEM_TUNING_JOBS="tuningJobs",t.PAGED_ITEM_FILES="files",t.PAGED_ITEM_CACHED_CONTENTS="cachedContents"})(Le||(Le={}));class xt{constructor(e,n,s,i){this.pageInternal=[],this.paramsInternal={},this.requestInternal=n,this.init(e,s,i)}init(e,n,s){var i,a;this.nameInternal=e,this.pageInternal=n[this.nameInternal]||[],this.idxInternal=0;let l={config:{}};s?typeof s=="object"?l=Object.assign({},s):l=s:l={config:{}},l.config&&(l.config.pageToken=n.nextPageToken),this.paramsInternal=l,this.pageInternalSize=(a=(i=l.config)===null||i===void 0?void 0:i.pageSize)!==null&&a!==void 0?a:this.pageInternal.length}initNextPage(e){this.init(this.nameInternal,e,this.paramsInternal)}get page(){return this.pageInternal}get name(){return this.nameInternal}get pageSize(){return this.pageInternalSize}get params(){return this.paramsInternal}get pageLength(){return this.pageInternal.length}getItem(e){return this.pageInternal[e]}[Symbol.asyncIterator](){return{next:async()=>{if(this.idxInternal>=this.pageLength)if(this.hasNextPage())await this.nextPage();else return{value:void 0,done:!0};const e=this.getItem(this.idxInternal);return this.idxInternal+=1,{value:e,done:!1}},return:async()=>({value:void 0,done:!0})}}async nextPage(){if(!this.hasNextPage())throw new Error("No more pages to fetch.");const e=await this.requestInternal(this.params);return this.initNextPage(e),this.page}hasNextPage(){var e;return((e=this.params.config)===null||e===void 0?void 0:e.pageToken)!==void 0}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let Ql=class extends Ue{constructor(e){super(),this.apiClient=e,this.create=async n=>{if(this.apiClient.isVertexAI()){const i=Date.now().toString();if(Array.isArray(n.src))throw new Error("InlinedRequest[] is not supported in Vertex AI. Please use Google Cloud Storage URI or BigQuery URI instead.");if(n.config=n.config||{},n.config.displayName===void 0&&(n.config.displayName="genaiBatchJob_${timestampStr}"),n.config.dest===void 0&&typeof n.src=="string")if(n.src.startsWith("gs://")&&n.src.endsWith(".jsonl"))n.config.dest=`${n.src.slice(0,-6)}/dest`;else if(n.src.startsWith("bq://"))n.config.dest=`${n.src}_dest_${i}`;else throw new Error("Unsupported source:"+n.src)}return await this.createInternal(n)},this.list=async(n={})=>new xt(Le.PAGED_ITEM_BATCH_JOBS,s=>this.listInternal(s),await this.listInternal(n),n)}async createInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Il(this.apiClient,e);return u=F("batchPredictionJobs",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Mn(m))}else{const c=xl(this.apiClient,e);return u=F("{model}:batchGenerateContent",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>bn(m))}}async get(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Nl(this.apiClient,e);return u=F("batchPredictionJobs/{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Mn(m))}else{const c=vl(this.apiClient,e);return u=F("batches/{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>bn(m))}}async cancel(e){var n,s,i,a;let l="",u={};if(this.apiClient.isVertexAI()){const f=bl(this.apiClient,e);l=F("batchPredictionJobs/{name}:cancel",f._url),u=f._query,delete f.config,delete f._url,delete f._query,await this.apiClient.request({path:l,queryParams:u,body:JSON.stringify(f),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal})}else{const f=Cl(this.apiClient,e);l=F("batches/{name}:cancel",f._url),u=f._query,delete f.config,delete f._url,delete f._query,await this.apiClient.request({path:l,queryParams:u,body:JSON.stringify(f),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal})}}async listInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Pl(e);return u=F("batchPredictionJobs",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=zl(m),p=new yi;return Object.assign(p,h),p})}else{const c=Tl(e);return u=F("batches",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Hl(m),p=new yi;return Object.assign(p,h),p})}}async delete(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Rl(this.apiClient,e);return u=F("batchPredictionJobs/{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Xl(m))}else{const c=Sl(this.apiClient,e);return u=F("batches/{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Wl(m))}}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Zl(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function jl(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function eu(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function tu(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Zl(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],jl(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],eu(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function _i(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>tu(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function nu(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const f=o(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function su(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function iu(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],su(n)),e}function ou(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function ru(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],ou(n)),e}function au(){return{}}function lu(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let f=n;Array.isArray(f)&&(f=f.map(c=>nu(c))),r(e,["functionDeclarations"],f)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],iu(s));const i=o(t,["googleSearchRetrieval"]);if(i!=null&&r(e,["googleSearchRetrieval"],ru(i)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],au());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function uu(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["allowedFunctionNames"]);return s!=null&&r(e,["allowedFunctionNames"],s),e}function cu(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const s=o(t,["longitude"]);return s!=null&&r(e,["longitude"],s),e}function du(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],cu(n));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function fu(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],uu(n));const s=o(t,["retrievalConfig"]);return s!=null&&r(e,["retrievalConfig"],du(s)),e}function mu(t,e){const n={},s=o(t,["ttl"]);e!==void 0&&s!=null&&r(e,["ttl"],s);const i=o(t,["expireTime"]);e!==void 0&&i!=null&&r(e,["expireTime"],i);const a=o(t,["displayName"]);e!==void 0&&a!=null&&r(e,["displayName"],a);const l=o(t,["contents"]);if(e!==void 0&&l!=null){let m=he(l);Array.isArray(m)&&(m=m.map(h=>_i(h))),r(e,["contents"],m)}const u=o(t,["systemInstruction"]);e!==void 0&&u!=null&&r(e,["systemInstruction"],_i(re(u)));const f=o(t,["tools"]);if(e!==void 0&&f!=null){let m=f;Array.isArray(m)&&(m=m.map(h=>lu(h))),r(e,["tools"],m)}const c=o(t,["toolConfig"]);if(e!==void 0&&c!=null&&r(e,["toolConfig"],fu(c)),o(t,["kmsKeyName"])!==void 0)throw new Error("kmsKeyName parameter is not supported in Gemini API.");return n}function hu(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["model"],yo(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],mu(i,n)),n}function pu(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Se(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function gu(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Se(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function yu(t,e){const n={},s=o(t,["ttl"]);e!==void 0&&s!=null&&r(e,["ttl"],s);const i=o(t,["expireTime"]);return e!==void 0&&i!=null&&r(e,["expireTime"],i),n}function xu(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Se(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],yu(i,n)),n}function vu(t,e){const n={},s=o(t,["pageSize"]);e!==void 0&&s!=null&&r(e,["_query","pageSize"],s);const i=o(t,["pageToken"]);return e!==void 0&&i!=null&&r(e,["_query","pageToken"],i),n}function Cu(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],vu(n,e)),e}function _u(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Tu(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["data"]);s!=null&&r(e,["data"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Su(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["fileUri"]);s!=null&&r(e,["fileUri"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function wu(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],_u(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Tu(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Su(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Ti(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>wu(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Au(t){const e={};if(o(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=o(t,["description"]);n!=null&&r(e,["description"],n);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const i=o(t,["parameters"]);i!=null&&r(e,["parameters"],i);const a=o(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=o(t,["response"]);l!=null&&r(e,["response"],l);const u=o(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function Eu(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function Iu(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Eu(n)),e}function Nu(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function bu(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Nu(n)),e}function Mu(){return{}}function Pu(t){const e={},n=o(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Ru(t){const e={},n=o(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Pu(n));const s=o(t,["authType"]);s!=null&&r(e,["authType"],s);const i=o(t,["googleServiceAccountConfig"]);i!=null&&r(e,["googleServiceAccountConfig"],i);const a=o(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=o(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const u=o(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function ku(t){const e={},n=o(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Ru(n)),e}function Du(){return{}}function Lu(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let h=n;Array.isArray(h)&&(h=h.map(p=>Au(p))),r(e,["functionDeclarations"],h)}const s=o(t,["retrieval"]);s!=null&&r(e,["retrieval"],s);const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],Iu(i));const a=o(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],bu(a)),o(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Mu());const u=o(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],ku(u)),o(t,["urlContext"])!=null&&r(e,["urlContext"],Du());const c=o(t,["codeExecution"]);c!=null&&r(e,["codeExecution"],c);const m=o(t,["computerUse"]);return m!=null&&r(e,["computerUse"],m),e}function Fu(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["allowedFunctionNames"]);return s!=null&&r(e,["allowedFunctionNames"],s),e}function Uu(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const s=o(t,["longitude"]);return s!=null&&r(e,["longitude"],s),e}function $u(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],Uu(n));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function Vu(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],Fu(n));const s=o(t,["retrievalConfig"]);return s!=null&&r(e,["retrievalConfig"],$u(s)),e}function Bu(t,e){const n={},s=o(t,["ttl"]);e!==void 0&&s!=null&&r(e,["ttl"],s);const i=o(t,["expireTime"]);e!==void 0&&i!=null&&r(e,["expireTime"],i);const a=o(t,["displayName"]);e!==void 0&&a!=null&&r(e,["displayName"],a);const l=o(t,["contents"]);if(e!==void 0&&l!=null){let h=he(l);Array.isArray(h)&&(h=h.map(p=>Ti(p))),r(e,["contents"],h)}const u=o(t,["systemInstruction"]);e!==void 0&&u!=null&&r(e,["systemInstruction"],Ti(re(u)));const f=o(t,["tools"]);if(e!==void 0&&f!=null){let h=f;Array.isArray(h)&&(h=h.map(p=>Lu(p))),r(e,["tools"],h)}const c=o(t,["toolConfig"]);e!==void 0&&c!=null&&r(e,["toolConfig"],Vu(c));const m=o(t,["kmsKeyName"]);return e!==void 0&&m!=null&&r(e,["encryption_spec","kmsKeyName"],m),n}function qu(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["model"],yo(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],Bu(i,n)),n}function Gu(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Se(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Ju(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Se(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Ou(t,e){const n={},s=o(t,["ttl"]);e!==void 0&&s!=null&&r(e,["ttl"],s);const i=o(t,["expireTime"]);return e!==void 0&&i!=null&&r(e,["expireTime"],i),n}function Hu(t,e){const n={},s=o(e,["name"]);s!=null&&r(n,["_url","name"],Se(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],Ou(i,n)),n}function Wu(t,e){const n={},s=o(t,["pageSize"]);e!==void 0&&s!=null&&r(e,["_query","pageSize"],s);const i=o(t,["pageToken"]);return e!==void 0&&i!=null&&r(e,["_query","pageToken"],i),n}function Ku(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],Wu(n,e)),e}function Lt(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["model"]);i!=null&&r(e,["model"],i);const a=o(t,["createTime"]);a!=null&&r(e,["createTime"],a);const l=o(t,["updateTime"]);l!=null&&r(e,["updateTime"],l);const u=o(t,["expireTime"]);u!=null&&r(e,["expireTime"],u);const f=o(t,["usageMetadata"]);return f!=null&&r(e,["usageMetadata"],f),e}function Yu(){return{}}function zu(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["cachedContents"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>Lt(a))),r(e,["cachedContents"],i)}return e}function Ft(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["model"]);i!=null&&r(e,["model"],i);const a=o(t,["createTime"]);a!=null&&r(e,["createTime"],a);const l=o(t,["updateTime"]);l!=null&&r(e,["updateTime"],l);const u=o(t,["expireTime"]);u!=null&&r(e,["expireTime"],u);const f=o(t,["usageMetadata"]);return f!=null&&r(e,["usageMetadata"],f),e}function Xu(){return{}}function Qu(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["cachedContents"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>Ft(a))),r(e,["cachedContents"],i)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Zu extends Ue{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new xt(Le.PAGED_ITEM_CACHED_CONTENTS,s=>this.listInternal(s),await this.listInternal(n),n)}async create(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=qu(this.apiClient,e);return u=F("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Ft(m))}else{const c=hu(this.apiClient,e);return u=F("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Lt(m))}}async get(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Gu(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Ft(m))}else{const c=pu(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Lt(m))}}async delete(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Ju(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(()=>{const m=Xu(),h=new pi;return Object.assign(h,m),h})}else{const c=gu(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(()=>{const m=Yu(),h=new pi;return Object.assign(h,m),h})}}async update(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Hu(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Ft(m))}else{const c=xu(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Lt(m))}}async listInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Ku(e);return u=F("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Qu(m),p=new gi;return Object.assign(p,h),p})}else{const c=Cu(e);return u=F("cachedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=zu(m),p=new gi;return Object.assign(p,h),p})}}}function Si(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],s=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&s>=t.length&&(t=void 0),{value:t&&t[s++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function W(t){return this instanceof W?(this.v=t,this):new W(t)}function Ye(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var s=n.apply(t,e||[]),i,a=[];return i=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",l),i[Symbol.asyncIterator]=function(){return this},i;function l(g){return function(y){return Promise.resolve(y).then(g,h)}}function u(g,y){s[g]&&(i[g]=function(x){return new Promise(function(T,I){a.push([g,x,T,I])>1||f(g,x)})},y&&(i[g]=y(i[g])))}function f(g,y){try{c(s[g](y))}catch(x){p(a[0][3],x)}}function c(g){g.value instanceof W?Promise.resolve(g.value.v).then(m,h):p(a[0][2],g)}function m(g){f("next",g)}function h(g){f("throw",g)}function p(g,y){g(y),a.shift(),a.length&&f(a[0][0],a[0][1])}}function mt(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof Si=="function"?Si(t):t[Symbol.iterator](),n={},s("next"),s("throw"),s("return"),n[Symbol.asyncIterator]=function(){return this},n);function s(a){n[a]=t[a]&&function(l){return new Promise(function(u,f){l=t[a](l),i(u,f,l.done,l.value)})}}function i(a,l,u,f){Promise.resolve(f).then(function(c){a({value:c,done:u})},l)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function ju(t){var e;if(t.candidates==null||t.candidates.length===0)return!1;const n=(e=t.candidates[0])===null||e===void 0?void 0:e.content;return n===void 0?!1:ko(n)}function ko(t){if(t.parts===void 0||t.parts.length===0)return!1;for(const e of t.parts)if(e===void 0||Object.keys(e).length===0||!e.thought&&e.text!==void 0&&e.text==="")return!1;return!0}function ec(t){if(t.length!==0){for(const e of t)if(e.role!=="user"&&e.role!=="model")throw new Error(`Role must be user or model, but got ${e.role}.`)}}function wi(t){if(t===void 0||t.length===0)return[];const e=[],n=t.length;let s=0;for(;s<n;)if(t[s].role==="user")e.push(t[s]),s++;else{const i=[];let a=!0;for(;s<n&&t[s].role==="model";)i.push(t[s]),a&&!ko(t[s])&&(a=!1),s++;a?e.push(...i):e.pop()}return e}class tc{constructor(e,n){this.modelsModule=e,this.apiClient=n}create(e){return new nc(this.apiClient,this.modelsModule,e.model,e.config,structuredClone(e.history))}}let nc=class{constructor(e,n,s,i={},a=[]){this.apiClient=e,this.modelsModule=n,this.model=s,this.config=i,this.history=a,this.sendPromise=Promise.resolve(),ec(a)}async sendMessage(e){var n;await this.sendPromise;const s=re(e.message),i=this.modelsModule.generateContent({model:this.model,contents:this.getHistory(!0).concat(s),config:(n=e.config)!==null&&n!==void 0?n:this.config});return this.sendPromise=(async()=>{var a,l,u;const f=await i,c=(l=(a=f.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content,m=f.automaticFunctionCallingHistory,h=this.getHistory(!0).length;let p=[];m!=null&&(p=(u=m.slice(h))!==null&&u!==void 0?u:[]);const g=c?[c]:[];this.recordHistory(s,g,p)})(),await this.sendPromise.catch(()=>{this.sendPromise=Promise.resolve()}),i}async sendMessageStream(e){var n;await this.sendPromise;const s=re(e.message),i=this.modelsModule.generateContentStream({model:this.model,contents:this.getHistory(!0).concat(s),config:(n=e.config)!==null&&n!==void 0?n:this.config});this.sendPromise=i.then(()=>{}).catch(()=>{});const a=await i;return this.processStreamResponse(a,s)}getHistory(e=!1){const n=e?wi(this.history):this.history;return structuredClone(n)}processStreamResponse(e,n){var s,i;return Ye(this,arguments,function*(){var l,u,f,c;const m=[];try{for(var h=!0,p=mt(e),g;g=yield W(p.next()),l=g.done,!l;h=!0){c=g.value,h=!1;const y=c;if(ju(y)){const x=(i=(s=y.candidates)===null||s===void 0?void 0:s[0])===null||i===void 0?void 0:i.content;x!==void 0&&m.push(x)}yield yield W(y)}}catch(y){u={error:y}}finally{try{!h&&!l&&(f=p.return)&&(yield W(f.call(p)))}finally{if(u)throw u.error}}this.recordHistory(n,m)})}recordHistory(e,n,s){let i=[];n.length>0&&n.every(a=>a.role!==void 0)?i=n:i.push({role:"model",parts:[]}),s&&s.length>0?this.history.push(...wi(s)):this.history.push(e),this.history.push(...i)}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class nn extends Error{constructor(e){super(e.message),this.name="ApiError",this.status=e.status,Object.setPrototypeOf(this,nn.prototype)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function sc(t,e){const n={},s=o(t,["pageSize"]);e!==void 0&&s!=null&&r(e,["_query","pageSize"],s);const i=o(t,["pageToken"]);return e!==void 0&&i!=null&&r(e,["_query","pageToken"],i),n}function ic(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],sc(n,e)),e}function oc(t){const e={},n=o(t,["details"]);n!=null&&r(e,["details"],n);const s=o(t,["message"]);s!=null&&r(e,["message"],s);const i=o(t,["code"]);return i!=null&&r(e,["code"],i),e}function rc(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["mimeType"]);i!=null&&r(e,["mimeType"],i);const a=o(t,["sizeBytes"]);a!=null&&r(e,["sizeBytes"],a);const l=o(t,["createTime"]);l!=null&&r(e,["createTime"],l);const u=o(t,["expirationTime"]);u!=null&&r(e,["expirationTime"],u);const f=o(t,["updateTime"]);f!=null&&r(e,["updateTime"],f);const c=o(t,["sha256Hash"]);c!=null&&r(e,["sha256Hash"],c);const m=o(t,["uri"]);m!=null&&r(e,["uri"],m);const h=o(t,["downloadUri"]);h!=null&&r(e,["downloadUri"],h);const p=o(t,["state"]);p!=null&&r(e,["state"],p);const g=o(t,["source"]);g!=null&&r(e,["source"],g);const y=o(t,["videoMetadata"]);y!=null&&r(e,["videoMetadata"],y);const x=o(t,["error"]);return x!=null&&r(e,["error"],oc(x)),e}function ac(t){const e={},n=o(t,["file"]);n!=null&&r(e,["file"],rc(n));const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function lc(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","file"],wo(n));const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function uc(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","file"],wo(n));const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function cc(t){const e={},n=o(t,["details"]);n!=null&&r(e,["details"],n);const s=o(t,["message"]);s!=null&&r(e,["message"],s);const i=o(t,["code"]);return i!=null&&r(e,["code"],i),e}function Pn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["mimeType"]);i!=null&&r(e,["mimeType"],i);const a=o(t,["sizeBytes"]);a!=null&&r(e,["sizeBytes"],a);const l=o(t,["createTime"]);l!=null&&r(e,["createTime"],l);const u=o(t,["expirationTime"]);u!=null&&r(e,["expirationTime"],u);const f=o(t,["updateTime"]);f!=null&&r(e,["updateTime"],f);const c=o(t,["sha256Hash"]);c!=null&&r(e,["sha256Hash"],c);const m=o(t,["uri"]);m!=null&&r(e,["uri"],m);const h=o(t,["downloadUri"]);h!=null&&r(e,["downloadUri"],h);const p=o(t,["state"]);p!=null&&r(e,["state"],p);const g=o(t,["source"]);g!=null&&r(e,["source"],g);const y=o(t,["videoMetadata"]);y!=null&&r(e,["videoMetadata"],y);const x=o(t,["error"]);return x!=null&&r(e,["error"],cc(x)),e}function dc(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["files"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>Pn(a))),r(e,["files"],i)}return e}function fc(){return{}}function mc(){return{}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let hc=class extends Ue{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new xt(Le.PAGED_ITEM_FILES,s=>this.listInternal(s),await this.listInternal(n),n)}async upload(e){if(this.apiClient.isVertexAI())throw new Error("Vertex AI does not support uploading files. You can share files through a GCS bucket.");return this.apiClient.uploadFile(e.file,e.config).then(n=>Pn(n))}async download(e){await this.apiClient.downloadFile(e)}async listInternal(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=ic(e);return a=F("files",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>{const c=dc(f),m=new Ra;return Object.assign(m,c),m})}}async createInternal(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=ac(e);return a=F("upload/v1beta/files",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(()=>{const f=fc(),c=new ka;return Object.assign(c,f),c})}}async get(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=lc(e);return a=F("files/{file}",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>Pn(f))}}async delete(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=uc(e);return a=F("files/{file}",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(()=>{const f=mc(),c=new Da;return Object.assign(c,f),c})}}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function pc(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function gc(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Do(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],pc(n)),e}function yc(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],gc(n)),e}function xc(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const s=o(t,["voiceConfig"]);return s!=null&&r(e,["voiceConfig"],Do(s)),e}function vc(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>xc(i))),r(e,["speakerVoiceConfigs"],s)}return e}function Cc(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Do(n));const s=o(t,["multiSpeakerVoiceConfig"]);s!=null&&r(e,["multiSpeakerVoiceConfig"],vc(s));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function _c(t){const e={},n=o(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],yc(n)),o(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function Tc(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Sc(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function wc(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ac(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["data"]);s!=null&&r(e,["data"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ec(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ic(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["fileUri"]);s!=null&&r(e,["fileUri"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Nc(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Tc(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],wc(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ec(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bc(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Sc(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Ac(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ic(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Mc(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Nc(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Pc(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>bc(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Rc(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const f=o(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function kc(t){const e={};if(o(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=o(t,["description"]);n!=null&&r(e,["description"],n);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const i=o(t,["parameters"]);i!=null&&r(e,["parameters"],i);const a=o(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=o(t,["response"]);l!=null&&r(e,["response"],l);const u=o(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function Dc(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function Lc(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function Fc(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Dc(n)),e}function Uc(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Lc(n)),e}function $c(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function Vc(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function Bc(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],$c(n)),e}function qc(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Vc(n)),e}function Gc(){return{}}function Jc(t){const e={},n=o(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Oc(t){const e={},n=o(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Jc(n));const s=o(t,["authType"]);s!=null&&r(e,["authType"],s);const i=o(t,["googleServiceAccountConfig"]);i!=null&&r(e,["googleServiceAccountConfig"],i);const a=o(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=o(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const u=o(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function Hc(t){const e={},n=o(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Oc(n)),e}function Wc(){return{}}function Kc(){return{}}function Yc(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let f=n;Array.isArray(f)&&(f=f.map(c=>Rc(c))),r(e,["functionDeclarations"],f)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],Fc(s));const i=o(t,["googleSearchRetrieval"]);if(i!=null&&r(e,["googleSearchRetrieval"],Bc(i)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],Wc());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function zc(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let h=n;Array.isArray(h)&&(h=h.map(p=>kc(p))),r(e,["functionDeclarations"],h)}const s=o(t,["retrieval"]);s!=null&&r(e,["retrieval"],s);const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],Uc(i));const a=o(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],qc(a)),o(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Gc());const u=o(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],Hc(u)),o(t,["urlContext"])!=null&&r(e,["urlContext"],Kc());const c=o(t,["codeExecution"]);c!=null&&r(e,["codeExecution"],c);const m=o(t,["computerUse"]);return m!=null&&r(e,["computerUse"],m),e}function Xc(t){const e={},n=o(t,["handle"]);if(n!=null&&r(e,["handle"],n),o(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function Qc(t){const e={},n=o(t,["handle"]);n!=null&&r(e,["handle"],n);const s=o(t,["transparent"]);return s!=null&&r(e,["transparent"],s),e}function Ai(){return{}}function Ei(){return{}}function Zc(t){const e={},n=o(t,["disabled"]);n!=null&&r(e,["disabled"],n);const s=o(t,["startOfSpeechSensitivity"]);s!=null&&r(e,["startOfSpeechSensitivity"],s);const i=o(t,["endOfSpeechSensitivity"]);i!=null&&r(e,["endOfSpeechSensitivity"],i);const a=o(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=o(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function jc(t){const e={},n=o(t,["disabled"]);n!=null&&r(e,["disabled"],n);const s=o(t,["startOfSpeechSensitivity"]);s!=null&&r(e,["startOfSpeechSensitivity"],s);const i=o(t,["endOfSpeechSensitivity"]);i!=null&&r(e,["endOfSpeechSensitivity"],i);const a=o(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=o(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function ed(t){const e={},n=o(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],Zc(n));const s=o(t,["activityHandling"]);s!=null&&r(e,["activityHandling"],s);const i=o(t,["turnCoverage"]);return i!=null&&r(e,["turnCoverage"],i),e}function td(t){const e={},n=o(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],jc(n));const s=o(t,["activityHandling"]);s!=null&&r(e,["activityHandling"],s);const i=o(t,["turnCoverage"]);return i!=null&&r(e,["turnCoverage"],i),e}function nd(t){const e={},n=o(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function sd(t){const e={},n=o(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function id(t){const e={},n=o(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const s=o(t,["slidingWindow"]);return s!=null&&r(e,["slidingWindow"],nd(s)),e}function od(t){const e={},n=o(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const s=o(t,["slidingWindow"]);return s!=null&&r(e,["slidingWindow"],sd(s)),e}function rd(t){const e={},n=o(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function ad(t){const e={},n=o(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function ld(t,e){const n={},s=o(t,["generationConfig"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig"],s);const i=o(t,["responseModalities"]);e!==void 0&&i!=null&&r(e,["setup","generationConfig","responseModalities"],i);const a=o(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=o(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const u=o(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const f=o(t,["maxOutputTokens"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","maxOutputTokens"],f);const c=o(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const m=o(t,["seed"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","seed"],m);const h=o(t,["speechConfig"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","speechConfig"],Cc(Zn(h)));const p=o(t,["enableAffectiveDialog"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],p);const g=o(t,["systemInstruction"]);e!==void 0&&g!=null&&r(e,["setup","systemInstruction"],Mc(re(g)));const y=o(t,["tools"]);if(e!==void 0&&y!=null){let C=Xe(y);Array.isArray(C)&&(C=C.map(E=>Yc(ze(E)))),r(e,["setup","tools"],C)}const x=o(t,["sessionResumption"]);e!==void 0&&x!=null&&r(e,["setup","sessionResumption"],Xc(x));const T=o(t,["inputAudioTranscription"]);e!==void 0&&T!=null&&r(e,["setup","inputAudioTranscription"],Ai());const I=o(t,["outputAudioTranscription"]);e!==void 0&&I!=null&&r(e,["setup","outputAudioTranscription"],Ai());const w=o(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],ed(w));const A=o(t,["contextWindowCompression"]);e!==void 0&&A!=null&&r(e,["setup","contextWindowCompression"],id(A));const _=o(t,["proactivity"]);return e!==void 0&&_!=null&&r(e,["setup","proactivity"],rd(_)),n}function ud(t,e){const n={},s=o(t,["generationConfig"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig"],s);const i=o(t,["responseModalities"]);e!==void 0&&i!=null&&r(e,["setup","generationConfig","responseModalities"],i);const a=o(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=o(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const u=o(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const f=o(t,["maxOutputTokens"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","maxOutputTokens"],f);const c=o(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const m=o(t,["seed"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","seed"],m);const h=o(t,["speechConfig"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","speechConfig"],_c(Zn(h)));const p=o(t,["enableAffectiveDialog"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],p);const g=o(t,["systemInstruction"]);e!==void 0&&g!=null&&r(e,["setup","systemInstruction"],Pc(re(g)));const y=o(t,["tools"]);if(e!==void 0&&y!=null){let C=Xe(y);Array.isArray(C)&&(C=C.map(E=>zc(ze(E)))),r(e,["setup","tools"],C)}const x=o(t,["sessionResumption"]);e!==void 0&&x!=null&&r(e,["setup","sessionResumption"],Qc(x));const T=o(t,["inputAudioTranscription"]);e!==void 0&&T!=null&&r(e,["setup","inputAudioTranscription"],Ei());const I=o(t,["outputAudioTranscription"]);e!==void 0&&I!=null&&r(e,["setup","outputAudioTranscription"],Ei());const w=o(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],td(w));const A=o(t,["contextWindowCompression"]);e!==void 0&&A!=null&&r(e,["setup","contextWindowCompression"],od(A));const _=o(t,["proactivity"]);return e!==void 0&&_!=null&&r(e,["setup","proactivity"],ad(_)),n}function cd(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["setup","model"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],ld(i,n)),n}function dd(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["setup","model"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],ud(i,n)),n}function fd(){return{}}function md(){return{}}function hd(){return{}}function pd(){return{}}function gd(t){const e={},n=o(t,["media"]);n!=null&&r(e,["mediaChunks"],xo(n));const s=o(t,["audio"]);s!=null&&r(e,["audio"],Co(s));const i=o(t,["audioStreamEnd"]);i!=null&&r(e,["audioStreamEnd"],i);const a=o(t,["video"]);a!=null&&r(e,["video"],vo(a));const l=o(t,["text"]);return l!=null&&r(e,["text"],l),o(t,["activityStart"])!=null&&r(e,["activityStart"],fd()),o(t,["activityEnd"])!=null&&r(e,["activityEnd"],hd()),e}function yd(t){const e={},n=o(t,["media"]);n!=null&&r(e,["mediaChunks"],xo(n));const s=o(t,["audio"]);s!=null&&r(e,["audio"],Co(s));const i=o(t,["audioStreamEnd"]);i!=null&&r(e,["audioStreamEnd"],i);const a=o(t,["video"]);a!=null&&r(e,["video"],vo(a));const l=o(t,["text"]);return l!=null&&r(e,["text"],l),o(t,["activityStart"])!=null&&r(e,["activityStart"],md()),o(t,["activityEnd"])!=null&&r(e,["activityEnd"],pd()),e}function Lo(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const s=o(t,["weight"]);return s!=null&&r(e,["weight"],s),e}function xd(t){const e={},n=o(t,["weightedPrompts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Lo(i))),r(e,["weightedPrompts"],s)}return e}function Fo(t){const e={},n=o(t,["temperature"]);n!=null&&r(e,["temperature"],n);const s=o(t,["topK"]);s!=null&&r(e,["topK"],s);const i=o(t,["seed"]);i!=null&&r(e,["seed"],i);const a=o(t,["guidance"]);a!=null&&r(e,["guidance"],a);const l=o(t,["bpm"]);l!=null&&r(e,["bpm"],l);const u=o(t,["density"]);u!=null&&r(e,["density"],u);const f=o(t,["brightness"]);f!=null&&r(e,["brightness"],f);const c=o(t,["scale"]);c!=null&&r(e,["scale"],c);const m=o(t,["muteBass"]);m!=null&&r(e,["muteBass"],m);const h=o(t,["muteDrums"]);h!=null&&r(e,["muteDrums"],h);const p=o(t,["onlyBassAndDrums"]);return p!=null&&r(e,["onlyBassAndDrums"],p),e}function vd(t){const e={},n=o(t,["musicGenerationConfig"]);return n!=null&&r(e,["musicGenerationConfig"],Fo(n)),e}function Uo(t){const e={},n=o(t,["model"]);return n!=null&&r(e,["model"],n),e}function $o(t){const e={},n=o(t,["weightedPrompts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Lo(i))),r(e,["weightedPrompts"],s)}return e}function Rn(t){const e={},n=o(t,["setup"]);n!=null&&r(e,["setup"],Uo(n));const s=o(t,["clientContent"]);s!=null&&r(e,["clientContent"],$o(s));const i=o(t,["musicGenerationConfig"]);i!=null&&r(e,["musicGenerationConfig"],Fo(i));const a=o(t,["playbackControl"]);return a!=null&&r(e,["playbackControl"],a),e}function Cd(){return{}}function _d(t){const e={},n=o(t,["sessionId"]);return n!=null&&r(e,["sessionId"],n),e}function Td(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Sd(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function wd(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ad(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["data"]);s!=null&&r(e,["data"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ed(t){const e={},n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Id(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["fileUri"]);s!=null&&r(e,["fileUri"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Nd(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Td(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],wd(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ed(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bd(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Sd(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Ad(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Id(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Md(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Nd(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Pd(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>bd(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Ii(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const s=o(t,["finished"]);return s!=null&&r(e,["finished"],s),e}function Ni(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const s=o(t,["finished"]);return s!=null&&r(e,["finished"],s),e}function Rd(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const s=o(t,["urlRetrievalStatus"]);return s!=null&&r(e,["urlRetrievalStatus"],s),e}function kd(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Rd(i))),r(e,["urlMetadata"],s)}return e}function Dd(t){const e={},n=o(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],Md(n));const s=o(t,["turnComplete"]);s!=null&&r(e,["turnComplete"],s);const i=o(t,["interrupted"]);i!=null&&r(e,["interrupted"],i);const a=o(t,["groundingMetadata"]);a!=null&&r(e,["groundingMetadata"],a);const l=o(t,["generationComplete"]);l!=null&&r(e,["generationComplete"],l);const u=o(t,["inputTranscription"]);u!=null&&r(e,["inputTranscription"],Ii(u));const f=o(t,["outputTranscription"]);f!=null&&r(e,["outputTranscription"],Ii(f));const c=o(t,["urlContextMetadata"]);return c!=null&&r(e,["urlContextMetadata"],kd(c)),e}function Ld(t){const e={},n=o(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],Pd(n));const s=o(t,["turnComplete"]);s!=null&&r(e,["turnComplete"],s);const i=o(t,["interrupted"]);i!=null&&r(e,["interrupted"],i);const a=o(t,["groundingMetadata"]);a!=null&&r(e,["groundingMetadata"],a);const l=o(t,["generationComplete"]);l!=null&&r(e,["generationComplete"],l);const u=o(t,["inputTranscription"]);u!=null&&r(e,["inputTranscription"],Ni(u));const f=o(t,["outputTranscription"]);return f!=null&&r(e,["outputTranscription"],Ni(f)),e}function Fd(t){const e={},n=o(t,["id"]);n!=null&&r(e,["id"],n);const s=o(t,["args"]);s!=null&&r(e,["args"],s);const i=o(t,["name"]);return i!=null&&r(e,["name"],i),e}function Ud(t){const e={},n=o(t,["args"]);n!=null&&r(e,["args"],n);const s=o(t,["name"]);return s!=null&&r(e,["name"],s),e}function $d(t){const e={},n=o(t,["functionCalls"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Fd(i))),r(e,["functionCalls"],s)}return e}function Vd(t){const e={},n=o(t,["functionCalls"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Ud(i))),r(e,["functionCalls"],s)}return e}function Bd(t){const e={},n=o(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function qd(t){const e={},n=o(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function At(t){const e={},n=o(t,["modality"]);n!=null&&r(e,["modality"],n);const s=o(t,["tokenCount"]);return s!=null&&r(e,["tokenCount"],s),e}function Et(t){const e={},n=o(t,["modality"]);n!=null&&r(e,["modality"],n);const s=o(t,["tokenCount"]);return s!=null&&r(e,["tokenCount"],s),e}function Gd(t){const e={},n=o(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const s=o(t,["cachedContentTokenCount"]);s!=null&&r(e,["cachedContentTokenCount"],s);const i=o(t,["responseTokenCount"]);i!=null&&r(e,["responseTokenCount"],i);const a=o(t,["toolUsePromptTokenCount"]);a!=null&&r(e,["toolUsePromptTokenCount"],a);const l=o(t,["thoughtsTokenCount"]);l!=null&&r(e,["thoughtsTokenCount"],l);const u=o(t,["totalTokenCount"]);u!=null&&r(e,["totalTokenCount"],u);const f=o(t,["promptTokensDetails"]);if(f!=null){let p=f;Array.isArray(p)&&(p=p.map(g=>At(g))),r(e,["promptTokensDetails"],p)}const c=o(t,["cacheTokensDetails"]);if(c!=null){let p=c;Array.isArray(p)&&(p=p.map(g=>At(g))),r(e,["cacheTokensDetails"],p)}const m=o(t,["responseTokensDetails"]);if(m!=null){let p=m;Array.isArray(p)&&(p=p.map(g=>At(g))),r(e,["responseTokensDetails"],p)}const h=o(t,["toolUsePromptTokensDetails"]);if(h!=null){let p=h;Array.isArray(p)&&(p=p.map(g=>At(g))),r(e,["toolUsePromptTokensDetails"],p)}return e}function Jd(t){const e={},n=o(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const s=o(t,["cachedContentTokenCount"]);s!=null&&r(e,["cachedContentTokenCount"],s);const i=o(t,["candidatesTokenCount"]);i!=null&&r(e,["responseTokenCount"],i);const a=o(t,["toolUsePromptTokenCount"]);a!=null&&r(e,["toolUsePromptTokenCount"],a);const l=o(t,["thoughtsTokenCount"]);l!=null&&r(e,["thoughtsTokenCount"],l);const u=o(t,["totalTokenCount"]);u!=null&&r(e,["totalTokenCount"],u);const f=o(t,["promptTokensDetails"]);if(f!=null){let g=f;Array.isArray(g)&&(g=g.map(y=>Et(y))),r(e,["promptTokensDetails"],g)}const c=o(t,["cacheTokensDetails"]);if(c!=null){let g=c;Array.isArray(g)&&(g=g.map(y=>Et(y))),r(e,["cacheTokensDetails"],g)}const m=o(t,["candidatesTokensDetails"]);if(m!=null){let g=m;Array.isArray(g)&&(g=g.map(y=>Et(y))),r(e,["responseTokensDetails"],g)}const h=o(t,["toolUsePromptTokensDetails"]);if(h!=null){let g=h;Array.isArray(g)&&(g=g.map(y=>Et(y))),r(e,["toolUsePromptTokensDetails"],g)}const p=o(t,["trafficType"]);return p!=null&&r(e,["trafficType"],p),e}function Od(t){const e={},n=o(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function Hd(t){const e={},n=o(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function Wd(t){const e={},n=o(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const s=o(t,["resumable"]);s!=null&&r(e,["resumable"],s);const i=o(t,["lastConsumedClientMessageIndex"]);return i!=null&&r(e,["lastConsumedClientMessageIndex"],i),e}function Kd(t){const e={},n=o(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const s=o(t,["resumable"]);s!=null&&r(e,["resumable"],s);const i=o(t,["lastConsumedClientMessageIndex"]);return i!=null&&r(e,["lastConsumedClientMessageIndex"],i),e}function Yd(t){const e={};o(t,["setupComplete"])!=null&&r(e,["setupComplete"],Cd());const s=o(t,["serverContent"]);s!=null&&r(e,["serverContent"],Dd(s));const i=o(t,["toolCall"]);i!=null&&r(e,["toolCall"],$d(i));const a=o(t,["toolCallCancellation"]);a!=null&&r(e,["toolCallCancellation"],Bd(a));const l=o(t,["usageMetadata"]);l!=null&&r(e,["usageMetadata"],Gd(l));const u=o(t,["goAway"]);u!=null&&r(e,["goAway"],Od(u));const f=o(t,["sessionResumptionUpdate"]);return f!=null&&r(e,["sessionResumptionUpdate"],Wd(f)),e}function zd(t){const e={},n=o(t,["setupComplete"]);n!=null&&r(e,["setupComplete"],_d(n));const s=o(t,["serverContent"]);s!=null&&r(e,["serverContent"],Ld(s));const i=o(t,["toolCall"]);i!=null&&r(e,["toolCall"],Vd(i));const a=o(t,["toolCallCancellation"]);a!=null&&r(e,["toolCallCancellation"],qd(a));const l=o(t,["usageMetadata"]);l!=null&&r(e,["usageMetadata"],Jd(l));const u=o(t,["goAway"]);u!=null&&r(e,["goAway"],Hd(u));const f=o(t,["sessionResumptionUpdate"]);return f!=null&&r(e,["sessionResumptionUpdate"],Kd(f)),e}function Xd(){return{}}function Qd(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const s=o(t,["weight"]);return s!=null&&r(e,["weight"],s),e}function Zd(t){const e={},n=o(t,["weightedPrompts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Qd(i))),r(e,["weightedPrompts"],s)}return e}function jd(t){const e={},n=o(t,["temperature"]);n!=null&&r(e,["temperature"],n);const s=o(t,["topK"]);s!=null&&r(e,["topK"],s);const i=o(t,["seed"]);i!=null&&r(e,["seed"],i);const a=o(t,["guidance"]);a!=null&&r(e,["guidance"],a);const l=o(t,["bpm"]);l!=null&&r(e,["bpm"],l);const u=o(t,["density"]);u!=null&&r(e,["density"],u);const f=o(t,["brightness"]);f!=null&&r(e,["brightness"],f);const c=o(t,["scale"]);c!=null&&r(e,["scale"],c);const m=o(t,["muteBass"]);m!=null&&r(e,["muteBass"],m);const h=o(t,["muteDrums"]);h!=null&&r(e,["muteDrums"],h);const p=o(t,["onlyBassAndDrums"]);return p!=null&&r(e,["onlyBassAndDrums"],p),e}function ef(t){const e={},n=o(t,["clientContent"]);n!=null&&r(e,["clientContent"],Zd(n));const s=o(t,["musicGenerationConfig"]);return s!=null&&r(e,["musicGenerationConfig"],jd(s)),e}function tf(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);s!=null&&r(e,["mimeType"],s);const i=o(t,["sourceMetadata"]);return i!=null&&r(e,["sourceMetadata"],ef(i)),e}function nf(t){const e={},n=o(t,["audioChunks"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>tf(i))),r(e,["audioChunks"],s)}return e}function sf(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const s=o(t,["filteredReason"]);return s!=null&&r(e,["filteredReason"],s),e}function of(t){const e={};o(t,["setupComplete"])!=null&&r(e,["setupComplete"],Xd());const s=o(t,["serverContent"]);s!=null&&r(e,["serverContent"],nf(s));const i=o(t,["filteredPrompt"]);return i!=null&&r(e,["filteredPrompt"],sf(i)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function rf(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function af(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function lf(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function uf(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],rf(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],af(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],lf(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function sn(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>uf(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function cf(t){const e={},n=o(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const s=o(t,["default"]);s!=null&&r(e,["default"],s);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const a=o(t,["enum"]);a!=null&&r(e,["enum"],a);const l=o(t,["example"]);l!=null&&r(e,["example"],l);const u=o(t,["format"]);u!=null&&r(e,["format"],u);const f=o(t,["items"]);f!=null&&r(e,["items"],f);const c=o(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const m=o(t,["maxLength"]);m!=null&&r(e,["maxLength"],m);const h=o(t,["maxProperties"]);h!=null&&r(e,["maxProperties"],h);const p=o(t,["maximum"]);p!=null&&r(e,["maximum"],p);const g=o(t,["minItems"]);g!=null&&r(e,["minItems"],g);const y=o(t,["minLength"]);y!=null&&r(e,["minLength"],y);const x=o(t,["minProperties"]);x!=null&&r(e,["minProperties"],x);const T=o(t,["minimum"]);T!=null&&r(e,["minimum"],T);const I=o(t,["nullable"]);I!=null&&r(e,["nullable"],I);const w=o(t,["pattern"]);w!=null&&r(e,["pattern"],w);const A=o(t,["properties"]);A!=null&&r(e,["properties"],A);const _=o(t,["propertyOrdering"]);_!=null&&r(e,["propertyOrdering"],_);const C=o(t,["required"]);C!=null&&r(e,["required"],C);const E=o(t,["title"]);E!=null&&r(e,["title"],E);const N=o(t,["type"]);return N!=null&&r(e,["type"],N),e}function df(t){const e={};if(o(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=o(t,["category"]);n!=null&&r(e,["category"],n);const s=o(t,["threshold"]);return s!=null&&r(e,["threshold"],s),e}function ff(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const f=o(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function mf(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function hf(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],mf(n)),e}function pf(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function gf(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],pf(n)),e}function yf(){return{}}function xf(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let f=n;Array.isArray(f)&&(f=f.map(c=>ff(c))),r(e,["functionDeclarations"],f)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],hf(s));const i=o(t,["googleSearchRetrieval"]);if(i!=null&&r(e,["googleSearchRetrieval"],gf(i)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],yf());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function vf(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["allowedFunctionNames"]);return s!=null&&r(e,["allowedFunctionNames"],s),e}function Cf(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const s=o(t,["longitude"]);return s!=null&&r(e,["longitude"],s),e}function _f(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],Cf(n));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function Tf(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],vf(n));const s=o(t,["retrievalConfig"]);return s!=null&&r(e,["retrievalConfig"],_f(s)),e}function Sf(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Vo(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],Sf(n)),e}function wf(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const s=o(t,["voiceConfig"]);return s!=null&&r(e,["voiceConfig"],Vo(s)),e}function Af(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>wf(i))),r(e,["speakerVoiceConfigs"],s)}return e}function Ef(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Vo(n));const s=o(t,["multiSpeakerVoiceConfig"]);s!=null&&r(e,["multiSpeakerVoiceConfig"],Af(s));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function If(t){const e={},n=o(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const s=o(t,["thinkingBudget"]);return s!=null&&r(e,["thinkingBudget"],s),e}function Nf(t,e,n){const s={},i=o(e,["systemInstruction"]);n!==void 0&&i!=null&&r(n,["systemInstruction"],sn(re(i)));const a=o(e,["temperature"]);a!=null&&r(s,["temperature"],a);const l=o(e,["topP"]);l!=null&&r(s,["topP"],l);const u=o(e,["topK"]);u!=null&&r(s,["topK"],u);const f=o(e,["candidateCount"]);f!=null&&r(s,["candidateCount"],f);const c=o(e,["maxOutputTokens"]);c!=null&&r(s,["maxOutputTokens"],c);const m=o(e,["stopSequences"]);m!=null&&r(s,["stopSequences"],m);const h=o(e,["responseLogprobs"]);h!=null&&r(s,["responseLogprobs"],h);const p=o(e,["logprobs"]);p!=null&&r(s,["logprobs"],p);const g=o(e,["presencePenalty"]);g!=null&&r(s,["presencePenalty"],g);const y=o(e,["frequencyPenalty"]);y!=null&&r(s,["frequencyPenalty"],y);const x=o(e,["seed"]);x!=null&&r(s,["seed"],x);const T=o(e,["responseMimeType"]);T!=null&&r(s,["responseMimeType"],T);const I=o(e,["responseSchema"]);I!=null&&r(s,["responseSchema"],cf(Xn(I)));const w=o(e,["responseJsonSchema"]);if(w!=null&&r(s,["responseJsonSchema"],w),o(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(o(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const A=o(e,["safetySettings"]);if(n!==void 0&&A!=null){let k=A;Array.isArray(k)&&(k=k.map(K=>df(K))),r(n,["safetySettings"],k)}const _=o(e,["tools"]);if(n!==void 0&&_!=null){let k=Xe(_);Array.isArray(k)&&(k=k.map(K=>xf(ze(K)))),r(n,["tools"],k)}const C=o(e,["toolConfig"]);if(n!==void 0&&C!=null&&r(n,["toolConfig"],Tf(C)),o(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const E=o(e,["cachedContent"]);n!==void 0&&E!=null&&r(n,["cachedContent"],Se(t,E));const N=o(e,["responseModalities"]);N!=null&&r(s,["responseModalities"],N);const P=o(e,["mediaResolution"]);P!=null&&r(s,["mediaResolution"],P);const b=o(e,["speechConfig"]);if(b!=null&&r(s,["speechConfig"],Ef(Qn(b))),o(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const L=o(e,["thinkingConfig"]);return L!=null&&r(s,["thinkingConfig"],If(L)),s}function bi(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["contents"]);if(i!=null){let l=he(i);Array.isArray(l)&&(l=l.map(u=>sn(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["generationConfig"],Nf(t,a,n)),n}function bf(t,e){const n={},s=o(t,["taskType"]);e!==void 0&&s!=null&&r(e,["requests[]","taskType"],s);const i=o(t,["title"]);e!==void 0&&i!=null&&r(e,["requests[]","title"],i);const a=o(t,["outputDimensionality"]);if(e!==void 0&&a!=null&&r(e,["requests[]","outputDimensionality"],a),o(t,["mimeType"])!==void 0)throw new Error("mimeType parameter is not supported in Gemini API.");if(o(t,["autoTruncate"])!==void 0)throw new Error("autoTruncate parameter is not supported in Gemini API.");return n}function Mf(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["contents"]);i!=null&&r(n,["requests[]","content"],To(t,i));const a=o(e,["config"]);a!=null&&r(n,["config"],bf(a,n));const l=o(e,["model"]);return l!==void 0&&r(n,["requests[]","model"],J(t,l)),n}function Pf(t,e){const n={};if(o(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(o(t,["negativePrompt"])!==void 0)throw new Error("negativePrompt parameter is not supported in Gemini API.");const s=o(t,["numberOfImages"]);e!==void 0&&s!=null&&r(e,["parameters","sampleCount"],s);const i=o(t,["aspectRatio"]);e!==void 0&&i!=null&&r(e,["parameters","aspectRatio"],i);const a=o(t,["guidanceScale"]);if(e!==void 0&&a!=null&&r(e,["parameters","guidanceScale"],a),o(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const l=o(t,["safetyFilterLevel"]);e!==void 0&&l!=null&&r(e,["parameters","safetySetting"],l);const u=o(t,["personGeneration"]);e!==void 0&&u!=null&&r(e,["parameters","personGeneration"],u);const f=o(t,["includeSafetyAttributes"]);e!==void 0&&f!=null&&r(e,["parameters","includeSafetyAttributes"],f);const c=o(t,["includeRaiReason"]);e!==void 0&&c!=null&&r(e,["parameters","includeRaiReason"],c);const m=o(t,["language"]);e!==void 0&&m!=null&&r(e,["parameters","language"],m);const h=o(t,["outputMimeType"]);e!==void 0&&h!=null&&r(e,["parameters","outputOptions","mimeType"],h);const p=o(t,["outputCompressionQuality"]);if(e!==void 0&&p!=null&&r(e,["parameters","outputOptions","compressionQuality"],p),o(t,["addWatermark"])!==void 0)throw new Error("addWatermark parameter is not supported in Gemini API.");if(o(t,["enhancePrompt"])!==void 0)throw new Error("enhancePrompt parameter is not supported in Gemini API.");return n}function Rf(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["prompt"]);i!=null&&r(n,["instances[0]","prompt"],i);const a=o(e,["config"]);return a!=null&&r(n,["config"],Pf(a,n)),n}function kf(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","name"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Df(t,e,n){const s={},i=o(e,["pageSize"]);n!==void 0&&i!=null&&r(n,["_query","pageSize"],i);const a=o(e,["pageToken"]);n!==void 0&&a!=null&&r(n,["_query","pageToken"],a);const l=o(e,["filter"]);n!==void 0&&l!=null&&r(n,["_query","filter"],l);const u=o(e,["queryBase"]);return n!==void 0&&u!=null&&r(n,["_url","models_url"],Ao(t,u)),s}function Lf(t,e){const n={},s=o(e,["config"]);return s!=null&&r(n,["config"],Df(t,s,n)),n}function Ff(t,e){const n={},s=o(t,["displayName"]);e!==void 0&&s!=null&&r(e,["displayName"],s);const i=o(t,["description"]);e!==void 0&&i!=null&&r(e,["description"],i);const a=o(t,["defaultCheckpointId"]);return e!==void 0&&a!=null&&r(e,["defaultCheckpointId"],a),n}function Uf(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","name"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],Ff(i,n)),n}function $f(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","name"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Vf(t){const e={};if(o(t,["systemInstruction"])!==void 0)throw new Error("systemInstruction parameter is not supported in Gemini API.");if(o(t,["tools"])!==void 0)throw new Error("tools parameter is not supported in Gemini API.");if(o(t,["generationConfig"])!==void 0)throw new Error("generationConfig parameter is not supported in Gemini API.");return e}function Bf(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["contents"]);if(i!=null){let l=he(i);Array.isArray(l)&&(l=l.map(u=>sn(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["config"],Vf(a)),n}function qf(t){const e={};if(o(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");const n=o(t,["imageBytes"]);n!=null&&r(e,["bytesBase64Encoded"],we(n));const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Gf(t,e){const n={},s=o(t,["numberOfVideos"]);if(e!==void 0&&s!=null&&r(e,["parameters","sampleCount"],s),o(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(o(t,["fps"])!==void 0)throw new Error("fps parameter is not supported in Gemini API.");const i=o(t,["durationSeconds"]);if(e!==void 0&&i!=null&&r(e,["parameters","durationSeconds"],i),o(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const a=o(t,["aspectRatio"]);if(e!==void 0&&a!=null&&r(e,["parameters","aspectRatio"],a),o(t,["resolution"])!==void 0)throw new Error("resolution parameter is not supported in Gemini API.");const l=o(t,["personGeneration"]);if(e!==void 0&&l!=null&&r(e,["parameters","personGeneration"],l),o(t,["pubsubTopic"])!==void 0)throw new Error("pubsubTopic parameter is not supported in Gemini API.");const u=o(t,["negativePrompt"]);e!==void 0&&u!=null&&r(e,["parameters","negativePrompt"],u);const f=o(t,["enhancePrompt"]);if(e!==void 0&&f!=null&&r(e,["parameters","enhancePrompt"],f),o(t,["generateAudio"])!==void 0)throw new Error("generateAudio parameter is not supported in Gemini API.");if(o(t,["lastFrame"])!==void 0)throw new Error("lastFrame parameter is not supported in Gemini API.");if(o(t,["compressionQuality"])!==void 0)throw new Error("compressionQuality parameter is not supported in Gemini API.");return n}function Jf(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["prompt"]);i!=null&&r(n,["instances[0]","prompt"],i);const a=o(e,["image"]);if(a!=null&&r(n,["instances[0]","image"],qf(a)),o(e,["video"])!==void 0)throw new Error("video parameter is not supported in Gemini API.");const l=o(e,["config"]);return l!=null&&r(n,["config"],Gf(l,n)),n}function Of(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Hf(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["data"]);s!=null&&r(e,["data"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Wf(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["fileUri"]);s!=null&&r(e,["fileUri"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Kf(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Of(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Hf(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Wf(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Ze(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Kf(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Yf(t){const e={},n=o(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const s=o(t,["default"]);s!=null&&r(e,["default"],s);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const a=o(t,["enum"]);a!=null&&r(e,["enum"],a);const l=o(t,["example"]);l!=null&&r(e,["example"],l);const u=o(t,["format"]);u!=null&&r(e,["format"],u);const f=o(t,["items"]);f!=null&&r(e,["items"],f);const c=o(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const m=o(t,["maxLength"]);m!=null&&r(e,["maxLength"],m);const h=o(t,["maxProperties"]);h!=null&&r(e,["maxProperties"],h);const p=o(t,["maximum"]);p!=null&&r(e,["maximum"],p);const g=o(t,["minItems"]);g!=null&&r(e,["minItems"],g);const y=o(t,["minLength"]);y!=null&&r(e,["minLength"],y);const x=o(t,["minProperties"]);x!=null&&r(e,["minProperties"],x);const T=o(t,["minimum"]);T!=null&&r(e,["minimum"],T);const I=o(t,["nullable"]);I!=null&&r(e,["nullable"],I);const w=o(t,["pattern"]);w!=null&&r(e,["pattern"],w);const A=o(t,["properties"]);A!=null&&r(e,["properties"],A);const _=o(t,["propertyOrdering"]);_!=null&&r(e,["propertyOrdering"],_);const C=o(t,["required"]);C!=null&&r(e,["required"],C);const E=o(t,["title"]);E!=null&&r(e,["title"],E);const N=o(t,["type"]);return N!=null&&r(e,["type"],N),e}function zf(t){const e={},n=o(t,["featureSelectionPreference"]);return n!=null&&r(e,["featureSelectionPreference"],n),e}function Xf(t){const e={},n=o(t,["method"]);n!=null&&r(e,["method"],n);const s=o(t,["category"]);s!=null&&r(e,["category"],s);const i=o(t,["threshold"]);return i!=null&&r(e,["threshold"],i),e}function Qf(t){const e={};if(o(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=o(t,["description"]);n!=null&&r(e,["description"],n);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const i=o(t,["parameters"]);i!=null&&r(e,["parameters"],i);const a=o(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=o(t,["response"]);l!=null&&r(e,["response"],l);const u=o(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function Zf(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function jf(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Zf(n)),e}function em(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function tm(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],em(n)),e}function nm(){return{}}function sm(t){const e={},n=o(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function im(t){const e={},n=o(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],sm(n));const s=o(t,["authType"]);s!=null&&r(e,["authType"],s);const i=o(t,["googleServiceAccountConfig"]);i!=null&&r(e,["googleServiceAccountConfig"],i);const a=o(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=o(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const u=o(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function om(t){const e={},n=o(t,["authConfig"]);return n!=null&&r(e,["authConfig"],im(n)),e}function rm(){return{}}function Bo(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let h=n;Array.isArray(h)&&(h=h.map(p=>Qf(p))),r(e,["functionDeclarations"],h)}const s=o(t,["retrieval"]);s!=null&&r(e,["retrieval"],s);const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],jf(i));const a=o(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],tm(a)),o(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],nm());const u=o(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],om(u)),o(t,["urlContext"])!=null&&r(e,["urlContext"],rm());const c=o(t,["codeExecution"]);c!=null&&r(e,["codeExecution"],c);const m=o(t,["computerUse"]);return m!=null&&r(e,["computerUse"],m),e}function am(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["allowedFunctionNames"]);return s!=null&&r(e,["allowedFunctionNames"],s),e}function lm(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const s=o(t,["longitude"]);return s!=null&&r(e,["longitude"],s),e}function um(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],lm(n));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function cm(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],am(n));const s=o(t,["retrievalConfig"]);return s!=null&&r(e,["retrievalConfig"],um(s)),e}function dm(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function fm(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],dm(n)),e}function mm(t){const e={},n=o(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],fm(n)),o(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function hm(t){const e={},n=o(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const s=o(t,["thinkingBudget"]);return s!=null&&r(e,["thinkingBudget"],s),e}function pm(t,e,n){const s={},i=o(e,["systemInstruction"]);n!==void 0&&i!=null&&r(n,["systemInstruction"],Ze(re(i)));const a=o(e,["temperature"]);a!=null&&r(s,["temperature"],a);const l=o(e,["topP"]);l!=null&&r(s,["topP"],l);const u=o(e,["topK"]);u!=null&&r(s,["topK"],u);const f=o(e,["candidateCount"]);f!=null&&r(s,["candidateCount"],f);const c=o(e,["maxOutputTokens"]);c!=null&&r(s,["maxOutputTokens"],c);const m=o(e,["stopSequences"]);m!=null&&r(s,["stopSequences"],m);const h=o(e,["responseLogprobs"]);h!=null&&r(s,["responseLogprobs"],h);const p=o(e,["logprobs"]);p!=null&&r(s,["logprobs"],p);const g=o(e,["presencePenalty"]);g!=null&&r(s,["presencePenalty"],g);const y=o(e,["frequencyPenalty"]);y!=null&&r(s,["frequencyPenalty"],y);const x=o(e,["seed"]);x!=null&&r(s,["seed"],x);const T=o(e,["responseMimeType"]);T!=null&&r(s,["responseMimeType"],T);const I=o(e,["responseSchema"]);I!=null&&r(s,["responseSchema"],Yf(Xn(I)));const w=o(e,["responseJsonSchema"]);w!=null&&r(s,["responseJsonSchema"],w);const A=o(e,["routingConfig"]);A!=null&&r(s,["routingConfig"],A);const _=o(e,["modelSelectionConfig"]);_!=null&&r(s,["modelConfig"],zf(_));const C=o(e,["safetySettings"]);if(n!==void 0&&C!=null){let X=C;Array.isArray(X)&&(X=X.map(ee=>Xf(ee))),r(n,["safetySettings"],X)}const E=o(e,["tools"]);if(n!==void 0&&E!=null){let X=Xe(E);Array.isArray(X)&&(X=X.map(ee=>Bo(ze(ee)))),r(n,["tools"],X)}const N=o(e,["toolConfig"]);n!==void 0&&N!=null&&r(n,["toolConfig"],cm(N));const P=o(e,["labels"]);n!==void 0&&P!=null&&r(n,["labels"],P);const b=o(e,["cachedContent"]);n!==void 0&&b!=null&&r(n,["cachedContent"],Se(t,b));const L=o(e,["responseModalities"]);L!=null&&r(s,["responseModalities"],L);const k=o(e,["mediaResolution"]);k!=null&&r(s,["mediaResolution"],k);const K=o(e,["speechConfig"]);K!=null&&r(s,["speechConfig"],mm(Qn(K)));const fe=o(e,["audioTimestamp"]);fe!=null&&r(s,["audioTimestamp"],fe);const z=o(e,["thinkingConfig"]);return z!=null&&r(s,["thinkingConfig"],hm(z)),s}function Mi(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["contents"]);if(i!=null){let l=he(i);Array.isArray(l)&&(l=l.map(u=>Ze(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["generationConfig"],pm(t,a,n)),n}function gm(t,e){const n={},s=o(t,["taskType"]);e!==void 0&&s!=null&&r(e,["instances[]","task_type"],s);const i=o(t,["title"]);e!==void 0&&i!=null&&r(e,["instances[]","title"],i);const a=o(t,["outputDimensionality"]);e!==void 0&&a!=null&&r(e,["parameters","outputDimensionality"],a);const l=o(t,["mimeType"]);e!==void 0&&l!=null&&r(e,["instances[]","mimeType"],l);const u=o(t,["autoTruncate"]);return e!==void 0&&u!=null&&r(e,["parameters","autoTruncate"],u),n}function ym(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["contents"]);i!=null&&r(n,["instances[]","content"],To(t,i));const a=o(e,["config"]);return a!=null&&r(n,["config"],gm(a,n)),n}function xm(t,e){const n={},s=o(t,["outputGcsUri"]);e!==void 0&&s!=null&&r(e,["parameters","storageUri"],s);const i=o(t,["negativePrompt"]);e!==void 0&&i!=null&&r(e,["parameters","negativePrompt"],i);const a=o(t,["numberOfImages"]);e!==void 0&&a!=null&&r(e,["parameters","sampleCount"],a);const l=o(t,["aspectRatio"]);e!==void 0&&l!=null&&r(e,["parameters","aspectRatio"],l);const u=o(t,["guidanceScale"]);e!==void 0&&u!=null&&r(e,["parameters","guidanceScale"],u);const f=o(t,["seed"]);e!==void 0&&f!=null&&r(e,["parameters","seed"],f);const c=o(t,["safetyFilterLevel"]);e!==void 0&&c!=null&&r(e,["parameters","safetySetting"],c);const m=o(t,["personGeneration"]);e!==void 0&&m!=null&&r(e,["parameters","personGeneration"],m);const h=o(t,["includeSafetyAttributes"]);e!==void 0&&h!=null&&r(e,["parameters","includeSafetyAttributes"],h);const p=o(t,["includeRaiReason"]);e!==void 0&&p!=null&&r(e,["parameters","includeRaiReason"],p);const g=o(t,["language"]);e!==void 0&&g!=null&&r(e,["parameters","language"],g);const y=o(t,["outputMimeType"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","mimeType"],y);const x=o(t,["outputCompressionQuality"]);e!==void 0&&x!=null&&r(e,["parameters","outputOptions","compressionQuality"],x);const T=o(t,["addWatermark"]);e!==void 0&&T!=null&&r(e,["parameters","addWatermark"],T);const I=o(t,["enhancePrompt"]);return e!==void 0&&I!=null&&r(e,["parameters","enhancePrompt"],I),n}function vm(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["prompt"]);i!=null&&r(n,["instances[0]","prompt"],i);const a=o(e,["config"]);return a!=null&&r(n,["config"],xm(a,n)),n}function on(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const s=o(t,["imageBytes"]);s!=null&&r(e,["bytesBase64Encoded"],we(s));const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Cm(t){const e={},n=o(t,["maskMode"]);n!=null&&r(e,["maskMode"],n);const s=o(t,["segmentationClasses"]);s!=null&&r(e,["maskClasses"],s);const i=o(t,["maskDilation"]);return i!=null&&r(e,["dilation"],i),e}function _m(t){const e={},n=o(t,["controlType"]);n!=null&&r(e,["controlType"],n);const s=o(t,["enableControlImageComputation"]);return s!=null&&r(e,["computeControl"],s),e}function Tm(t){const e={},n=o(t,["styleDescription"]);return n!=null&&r(e,["styleDescription"],n),e}function Sm(t){const e={},n=o(t,["subjectType"]);n!=null&&r(e,["subjectType"],n);const s=o(t,["subjectDescription"]);return s!=null&&r(e,["subjectDescription"],s),e}function wm(t){const e={},n=o(t,["referenceImage"]);n!=null&&r(e,["referenceImage"],on(n));const s=o(t,["referenceId"]);s!=null&&r(e,["referenceId"],s);const i=o(t,["referenceType"]);i!=null&&r(e,["referenceType"],i);const a=o(t,["maskImageConfig"]);a!=null&&r(e,["maskImageConfig"],Cm(a));const l=o(t,["controlImageConfig"]);l!=null&&r(e,["controlImageConfig"],_m(l));const u=o(t,["styleImageConfig"]);u!=null&&r(e,["styleImageConfig"],Tm(u));const f=o(t,["subjectImageConfig"]);return f!=null&&r(e,["subjectImageConfig"],Sm(f)),e}function Am(t,e){const n={},s=o(t,["outputGcsUri"]);e!==void 0&&s!=null&&r(e,["parameters","storageUri"],s);const i=o(t,["negativePrompt"]);e!==void 0&&i!=null&&r(e,["parameters","negativePrompt"],i);const a=o(t,["numberOfImages"]);e!==void 0&&a!=null&&r(e,["parameters","sampleCount"],a);const l=o(t,["aspectRatio"]);e!==void 0&&l!=null&&r(e,["parameters","aspectRatio"],l);const u=o(t,["guidanceScale"]);e!==void 0&&u!=null&&r(e,["parameters","guidanceScale"],u);const f=o(t,["seed"]);e!==void 0&&f!=null&&r(e,["parameters","seed"],f);const c=o(t,["safetyFilterLevel"]);e!==void 0&&c!=null&&r(e,["parameters","safetySetting"],c);const m=o(t,["personGeneration"]);e!==void 0&&m!=null&&r(e,["parameters","personGeneration"],m);const h=o(t,["includeSafetyAttributes"]);e!==void 0&&h!=null&&r(e,["parameters","includeSafetyAttributes"],h);const p=o(t,["includeRaiReason"]);e!==void 0&&p!=null&&r(e,["parameters","includeRaiReason"],p);const g=o(t,["language"]);e!==void 0&&g!=null&&r(e,["parameters","language"],g);const y=o(t,["outputMimeType"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","mimeType"],y);const x=o(t,["outputCompressionQuality"]);e!==void 0&&x!=null&&r(e,["parameters","outputOptions","compressionQuality"],x);const T=o(t,["editMode"]);e!==void 0&&T!=null&&r(e,["parameters","editMode"],T);const I=o(t,["baseSteps"]);return e!==void 0&&I!=null&&r(e,["parameters","editConfig","baseSteps"],I),n}function Em(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["prompt"]);i!=null&&r(n,["instances[0]","prompt"],i);const a=o(e,["referenceImages"]);if(a!=null){let u=a;Array.isArray(u)&&(u=u.map(f=>wm(f))),r(n,["instances[0]","referenceImages"],u)}const l=o(e,["config"]);return l!=null&&r(n,["config"],Am(l,n)),n}function Im(t,e){const n={},s=o(t,["includeRaiReason"]);e!==void 0&&s!=null&&r(e,["parameters","includeRaiReason"],s);const i=o(t,["outputMimeType"]);e!==void 0&&i!=null&&r(e,["parameters","outputOptions","mimeType"],i);const a=o(t,["outputCompressionQuality"]);e!==void 0&&a!=null&&r(e,["parameters","outputOptions","compressionQuality"],a);const l=o(t,["enhanceInputImage"]);e!==void 0&&l!=null&&r(e,["parameters","upscaleConfig","enhanceInputImage"],l);const u=o(t,["imagePreservationFactor"]);e!==void 0&&u!=null&&r(e,["parameters","upscaleConfig","imagePreservationFactor"],u);const f=o(t,["numberOfImages"]);e!==void 0&&f!=null&&r(e,["parameters","sampleCount"],f);const c=o(t,["mode"]);return e!==void 0&&c!=null&&r(e,["parameters","mode"],c),n}function Nm(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["image"]);i!=null&&r(n,["instances[0]","image"],on(i));const a=o(e,["upscaleFactor"]);a!=null&&r(n,["parameters","upscaleConfig","upscaleFactor"],a);const l=o(e,["config"]);return l!=null&&r(n,["config"],Im(l,n)),n}function bm(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","name"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Mm(t,e,n){const s={},i=o(e,["pageSize"]);n!==void 0&&i!=null&&r(n,["_query","pageSize"],i);const a=o(e,["pageToken"]);n!==void 0&&a!=null&&r(n,["_query","pageToken"],a);const l=o(e,["filter"]);n!==void 0&&l!=null&&r(n,["_query","filter"],l);const u=o(e,["queryBase"]);return n!==void 0&&u!=null&&r(n,["_url","models_url"],Ao(t,u)),s}function Pm(t,e){const n={},s=o(e,["config"]);return s!=null&&r(n,["config"],Mm(t,s,n)),n}function Rm(t,e){const n={},s=o(t,["displayName"]);e!==void 0&&s!=null&&r(e,["displayName"],s);const i=o(t,["description"]);e!==void 0&&i!=null&&r(e,["description"],i);const a=o(t,["defaultCheckpointId"]);return e!==void 0&&a!=null&&r(e,["defaultCheckpointId"],a),n}function km(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],Rm(i,n)),n}function Dm(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","name"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],i),n}function Lm(t,e){const n={},s=o(t,["systemInstruction"]);e!==void 0&&s!=null&&r(e,["systemInstruction"],Ze(re(s)));const i=o(t,["tools"]);if(e!==void 0&&i!=null){let l=i;Array.isArray(l)&&(l=l.map(u=>Bo(u))),r(e,["tools"],l)}const a=o(t,["generationConfig"]);return e!==void 0&&a!=null&&r(e,["generationConfig"],a),n}function Fm(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["contents"]);if(i!=null){let l=he(i);Array.isArray(l)&&(l=l.map(u=>Ze(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["config"],Lm(a,n)),n}function Um(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["contents"]);if(i!=null){let l=he(i);Array.isArray(l)&&(l=l.map(u=>Ze(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["config"],a),n}function $m(t){const e={},n=o(t,["uri"]);n!=null&&r(e,["gcsUri"],n);const s=o(t,["videoBytes"]);s!=null&&r(e,["bytesBase64Encoded"],we(s));const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Vm(t,e){const n={},s=o(t,["numberOfVideos"]);e!==void 0&&s!=null&&r(e,["parameters","sampleCount"],s);const i=o(t,["outputGcsUri"]);e!==void 0&&i!=null&&r(e,["parameters","storageUri"],i);const a=o(t,["fps"]);e!==void 0&&a!=null&&r(e,["parameters","fps"],a);const l=o(t,["durationSeconds"]);e!==void 0&&l!=null&&r(e,["parameters","durationSeconds"],l);const u=o(t,["seed"]);e!==void 0&&u!=null&&r(e,["parameters","seed"],u);const f=o(t,["aspectRatio"]);e!==void 0&&f!=null&&r(e,["parameters","aspectRatio"],f);const c=o(t,["resolution"]);e!==void 0&&c!=null&&r(e,["parameters","resolution"],c);const m=o(t,["personGeneration"]);e!==void 0&&m!=null&&r(e,["parameters","personGeneration"],m);const h=o(t,["pubsubTopic"]);e!==void 0&&h!=null&&r(e,["parameters","pubsubTopic"],h);const p=o(t,["negativePrompt"]);e!==void 0&&p!=null&&r(e,["parameters","negativePrompt"],p);const g=o(t,["enhancePrompt"]);e!==void 0&&g!=null&&r(e,["parameters","enhancePrompt"],g);const y=o(t,["generateAudio"]);e!==void 0&&y!=null&&r(e,["parameters","generateAudio"],y);const x=o(t,["lastFrame"]);e!==void 0&&x!=null&&r(e,["instances[0]","lastFrame"],on(x));const T=o(t,["compressionQuality"]);return e!==void 0&&T!=null&&r(e,["parameters","compressionQuality"],T),n}function Bm(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["_url","model"],J(t,s));const i=o(e,["prompt"]);i!=null&&r(n,["instances[0]","prompt"],i);const a=o(e,["image"]);a!=null&&r(n,["instances[0]","image"],on(a));const l=o(e,["video"]);l!=null&&r(n,["instances[0]","video"],$m(l));const u=o(e,["config"]);return u!=null&&r(n,["config"],Vm(u,n)),n}function qm(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Gm(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Jm(t){const e={},n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Om(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],qm(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Gm(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Jm(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Hm(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Om(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Wm(t){const e={},n=o(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function Km(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const s=o(t,["urlRetrievalStatus"]);return s!=null&&r(e,["urlRetrievalStatus"],s),e}function Ym(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Km(i))),r(e,["urlMetadata"],s)}return e}function zm(t){const e={},n=o(t,["content"]);n!=null&&r(e,["content"],Hm(n));const s=o(t,["citationMetadata"]);s!=null&&r(e,["citationMetadata"],Wm(s));const i=o(t,["tokenCount"]);i!=null&&r(e,["tokenCount"],i);const a=o(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=o(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],Ym(l));const u=o(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const f=o(t,["groundingMetadata"]);f!=null&&r(e,["groundingMetadata"],f);const c=o(t,["index"]);c!=null&&r(e,["index"],c);const m=o(t,["logprobsResult"]);m!=null&&r(e,["logprobsResult"],m);const h=o(t,["safetyRatings"]);return h!=null&&r(e,["safetyRatings"],h),e}function Pi(t){const e={},n=o(t,["candidates"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(u=>zm(u))),r(e,["candidates"],l)}const s=o(t,["modelVersion"]);s!=null&&r(e,["modelVersion"],s);const i=o(t,["promptFeedback"]);i!=null&&r(e,["promptFeedback"],i);const a=o(t,["usageMetadata"]);return a!=null&&r(e,["usageMetadata"],a),e}function Xm(t){const e={},n=o(t,["values"]);return n!=null&&r(e,["values"],n),e}function Qm(){return{}}function Zm(t){const e={},n=o(t,["embeddings"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Xm(a))),r(e,["embeddings"],i)}return o(t,["metadata"])!=null&&r(e,["metadata"],Qm()),e}function jm(t){const e={},n=o(t,["bytesBase64Encoded"]);n!=null&&r(e,["imageBytes"],we(n));const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function qo(t){const e={},n=o(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const s=o(t,["safetyAttributes","scores"]);s!=null&&r(e,["scores"],s);const i=o(t,["contentType"]);return i!=null&&r(e,["contentType"],i),e}function eh(t){const e={},n=o(t,["_self"]);n!=null&&r(e,["image"],jm(n));const s=o(t,["raiFilteredReason"]);s!=null&&r(e,["raiFilteredReason"],s);const i=o(t,["_self"]);return i!=null&&r(e,["safetyAttributes"],qo(i)),e}function th(t){const e={},n=o(t,["predictions"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>eh(a))),r(e,["generatedImages"],i)}const s=o(t,["positivePromptSafetyAttributes"]);return s!=null&&r(e,["positivePromptSafetyAttributes"],qo(s)),e}function nh(t){const e={},n=o(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const s=o(t,["createTime"]);s!=null&&r(e,["createTime"],s);const i=o(t,["updateTime"]);return i!=null&&r(e,["updateTime"],i),e}function kn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const a=o(t,["version"]);a!=null&&r(e,["version"],a);const l=o(t,["_self"]);l!=null&&r(e,["tunedModelInfo"],nh(l));const u=o(t,["inputTokenLimit"]);u!=null&&r(e,["inputTokenLimit"],u);const f=o(t,["outputTokenLimit"]);f!=null&&r(e,["outputTokenLimit"],f);const c=o(t,["supportedGenerationMethods"]);return c!=null&&r(e,["supportedActions"],c),e}function sh(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["_self"]);if(s!=null){let i=Eo(s);Array.isArray(i)&&(i=i.map(a=>kn(a))),r(e,["models"],i)}return e}function ih(){return{}}function oh(t){const e={},n=o(t,["totalTokens"]);n!=null&&r(e,["totalTokens"],n);const s=o(t,["cachedContentTokenCount"]);return s!=null&&r(e,["cachedContentTokenCount"],s),e}function rh(t){const e={},n=o(t,["video","uri"]);n!=null&&r(e,["uri"],n);const s=o(t,["video","encodedVideo"]);s!=null&&r(e,["videoBytes"],we(s));const i=o(t,["encoding"]);return i!=null&&r(e,["mimeType"],i),e}function ah(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],rh(n)),e}function lh(t){const e={},n=o(t,["generatedSamples"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>ah(l))),r(e,["generatedVideos"],a)}const s=o(t,["raiMediaFilteredCount"]);s!=null&&r(e,["raiMediaFilteredCount"],s);const i=o(t,["raiMediaFilteredReasons"]);return i!=null&&r(e,["raiMediaFilteredReasons"],i),e}function uh(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["metadata"]);s!=null&&r(e,["metadata"],s);const i=o(t,["done"]);i!=null&&r(e,["done"],i);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response","generateVideoResponse"]);return l!=null&&r(e,["response"],lh(l)),e}function ch(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function dh(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["data"]);s!=null&&r(e,["data"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function fh(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const s=o(t,["fileUri"]);s!=null&&r(e,["fileUri"],s);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function mh(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],ch(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],dh(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],fh(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function hh(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>mh(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function ph(t){const e={},n=o(t,["citations"]);return n!=null&&r(e,["citations"],n),e}function gh(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const s=o(t,["urlRetrievalStatus"]);return s!=null&&r(e,["urlRetrievalStatus"],s),e}function yh(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>gh(i))),r(e,["urlMetadata"],s)}return e}function xh(t){const e={},n=o(t,["content"]);n!=null&&r(e,["content"],hh(n));const s=o(t,["citationMetadata"]);s!=null&&r(e,["citationMetadata"],ph(s));const i=o(t,["finishMessage"]);i!=null&&r(e,["finishMessage"],i);const a=o(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=o(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],yh(l));const u=o(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const f=o(t,["groundingMetadata"]);f!=null&&r(e,["groundingMetadata"],f);const c=o(t,["index"]);c!=null&&r(e,["index"],c);const m=o(t,["logprobsResult"]);m!=null&&r(e,["logprobsResult"],m);const h=o(t,["safetyRatings"]);return h!=null&&r(e,["safetyRatings"],h),e}function Ri(t){const e={},n=o(t,["candidates"]);if(n!=null){let f=n;Array.isArray(f)&&(f=f.map(c=>xh(c))),r(e,["candidates"],f)}const s=o(t,["createTime"]);s!=null&&r(e,["createTime"],s);const i=o(t,["responseId"]);i!=null&&r(e,["responseId"],i);const a=o(t,["modelVersion"]);a!=null&&r(e,["modelVersion"],a);const l=o(t,["promptFeedback"]);l!=null&&r(e,["promptFeedback"],l);const u=o(t,["usageMetadata"]);return u!=null&&r(e,["usageMetadata"],u),e}function vh(t){const e={},n=o(t,["truncated"]);n!=null&&r(e,["truncated"],n);const s=o(t,["token_count"]);return s!=null&&r(e,["tokenCount"],s),e}function Ch(t){const e={},n=o(t,["values"]);n!=null&&r(e,["values"],n);const s=o(t,["statistics"]);return s!=null&&r(e,["statistics"],vh(s)),e}function _h(t){const e={},n=o(t,["billableCharacterCount"]);return n!=null&&r(e,["billableCharacterCount"],n),e}function Th(t){const e={},n=o(t,["predictions[]","embeddings"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Ch(a))),r(e,["embeddings"],i)}const s=o(t,["metadata"]);return s!=null&&r(e,["metadata"],_h(s)),e}function Sh(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const s=o(t,["bytesBase64Encoded"]);s!=null&&r(e,["imageBytes"],we(s));const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Go(t){const e={},n=o(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const s=o(t,["safetyAttributes","scores"]);s!=null&&r(e,["scores"],s);const i=o(t,["contentType"]);return i!=null&&r(e,["contentType"],i),e}function jn(t){const e={},n=o(t,["_self"]);n!=null&&r(e,["image"],Sh(n));const s=o(t,["raiFilteredReason"]);s!=null&&r(e,["raiFilteredReason"],s);const i=o(t,["_self"]);i!=null&&r(e,["safetyAttributes"],Go(i));const a=o(t,["prompt"]);return a!=null&&r(e,["enhancedPrompt"],a),e}function wh(t){const e={},n=o(t,["predictions"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>jn(a))),r(e,["generatedImages"],i)}const s=o(t,["positivePromptSafetyAttributes"]);return s!=null&&r(e,["positivePromptSafetyAttributes"],Go(s)),e}function Ah(t){const e={},n=o(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>jn(i))),r(e,["generatedImages"],s)}return e}function Eh(t){const e={},n=o(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>jn(i))),r(e,["generatedImages"],s)}return e}function Ih(t){const e={},n=o(t,["endpoint"]);n!=null&&r(e,["name"],n);const s=o(t,["deployedModelId"]);return s!=null&&r(e,["deployedModelId"],s),e}function Nh(t){const e={},n=o(t,["labels","google-vertex-llm-tuning-base-model-id"]);n!=null&&r(e,["baseModel"],n);const s=o(t,["createTime"]);s!=null&&r(e,["createTime"],s);const i=o(t,["updateTime"]);return i!=null&&r(e,["updateTime"],i),e}function bh(t){const e={},n=o(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const s=o(t,["epoch"]);s!=null&&r(e,["epoch"],s);const i=o(t,["step"]);return i!=null&&r(e,["step"],i),e}function Dn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["displayName"]);s!=null&&r(e,["displayName"],s);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const a=o(t,["versionId"]);a!=null&&r(e,["version"],a);const l=o(t,["deployedModels"]);if(l!=null){let h=l;Array.isArray(h)&&(h=h.map(p=>Ih(p))),r(e,["endpoints"],h)}const u=o(t,["labels"]);u!=null&&r(e,["labels"],u);const f=o(t,["_self"]);f!=null&&r(e,["tunedModelInfo"],Nh(f));const c=o(t,["defaultCheckpointId"]);c!=null&&r(e,["defaultCheckpointId"],c);const m=o(t,["checkpoints"]);if(m!=null){let h=m;Array.isArray(h)&&(h=h.map(p=>bh(p))),r(e,["checkpoints"],h)}return e}function Mh(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["_self"]);if(s!=null){let i=Eo(s);Array.isArray(i)&&(i=i.map(a=>Dn(a))),r(e,["models"],i)}return e}function Ph(){return{}}function Rh(t){const e={},n=o(t,["totalTokens"]);return n!=null&&r(e,["totalTokens"],n),e}function kh(t){const e={},n=o(t,["tokensInfo"]);return n!=null&&r(e,["tokensInfo"],n),e}function Dh(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const s=o(t,["bytesBase64Encoded"]);s!=null&&r(e,["videoBytes"],we(s));const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Lh(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],Dh(n)),e}function Fh(t){const e={},n=o(t,["videos"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>Lh(l))),r(e,["generatedVideos"],a)}const s=o(t,["raiMediaFilteredCount"]);s!=null&&r(e,["raiMediaFilteredCount"],s);const i=o(t,["raiMediaFilteredReasons"]);return i!=null&&r(e,["raiMediaFilteredReasons"],i),e}function Uh(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["metadata"]);s!=null&&r(e,["metadata"],s);const i=o(t,["done"]);i!=null&&r(e,["done"],i);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response"]);return l!=null&&r(e,["response"],Fh(l)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const $h="Content-Type",Vh="X-Server-Timeout",Bh="User-Agent",Ln="x-goog-api-client",qh="1.9.0",Gh=`google-genai-sdk/${qh}`,Jh="v1beta1",Oh="v1beta",ki=/^data: (.*)(?:\n\n|\r\r|\r\n\r\n)/;class Hh{constructor(e){var n,s;this.clientOptions=Object.assign(Object.assign({},e),{project:e.project,location:e.location,apiKey:e.apiKey,vertexai:e.vertexai});const i={};this.clientOptions.vertexai?(i.apiVersion=(n=this.clientOptions.apiVersion)!==null&&n!==void 0?n:Jh,i.baseUrl=this.baseUrlFromProjectLocation(),this.normalizeAuthParameters()):(i.apiVersion=(s=this.clientOptions.apiVersion)!==null&&s!==void 0?s:Oh,i.baseUrl="https://generativelanguage.googleapis.com/"),i.headers=this.getDefaultHeaders(),this.clientOptions.httpOptions=i,e.httpOptions&&(this.clientOptions.httpOptions=this.patchHttpOptions(i,e.httpOptions))}baseUrlFromProjectLocation(){return this.clientOptions.project&&this.clientOptions.location&&this.clientOptions.location!=="global"?`https://${this.clientOptions.location}-aiplatform.googleapis.com/`:"https://aiplatform.googleapis.com/"}normalizeAuthParameters(){if(this.clientOptions.project&&this.clientOptions.location){this.clientOptions.apiKey=void 0;return}this.clientOptions.project=void 0,this.clientOptions.location=void 0}isVertexAI(){var e;return(e=this.clientOptions.vertexai)!==null&&e!==void 0?e:!1}getProject(){return this.clientOptions.project}getLocation(){return this.clientOptions.location}getApiVersion(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.apiVersion!==void 0)return this.clientOptions.httpOptions.apiVersion;throw new Error("API version is not set.")}getBaseUrl(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.baseUrl!==void 0)return this.clientOptions.httpOptions.baseUrl;throw new Error("Base URL is not set.")}getRequestUrl(){return this.getRequestUrlInternal(this.clientOptions.httpOptions)}getHeaders(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.headers!==void 0)return this.clientOptions.httpOptions.headers;throw new Error("Headers are not set.")}getRequestUrlInternal(e){if(!e||e.baseUrl===void 0||e.apiVersion===void 0)throw new Error("HTTP options are not correctly set.");const s=[e.baseUrl.endsWith("/")?e.baseUrl.slice(0,-1):e.baseUrl];return e.apiVersion&&e.apiVersion!==""&&s.push(e.apiVersion),s.join("/")}getBaseResourcePath(){return`projects/${this.clientOptions.project}/locations/${this.clientOptions.location}`}getApiKey(){return this.clientOptions.apiKey}getWebsocketBaseUrl(){const e=this.getBaseUrl(),n=new URL(e);return n.protocol=n.protocol=="http:"?"ws":"wss",n.toString()}setBaseUrl(e){if(this.clientOptions.httpOptions)this.clientOptions.httpOptions.baseUrl=e;else throw new Error("HTTP options are not correctly set.")}constructUrl(e,n,s){const i=[this.getRequestUrlInternal(n)];return s&&i.push(this.getBaseResourcePath()),e!==""&&i.push(e),new URL(`${i.join("/")}`)}shouldPrependVertexProjectPath(e){return!(this.clientOptions.apiKey||!this.clientOptions.vertexai||e.path.startsWith("projects/")||e.httpMethod==="GET"&&e.path.startsWith("publishers/google/models"))}async request(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const s=this.shouldPrependVertexProjectPath(e),i=this.constructUrl(e.path,n,s);if(e.queryParams)for(const[l,u]of Object.entries(e.queryParams))i.searchParams.append(l,String(u));let a={};if(e.httpMethod==="GET"){if(e.body&&e.body!=="{}")throw new Error("Request body should be empty for GET request, but got non empty request body")}else a.body=e.body;return a=await this.includeExtraHttpOptionsToRequestInit(a,n,e.abortSignal),this.unaryApiCall(i,a,e.httpMethod)}patchHttpOptions(e,n){const s=JSON.parse(JSON.stringify(e));for(const[i,a]of Object.entries(n))typeof a=="object"?s[i]=Object.assign(Object.assign({},s[i]),a):a!==void 0&&(s[i]=a);return s}async requestStream(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const s=this.shouldPrependVertexProjectPath(e),i=this.constructUrl(e.path,n,s);(!i.searchParams.has("alt")||i.searchParams.get("alt")!=="sse")&&i.searchParams.set("alt","sse");let a={};return a.body=e.body,a=await this.includeExtraHttpOptionsToRequestInit(a,n,e.abortSignal),this.streamApiCall(i,a,e.httpMethod)}async includeExtraHttpOptionsToRequestInit(e,n,s){if(n&&n.timeout||s){const i=new AbortController,a=i.signal;if(n.timeout&&(n==null?void 0:n.timeout)>0){const l=setTimeout(()=>i.abort(),n.timeout);l&&typeof l.unref=="function"&&l.unref()}s&&s.addEventListener("abort",()=>{i.abort()}),e.signal=a}return n&&n.extraBody!==null&&Wh(e,n.extraBody),e.headers=await this.getHeadersInternal(n),e}async unaryApiCall(e,n,s){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:s})).then(async i=>(await Di(i),new In(i))).catch(i=>{throw i instanceof Error?i:new Error(JSON.stringify(i))})}async streamApiCall(e,n,s){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:s})).then(async i=>(await Di(i),this.processStreamResponse(i))).catch(i=>{throw i instanceof Error?i:new Error(JSON.stringify(i))})}processStreamResponse(e){var n;return Ye(this,arguments,function*(){const i=(n=e==null?void 0:e.body)===null||n===void 0?void 0:n.getReader(),a=new TextDecoder("utf-8");if(!i)throw new Error("Response body is empty");try{let l="";for(;;){const{done:u,value:f}=yield W(i.read());if(u){if(l.trim().length>0)throw new Error("Incomplete JSON segment at the end");break}const c=a.decode(f,{stream:!0});try{const h=JSON.parse(c);if("error"in h){const p=JSON.parse(JSON.stringify(h.error)),g=p.status,y=p.code,x=`got status: ${g}. ${JSON.stringify(h)}`;if(y>=400&&y<600)throw new nn({message:x,status:y})}}catch(h){if(h.name==="ApiError")throw h}l+=c;let m=l.match(ki);for(;m;){const h=m[1];try{const p=new Response(h,{headers:e==null?void 0:e.headers,status:e==null?void 0:e.status,statusText:e==null?void 0:e.statusText});yield yield W(new In(p)),l=l.slice(m[0].length),m=l.match(ki)}catch(p){throw new Error(`exception parsing stream chunk ${h}. ${p}`)}}}}finally{i.releaseLock()}})}async apiCall(e,n){return fetch(e,n).catch(s=>{throw new Error(`exception ${s} sending request`)})}getDefaultHeaders(){const e={},n=Gh+" "+this.clientOptions.userAgentExtra;return e[Bh]=n,e[Ln]=n,e[$h]="application/json",e}async getHeadersInternal(e){const n=new Headers;if(e&&e.headers){for(const[s,i]of Object.entries(e.headers))n.append(s,i);e.timeout&&e.timeout>0&&n.append(Vh,String(Math.ceil(e.timeout/1e3)))}return await this.clientOptions.auth.addAuthHeaders(n),n}async uploadFile(e,n){var s;const i={};n!=null&&(i.mimeType=n.mimeType,i.name=n.name,i.displayName=n.displayName),i.name&&!i.name.startsWith("files/")&&(i.name=`files/${i.name}`);const a=this.clientOptions.uploader,l=await a.stat(e);i.sizeBytes=String(l.size);const u=(s=n==null?void 0:n.mimeType)!==null&&s!==void 0?s:l.type;if(u===void 0||u==="")throw new Error("Can not determine mimeType. Please provide mimeType in the config.");i.mimeType=u;const f=await this.fetchUploadUrl(i,n);return a.upload(e,f,this)}async downloadFile(e){await this.clientOptions.downloader.download(e,this)}async fetchUploadUrl(e,n){var s;let i={};n!=null&&n.httpOptions?i=n.httpOptions:i={apiVersion:"",headers:{"Content-Type":"application/json","X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${e.sizeBytes}`,"X-Goog-Upload-Header-Content-Type":`${e.mimeType}`}};const a={file:e},l=await this.request({path:F("upload/v1beta/files",a._url),body:JSON.stringify(a),httpMethod:"POST",httpOptions:i});if(!l||!(l!=null&&l.headers))throw new Error("Server did not return an HttpResponse or the returned HttpResponse did not have headers.");const u=(s=l==null?void 0:l.headers)===null||s===void 0?void 0:s["x-goog-upload-url"];if(u===void 0)throw new Error("Failed to get upload url. Server did not return the x-google-upload-url in the headers");return u}}async function Di(t){var e;if(t===void 0)throw new Error("response is undefined");if(!t.ok){const n=t.status;let s;!((e=t.headers.get("content-type"))===null||e===void 0)&&e.includes("application/json")?s=await t.json():s={error:{message:await t.text(),code:t.status,status:t.statusText}};const i=JSON.stringify(s);throw n>=400&&n<600?new nn({message:i,status:n}):new Error(i)}}function Wh(t,e){if(!e||Object.keys(e).length===0)return;if(t.body instanceof Blob){console.warn("includeExtraBodyToRequestInit: extraBody provided but current request body is a Blob. extraBody will be ignored as merging is not supported for Blob bodies.");return}let n={};if(typeof t.body=="string"&&t.body.length>0)try{const a=JSON.parse(t.body);if(typeof a=="object"&&a!==null&&!Array.isArray(a))n=a;else{console.warn("includeExtraBodyToRequestInit: Original request body is valid JSON but not a non-array object. Skip applying extraBody to the request body.");return}}catch{console.warn("includeExtraBodyToRequestInit: Original request body is not valid JSON. Skip applying extraBody to the request body.");return}function s(a,l){const u=Object.assign({},a);for(const f in l)if(Object.prototype.hasOwnProperty.call(l,f)){const c=l[f],m=u[f];c&&typeof c=="object"&&!Array.isArray(c)&&m&&typeof m=="object"&&!Array.isArray(m)?u[f]=s(m,c):(m&&c&&typeof m!=typeof c&&console.warn(`includeExtraBodyToRequestInit:deepMerge: Type mismatch for key "${f}". Original type: ${typeof m}, New type: ${typeof c}. Overwriting.`),u[f]=c)}return u}const i=s(n,e);t.body=JSON.stringify(i)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Kh="mcp_used/unknown";function Jo(t){for(const e of t)if(es(e)||typeof e=="object"&&"inputSchema"in e)return!0;return!1}function Oo(t){var e;const n=(e=t[Ln])!==null&&e!==void 0?e:"";t[Ln]=(n+` ${Kh}`).trimStart()}function Yh(t){var e,n,s;return(s=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(i=>es(i)))!==null&&s!==void 0?s:!1}function zh(t){var e,n,s;return(s=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(i=>!es(i)))!==null&&s!==void 0?s:!1}function es(t){return t!==null&&typeof t=="object"&&t instanceof ts}function Xh(t,e=100){return Ye(this,arguments,function*(){let s,i=0;for(;i<e;){const a=yield W(t.listTools({cursor:s}));for(const l of a.tools)yield yield W(l),i++;if(!a.nextCursor)break;s=a.nextCursor}})}class ts{constructor(e=[],n){this.mcpTools=[],this.functionNameToMcpClient={},this.mcpClients=e,this.config=n}static create(e,n){return new ts(e,n)}async initialize(){var e,n,s,i;if(this.mcpTools.length>0)return;const a={},l=[];for(const m of this.mcpClients)try{for(var u=!0,f=(n=void 0,mt(Xh(m))),c;c=await f.next(),e=c.done,!e;u=!0){i=c.value,u=!1;const h=i;l.push(h);const p=h.name;if(a[p])throw new Error(`Duplicate function name ${p} found in MCP tools. Please ensure function names are unique.`);a[p]=m}}catch(h){n={error:h}}finally{try{!u&&!e&&(s=f.return)&&await s.call(f)}finally{if(n)throw n.error}}this.mcpTools=l,this.functionNameToMcpClient=a}async tool(){return await this.initialize(),Oa(this.mcpTools,this.config)}async callTool(e){await this.initialize();const n=[];for(const s of e)if(s.name in this.functionNameToMcpClient){const i=this.functionNameToMcpClient[s.name];let a;this.config.timeout&&(a={timeout:this.config.timeout});const l=await i.callTool({name:s.name,arguments:s.args},void 0,a);n.push({functionResponse:{name:s.name,response:l.isError?{error:l}:l}})}return n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */async function Qh(t,e,n){const s=new Fa;let i;n.data instanceof Blob?i=JSON.parse(await n.data.text()):i=JSON.parse(n.data);const a=of(i);Object.assign(s,a),e(s)}class Zh{constructor(e,n,s){this.apiClient=e,this.auth=n,this.webSocketFactory=s}async connect(e){var n,s;if(this.apiClient.isVertexAI())throw new Error("Live music is not supported for Vertex AI.");console.warn("Live music generation is experimental and may change in future versions.");const i=this.apiClient.getWebsocketBaseUrl(),a=this.apiClient.getApiVersion(),l=tp(this.apiClient.getDefaultHeaders()),u=this.apiClient.getApiKey(),f=`${i}/ws/google.ai.generativelanguage.${a}.GenerativeService.BidiGenerateMusic?key=${u}`;let c=()=>{};const m=new Promise(A=>{c=A}),h=e.callbacks,p=function(){c({})},g=this.apiClient,y={onopen:p,onmessage:A=>{Qh(g,h.onmessage,A)},onerror:(n=h==null?void 0:h.onerror)!==null&&n!==void 0?n:function(A){},onclose:(s=h==null?void 0:h.onclose)!==null&&s!==void 0?s:function(A){}},x=this.webSocketFactory.create(f,ep(l),y);x.connect(),await m;const T=J(this.apiClient,e.model),I=Uo({model:T}),w=Rn({setup:I});return x.send(JSON.stringify(w)),new jh(x,this.apiClient)}}class jh{constructor(e,n){this.conn=e,this.apiClient=n}async setWeightedPrompts(e){if(!e.weightedPrompts||Object.keys(e.weightedPrompts).length===0)throw new Error("Weighted prompts must be set and contain at least one entry.");const n=xd(e),s=$o(n);this.conn.send(JSON.stringify({clientContent:s}))}async setMusicGenerationConfig(e){e.musicGenerationConfig||(e.musicGenerationConfig={});const n=vd(e),s=Rn(n);this.conn.send(JSON.stringify(s))}sendPlaybackControl(e){const n=Rn({playbackControl:e});this.conn.send(JSON.stringify(n))}play(){this.sendPlaybackControl(He.PLAY)}pause(){this.sendPlaybackControl(He.PAUSE)}stop(){this.sendPlaybackControl(He.STOP)}resetContext(){this.sendPlaybackControl(He.RESET_CONTEXT)}close(){this.conn.close()}}function ep(t){const e={};return t.forEach((n,s)=>{e[s]=n}),e}function tp(t){const e=new Headers;for(const[n,s]of Object.entries(t))e.append(n,s);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const np="FunctionResponse request must have an `id` field from the response of a ToolCall.FunctionalCalls in Google AI.";async function sp(t,e,n){const s=new La;let i;n.data instanceof Blob?i=await n.data.text():n.data instanceof ArrayBuffer?i=new TextDecoder().decode(n.data):i=n.data;const a=JSON.parse(i);if(t.isVertexAI()){const l=zd(a);Object.assign(s,l)}else{const l=Yd(a);Object.assign(s,l)}e(s)}class ip{constructor(e,n,s){this.apiClient=e,this.auth=n,this.webSocketFactory=s,this.music=new Zh(this.apiClient,this.auth,this.webSocketFactory)}async connect(e){var n,s,i,a,l,u;const f=this.apiClient.getWebsocketBaseUrl(),c=this.apiClient.getApiVersion();let m;const h=this.apiClient.getDefaultHeaders();e.config&&e.config.tools&&Jo(e.config.tools)&&Oo(h);const p=lp(h);if(this.apiClient.isVertexAI())m=`${f}/ws/google.cloud.aiplatform.${c}.LlmBidiService/BidiGenerateContent`,await this.auth.addAuthHeaders(p);else{const b=this.apiClient.getApiKey();let L="BidiGenerateContent",k="key";b!=null&&b.startsWith("auth_tokens/")&&(console.warn("Warning: Ephemeral token support is experimental and may change in future versions."),c!=="v1alpha"&&console.warn("Warning: The SDK's ephemeral token support is in v1alpha only. Please use const ai = new GoogleGenAI({apiKey: token.name, httpOptions: { apiVersion: 'v1alpha' }}); before session connection."),L="BidiGenerateContentConstrained",k="access_token"),m=`${f}/ws/google.ai.generativelanguage.${c}.GenerativeService.${L}?${k}=${b}`}let g=()=>{};const y=new Promise(b=>{g=b}),x=e.callbacks,T=function(){var b;(b=x==null?void 0:x.onopen)===null||b===void 0||b.call(x),g({})},I=this.apiClient,w={onopen:T,onmessage:b=>{sp(I,x.onmessage,b)},onerror:(n=x==null?void 0:x.onerror)!==null&&n!==void 0?n:function(b){},onclose:(s=x==null?void 0:x.onclose)!==null&&s!==void 0?s:function(b){}},A=this.webSocketFactory.create(m,ap(p),w);A.connect(),await y;let _=J(this.apiClient,e.model);if(this.apiClient.isVertexAI()&&_.startsWith("publishers/")){const b=this.apiClient.getProject(),L=this.apiClient.getLocation();_=`projects/${b}/locations/${L}/`+_}let C={};this.apiClient.isVertexAI()&&((i=e.config)===null||i===void 0?void 0:i.responseModalities)===void 0&&(e.config===void 0?e.config={responseModalities:[Kt.AUDIO]}:e.config.responseModalities=[Kt.AUDIO]),!((a=e.config)===null||a===void 0)&&a.generationConfig&&console.warn("Setting `LiveConnectConfig.generation_config` is deprecated, please set the fields on `LiveConnectConfig` directly. This will become an error in a future version (not before Q3 2025).");const E=(u=(l=e.config)===null||l===void 0?void 0:l.tools)!==null&&u!==void 0?u:[],N=[];for(const b of E)if(this.isCallableTool(b)){const L=b;N.push(await L.tool())}else N.push(b);N.length>0&&(e.config.tools=N);const P={model:_,config:e.config,callbacks:e.callbacks};return this.apiClient.isVertexAI()?C=dd(this.apiClient,P):C=cd(this.apiClient,P),delete C.config,A.send(JSON.stringify(C)),new rp(A,this.apiClient)}isCallableTool(e){return"callTool"in e&&typeof e.callTool=="function"}}const op={turnComplete:!0};class rp{constructor(e,n){this.conn=e,this.apiClient=n}tLiveClientContent(e,n){if(n.turns!==null&&n.turns!==void 0){let s=[];try{s=he(n.turns),e.isVertexAI()?s=s.map(i=>Ze(i)):s=s.map(i=>sn(i))}catch{throw new Error(`Failed to parse client content "turns", type: '${typeof n.turns}'`)}return{clientContent:{turns:s,turnComplete:n.turnComplete}}}return{clientContent:{turnComplete:n.turnComplete}}}tLiveClienttToolResponse(e,n){let s=[];if(n.functionResponses==null)throw new Error("functionResponses is required.");if(Array.isArray(n.functionResponses)?s=n.functionResponses:s=[n.functionResponses],s.length===0)throw new Error("functionResponses is required.");for(const a of s){if(typeof a!="object"||a===null||!("name"in a)||!("response"in a))throw new Error(`Could not parse function response, type '${typeof a}'.`);if(!e.isVertexAI()&&!("id"in a))throw new Error(np)}return{toolResponse:{functionResponses:s}}}sendClientContent(e){e=Object.assign(Object.assign({},op),e);const n=this.tLiveClientContent(this.apiClient,e);this.conn.send(JSON.stringify(n))}sendRealtimeInput(e){let n={};this.apiClient.isVertexAI()?n={realtimeInput:yd(e)}:n={realtimeInput:gd(e)},this.conn.send(JSON.stringify(n))}sendToolResponse(e){if(e.functionResponses==null)throw new Error("Tool response parameters are required.");const n=this.tLiveClienttToolResponse(this.apiClient,e);this.conn.send(JSON.stringify(n))}close(){this.conn.close()}}function ap(t){const e={};return t.forEach((n,s)=>{e[s]=n}),e}function lp(t){const e=new Headers;for(const[n,s]of Object.entries(t))e.append(n,s);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Li=10;function Fi(t){var e,n,s;if(!((e=t==null?void 0:t.automaticFunctionCalling)===null||e===void 0)&&e.disable)return!0;let i=!1;for(const l of(n=t==null?void 0:t.tools)!==null&&n!==void 0?n:[])if(Ut(l)){i=!0;break}if(!i)return!0;const a=(s=t==null?void 0:t.automaticFunctionCalling)===null||s===void 0?void 0:s.maximumRemoteCalls;return a&&(a<0||!Number.isInteger(a))||a==0?(console.warn("Invalid maximumRemoteCalls value provided for automatic function calling. Disabled automatic function calling. Please provide a valid integer value greater than 0. maximumRemoteCalls provided:",a),!0):!1}function Ut(t){return"callTool"in t&&typeof t.callTool=="function"}function Ui(t){var e;return!(!((e=t==null?void 0:t.automaticFunctionCalling)===null||e===void 0)&&e.ignoreCallHistory)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let up=class extends Ue{constructor(e){super(),this.apiClient=e,this.generateContent=async n=>{var s,i,a,l,u;const f=await this.processParamsForMcpUsage(n);if(this.maybeMoveToResponseJsonSchem(n),!Yh(n)||Fi(n.config))return await this.generateContentInternal(f);if(zh(n))throw new Error("Automatic function calling with CallableTools and Tools is not yet supported.");let c,m;const h=he(f.contents),p=(a=(i=(s=f.config)===null||s===void 0?void 0:s.automaticFunctionCalling)===null||i===void 0?void 0:i.maximumRemoteCalls)!==null&&a!==void 0?a:Li;let g=0;for(;g<p&&(c=await this.generateContentInternal(f),!(!c.functionCalls||c.functionCalls.length===0));){const y=c.candidates[0].content,x=[];for(const T of(u=(l=n.config)===null||l===void 0?void 0:l.tools)!==null&&u!==void 0?u:[])if(Ut(T)){const w=await T.callTool(c.functionCalls);x.push(...w)}g++,m={role:"user",parts:x},f.contents=he(f.contents),f.contents.push(y),f.contents.push(m),Ui(f.config)&&(h.push(y),h.push(m))}return Ui(f.config)&&(c.automaticFunctionCallingHistory=h),c},this.generateContentStream=async n=>{if(this.maybeMoveToResponseJsonSchem(n),Fi(n.config)){const s=await this.processParamsForMcpUsage(n);return await this.generateContentStreamInternal(s)}else return await this.processAfcStream(n)},this.generateImages=async n=>await this.generateImagesInternal(n).then(s=>{var i;let a;const l=[];if(s!=null&&s.generatedImages)for(const f of s.generatedImages)f&&(f!=null&&f.safetyAttributes)&&((i=f==null?void 0:f.safetyAttributes)===null||i===void 0?void 0:i.contentType)==="Positive Prompt"?a=f==null?void 0:f.safetyAttributes:l.push(f);let u;return a?u={generatedImages:l,positivePromptSafetyAttributes:a}:u={generatedImages:l},u}),this.list=async n=>{var s;const l={config:Object.assign(Object.assign({},{queryBase:!0}),n==null?void 0:n.config)};if(this.apiClient.isVertexAI()&&!l.config.queryBase){if(!((s=l.config)===null||s===void 0)&&s.filter)throw new Error("Filtering tuned models list for Vertex AI is not currently supported");l.config.filter="labels.tune-type:*"}return new xt(Le.PAGED_ITEM_MODELS,u=>this.listInternal(u),await this.listInternal(l),l)},this.editImage=async n=>{const s={model:n.model,prompt:n.prompt,referenceImages:[],config:n.config};return n.referenceImages&&n.referenceImages&&(s.referenceImages=n.referenceImages.map(i=>i.toReferenceImageAPI())),await this.editImageInternal(s)},this.upscaleImage=async n=>{let s={numberOfImages:1,mode:"upscale"};n.config&&(s=Object.assign(Object.assign({},s),n.config));const i={model:n.model,image:n.image,upscaleFactor:n.upscaleFactor,config:s};return await this.upscaleImageInternal(i)}}maybeMoveToResponseJsonSchem(e){e.config&&e.config.responseSchema&&(e.config.responseJsonSchema||Object.keys(e.config.responseSchema).includes("$schema")&&(e.config.responseJsonSchema=e.config.responseSchema,delete e.config.responseSchema))}async processParamsForMcpUsage(e){var n,s,i;const a=(n=e.config)===null||n===void 0?void 0:n.tools;if(!a)return e;const l=await Promise.all(a.map(async f=>Ut(f)?await f.tool():f)),u={model:e.model,contents:e.contents,config:Object.assign(Object.assign({},e.config),{tools:l})};if(u.config.tools=l,e.config&&e.config.tools&&Jo(e.config.tools)){const f=(i=(s=e.config.httpOptions)===null||s===void 0?void 0:s.headers)!==null&&i!==void 0?i:{};let c=Object.assign({},f);Object.keys(c).length===0&&(c=this.apiClient.getDefaultHeaders()),Oo(c),u.config.httpOptions=Object.assign(Object.assign({},e.config.httpOptions),{headers:c})}return u}async initAfcToolsMap(e){var n,s,i;const a=new Map;for(const l of(s=(n=e.config)===null||n===void 0?void 0:n.tools)!==null&&s!==void 0?s:[])if(Ut(l)){const u=l,f=await u.tool();for(const c of(i=f.functionDeclarations)!==null&&i!==void 0?i:[]){if(!c.name)throw new Error("Function declaration name is required.");if(a.has(c.name))throw new Error(`Duplicate tool declaration name: ${c.name}`);a.set(c.name,u)}}return a}async processAfcStream(e){var n,s,i;const a=(i=(s=(n=e.config)===null||n===void 0?void 0:n.automaticFunctionCalling)===null||s===void 0?void 0:s.maximumRemoteCalls)!==null&&i!==void 0?i:Li;let l=!1,u=0;const f=await this.initAfcToolsMap(e);return function(c,m,h){var p,g;return Ye(this,arguments,function*(){for(var y,x,T,I;u<a;){l&&(u++,l=!1);const C=yield W(c.processParamsForMcpUsage(h)),E=yield W(c.generateContentStreamInternal(C)),N=[],P=[];try{for(var w=!0,A=(x=void 0,mt(E)),_;_=yield W(A.next()),y=_.done,!y;w=!0){I=_.value,w=!1;const b=I;if(yield yield W(b),b.candidates&&(!((p=b.candidates[0])===null||p===void 0)&&p.content)){P.push(b.candidates[0].content);for(const L of(g=b.candidates[0].content.parts)!==null&&g!==void 0?g:[])if(u<a&&L.functionCall){if(!L.functionCall.name)throw new Error("Function call name was not returned by the model.");if(m.has(L.functionCall.name)){const k=yield W(m.get(L.functionCall.name).callTool([L.functionCall]));N.push(...k)}else throw new Error(`Automatic function calling was requested, but not all the tools the model used implement the CallableTool interface. Available tools: ${m.keys()}, mising tool: ${L.functionCall.name}`)}}}}catch(b){x={error:b}}finally{try{!w&&!y&&(T=A.return)&&(yield W(T.call(A)))}finally{if(x)throw x.error}}if(N.length>0){l=!0;const b=new nt;b.candidates=[{content:{role:"user",parts:N}}],yield yield W(b);const L=[];L.push(...P),L.push({role:"user",parts:N});const k=he(h.contents).concat(L);h.contents=k}else break}})}(this,f,e)}async generateContentInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Mi(this.apiClient,e);return u=F("{model}:generateContent",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Ri(m),p=new nt;return Object.assign(p,h),p})}else{const c=bi(this.apiClient,e);return u=F("{model}:generateContent",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Pi(m),p=new nt;return Object.assign(p,h),p})}}async generateContentStreamInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Mi(this.apiClient,e);return u=F("{model}:streamGenerateContent?alt=sse",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.requestStream({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}),l.then(function(h){return Ye(this,arguments,function*(){var p,g,y,x;try{for(var T=!0,I=mt(h),w;w=yield W(I.next()),p=w.done,!p;T=!0){x=w.value,T=!1;const _=Ri(yield W(x.json())),C=new nt;Object.assign(C,_),yield yield W(C)}}catch(A){g={error:A}}finally{try{!T&&!p&&(y=I.return)&&(yield W(y.call(I)))}finally{if(g)throw g.error}}})})}else{const c=bi(this.apiClient,e);return u=F("{model}:streamGenerateContent?alt=sse",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.requestStream({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}),l.then(function(h){return Ye(this,arguments,function*(){var p,g,y,x;try{for(var T=!0,I=mt(h),w;w=yield W(I.next()),p=w.done,!p;T=!0){x=w.value,T=!1;const _=Pi(yield W(x.json())),C=new nt;Object.assign(C,_),yield yield W(C)}}catch(A){g={error:A}}finally{try{!T&&!p&&(y=I.return)&&(yield W(y.call(I)))}finally{if(g)throw g.error}}})})}}async embedContent(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=ym(this.apiClient,e);return u=F("{model}:predict",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Th(m),p=new ui;return Object.assign(p,h),p})}else{const c=Mf(this.apiClient,e);return u=F("{model}:batchEmbedContents",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Zm(m),p=new ui;return Object.assign(p,h),p})}}async generateImagesInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=vm(this.apiClient,e);return u=F("{model}:predict",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=wh(m),p=new ci;return Object.assign(p,h),p})}else{const c=Rf(this.apiClient,e);return u=F("{model}:predict",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=th(m),p=new ci;return Object.assign(p,h),p})}}async editImageInternal(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI()){const u=Em(this.apiClient,e);return a=F("{model}:predict",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>{const c=Ah(f),m=new ba;return Object.assign(m,c),m})}else throw new Error("This method is only supported by the Vertex AI.")}async upscaleImageInternal(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI()){const u=Nm(this.apiClient,e);return a=F("{model}:predict",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>{const c=Eh(f),m=new Ma;return Object.assign(m,c),m})}else throw new Error("This method is only supported by the Vertex AI.")}async get(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=bm(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Dn(m))}else{const c=kf(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>kn(m))}}async listInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Pm(this.apiClient,e);return u=F("{models_url}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Mh(m),p=new di;return Object.assign(p,h),p})}else{const c=Lf(this.apiClient,e);return u=F("{models_url}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=sh(m),p=new di;return Object.assign(p,h),p})}}async update(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=km(this.apiClient,e);return u=F("{model}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Dn(m))}else{const c=Uf(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>kn(m))}}async delete(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Dm(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(()=>{const m=Ph(),h=new fi;return Object.assign(h,m),h})}else{const c=$f(this.apiClient,e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(()=>{const m=ih(),h=new fi;return Object.assign(h,m),h})}}async countTokens(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Fm(this.apiClient,e);return u=F("{model}:countTokens",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Rh(m),p=new mi;return Object.assign(p,h),p})}else{const c=Bf(this.apiClient,e);return u=F("{model}:countTokens",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=oh(m),p=new mi;return Object.assign(p,h),p})}}async computeTokens(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI()){const u=Um(this.apiClient,e);return a=F("{model}:computeTokens",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>{const c=kh(f),m=new Pa;return Object.assign(m,c),m})}else throw new Error("This method is only supported by the Vertex AI.")}async generateVideos(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=Bm(this.apiClient,e);return u=F("{model}:predictLongRunning",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Uh(m))}else{const c=Jf(this.apiClient,e);return u=F("{model}:predictLongRunning",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>uh(m))}}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function cp(t){const e={},n=o(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function dp(t){const e={},n=o(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function fp(t){const e={},n=o(t,["operationName"]);n!=null&&r(e,["operationName"],n);const s=o(t,["resourceName"]);s!=null&&r(e,["_url","resourceName"],s);const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function mp(t){const e={},n=o(t,["video","uri"]);n!=null&&r(e,["uri"],n);const s=o(t,["video","encodedVideo"]);s!=null&&r(e,["videoBytes"],we(s));const i=o(t,["encoding"]);return i!=null&&r(e,["mimeType"],i),e}function hp(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],mp(n)),e}function pp(t){const e={},n=o(t,["generatedSamples"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>hp(l))),r(e,["generatedVideos"],a)}const s=o(t,["raiMediaFilteredCount"]);s!=null&&r(e,["raiMediaFilteredCount"],s);const i=o(t,["raiMediaFilteredReasons"]);return i!=null&&r(e,["raiMediaFilteredReasons"],i),e}function gp(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["metadata"]);s!=null&&r(e,["metadata"],s);const i=o(t,["done"]);i!=null&&r(e,["done"],i);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response","generateVideoResponse"]);return l!=null&&r(e,["response"],pp(l)),e}function yp(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const s=o(t,["bytesBase64Encoded"]);s!=null&&r(e,["videoBytes"],we(s));const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function xp(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],yp(n)),e}function vp(t){const e={},n=o(t,["videos"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>xp(l))),r(e,["generatedVideos"],a)}const s=o(t,["raiMediaFilteredCount"]);s!=null&&r(e,["raiMediaFilteredCount"],s);const i=o(t,["raiMediaFilteredReasons"]);return i!=null&&r(e,["raiMediaFilteredReasons"],i),e}function $i(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["metadata"]);s!=null&&r(e,["metadata"],s);const i=o(t,["done"]);i!=null&&r(e,["done"],i);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response"]);return l!=null&&r(e,["response"],vp(l)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Cp extends Ue{constructor(e){super(),this.apiClient=e}async getVideosOperation(e){const n=e.operation,s=e.config;if(n.name===void 0||n.name==="")throw new Error("Operation name is required.");if(this.apiClient.isVertexAI()){const i=n.name.split("/operations/")[0];let a;return s&&"httpOptions"in s&&(a=s.httpOptions),this.fetchPredictVideosOperationInternal({operationName:n.name,resourceName:i,config:{httpOptions:a}})}else return this.getVideosOperationInternal({operationName:n.name,config:s})}async getVideosOperationInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=dp(e);return u=F("{operationName}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>$i(m))}else{const c=cp(e);return u=F("{operationName}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>gp(m))}}async fetchPredictVideosOperationInternal(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI()){const u=fp(e);return a=F("{resourceName}:fetchPredictOperation",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>$i(f))}else throw new Error("This method is only supported by the Vertex AI.")}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function _p(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Ho(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],_p(n)),e}function Tp(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const s=o(t,["voiceConfig"]);return s!=null&&r(e,["voiceConfig"],Ho(s)),e}function Sp(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>Tp(i))),r(e,["speakerVoiceConfigs"],s)}return e}function wp(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Ho(n));const s=o(t,["multiSpeakerVoiceConfig"]);s!=null&&r(e,["multiSpeakerVoiceConfig"],Sp(s));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function Ap(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const s=o(t,["endOffset"]);s!=null&&r(e,["endOffset"],s);const i=o(t,["startOffset"]);return i!=null&&r(e,["startOffset"],i),e}function Ep(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ip(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Np(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Ap(n));const s=o(t,["thought"]);s!=null&&r(e,["thought"],s);const i=o(t,["inlineData"]);i!=null&&r(e,["inlineData"],Ep(i));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ip(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const f=o(t,["executableCode"]);f!=null&&r(e,["executableCode"],f);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bp(t){const e={},n=o(t,["parts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(a=>Np(a))),r(e,["parts"],i)}const s=o(t,["role"]);return s!=null&&r(e,["role"],s),e}function Mp(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const f=o(t,["responseJsonSchema"]);return f!=null&&r(e,["responseJsonSchema"],f),e}function Pp(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const s=o(t,["endTime"]);return s!=null&&r(e,["endTime"],s),e}function Rp(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Pp(n)),e}function kp(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const s=o(t,["dynamicThreshold"]);return s!=null&&r(e,["dynamicThreshold"],s),e}function Dp(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],kp(n)),e}function Lp(){return{}}function Fp(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let f=n;Array.isArray(f)&&(f=f.map(c=>Mp(c))),r(e,["functionDeclarations"],f)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],Rp(s));const i=o(t,["googleSearchRetrieval"]);if(i!=null&&r(e,["googleSearchRetrieval"],Dp(i)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],Lp());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function Up(t){const e={},n=o(t,["handle"]);if(n!=null&&r(e,["handle"],n),o(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function Vi(){return{}}function $p(t){const e={},n=o(t,["disabled"]);n!=null&&r(e,["disabled"],n);const s=o(t,["startOfSpeechSensitivity"]);s!=null&&r(e,["startOfSpeechSensitivity"],s);const i=o(t,["endOfSpeechSensitivity"]);i!=null&&r(e,["endOfSpeechSensitivity"],i);const a=o(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=o(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function Vp(t){const e={},n=o(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],$p(n));const s=o(t,["activityHandling"]);s!=null&&r(e,["activityHandling"],s);const i=o(t,["turnCoverage"]);return i!=null&&r(e,["turnCoverage"],i),e}function Bp(t){const e={},n=o(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function qp(t){const e={},n=o(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const s=o(t,["slidingWindow"]);return s!=null&&r(e,["slidingWindow"],Bp(s)),e}function Gp(t){const e={},n=o(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function Jp(t,e){const n={},s=o(t,["generationConfig"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig"],s);const i=o(t,["responseModalities"]);e!==void 0&&i!=null&&r(e,["setup","generationConfig","responseModalities"],i);const a=o(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=o(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const u=o(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const f=o(t,["maxOutputTokens"]);e!==void 0&&f!=null&&r(e,["setup","generationConfig","maxOutputTokens"],f);const c=o(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const m=o(t,["seed"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","seed"],m);const h=o(t,["speechConfig"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","speechConfig"],wp(Zn(h)));const p=o(t,["enableAffectiveDialog"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],p);const g=o(t,["systemInstruction"]);e!==void 0&&g!=null&&r(e,["setup","systemInstruction"],bp(re(g)));const y=o(t,["tools"]);if(e!==void 0&&y!=null){let C=Xe(y);Array.isArray(C)&&(C=C.map(E=>Fp(ze(E)))),r(e,["setup","tools"],C)}const x=o(t,["sessionResumption"]);e!==void 0&&x!=null&&r(e,["setup","sessionResumption"],Up(x));const T=o(t,["inputAudioTranscription"]);e!==void 0&&T!=null&&r(e,["setup","inputAudioTranscription"],Vi());const I=o(t,["outputAudioTranscription"]);e!==void 0&&I!=null&&r(e,["setup","outputAudioTranscription"],Vi());const w=o(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],Vp(w));const A=o(t,["contextWindowCompression"]);e!==void 0&&A!=null&&r(e,["setup","contextWindowCompression"],qp(A));const _=o(t,["proactivity"]);return e!==void 0&&_!=null&&r(e,["setup","proactivity"],Gp(_)),n}function Op(t,e){const n={},s=o(e,["model"]);s!=null&&r(n,["setup","model"],J(t,s));const i=o(e,["config"]);return i!=null&&r(n,["config"],Jp(i,n)),n}function Hp(t,e,n){const s={},i=o(e,["expireTime"]);n!==void 0&&i!=null&&r(n,["expireTime"],i);const a=o(e,["newSessionExpireTime"]);n!==void 0&&a!=null&&r(n,["newSessionExpireTime"],a);const l=o(e,["uses"]);n!==void 0&&l!=null&&r(n,["uses"],l);const u=o(e,["liveConnectConstraints"]);n!==void 0&&u!=null&&r(n,["bidiGenerateContentSetup"],Op(t,u));const f=o(e,["lockAdditionalFields"]);return n!==void 0&&f!=null&&r(n,["fieldMask"],f),s}function Wp(t,e){const n={},s=o(e,["config"]);return s!=null&&r(n,["config"],Hp(t,s,n)),n}function Kp(t){const e={},n=o(t,["name"]);return n!=null&&r(e,["name"],n),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Yp(t){const e=[];for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)){const s=t[n];if(typeof s=="object"&&s!=null&&Object.keys(s).length>0){const i=Object.keys(s).map(a=>`${n}.${a}`);e.push(...i)}else e.push(n)}return e.join(",")}function zp(t,e){let n=null;const s=t.bidiGenerateContentSetup;if(typeof s=="object"&&s!==null&&"setup"in s){const a=s.setup;typeof a=="object"&&a!==null?(t.bidiGenerateContentSetup=a,n=a):delete t.bidiGenerateContentSetup}else s!==void 0&&delete t.bidiGenerateContentSetup;const i=t.fieldMask;if(n){const a=Yp(n);if(Array.isArray(e==null?void 0:e.lockAdditionalFields)&&(e==null?void 0:e.lockAdditionalFields.length)===0)a?t.fieldMask=a:delete t.fieldMask;else if(e!=null&&e.lockAdditionalFields&&e.lockAdditionalFields.length>0&&i!==null&&Array.isArray(i)&&i.length>0){const l=["temperature","topK","topP","maxOutputTokens","responseModalities","seed","speechConfig"];let u=[];i.length>0&&(u=i.map(c=>l.includes(c)?`generationConfig.${c}`:c));const f=[];a&&f.push(a),u.length>0&&f.push(...u),f.length>0?t.fieldMask=f.join(","):delete t.fieldMask}else delete t.fieldMask}else i!==null&&Array.isArray(i)&&i.length>0?t.fieldMask=i.join(","):delete t.fieldMask;return t}class Xp extends Ue{constructor(e){super(),this.apiClient=e}async create(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI())throw new Error("The client.tokens.create method is only supported by the Gemini Developer API.");{const u=Wp(this.apiClient,e);a=F("auth_tokens",u._url),l=u._query,delete u.config,delete u._url,delete u._query;const f=zp(u,e.config);return i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(f),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(c=>c.json()),i.then(c=>Kp(c))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Qp(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","name"],n);const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function Zp(t,e){const n={},s=o(t,["pageSize"]);e!==void 0&&s!=null&&r(e,["_query","pageSize"],s);const i=o(t,["pageToken"]);e!==void 0&&i!=null&&r(e,["_query","pageToken"],i);const a=o(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function jp(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],Zp(n,e)),e}function eg(t){const e={},n=o(t,["textInput"]);n!=null&&r(e,["textInput"],n);const s=o(t,["output"]);return s!=null&&r(e,["output"],s),e}function tg(t){const e={};if(o(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");if(o(t,["vertexDatasetResource"])!==void 0)throw new Error("vertexDatasetResource parameter is not supported in Gemini API.");const n=o(t,["examples"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(i=>eg(i))),r(e,["examples","examples"],s)}return e}function ng(t,e){const n={};if(o(t,["validationDataset"])!==void 0)throw new Error("validationDataset parameter is not supported in Gemini API.");const s=o(t,["tunedModelDisplayName"]);if(e!==void 0&&s!=null&&r(e,["displayName"],s),o(t,["description"])!==void 0)throw new Error("description parameter is not supported in Gemini API.");const i=o(t,["epochCount"]);e!==void 0&&i!=null&&r(e,["tuningTask","hyperparameters","epochCount"],i);const a=o(t,["learningRateMultiplier"]);if(a!=null&&r(n,["tuningTask","hyperparameters","learningRateMultiplier"],a),o(t,["exportLastCheckpointOnly"])!==void 0)throw new Error("exportLastCheckpointOnly parameter is not supported in Gemini API.");if(o(t,["adapterSize"])!==void 0)throw new Error("adapterSize parameter is not supported in Gemini API.");const l=o(t,["batchSize"]);e!==void 0&&l!=null&&r(e,["tuningTask","hyperparameters","batchSize"],l);const u=o(t,["learningRate"]);return e!==void 0&&u!=null&&r(e,["tuningTask","hyperparameters","learningRate"],u),n}function sg(t){const e={},n=o(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const s=o(t,["trainingDataset"]);s!=null&&r(e,["tuningTask","trainingData"],tg(s));const i=o(t,["config"]);return i!=null&&r(e,["config"],ng(i,e)),e}function ig(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","name"],n);const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function og(t,e){const n={},s=o(t,["pageSize"]);e!==void 0&&s!=null&&r(e,["_query","pageSize"],s);const i=o(t,["pageToken"]);e!==void 0&&i!=null&&r(e,["_query","pageToken"],i);const a=o(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function rg(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],og(n,e)),e}function ag(t,e){const n={},s=o(t,["gcsUri"]);e!==void 0&&s!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],s);const i=o(t,["vertexDatasetResource"]);if(e!==void 0&&i!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],i),o(t,["examples"])!==void 0)throw new Error("examples parameter is not supported in Vertex AI.");return n}function lg(t,e){const n={},s=o(t,["gcsUri"]);s!=null&&r(n,["validationDatasetUri"],s);const i=o(t,["vertexDatasetResource"]);return e!==void 0&&i!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],i),n}function ug(t,e){const n={},s=o(t,["validationDataset"]);e!==void 0&&s!=null&&r(e,["supervisedTuningSpec"],lg(s,n));const i=o(t,["tunedModelDisplayName"]);e!==void 0&&i!=null&&r(e,["tunedModelDisplayName"],i);const a=o(t,["description"]);e!==void 0&&a!=null&&r(e,["description"],a);const l=o(t,["epochCount"]);e!==void 0&&l!=null&&r(e,["supervisedTuningSpec","hyperParameters","epochCount"],l);const u=o(t,["learningRateMultiplier"]);e!==void 0&&u!=null&&r(e,["supervisedTuningSpec","hyperParameters","learningRateMultiplier"],u);const f=o(t,["exportLastCheckpointOnly"]);e!==void 0&&f!=null&&r(e,["supervisedTuningSpec","exportLastCheckpointOnly"],f);const c=o(t,["adapterSize"]);if(e!==void 0&&c!=null&&r(e,["supervisedTuningSpec","hyperParameters","adapterSize"],c),o(t,["batchSize"])!==void 0)throw new Error("batchSize parameter is not supported in Vertex AI.");if(o(t,["learningRate"])!==void 0)throw new Error("learningRate parameter is not supported in Vertex AI.");return n}function cg(t){const e={},n=o(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const s=o(t,["trainingDataset"]);s!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],ag(s,e));const i=o(t,["config"]);return i!=null&&r(e,["config"],ug(i,e)),e}function dg(t){const e={},n=o(t,["name"]);n!=null&&r(e,["model"],n);const s=o(t,["name"]);return s!=null&&r(e,["endpoint"],s),e}function Wo(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["state"]);s!=null&&r(e,["state"],So(s));const i=o(t,["createTime"]);i!=null&&r(e,["createTime"],i);const a=o(t,["tuningTask","startTime"]);a!=null&&r(e,["startTime"],a);const l=o(t,["tuningTask","completeTime"]);l!=null&&r(e,["endTime"],l);const u=o(t,["updateTime"]);u!=null&&r(e,["updateTime"],u);const f=o(t,["description"]);f!=null&&r(e,["description"],f);const c=o(t,["baseModel"]);c!=null&&r(e,["baseModel"],c);const m=o(t,["_self"]);m!=null&&r(e,["tunedModel"],dg(m));const h=o(t,["distillationSpec"]);h!=null&&r(e,["distillationSpec"],h);const p=o(t,["experiment"]);p!=null&&r(e,["experiment"],p);const g=o(t,["labels"]);g!=null&&r(e,["labels"],g);const y=o(t,["pipelineJob"]);y!=null&&r(e,["pipelineJob"],y);const x=o(t,["satisfiesPzi"]);x!=null&&r(e,["satisfiesPzi"],x);const T=o(t,["satisfiesPzs"]);T!=null&&r(e,["satisfiesPzs"],T);const I=o(t,["serviceAccount"]);I!=null&&r(e,["serviceAccount"],I);const w=o(t,["tunedModelDisplayName"]);return w!=null&&r(e,["tunedModelDisplayName"],w),e}function fg(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["tunedModels"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>Wo(a))),r(e,["tuningJobs"],i)}return e}function mg(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["metadata"]);s!=null&&r(e,["metadata"],s);const i=o(t,["done"]);i!=null&&r(e,["done"],i);const a=o(t,["error"]);return a!=null&&r(e,["error"],a),e}function hg(t){const e={},n=o(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const s=o(t,["epoch"]);s!=null&&r(e,["epoch"],s);const i=o(t,["step"]);i!=null&&r(e,["step"],i);const a=o(t,["endpoint"]);return a!=null&&r(e,["endpoint"],a),e}function pg(t){const e={},n=o(t,["model"]);n!=null&&r(e,["model"],n);const s=o(t,["endpoint"]);s!=null&&r(e,["endpoint"],s);const i=o(t,["checkpoints"]);if(i!=null){let a=i;Array.isArray(a)&&(a=a.map(l=>hg(l))),r(e,["checkpoints"],a)}return e}function Fn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const s=o(t,["state"]);s!=null&&r(e,["state"],So(s));const i=o(t,["createTime"]);i!=null&&r(e,["createTime"],i);const a=o(t,["startTime"]);a!=null&&r(e,["startTime"],a);const l=o(t,["endTime"]);l!=null&&r(e,["endTime"],l);const u=o(t,["updateTime"]);u!=null&&r(e,["updateTime"],u);const f=o(t,["error"]);f!=null&&r(e,["error"],f);const c=o(t,["description"]);c!=null&&r(e,["description"],c);const m=o(t,["baseModel"]);m!=null&&r(e,["baseModel"],m);const h=o(t,["tunedModel"]);h!=null&&r(e,["tunedModel"],pg(h));const p=o(t,["supervisedTuningSpec"]);p!=null&&r(e,["supervisedTuningSpec"],p);const g=o(t,["tuningDataStats"]);g!=null&&r(e,["tuningDataStats"],g);const y=o(t,["encryptionSpec"]);y!=null&&r(e,["encryptionSpec"],y);const x=o(t,["partnerModelTuningSpec"]);x!=null&&r(e,["partnerModelTuningSpec"],x);const T=o(t,["distillationSpec"]);T!=null&&r(e,["distillationSpec"],T);const I=o(t,["experiment"]);I!=null&&r(e,["experiment"],I);const w=o(t,["labels"]);w!=null&&r(e,["labels"],w);const A=o(t,["pipelineJob"]);A!=null&&r(e,["pipelineJob"],A);const _=o(t,["satisfiesPzi"]);_!=null&&r(e,["satisfiesPzi"],_);const C=o(t,["satisfiesPzs"]);C!=null&&r(e,["satisfiesPzs"],C);const E=o(t,["serviceAccount"]);E!=null&&r(e,["serviceAccount"],E);const N=o(t,["tunedModelDisplayName"]);return N!=null&&r(e,["tunedModelDisplayName"],N),e}function gg(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const s=o(t,["tuningJobs"]);if(s!=null){let i=s;Array.isArray(i)&&(i=i.map(a=>Fn(a))),r(e,["tuningJobs"],i)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class yg extends Ue{constructor(e){super(),this.apiClient=e,this.get=async n=>await this.getInternal(n),this.list=async(n={})=>new xt(Le.PAGED_ITEM_TUNING_JOBS,s=>this.listInternal(s),await this.listInternal(n),n),this.tune=async n=>{if(this.apiClient.isVertexAI())return await this.tuneInternal(n);{const s=await this.tuneMldevInternal(n);let i="";return s.metadata!==void 0&&s.metadata.tunedModel!==void 0?i=s.metadata.tunedModel:s.name!==void 0&&s.name.includes("/operations/")&&(i=s.name.split("/operations/")[0]),{name:i,state:En.JOB_STATE_QUEUED}}}}async getInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=ig(e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>Fn(m))}else{const c=Qp(e);return u=F("{name}",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Wo(m))}}async listInternal(e){var n,s,i,a;let l,u="",f={};if(this.apiClient.isVertexAI()){const c=rg(e);return u=F("tuningJobs",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(m=>m.json()),l.then(m=>{const h=gg(m),p=new hi;return Object.assign(p,h),p})}else{const c=jp(e);return u=F("tunedModels",c._url),f=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:f,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(i=e.config)===null||i===void 0?void 0:i.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=fg(m),p=new hi;return Object.assign(p,h),p})}}async tuneInternal(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI()){const u=cg(e);return a=F("tuningJobs",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>Fn(f))}else throw new Error("This method is only supported by the Vertex AI.")}async tuneMldevInternal(e){var n,s;let i,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=sg(e);return a=F("tunedModels",u._url),l=u._query,delete u.config,delete u._url,delete u._query,i=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(s=e.config)===null||s===void 0?void 0:s.abortSignal}).then(f=>f.json()),i.then(f=>mg(f))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class xg{async download(e,n){throw new Error("Download to file is not supported in the browser, please use a browser compliant download like an <a> tag.")}}const vg=1024*1024*8,Cg=3,_g=1e3,Tg=2,hn="x-goog-upload-status";async function Sg(t,e,n){var s,i,a;let l=0,u=0,f=new In(new Response),c="upload";for(l=t.size;u<l;){const h=Math.min(vg,l-u),p=t.slice(u,u+h);u+h>=l&&(c+=", finalize");let g=0,y=_g;for(;g<Cg&&(f=await n.request({path:"",body:p,httpMethod:"POST",httpOptions:{apiVersion:"",baseUrl:e,headers:{"X-Goog-Upload-Command":c,"X-Goog-Upload-Offset":String(u),"Content-Length":String(h)}}}),!(!((s=f==null?void 0:f.headers)===null||s===void 0)&&s[hn]));)g++,await Ag(y),y=y*Tg;if(u+=h,((i=f==null?void 0:f.headers)===null||i===void 0?void 0:i[hn])!=="active")break;if(l<=u)throw new Error("All content has been uploaded, but the upload status is not finalized.")}const m=await(f==null?void 0:f.json());if(((a=f==null?void 0:f.headers)===null||a===void 0?void 0:a[hn])!=="final")throw new Error("Failed to upload file: Upload status is not finalized.");return m.file}async function wg(t){return{size:t.size,type:t.type}}function Ag(t){return new Promise(e=>setTimeout(e,t))}class Eg{async upload(e,n,s){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await Sg(e,n,s)}async stat(e){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await wg(e)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Ig{create(e,n,s){return new Ng(e,n,s)}}class Ng{constructor(e,n,s){this.url=e,this.headers=n,this.callbacks=s}connect(){this.ws=new WebSocket(this.url),this.ws.onopen=this.callbacks.onopen,this.ws.onerror=this.callbacks.onerror,this.ws.onclose=this.callbacks.onclose,this.ws.onmessage=this.callbacks.onmessage}send(e){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.send(e)}close(){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.close()}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Bi="x-goog-api-key";class bg{constructor(e){this.apiKey=e}async addAuthHeaders(e){if(e.get(Bi)===null){if(this.apiKey.startsWith("auth_tokens/"))throw new Error("Ephemeral tokens are only supported by the live API.");if(!this.apiKey)throw new Error("API key is missing. Please provide a valid API key.");e.append(Bi,this.apiKey)}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Mg="gl-node/";class qi{constructor(e){var n;if(e.apiKey==null)throw new Error("An API Key must be set when running in a browser");if(e.project||e.location)throw new Error("Vertex AI project based authentication is not supported on browser runtimes. Please do not provide a project or location.");this.vertexai=(n=e.vertexai)!==null&&n!==void 0?n:!1,this.apiKey=e.apiKey;const s=Na(e,void 0,void 0);s&&(e.httpOptions?e.httpOptions.baseUrl=s:e.httpOptions={baseUrl:s}),this.apiVersion=e.apiVersion;const i=new bg(this.apiKey);this.apiClient=new Hh({auth:i,apiVersion:this.apiVersion,apiKey:this.apiKey,vertexai:this.vertexai,httpOptions:e.httpOptions,userAgentExtra:Mg+"web",uploader:new Eg,downloader:new xg}),this.models=new up(this.apiClient),this.live=new ip(this.apiClient,i,new Ig),this.batches=new Ql(this.apiClient),this.chats=new tc(this.models,this.apiClient),this.caches=new Zu(this.apiClient),this.files=new hc(this.apiClient),this.operations=new Cp(this.apiClient),this.authTokens=new Xp(this.apiClient),this.tunings=new yg(this.apiClient)}}function $(t,e,n,s,i){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,n),n}function v(t,e,n,s){if(n==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!s:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?s:n==="a"?s.call(t):s?s.value:e.get(t)}let Ko=function(){const{crypto:t}=globalThis;if(t!=null&&t.randomUUID)return Ko=t.randomUUID.bind(t),t.randomUUID();const e=new Uint8Array(1),n=t?()=>t.getRandomValues(e)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,s=>(+s^n()&15>>+s/4).toString(16))};function Un(t){return typeof t=="object"&&t!==null&&("name"in t&&t.name==="AbortError"||"message"in t&&String(t.message).includes("FetchRequestCanceledException"))}const $n=t=>{if(t instanceof Error)return t;if(typeof t=="object"&&t!==null){try{if(Object.prototype.toString.call(t)==="[object Error]"){const e=new Error(t.message,t.cause?{cause:t.cause}:{});return t.stack&&(e.stack=t.stack),t.cause&&!e.cause&&(e.cause=t.cause),t.name&&(e.name=t.name),e}}catch{}try{return new Error(JSON.stringify(t))}catch{}}return new Error(t)};class V extends Error{}class se extends V{constructor(e,n,s,i){super(`${se.makeMessage(e,n,s)}`),this.status=e,this.headers=i,this.requestID=i==null?void 0:i.get("x-request-id"),this.error=n;const a=n;this.code=a==null?void 0:a.code,this.param=a==null?void 0:a.param,this.type=a==null?void 0:a.type}static makeMessage(e,n,s){const i=n!=null&&n.message?typeof n.message=="string"?n.message:JSON.stringify(n.message):n?JSON.stringify(n):s;return e&&i?`${e} ${i}`:e?`${e} status code (no body)`:i||"(no status code or body)"}static generate(e,n,s,i){if(!e||!i)return new rn({message:s,cause:$n(n)});const a=n==null?void 0:n.error;return e===400?new Yo(e,a,s,i):e===401?new zo(e,a,s,i):e===403?new Xo(e,a,s,i):e===404?new Qo(e,a,s,i):e===409?new Zo(e,a,s,i):e===422?new jo(e,a,s,i):e===429?new er(e,a,s,i):e>=500?new tr(e,a,s,i):new se(e,a,s,i)}}class me extends se{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class rn extends se{constructor({message:e,cause:n}){super(void 0,void 0,e||"Connection error.",void 0),n&&(this.cause=n)}}class ns extends rn{constructor({message:e}={}){super({message:e??"Request timed out."})}}class Yo extends se{}class zo extends se{}class Xo extends se{}class Qo extends se{}class Zo extends se{}class jo extends se{}class er extends se{}class tr extends se{}class nr extends V{constructor(){super("Could not parse response content as the length limit was reached")}}class sr extends V{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class it extends Error{constructor(e){super(e)}}const Pg=/^[a-z][a-z0-9+.-]*:/i,Rg=t=>Pg.test(t);let le=t=>(le=Array.isArray,le(t)),Gi=le;function kg(t){return typeof t!="object"?{}:t??{}}function Dg(t){if(!t)return!0;for(const e in t)return!1;return!0}function Lg(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function pn(t){return t!=null&&typeof t=="object"&&!Array.isArray(t)}const Fg=(t,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new V(`${t} must be an integer`);if(e<0)throw new V(`${t} must be a positive integer`);return e},Ug=t=>{try{return JSON.parse(t)}catch{return}},vt=t=>new Promise(e=>setTimeout(e,t)),Ge="5.10.2",$g=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u";function Vg(){return typeof Deno<"u"&&Deno.build!=null?"deno":typeof EdgeRuntime<"u"?"edge":Object.prototype.toString.call(typeof globalThis.process<"u"?globalThis.process:0)==="[object process]"?"node":"unknown"}const Bg=()=>{var n;const t=Vg();if(t==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":Oi(Deno.build.os),"X-Stainless-Arch":Ji(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((n=Deno.version)==null?void 0:n.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(t==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":Oi(globalThis.process.platform??"unknown"),"X-Stainless-Arch":Ji(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};const e=qg();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function qg(){if(typeof navigator>"u"||!navigator)return null;const t=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:e,pattern:n}of t){const s=n.exec(navigator.userAgent);if(s){const i=s[1]||0,a=s[2]||0,l=s[3]||0;return{browser:e,version:`${i}.${a}.${l}`}}}return null}const Ji=t=>t==="x32"?"x32":t==="x86_64"||t==="x64"?"x64":t==="arm"?"arm":t==="aarch64"||t==="arm64"?"arm64":t?`other:${t}`:"unknown",Oi=t=>(t=t.toLowerCase(),t.includes("ios")?"iOS":t==="android"?"Android":t==="darwin"?"MacOS":t==="win32"?"Windows":t==="freebsd"?"FreeBSD":t==="openbsd"?"OpenBSD":t==="linux"?"Linux":t?`Other:${t}`:"Unknown");let Hi;const Gg=()=>Hi??(Hi=Bg());function Jg(){if(typeof fetch<"u")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function ir(...t){const e=globalThis.ReadableStream;if(typeof e>"u")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new e(...t)}function or(t){let e=Symbol.asyncIterator in t?t[Symbol.asyncIterator]():t[Symbol.iterator]();return ir({start(){},async pull(n){const{done:s,value:i}=await e.next();s?n.close():n.enqueue(i)},async cancel(){var n;await((n=e.return)==null?void 0:n.call(e))}})}function rr(t){if(t[Symbol.asyncIterator])return t;const e=t.getReader();return{async next(){try{const n=await e.read();return n!=null&&n.done&&e.releaseLock(),n}catch(n){throw e.releaseLock(),n}},async return(){const n=e.cancel();return e.releaseLock(),await n,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function Og(t){var s,i;if(t===null||typeof t!="object")return;if(t[Symbol.asyncIterator]){await((i=(s=t[Symbol.asyncIterator]()).return)==null?void 0:i.call(s));return}const e=t.getReader(),n=e.cancel();e.releaseLock(),await n}const Hg=({headers:t,body:e})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(e)}),ar="RFC3986",lr=t=>String(t),Wi={RFC1738:t=>String(t).replace(/%20/g,"+"),RFC3986:lr},Wg="RFC1738";let Vn=(t,e)=>(Vn=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty),Vn(t,e));const xe=(()=>{const t=[];for(let e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t})(),gn=1024,Kg=(t,e,n,s,i)=>{if(t.length===0)return t;let a=t;if(typeof t=="symbol"?a=Symbol.prototype.toString.call(t):typeof t!="string"&&(a=String(t)),n==="iso-8859-1")return escape(a).replace(/%u[0-9a-f]{4}/gi,function(u){return"%26%23"+parseInt(u.slice(2),16)+"%3B"});let l="";for(let u=0;u<a.length;u+=gn){const f=a.length>=gn?a.slice(u,u+gn):a,c=[];for(let m=0;m<f.length;++m){let h=f.charCodeAt(m);if(h===45||h===46||h===95||h===126||h>=48&&h<=57||h>=65&&h<=90||h>=97&&h<=122||i===Wg&&(h===40||h===41)){c[c.length]=f.charAt(m);continue}if(h<128){c[c.length]=xe[h];continue}if(h<2048){c[c.length]=xe[192|h>>6]+xe[128|h&63];continue}if(h<55296||h>=57344){c[c.length]=xe[224|h>>12]+xe[128|h>>6&63]+xe[128|h&63];continue}m+=1,h=65536+((h&1023)<<10|f.charCodeAt(m)&1023),c[c.length]=xe[240|h>>18]+xe[128|h>>12&63]+xe[128|h>>6&63]+xe[128|h&63]}l+=c.join("")}return l};function Yg(t){return!t||typeof t!="object"?!1:!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))}function Ki(t,e){if(le(t)){const n=[];for(let s=0;s<t.length;s+=1)n.push(e(t[s]));return n}return e(t)}const ur={brackets(t){return String(t)+"[]"},comma:"comma",indices(t,e){return String(t)+"["+e+"]"},repeat(t){return String(t)}},cr=function(t,e){Array.prototype.push.apply(t,le(e)?e:[e])};let Yi;const Z={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Kg,encodeValuesOnly:!1,format:ar,formatter:lr,indices:!1,serializeDate(t){return(Yi??(Yi=Function.prototype.call.bind(Date.prototype.toISOString)))(t)},skipNulls:!1,strictNullHandling:!1};function zg(t){return typeof t=="string"||typeof t=="number"||typeof t=="boolean"||typeof t=="symbol"||typeof t=="bigint"}const yn={};function dr(t,e,n,s,i,a,l,u,f,c,m,h,p,g,y,x,T,I){let w=t,A=I,_=0,C=!1;for(;(A=A.get(yn))!==void 0&&!C;){const L=A.get(t);if(_+=1,typeof L<"u"){if(L===_)throw new RangeError("Cyclic object value");C=!0}typeof A.get(yn)>"u"&&(_=0)}if(typeof c=="function"?w=c(e,w):w instanceof Date?w=p==null?void 0:p(w):n==="comma"&&le(w)&&(w=Ki(w,function(L){return L instanceof Date?p==null?void 0:p(L):L})),w===null){if(a)return f&&!x?f(e,Z.encoder,T,"key",g):e;w=""}if(zg(w)||Yg(w)){if(f){const L=x?e:f(e,Z.encoder,T,"key",g);return[(y==null?void 0:y(L))+"="+(y==null?void 0:y(f(w,Z.encoder,T,"value",g)))]}return[(y==null?void 0:y(e))+"="+(y==null?void 0:y(String(w)))]}const E=[];if(typeof w>"u")return E;let N;if(n==="comma"&&le(w))x&&f&&(w=Ki(w,f)),N=[{value:w.length>0?w.join(",")||null:void 0}];else if(le(c))N=c;else{const L=Object.keys(w);N=m?L.sort(m):L}const P=u?String(e).replace(/\./g,"%2E"):String(e),b=s&&le(w)&&w.length===1?P+"[]":P;if(i&&le(w)&&w.length===0)return b+"[]";for(let L=0;L<N.length;++L){const k=N[L],K=typeof k=="object"&&typeof k.value<"u"?k.value:w[k];if(l&&K===null)continue;const fe=h&&u?k.replace(/\./g,"%2E"):k,z=le(w)?typeof n=="function"?n(b,fe):b:b+(h?"."+fe:"["+fe+"]");I.set(t,_);const X=new WeakMap;X.set(yn,I),cr(E,dr(K,z,n,s,i,a,l,u,n==="comma"&&x&&le(w)?null:f,c,m,h,p,g,y,x,T,X))}return E}function Xg(t=Z){if(typeof t.allowEmptyArrays<"u"&&typeof t.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof t.encodeDotInKeys<"u"&&typeof t.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(t.encoder!==null&&typeof t.encoder<"u"&&typeof t.encoder!="function")throw new TypeError("Encoder has to be a function.");const e=t.charset||Z.charset;if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=ar;if(typeof t.format<"u"){if(!Vn(Wi,t.format))throw new TypeError("Unknown format option provided.");n=t.format}const s=Wi[n];let i=Z.filter;(typeof t.filter=="function"||le(t.filter))&&(i=t.filter);let a;if(t.arrayFormat&&t.arrayFormat in ur?a=t.arrayFormat:"indices"in t?a=t.indices?"indices":"repeat":a=Z.arrayFormat,"commaRoundTrip"in t&&typeof t.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const l=typeof t.allowDots>"u"?t.encodeDotInKeys?!0:Z.allowDots:!!t.allowDots;return{addQueryPrefix:typeof t.addQueryPrefix=="boolean"?t.addQueryPrefix:Z.addQueryPrefix,allowDots:l,allowEmptyArrays:typeof t.allowEmptyArrays=="boolean"?!!t.allowEmptyArrays:Z.allowEmptyArrays,arrayFormat:a,charset:e,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:Z.charsetSentinel,commaRoundTrip:!!t.commaRoundTrip,delimiter:typeof t.delimiter>"u"?Z.delimiter:t.delimiter,encode:typeof t.encode=="boolean"?t.encode:Z.encode,encodeDotInKeys:typeof t.encodeDotInKeys=="boolean"?t.encodeDotInKeys:Z.encodeDotInKeys,encoder:typeof t.encoder=="function"?t.encoder:Z.encoder,encodeValuesOnly:typeof t.encodeValuesOnly=="boolean"?t.encodeValuesOnly:Z.encodeValuesOnly,filter:i,format:n,formatter:s,serializeDate:typeof t.serializeDate=="function"?t.serializeDate:Z.serializeDate,skipNulls:typeof t.skipNulls=="boolean"?t.skipNulls:Z.skipNulls,sort:typeof t.sort=="function"?t.sort:null,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:Z.strictNullHandling}}function Qg(t,e={}){let n=t;const s=Xg(e);let i,a;typeof s.filter=="function"?(a=s.filter,n=a("",n)):le(s.filter)&&(a=s.filter,i=a);const l=[];if(typeof n!="object"||n===null)return"";const u=ur[s.arrayFormat],f=u==="comma"&&s.commaRoundTrip;i||(i=Object.keys(n)),s.sort&&i.sort(s.sort);const c=new WeakMap;for(let p=0;p<i.length;++p){const g=i[p];s.skipNulls&&n[g]===null||cr(l,dr(n[g],g,u,f,s.allowEmptyArrays,s.strictNullHandling,s.skipNulls,s.encodeDotInKeys,s.encode?s.encoder:null,s.filter,s.sort,s.allowDots,s.serializeDate,s.format,s.formatter,s.encodeValuesOnly,s.charset,c))}const m=l.join(s.delimiter);let h=s.addQueryPrefix===!0?"?":"";return s.charsetSentinel&&(s.charset==="iso-8859-1"?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),m.length>0?h+m:""}function Zg(t){let e=0;for(const i of t)e+=i.length;const n=new Uint8Array(e);let s=0;for(const i of t)n.set(i,s),s+=i.length;return n}let zi;function ss(t){let e;return(zi??(e=new globalThis.TextEncoder,zi=e.encode.bind(e)))(t)}let Xi;function Qi(t){let e;return(Xi??(e=new globalThis.TextDecoder,Xi=e.decode.bind(e)))(t)}var ue,ce;class an{constructor(){ue.set(this,void 0),ce.set(this,void 0),$(this,ue,new Uint8Array),$(this,ce,null)}decode(e){if(e==null)return[];const n=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?ss(e):e;$(this,ue,Zg([v(this,ue,"f"),n]));const s=[];let i;for(;(i=jg(v(this,ue,"f"),v(this,ce,"f")))!=null;){if(i.carriage&&v(this,ce,"f")==null){$(this,ce,i.index);continue}if(v(this,ce,"f")!=null&&(i.index!==v(this,ce,"f")+1||i.carriage)){s.push(Qi(v(this,ue,"f").subarray(0,v(this,ce,"f")-1))),$(this,ue,v(this,ue,"f").subarray(v(this,ce,"f"))),$(this,ce,null);continue}const a=v(this,ce,"f")!==null?i.preceding-1:i.preceding,l=Qi(v(this,ue,"f").subarray(0,a));s.push(l),$(this,ue,v(this,ue,"f").subarray(i.index)),$(this,ce,null)}return s}flush(){return v(this,ue,"f").length?this.decode(`
`):[]}}ue=new WeakMap,ce=new WeakMap;an.NEWLINE_CHARS=new Set([`
`,"\r"]);an.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function jg(t,e){for(let i=e??0;i<t.length;i++){if(t[i]===10)return{preceding:i,index:i+1,carriage:!1};if(t[i]===13)return{preceding:i,index:i+1,carriage:!0}}return null}function ey(t){for(let s=0;s<t.length-1;s++){if(t[s]===10&&t[s+1]===10||t[s]===13&&t[s+1]===13)return s+2;if(t[s]===13&&t[s+1]===10&&s+3<t.length&&t[s+2]===13&&t[s+3]===10)return s+4}return-1}const zt={off:0,error:200,warn:300,info:400,debug:500},Zi=(t,e,n)=>{if(t){if(Lg(zt,t))return t;te(n).warn(`${e} was set to ${JSON.stringify(t)}, expected one of ${JSON.stringify(Object.keys(zt))}`)}};function ot(){}function It(t,e,n){return!e||zt[t]>zt[n]?ot:e[t].bind(e)}const ty={error:ot,warn:ot,info:ot,debug:ot};let ji=new WeakMap;function te(t){const e=t.logger,n=t.logLevel??"off";if(!e)return ty;const s=ji.get(e);if(s&&s[0]===n)return s[1];const i={error:It("error",e,n),warn:It("warn",e,n),info:It("info",e,n),debug:It("debug",e,n)};return ji.set(e,[n,i]),i}const Pe=t=>(t.options&&(t.options={...t.options},delete t.options.headers),t.headers&&(t.headers=Object.fromEntries((t.headers instanceof Headers?[...t.headers]:Object.entries(t.headers)).map(([e,n])=>[e,e.toLowerCase()==="authorization"||e.toLowerCase()==="cookie"||e.toLowerCase()==="set-cookie"?"***":n]))),"retryOfRequestLogID"in t&&(t.retryOfRequestLogID&&(t.retryOf=t.retryOfRequestLogID),delete t.retryOfRequestLogID),t);var st;class Ce{constructor(e,n,s){this.iterator=e,st.set(this,void 0),this.controller=n,$(this,st,s)}static fromSSEResponse(e,n,s){let i=!1;const a=s?te(s):console;async function*l(){if(i)throw new V("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");i=!0;let u=!1;try{for await(const f of ny(e,n))if(!u){if(f.data.startsWith("[DONE]")){u=!0;continue}if(f.event===null||f.event.startsWith("response.")||f.event.startsWith("image_edit.")||f.event.startsWith("image_generation.")||f.event.startsWith("transcript.")){let c;try{c=JSON.parse(f.data)}catch(m){throw a.error("Could not parse message into JSON:",f.data),a.error("From chunk:",f.raw),m}if(c&&c.error)throw new se(void 0,c.error,void 0,e.headers);yield c}else{let c;try{c=JSON.parse(f.data)}catch(m){throw console.error("Could not parse message into JSON:",f.data),console.error("From chunk:",f.raw),m}if(f.event=="error")throw new se(void 0,c.error,c.message,void 0);yield{event:f.event,data:c}}}u=!0}catch(f){if(Un(f))return;throw f}finally{u||n.abort()}}return new Ce(l,n,s)}static fromReadableStream(e,n,s){let i=!1;async function*a(){const u=new an,f=rr(e);for await(const c of f)for(const m of u.decode(c))yield m;for(const c of u.flush())yield c}async function*l(){if(i)throw new V("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");i=!0;let u=!1;try{for await(const f of a())u||f&&(yield JSON.parse(f));u=!0}catch(f){if(Un(f))return;throw f}finally{u||n.abort()}}return new Ce(l,n,s)}[(st=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){const e=[],n=[],s=this.iterator(),i=a=>({next:()=>{if(a.length===0){const l=s.next();e.push(l),n.push(l)}return a.shift()}});return[new Ce(()=>i(e),this.controller,v(this,st,"f")),new Ce(()=>i(n),this.controller,v(this,st,"f"))]}toReadableStream(){const e=this;let n;return ir({async start(){n=e[Symbol.asyncIterator]()},async pull(s){try{const{value:i,done:a}=await n.next();if(a)return s.close();const l=ss(JSON.stringify(i)+`
`);s.enqueue(l)}catch(i){s.error(i)}},async cancel(){var s;await((s=n.return)==null?void 0:s.call(n))}})}}async function*ny(t,e){if(!t.body)throw e.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new V("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new V("Attempted to iterate over a response with no body");const n=new iy,s=new an,i=rr(t.body);for await(const a of sy(i))for(const l of s.decode(a)){const u=n.decode(l);u&&(yield u)}for(const a of s.flush()){const l=n.decode(a);l&&(yield l)}}async function*sy(t){let e=new Uint8Array;for await(const n of t){if(n==null)continue;const s=n instanceof ArrayBuffer?new Uint8Array(n):typeof n=="string"?ss(n):n;let i=new Uint8Array(e.length+s.length);i.set(e),i.set(s,e.length),e=i;let a;for(;(a=ey(e))!==-1;)yield e.slice(0,a),e=e.slice(a)}e.length>0&&(yield e)}class iy{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const a={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(e),e.startsWith(":"))return null;let[n,s,i]=oy(e,":");return i.startsWith(" ")&&(i=i.substring(1)),n==="event"?this.event=i:n==="data"&&this.data.push(i),null}}function oy(t,e){const n=t.indexOf(e);return n!==-1?[t.substring(0,n),e,t.substring(n+e.length)]:[t,"",""]}async function fr(t,e){const{response:n,requestLogID:s,retryOfRequestLogID:i,startTime:a}=e,l=await(async()=>{var h;if(e.options.stream)return te(t).debug("response",n.status,n.url,n.headers,n.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(n,e.controller,t):Ce.fromSSEResponse(n,e.controller,t);if(n.status===204)return null;if(e.options.__binaryResponse)return n;const u=n.headers.get("content-type"),f=(h=u==null?void 0:u.split(";")[0])==null?void 0:h.trim();if((f==null?void 0:f.includes("application/json"))||(f==null?void 0:f.endsWith("+json"))){const p=await n.json();return mr(p,n)}return await n.text()})();return te(t).debug(`[${s}] response parsed`,Pe({retryOfRequestLogID:i,url:n.url,status:n.status,body:l,durationMs:Date.now()-a})),l}function mr(t,e){return!t||typeof t!="object"||Array.isArray(t)?t:Object.defineProperty(t,"_request_id",{value:e.headers.get("x-request-id"),enumerable:!1})}var rt;class ln extends Promise{constructor(e,n,s=fr){super(i=>{i(null)}),this.responsePromise=n,this.parseResponse=s,rt.set(this,void 0),$(this,rt,e)}_thenUnwrap(e){return new ln(v(this,rt,"f"),this.responsePromise,async(n,s)=>mr(e(await this.parseResponse(n,s),s),s.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){const[e,n]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:n,request_id:n.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(v(this,rt,"f"),e))),this.parsedPromise}then(e,n){return this.parse().then(e,n)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}rt=new WeakMap;var Nt;class hr{constructor(e,n,s,i){Nt.set(this,void 0),$(this,Nt,e),this.options=i,this.response=n,this.body=s}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){const e=this.nextPageRequestOptions();if(!e)throw new V("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await v(this,Nt,"f").requestAPIList(this.constructor,e)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(Nt=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const n of e.getPaginatedItems())yield n}}class ry extends ln{constructor(e,n,s){super(e,n,async(i,a)=>new s(i,a.response,await fr(i,a),a.options))}async*[Symbol.asyncIterator](){const e=await this;for await(const n of e)yield n}}class un extends hr{constructor(e,n,s,i){super(e,n,s,i),this.data=s.data||[],this.object=s.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class Y extends hr{constructor(e,n,s,i){super(e,n,s,i),this.data=s.data||[],this.has_more=s.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var s;const e=this.getPaginatedItems(),n=(s=e[e.length-1])==null?void 0:s.id;return n?{...this.options,query:{...kg(this.options.query),after:n}}:null}}const pr=()=>{var t;if(typeof File>"u"){const{process:e}=globalThis,n=typeof((t=e==null?void 0:e.versions)==null?void 0:t.node)=="string"&&parseInt(e.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(n?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function ht(t,e,n){return pr(),new File(t,e??"unknown_file",n)}function $t(t){return(typeof t=="object"&&t!==null&&("name"in t&&t.name&&String(t.name)||"url"in t&&t.url&&String(t.url)||"filename"in t&&t.filename&&String(t.filename)||"path"in t&&t.path&&String(t.path))||"").split(/[\\/]/).pop()||void 0}const gr=t=>t!=null&&typeof t=="object"&&typeof t[Symbol.asyncIterator]=="function",Fe=async(t,e)=>({...t,body:await ly(t.body,e)}),eo=new WeakMap;function ay(t){const e=typeof t=="function"?t:t.fetch,n=eo.get(e);if(n)return n;const s=(async()=>{try{const i="Response"in e?e.Response:(await e("data:,")).constructor,a=new FormData;return a.toString()!==await new i(a).text()}catch{return!0}})();return eo.set(e,s),s}const ly=async(t,e)=>{if(!await ay(e))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");const n=new FormData;return await Promise.all(Object.entries(t||{}).map(([s,i])=>Bn(n,s,i))),n},uy=t=>t instanceof Blob&&"name"in t,Bn=async(t,e,n)=>{if(n!==void 0){if(n==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof n=="string"||typeof n=="number"||typeof n=="boolean")t.append(e,String(n));else if(n instanceof Response)t.append(e,ht([await n.blob()],$t(n)));else if(gr(n))t.append(e,ht([await new Response(or(n)).blob()],$t(n)));else if(uy(n))t.append(e,n,$t(n));else if(Array.isArray(n))await Promise.all(n.map(s=>Bn(t,e+"[]",s)));else if(typeof n=="object")await Promise.all(Object.entries(n).map(([s,i])=>Bn(t,`${e}[${s}]`,i)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${n} instead`)}},yr=t=>t!=null&&typeof t=="object"&&typeof t.size=="number"&&typeof t.type=="string"&&typeof t.text=="function"&&typeof t.slice=="function"&&typeof t.arrayBuffer=="function",cy=t=>t!=null&&typeof t=="object"&&typeof t.name=="string"&&typeof t.lastModified=="number"&&yr(t),dy=t=>t!=null&&typeof t=="object"&&typeof t.url=="string"&&typeof t.blob=="function";async function fy(t,e,n){if(pr(),t=await t,cy(t))return t instanceof File?t:ht([await t.arrayBuffer()],t.name);if(dy(t)){const i=await t.blob();return e||(e=new URL(t.url).pathname.split(/[\\/]/).pop()),ht(await qn(i),e,n)}const s=await qn(t);if(e||(e=$t(t)),!(n!=null&&n.type)){const i=s.find(a=>typeof a=="object"&&"type"in a&&a.type);typeof i=="string"&&(n={...n,type:i})}return ht(s,e,n)}async function qn(t){var n;let e=[];if(typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer)e.push(t);else if(yr(t))e.push(t instanceof Blob?t:await t.arrayBuffer());else if(gr(t))for await(const s of t)e.push(...await qn(s));else{const s=(n=t==null?void 0:t.constructor)==null?void 0:n.name;throw new Error(`Unexpected data type: ${typeof t}${s?`; constructor: ${s}`:""}${my(t)}`)}return e}function my(t){return typeof t!="object"||t===null?"":`; props: [${Object.getOwnPropertyNames(t).map(n=>`"${n}"`).join(", ")}]`}class B{constructor(e){this._client=e}}function xr(t){return t.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}const to=Object.freeze(Object.create(null)),hy=(t=xr)=>function(n,...s){if(n.length===1)return n[0];let i=!1;const a=[],l=n.reduce((m,h,p)=>{var x;/[?#]/.test(h)&&(i=!0);const g=s[p];let y=(i?encodeURIComponent:t)(""+g);return p!==s.length&&(g==null||typeof g=="object"&&g.toString===((x=Object.getPrototypeOf(Object.getPrototypeOf(g.hasOwnProperty??to)??to))==null?void 0:x.toString))&&(y=g+"",a.push({start:m.length+h.length,length:y.length,error:`Value of type ${Object.prototype.toString.call(g).slice(8,-1)} is not a valid path parameter`})),m+h+(p===s.length?"":y)},""),u=l.split(/[?#]/,1)[0],f=new RegExp("(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)","gi");let c;for(;(c=f.exec(u))!==null;)a.push({start:c.index,length:c[0].length,error:`Value "${c[0]}" can't be safely passed as a path parameter`});if(a.sort((m,h)=>m.start-h.start),a.length>0){let m=0;const h=a.reduce((p,g)=>{const y=" ".repeat(g.start-m),x="^".repeat(g.length);return m=g.start+g.length,p+y+x},"");throw new V(`Path parameters result in path with invalid segments:
${a.map(p=>p.error).join(`
`)}
${l}
${h}`)}return l},M=hy(xr);let vr=class extends B{list(e,n={},s){return this._client.getAPIList(M`/chat/completions/${e}/messages`,Y,{query:n,...s})}};function py(t){return typeof t.parse=="function"}const Xt=t=>(t==null?void 0:t.role)==="assistant",Cr=t=>(t==null?void 0:t.role)==="tool";var Gn,Vt,Bt,at,lt,qt,ut,Te,ct,Qt,Zt,Je,_r;class is{constructor(){Gn.add(this),this.controller=new AbortController,Vt.set(this,void 0),Bt.set(this,()=>{}),at.set(this,()=>{}),lt.set(this,void 0),qt.set(this,()=>{}),ut.set(this,()=>{}),Te.set(this,{}),ct.set(this,!1),Qt.set(this,!1),Zt.set(this,!1),Je.set(this,!1),$(this,Vt,new Promise((e,n)=>{$(this,Bt,e,"f"),$(this,at,n,"f")})),$(this,lt,new Promise((e,n)=>{$(this,qt,e,"f"),$(this,ut,n,"f")})),v(this,Vt,"f").catch(()=>{}),v(this,lt,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},v(this,Gn,"m",_r).bind(this))},0)}_connected(){this.ended||(v(this,Bt,"f").call(this),this._emit("connect"))}get ended(){return v(this,ct,"f")}get errored(){return v(this,Qt,"f")}get aborted(){return v(this,Zt,"f")}abort(){this.controller.abort()}on(e,n){return(v(this,Te,"f")[e]||(v(this,Te,"f")[e]=[])).push({listener:n}),this}off(e,n){const s=v(this,Te,"f")[e];if(!s)return this;const i=s.findIndex(a=>a.listener===n);return i>=0&&s.splice(i,1),this}once(e,n){return(v(this,Te,"f")[e]||(v(this,Te,"f")[e]=[])).push({listener:n,once:!0}),this}emitted(e){return new Promise((n,s)=>{$(this,Je,!0),e!=="error"&&this.once("error",s),this.once(e,n)})}async done(){$(this,Je,!0),await v(this,lt,"f")}_emit(e,...n){if(v(this,ct,"f"))return;e==="end"&&($(this,ct,!0),v(this,qt,"f").call(this));const s=v(this,Te,"f")[e];if(s&&(v(this,Te,"f")[e]=s.filter(i=>!i.once),s.forEach(({listener:i})=>i(...n))),e==="abort"){const i=n[0];!v(this,Je,"f")&&!(s!=null&&s.length)&&Promise.reject(i),v(this,at,"f").call(this,i),v(this,ut,"f").call(this,i),this._emit("end");return}if(e==="error"){const i=n[0];!v(this,Je,"f")&&!(s!=null&&s.length)&&Promise.reject(i),v(this,at,"f").call(this,i),v(this,ut,"f").call(this,i),this._emit("end")}}_emitFinal(){}}Vt=new WeakMap,Bt=new WeakMap,at=new WeakMap,lt=new WeakMap,qt=new WeakMap,ut=new WeakMap,Te=new WeakMap,ct=new WeakMap,Qt=new WeakMap,Zt=new WeakMap,Je=new WeakMap,Gn=new WeakSet,_r=function(e){if($(this,Qt,!0),e instanceof Error&&e.name==="AbortError"&&(e=new me),e instanceof me)return $(this,Zt,!0),this._emit("abort",e);if(e instanceof V)return this._emit("error",e);if(e instanceof Error){const n=new V(e.message);return n.cause=e,this._emit("error",n)}return this._emit("error",new V(String(e)))};function os(t){return(t==null?void 0:t.$brand)==="auto-parseable-response-format"}function Ct(t){return(t==null?void 0:t.$brand)==="auto-parseable-tool"}function gy(t,e){return!e||!Tr(e)?{...t,choices:t.choices.map(n=>({...n,message:{...n.message,parsed:null,...n.message.tool_calls?{tool_calls:n.message.tool_calls}:void 0}}))}:rs(t,e)}function rs(t,e){const n=t.choices.map(s=>{var i;if(s.finish_reason==="length")throw new nr;if(s.finish_reason==="content_filter")throw new sr;return{...s,message:{...s.message,...s.message.tool_calls?{tool_calls:((i=s.message.tool_calls)==null?void 0:i.map(a=>xy(e,a)))??void 0}:void 0,parsed:s.message.content&&!s.message.refusal?yy(e,s.message.content):null}}});return{...t,choices:n}}function yy(t,e){var n,s;return((n=t.response_format)==null?void 0:n.type)!=="json_schema"?null:((s=t.response_format)==null?void 0:s.type)==="json_schema"?"$parseRaw"in t.response_format?t.response_format.$parseRaw(e):JSON.parse(e):null}function xy(t,e){var s;const n=(s=t.tools)==null?void 0:s.find(i=>{var a;return((a=i.function)==null?void 0:a.name)===e.function.name});return{...e,function:{...e.function,parsed_arguments:Ct(n)?n.$parseRaw(e.function.arguments):n!=null&&n.function.strict?JSON.parse(e.function.arguments):null}}}function vy(t,e){var s;if(!t)return!1;const n=(s=t.tools)==null?void 0:s.find(i=>{var a;return((a=i.function)==null?void 0:a.name)===e.function.name});return Ct(n)||(n==null?void 0:n.function.strict)||!1}function Tr(t){var e;return os(t.response_format)?!0:((e=t.tools)==null?void 0:e.some(n=>Ct(n)||n.type==="function"&&n.function.strict===!0))??!1}function Cy(t){for(const e of t??[]){if(e.type!=="function")throw new V(`Currently only \`function\` tool types support auto-parsing; Received \`${e.type}\``);if(e.function.strict!==!0)throw new V(`The \`${e.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var ae,Jn,jt,On,Hn,Wn,Sr,wr;const _y=10;class Ar extends is{constructor(){super(...arguments),ae.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var s;this._chatCompletions.push(e),this._emit("chatCompletion",e);const n=(s=e.choices[0])==null?void 0:s.message;return n&&this._addMessage(n),e}_addMessage(e,n=!0){if("content"in e||(e.content=null),this.messages.push(e),n){if(this._emit("message",e),Cr(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(Xt(e)&&e.tool_calls)for(const s of e.tool_calls)s.type==="function"&&this._emit("functionToolCall",s.function)}}async finalChatCompletion(){await this.done();const e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new V("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),v(this,ae,"m",Jn).call(this)}async finalMessage(){return await this.done(),v(this,ae,"m",jt).call(this)}async finalFunctionToolCall(){return await this.done(),v(this,ae,"m",On).call(this)}async finalFunctionToolCallResult(){return await this.done(),v(this,ae,"m",Hn).call(this)}async totalUsage(){return await this.done(),v(this,ae,"m",Wn).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);const n=v(this,ae,"m",jt).call(this);n&&this._emit("finalMessage",n);const s=v(this,ae,"m",Jn).call(this);s&&this._emit("finalContent",s);const i=v(this,ae,"m",On).call(this);i&&this._emit("finalFunctionToolCall",i);const a=v(this,ae,"m",Hn).call(this);a!=null&&this._emit("finalFunctionToolCallResult",a),this._chatCompletions.some(l=>l.usage)&&this._emit("totalUsage",v(this,ae,"m",Wn).call(this))}async _createChatCompletion(e,n,s){const i=s==null?void 0:s.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort())),v(this,ae,"m",Sr).call(this,n);const a=await e.chat.completions.create({...n,stream:!1},{...s,signal:this.controller.signal});return this._connected(),this._addChatCompletion(rs(a,n))}async _runChatCompletion(e,n,s){for(const i of n.messages)this._addMessage(i,!1);return await this._createChatCompletion(e,n,s)}async _runTools(e,n,s){var g,y,x;const i="tool",{tool_choice:a="auto",stream:l,...u}=n,f=typeof a!="string"&&((g=a==null?void 0:a.function)==null?void 0:g.name),{maxChatCompletions:c=_y}=s||{},m=n.tools.map(T=>{if(Ct(T)){if(!T.$callback)throw new V("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:T.$callback,name:T.function.name,description:T.function.description||"",parameters:T.function.parameters,parse:T.$parseRaw,strict:!0}}}return T}),h={};for(const T of m)T.type==="function"&&(h[T.function.name||T.function.function.name]=T.function);const p="tools"in n?m.map(T=>T.type==="function"?{type:"function",function:{name:T.function.name||T.function.function.name,parameters:T.function.parameters,description:T.function.description,strict:T.function.strict}}:T):void 0;for(const T of n.messages)this._addMessage(T,!1);for(let T=0;T<c;++T){const w=(y=(await this._createChatCompletion(e,{...u,tool_choice:a,tools:p,messages:[...this.messages]},s)).choices[0])==null?void 0:y.message;if(!w)throw new V("missing message in ChatCompletion response");if(!((x=w.tool_calls)!=null&&x.length))return;for(const A of w.tool_calls){if(A.type!=="function")continue;const _=A.id,{name:C,arguments:E}=A.function,N=h[C];if(N){if(f&&f!==C){const k=`Invalid tool_call: ${JSON.stringify(C)}. ${JSON.stringify(f)} requested. Please try again`;this._addMessage({role:i,tool_call_id:_,content:k});continue}}else{const k=`Invalid tool_call: ${JSON.stringify(C)}. Available options are: ${Object.keys(h).map(K=>JSON.stringify(K)).join(", ")}. Please try again`;this._addMessage({role:i,tool_call_id:_,content:k});continue}let P;try{P=py(N)?await N.parse(E):E}catch(k){const K=k instanceof Error?k.message:String(k);this._addMessage({role:i,tool_call_id:_,content:K});continue}const b=await N.function(P,this),L=v(this,ae,"m",wr).call(this,b);if(this._addMessage({role:i,tool_call_id:_,content:L}),f)return}}}}ae=new WeakSet,Jn=function(){return v(this,ae,"m",jt).call(this).content??null},jt=function(){let e=this.messages.length;for(;e-- >0;){const n=this.messages[e];if(Xt(n))return{...n,content:n.content??null,refusal:n.refusal??null}}throw new V("stream ended without producing a ChatCompletionMessage with role=assistant")},On=function(){var e,n;for(let s=this.messages.length-1;s>=0;s--){const i=this.messages[s];if(Xt(i)&&((e=i==null?void 0:i.tool_calls)!=null&&e.length))return(n=i.tool_calls.at(-1))==null?void 0:n.function}},Hn=function(){for(let e=this.messages.length-1;e>=0;e--){const n=this.messages[e];if(Cr(n)&&n.content!=null&&typeof n.content=="string"&&this.messages.some(s=>{var i;return s.role==="assistant"&&((i=s.tool_calls)==null?void 0:i.some(a=>a.type==="function"&&a.id===n.tool_call_id))}))return n.content}},Wn=function(){const e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:n}of this._chatCompletions)n&&(e.completion_tokens+=n.completion_tokens,e.prompt_tokens+=n.prompt_tokens,e.total_tokens+=n.total_tokens);return e},Sr=function(e){if(e.n!=null&&e.n>1)throw new V("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},wr=function(e){return typeof e=="string"?e:e===void 0?"undefined":JSON.stringify(e)};class as extends Ar{static runTools(e,n,s){const i=new as,a={...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return i._run(()=>i._runTools(e,n,a)),i}_addMessage(e,n=!0){super._addMessage(e,n),Xt(e)&&e.content&&this._emit("content",e.content)}}const Er=1,Ir=2,Nr=4,br=8,Mr=16,Pr=32,Rr=64,kr=128,Dr=256,Lr=kr|Dr,Fr=Mr|Pr|Lr|Rr,Ur=Er|Ir|Fr,$r=Nr|br,Ty=Ur|$r,j={STR:Er,NUM:Ir,ARR:Nr,OBJ:br,NULL:Mr,BOOL:Pr,NAN:Rr,INFINITY:kr,MINUS_INFINITY:Dr,INF:Lr,SPECIAL:Fr,ATOM:Ur,COLLECTION:$r,ALL:Ty};class Sy extends Error{}class wy extends Error{}function Ay(t,e=j.ALL){if(typeof t!="string")throw new TypeError(`expecting str, got ${typeof t}`);if(!t.trim())throw new Error(`${t} is empty`);return Ey(t.trim(),e)}const Ey=(t,e)=>{const n=t.length;let s=0;const i=p=>{throw new Sy(`${p} at position ${s}`)},a=p=>{throw new wy(`${p} at position ${s}`)},l=()=>(h(),s>=n&&i("Unexpected end of input"),t[s]==='"'?u():t[s]==="{"?f():t[s]==="["?c():t.substring(s,s+4)==="null"||j.NULL&e&&n-s<4&&"null".startsWith(t.substring(s))?(s+=4,null):t.substring(s,s+4)==="true"||j.BOOL&e&&n-s<4&&"true".startsWith(t.substring(s))?(s+=4,!0):t.substring(s,s+5)==="false"||j.BOOL&e&&n-s<5&&"false".startsWith(t.substring(s))?(s+=5,!1):t.substring(s,s+8)==="Infinity"||j.INFINITY&e&&n-s<8&&"Infinity".startsWith(t.substring(s))?(s+=8,1/0):t.substring(s,s+9)==="-Infinity"||j.MINUS_INFINITY&e&&1<n-s&&n-s<9&&"-Infinity".startsWith(t.substring(s))?(s+=9,-1/0):t.substring(s,s+3)==="NaN"||j.NAN&e&&n-s<3&&"NaN".startsWith(t.substring(s))?(s+=3,NaN):m()),u=()=>{const p=s;let g=!1;for(s++;s<n&&(t[s]!=='"'||g&&t[s-1]==="\\");)g=t[s]==="\\"?!g:!1,s++;if(t.charAt(s)=='"')try{return JSON.parse(t.substring(p,++s-Number(g)))}catch(y){a(String(y))}else if(j.STR&e)try{return JSON.parse(t.substring(p,s-Number(g))+'"')}catch{return JSON.parse(t.substring(p,t.lastIndexOf("\\"))+'"')}i("Unterminated string literal")},f=()=>{s++,h();const p={};try{for(;t[s]!=="}";){if(h(),s>=n&&j.OBJ&e)return p;const g=u();h(),s++;try{const y=l();Object.defineProperty(p,g,{value:y,writable:!0,enumerable:!0,configurable:!0})}catch(y){if(j.OBJ&e)return p;throw y}h(),t[s]===","&&s++}}catch{if(j.OBJ&e)return p;i("Expected '}' at end of object")}return s++,p},c=()=>{s++;const p=[];try{for(;t[s]!=="]";)p.push(l()),h(),t[s]===","&&s++}catch{if(j.ARR&e)return p;i("Expected ']' at end of array")}return s++,p},m=()=>{if(s===0){t==="-"&&j.NUM&e&&i("Not sure what '-' is");try{return JSON.parse(t)}catch(g){if(j.NUM&e)try{return t[t.length-1]==="."?JSON.parse(t.substring(0,t.lastIndexOf("."))):JSON.parse(t.substring(0,t.lastIndexOf("e")))}catch{}a(String(g))}}const p=s;for(t[s]==="-"&&s++;t[s]&&!",]}".includes(t[s]);)s++;s==n&&!(j.NUM&e)&&i("Unterminated number literal");try{return JSON.parse(t.substring(p,s))}catch{t.substring(p,s)==="-"&&j.NUM&e&&i("Not sure what '-' is");try{return JSON.parse(t.substring(p,t.lastIndexOf("e")))}catch(y){a(String(y))}}},h=()=>{for(;s<n&&` 
\r	`.includes(t[s]);)s++};return l()},no=t=>Ay(t,j.ALL^j.NUM);var Q,_e,$e,Ee,xn,bt,vn,Cn,_n,Mt,Tn,so;class yt extends Ar{constructor(e){super(),Q.add(this),_e.set(this,void 0),$e.set(this,void 0),Ee.set(this,void 0),$(this,_e,e),$(this,$e,[])}get currentChatCompletionSnapshot(){return v(this,Ee,"f")}static fromReadableStream(e){const n=new yt(null);return n._run(()=>n._fromReadableStream(e)),n}static createChatCompletion(e,n,s){const i=new yt(n);return i._run(()=>i._runChatCompletion(e,{...n,stream:!0},{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),i}async _createChatCompletion(e,n,s){var l;super._createChatCompletion;const i=s==null?void 0:s.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort())),v(this,Q,"m",xn).call(this);const a=await e.chat.completions.create({...n,stream:!0},{...s,signal:this.controller.signal});this._connected();for await(const u of a)v(this,Q,"m",vn).call(this,u);if((l=a.controller.signal)!=null&&l.aborted)throw new me;return this._addChatCompletion(v(this,Q,"m",Mt).call(this))}async _fromReadableStream(e,n){var l;const s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),v(this,Q,"m",xn).call(this),this._connected();const i=Ce.fromReadableStream(e,this.controller);let a;for await(const u of i)a&&a!==u.id&&this._addChatCompletion(v(this,Q,"m",Mt).call(this)),v(this,Q,"m",vn).call(this,u),a=u.id;if((l=i.controller.signal)!=null&&l.aborted)throw new me;return this._addChatCompletion(v(this,Q,"m",Mt).call(this))}[(_e=new WeakMap,$e=new WeakMap,Ee=new WeakMap,Q=new WeakSet,xn=function(){this.ended||$(this,Ee,void 0)},bt=function(n){let s=v(this,$e,"f")[n.index];return s||(s={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},v(this,$e,"f")[n.index]=s,s)},vn=function(n){var i,a,l,u,f,c,m,h,p,g,y,x,T,I,w;if(this.ended)return;const s=v(this,Q,"m",so).call(this,n);this._emit("chunk",n,s);for(const A of n.choices){const _=s.choices[A.index];A.delta.content!=null&&((i=_.message)==null?void 0:i.role)==="assistant"&&((a=_.message)!=null&&a.content)&&(this._emit("content",A.delta.content,_.message.content),this._emit("content.delta",{delta:A.delta.content,snapshot:_.message.content,parsed:_.message.parsed})),A.delta.refusal!=null&&((l=_.message)==null?void 0:l.role)==="assistant"&&((u=_.message)!=null&&u.refusal)&&this._emit("refusal.delta",{delta:A.delta.refusal,snapshot:_.message.refusal}),((f=A.logprobs)==null?void 0:f.content)!=null&&((c=_.message)==null?void 0:c.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(m=A.logprobs)==null?void 0:m.content,snapshot:((h=_.logprobs)==null?void 0:h.content)??[]}),((p=A.logprobs)==null?void 0:p.refusal)!=null&&((g=_.message)==null?void 0:g.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(y=A.logprobs)==null?void 0:y.refusal,snapshot:((x=_.logprobs)==null?void 0:x.refusal)??[]});const C=v(this,Q,"m",bt).call(this,_);_.finish_reason&&(v(this,Q,"m",_n).call(this,_),C.current_tool_call_index!=null&&v(this,Q,"m",Cn).call(this,_,C.current_tool_call_index));for(const E of A.delta.tool_calls??[])C.current_tool_call_index!==E.index&&(v(this,Q,"m",_n).call(this,_),C.current_tool_call_index!=null&&v(this,Q,"m",Cn).call(this,_,C.current_tool_call_index)),C.current_tool_call_index=E.index;for(const E of A.delta.tool_calls??[]){const N=(T=_.message.tool_calls)==null?void 0:T[E.index];N!=null&&N.type&&((N==null?void 0:N.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(I=N.function)==null?void 0:I.name,index:E.index,arguments:N.function.arguments,parsed_arguments:N.function.parsed_arguments,arguments_delta:((w=E.function)==null?void 0:w.arguments)??""}):(N==null||N.type,void 0))}}},Cn=function(n,s){var l,u,f;if(v(this,Q,"m",bt).call(this,n).done_tool_calls.has(s))return;const a=(l=n.message.tool_calls)==null?void 0:l[s];if(!a)throw new Error("no tool call snapshot");if(!a.type)throw new Error("tool call snapshot missing `type`");if(a.type==="function"){const c=(f=(u=v(this,_e,"f"))==null?void 0:u.tools)==null?void 0:f.find(m=>m.type==="function"&&m.function.name===a.function.name);this._emit("tool_calls.function.arguments.done",{name:a.function.name,index:s,arguments:a.function.arguments,parsed_arguments:Ct(c)?c.$parseRaw(a.function.arguments):c!=null&&c.function.strict?JSON.parse(a.function.arguments):null})}else a.type},_n=function(n){var i,a;const s=v(this,Q,"m",bt).call(this,n);if(n.message.content&&!s.content_done){s.content_done=!0;const l=v(this,Q,"m",Tn).call(this);this._emit("content.done",{content:n.message.content,parsed:l?l.$parseRaw(n.message.content):null})}n.message.refusal&&!s.refusal_done&&(s.refusal_done=!0,this._emit("refusal.done",{refusal:n.message.refusal})),(i=n.logprobs)!=null&&i.content&&!s.logprobs_content_done&&(s.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:n.logprobs.content})),(a=n.logprobs)!=null&&a.refusal&&!s.logprobs_refusal_done&&(s.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:n.logprobs.refusal}))},Mt=function(){if(this.ended)throw new V("stream has ended, this shouldn't happen");const n=v(this,Ee,"f");if(!n)throw new V("request ended without sending any chunks");return $(this,Ee,void 0),$(this,$e,[]),Iy(n,v(this,_e,"f"))},Tn=function(){var s;const n=(s=v(this,_e,"f"))==null?void 0:s.response_format;return os(n)?n:null},so=function(n){var s,i,a,l;let u=v(this,Ee,"f");const{choices:f,...c}=n;u?Object.assign(u,c):u=$(this,Ee,{...c,choices:[]});for(const{delta:m,finish_reason:h,index:p,logprobs:g=null,...y}of n.choices){let x=u.choices[p];if(x||(x=u.choices[p]={finish_reason:h,index:p,message:{},logprobs:g,...y}),g)if(!x.logprobs)x.logprobs=Object.assign({},g);else{const{content:E,refusal:N,...P}=g;Object.assign(x.logprobs,P),E&&((s=x.logprobs).content??(s.content=[]),x.logprobs.content.push(...E)),N&&((i=x.logprobs).refusal??(i.refusal=[]),x.logprobs.refusal.push(...N))}if(h&&(x.finish_reason=h,v(this,_e,"f")&&Tr(v(this,_e,"f")))){if(h==="length")throw new nr;if(h==="content_filter")throw new sr}if(Object.assign(x,y),!m)continue;const{content:T,refusal:I,function_call:w,role:A,tool_calls:_,...C}=m;if(Object.assign(x.message,C),I&&(x.message.refusal=(x.message.refusal||"")+I),A&&(x.message.role=A),w&&(x.message.function_call?(w.name&&(x.message.function_call.name=w.name),w.arguments&&((a=x.message.function_call).arguments??(a.arguments=""),x.message.function_call.arguments+=w.arguments)):x.message.function_call=w),T&&(x.message.content=(x.message.content||"")+T,!x.message.refusal&&v(this,Q,"m",Tn).call(this)&&(x.message.parsed=no(x.message.content))),_){x.message.tool_calls||(x.message.tool_calls=[]);for(const{index:E,id:N,type:P,function:b,...L}of _){const k=(l=x.message.tool_calls)[E]??(l[E]={});Object.assign(k,L),N&&(k.id=N),P&&(k.type=P),b&&(k.function??(k.function={name:b.name??"",arguments:""})),b!=null&&b.name&&(k.function.name=b.name),b!=null&&b.arguments&&(k.function.arguments+=b.arguments,vy(v(this,_e,"f"),k)&&(k.function.parsed_arguments=no(k.function.arguments)))}}}return u},Symbol.asyncIterator)](){const e=[],n=[];let s=!1;return this.on("chunk",i=>{const a=n.shift();a?a.resolve(i):e.push(i)}),this.on("end",()=>{s=!0;for(const i of n)i.resolve(void 0);n.length=0}),this.on("abort",i=>{s=!0;for(const a of n)a.reject(i);n.length=0}),this.on("error",i=>{s=!0;for(const a of n)a.reject(i);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((a,l)=>n.push({resolve:a,reject:l})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new Ce(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function Iy(t,e){const{id:n,choices:s,created:i,model:a,system_fingerprint:l,...u}=t,f={...u,id:n,choices:s.map(({message:c,finish_reason:m,index:h,logprobs:p,...g})=>{if(!m)throw new V(`missing finish_reason for choice ${h}`);const{content:y=null,function_call:x,tool_calls:T,...I}=c,w=c.role;if(!w)throw new V(`missing role for choice ${h}`);if(x){const{arguments:A,name:_}=x;if(A==null)throw new V(`missing function_call.arguments for choice ${h}`);if(!_)throw new V(`missing function_call.name for choice ${h}`);return{...g,message:{content:y,function_call:{arguments:A,name:_},role:w,refusal:c.refusal??null},finish_reason:m,index:h,logprobs:p}}return T?{...g,index:h,finish_reason:m,logprobs:p,message:{...I,role:w,content:y,refusal:c.refusal??null,tool_calls:T.map((A,_)=>{const{function:C,type:E,id:N,...P}=A,{arguments:b,name:L,...k}=C||{};if(N==null)throw new V(`missing choices[${h}].tool_calls[${_}].id
${Pt(t)}`);if(E==null)throw new V(`missing choices[${h}].tool_calls[${_}].type
${Pt(t)}`);if(L==null)throw new V(`missing choices[${h}].tool_calls[${_}].function.name
${Pt(t)}`);if(b==null)throw new V(`missing choices[${h}].tool_calls[${_}].function.arguments
${Pt(t)}`);return{...P,id:N,type:E,function:{...k,name:L,arguments:b}}})}}:{...g,message:{...I,content:y,role:w,refusal:c.refusal??null},finish_reason:m,index:h,logprobs:p}}),created:i,model:a,object:"chat.completion",...l?{system_fingerprint:l}:{}};return gy(f,e)}function Pt(t){return JSON.stringify(t)}class en extends yt{static fromReadableStream(e){const n=new en(null);return n._run(()=>n._fromReadableStream(e)),n}static runTools(e,n,s){const i=new en(n),a={...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"runTools"}};return i._run(()=>i._runTools(e,n,a)),i}}let ls=class extends B{constructor(){super(...arguments),this.messages=new vr(this._client)}create(e,n){return this._client.post("/chat/completions",{body:e,...n,stream:e.stream??!1})}retrieve(e,n){return this._client.get(M`/chat/completions/${e}`,n)}update(e,n,s){return this._client.post(M`/chat/completions/${e}`,{body:n,...s})}list(e={},n){return this._client.getAPIList("/chat/completions",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/chat/completions/${e}`,n)}parse(e,n){return Cy(e.tools),this._client.chat.completions.create(e,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(s=>rs(s,e))}runTools(e,n){return e.stream?en.runTools(this._client,e,n):as.runTools(this._client,e,n)}stream(e,n){return yt.createChatCompletion(this._client,e,n)}};ls.Messages=vr;class us extends B{constructor(){super(...arguments),this.completions=new ls(this._client)}}us.Completions=ls;const Vr=Symbol("brand.privateNullableHeaders");function*Ny(t){if(!t)return;if(Vr in t){const{values:s,nulls:i}=t;yield*s.entries();for(const a of i)yield[a,null];return}let e=!1,n;t instanceof Headers?n=t.entries():Gi(t)?n=t:(e=!0,n=Object.entries(t??{}));for(let s of n){const i=s[0];if(typeof i!="string")throw new TypeError("expected header name to be a string");const a=Gi(s[1])?s[1]:[s[1]];let l=!1;for(const u of a)u!==void 0&&(e&&!l&&(l=!0,yield[i,null]),yield[i,u])}}const U=t=>{const e=new Headers,n=new Set;for(const s of t){const i=new Set;for(const[a,l]of Ny(s)){const u=a.toLowerCase();i.has(u)||(e.delete(a),i.add(u)),l===null?(e.delete(a),n.add(u)):(e.append(a,l),n.delete(u))}}return{[Vr]:!0,values:e,nulls:n}};class Br extends B{create(e,n){return this._client.post("/audio/speech",{body:e,...n,headers:U([{Accept:"application/octet-stream"},n==null?void 0:n.headers]),__binaryResponse:!0})}}class qr extends B{create(e,n){return this._client.post("/audio/transcriptions",Fe({body:e,...n,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class Gr extends B{create(e,n){return this._client.post("/audio/translations",Fe({body:e,...n,__metadata:{model:e.model}},this._client))}}class _t extends B{constructor(){super(...arguments),this.transcriptions=new qr(this._client),this.translations=new Gr(this._client),this.speech=new Br(this._client)}}_t.Transcriptions=qr;_t.Translations=Gr;_t.Speech=Br;class Jr extends B{create(e,n){return this._client.post("/batches",{body:e,...n})}retrieve(e,n){return this._client.get(M`/batches/${e}`,n)}list(e={},n){return this._client.getAPIList("/batches",Y,{query:e,...n})}cancel(e,n){return this._client.post(M`/batches/${e}/cancel`,n)}}class Or extends B{create(e,n){return this._client.post("/assistants",{body:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,n){return this._client.get(M`/assistants/${e}`,{...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,n,s){return this._client.post(M`/assistants/${e}`,{body:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e={},n){return this._client.getAPIList("/assistants",Y,{query:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}delete(e,n){return this._client.delete(M`/assistants/${e}`,{...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}}class Hr extends B{create(e,n){return this._client.post("/realtime/sessions",{body:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}}class Wr extends B{create(e,n){return this._client.post("/realtime/transcription_sessions",{body:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}}class cn extends B{constructor(){super(...arguments),this.sessions=new Hr(this._client),this.transcriptionSessions=new Wr(this._client)}}cn.Sessions=Hr;cn.TranscriptionSessions=Wr;class Kr extends B{create(e,n,s){return this._client.post(M`/threads/${e}/messages`,{body:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(e,n,s){const{thread_id:i}=n;return this._client.get(M`/threads/${i}/messages/${e}`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(e,n,s){const{thread_id:i,...a}=n;return this._client.post(M`/threads/${i}/messages/${e}`,{body:a,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,n={},s){return this._client.getAPIList(M`/threads/${e}/messages`,Y,{query:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(e,n,s){const{thread_id:i}=n;return this._client.delete(M`/threads/${i}/messages/${e}`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}class Yr extends B{retrieve(e,n,s){const{thread_id:i,run_id:a,...l}=n;return this._client.get(M`/threads/${i}/runs/${a}/steps/${e}`,{query:l,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,n,s){const{thread_id:i,...a}=n;return this._client.getAPIList(M`/threads/${i}/runs/${e}/steps`,Y,{query:a,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}const by=t=>{if(typeof Buffer<"u"){const e=Buffer.from(t,"base64");return Array.from(new Float32Array(e.buffer,e.byteOffset,e.length/Float32Array.BYTES_PER_ELEMENT))}else{const e=atob(t),n=e.length,s=new Uint8Array(n);for(let i=0;i<n;i++)s[i]=e.charCodeAt(i);return Array.from(new Float32Array(s.buffer))}};var Sn={};const Ve=t=>{var e,n,s,i;if(typeof globalThis.process<"u")return((e=Sn==null?void 0:Sn[t])==null?void 0:e.trim())??void 0;if(typeof globalThis.Deno<"u")return(i=(s=(n=globalThis.Deno.env)==null?void 0:n.get)==null?void 0:s.call(n,t))==null?void 0:i.trim()};var ne,ke,Kn,ve,Gt,pe,De,We,Re,tn,de,Jt,Ot,pt,dt,ft,io,oo,ro,ao,lo,uo,co;class gt extends is{constructor(){super(...arguments),ne.add(this),Kn.set(this,[]),ve.set(this,{}),Gt.set(this,{}),pe.set(this,void 0),De.set(this,void 0),We.set(this,void 0),Re.set(this,void 0),tn.set(this,void 0),de.set(this,void 0),Jt.set(this,void 0),Ot.set(this,void 0),pt.set(this,void 0)}[(Kn=new WeakMap,ve=new WeakMap,Gt=new WeakMap,pe=new WeakMap,De=new WeakMap,We=new WeakMap,Re=new WeakMap,tn=new WeakMap,de=new WeakMap,Jt=new WeakMap,Ot=new WeakMap,pt=new WeakMap,ne=new WeakSet,Symbol.asyncIterator)](){const e=[],n=[];let s=!1;return this.on("event",i=>{const a=n.shift();a?a.resolve(i):e.push(i)}),this.on("end",()=>{s=!0;for(const i of n)i.resolve(void 0);n.length=0}),this.on("abort",i=>{s=!0;for(const a of n)a.reject(i);n.length=0}),this.on("error",i=>{s=!0;for(const a of n)a.reject(i);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((a,l)=>n.push({resolve:a,reject:l})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){const n=new ke;return n._run(()=>n._fromReadableStream(e)),n}async _fromReadableStream(e,n){var a;const s=n==null?void 0:n.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),this._connected();const i=Ce.fromReadableStream(e,this.controller);for await(const l of i)v(this,ne,"m",dt).call(this,l);if((a=i.controller.signal)!=null&&a.aborted)throw new me;return this._addRun(v(this,ne,"m",ft).call(this))}toReadableStream(){return new Ce(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,n,s,i){const a=new ke;return a._run(()=>a._runToolAssistantStream(e,n,s,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),a}async _createToolAssistantStream(e,n,s,i){var f;const a=i==null?void 0:i.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const l={...s,stream:!0},u=await e.submitToolOutputs(n,l,{...i,signal:this.controller.signal});this._connected();for await(const c of u)v(this,ne,"m",dt).call(this,c);if((f=u.controller.signal)!=null&&f.aborted)throw new me;return this._addRun(v(this,ne,"m",ft).call(this))}static createThreadAssistantStream(e,n,s){const i=new ke;return i._run(()=>i._threadAssistantStream(e,n,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),i}static createAssistantStream(e,n,s,i){const a=new ke;return a._run(()=>a._runAssistantStream(e,n,s,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),a}currentEvent(){return v(this,Jt,"f")}currentRun(){return v(this,Ot,"f")}currentMessageSnapshot(){return v(this,pe,"f")}currentRunStepSnapshot(){return v(this,pt,"f")}async finalRunSteps(){return await this.done(),Object.values(v(this,ve,"f"))}async finalMessages(){return await this.done(),Object.values(v(this,Gt,"f"))}async finalRun(){if(await this.done(),!v(this,De,"f"))throw Error("Final run was not received.");return v(this,De,"f")}async _createThreadAssistantStream(e,n,s){var u;const i=s==null?void 0:s.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort()));const a={...n,stream:!0},l=await e.createAndRun(a,{...s,signal:this.controller.signal});this._connected();for await(const f of l)v(this,ne,"m",dt).call(this,f);if((u=l.controller.signal)!=null&&u.aborted)throw new me;return this._addRun(v(this,ne,"m",ft).call(this))}async _createAssistantStream(e,n,s,i){var f;const a=i==null?void 0:i.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const l={...s,stream:!0},u=await e.create(n,l,{...i,signal:this.controller.signal});this._connected();for await(const c of u)v(this,ne,"m",dt).call(this,c);if((f=u.controller.signal)!=null&&f.aborted)throw new me;return this._addRun(v(this,ne,"m",ft).call(this))}static accumulateDelta(e,n){for(const[s,i]of Object.entries(n)){if(!e.hasOwnProperty(s)){e[s]=i;continue}let a=e[s];if(a==null){e[s]=i;continue}if(s==="index"||s==="type"){e[s]=i;continue}if(typeof a=="string"&&typeof i=="string")a+=i;else if(typeof a=="number"&&typeof i=="number")a+=i;else if(pn(a)&&pn(i))a=this.accumulateDelta(a,i);else if(Array.isArray(a)&&Array.isArray(i)){if(a.every(l=>typeof l=="string"||typeof l=="number")){a.push(...i);continue}for(const l of i){if(!pn(l))throw new Error(`Expected array delta entry to be an object but got: ${l}`);const u=l.index;if(u==null)throw console.error(l),new Error("Expected array delta entry to have an `index` property");if(typeof u!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${u}`);const f=a[u];f==null?a.push(l):a[u]=this.accumulateDelta(f,l)}continue}else throw Error(`Unhandled record type: ${s}, deltaValue: ${i}, accValue: ${a}`);e[s]=a}return e}_addRun(e){return e}async _threadAssistantStream(e,n,s){return await this._createThreadAssistantStream(n,e,s)}async _runAssistantStream(e,n,s,i){return await this._createAssistantStream(n,e,s,i)}async _runToolAssistantStream(e,n,s,i){return await this._createToolAssistantStream(n,e,s,i)}}ke=gt,dt=function(e){if(!this.ended)switch($(this,Jt,e),v(this,ne,"m",ro).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":v(this,ne,"m",co).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":v(this,ne,"m",oo).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":v(this,ne,"m",io).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},ft=function(){if(this.ended)throw new V("stream has ended, this shouldn't happen");if(!v(this,De,"f"))throw Error("Final run has not been received");return v(this,De,"f")},io=function(e){const[n,s]=v(this,ne,"m",lo).call(this,e,v(this,pe,"f"));$(this,pe,n),v(this,Gt,"f")[n.id]=n;for(const i of s){const a=n.content[i.index];(a==null?void 0:a.type)=="text"&&this._emit("textCreated",a.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,n),e.data.delta.content)for(const i of e.data.delta.content){if(i.type=="text"&&i.text){let a=i.text,l=n.content[i.index];if(l&&l.type=="text")this._emit("textDelta",a,l.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(i.index!=v(this,We,"f")){if(v(this,Re,"f"))switch(v(this,Re,"f").type){case"text":this._emit("textDone",v(this,Re,"f").text,v(this,pe,"f"));break;case"image_file":this._emit("imageFileDone",v(this,Re,"f").image_file,v(this,pe,"f"));break}$(this,We,i.index)}$(this,Re,n.content[i.index])}break;case"thread.message.completed":case"thread.message.incomplete":if(v(this,We,"f")!==void 0){const i=e.data.content[v(this,We,"f")];if(i)switch(i.type){case"image_file":this._emit("imageFileDone",i.image_file,v(this,pe,"f"));break;case"text":this._emit("textDone",i.text,v(this,pe,"f"));break}}v(this,pe,"f")&&this._emit("messageDone",e.data),$(this,pe,void 0)}},oo=function(e){const n=v(this,ne,"m",ao).call(this,e);switch($(this,pt,n),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":const s=e.data.delta;if(s.step_details&&s.step_details.type=="tool_calls"&&s.step_details.tool_calls&&n.step_details.type=="tool_calls")for(const a of s.step_details.tool_calls)a.index==v(this,tn,"f")?this._emit("toolCallDelta",a,n.step_details.tool_calls[a.index]):(v(this,de,"f")&&this._emit("toolCallDone",v(this,de,"f")),$(this,tn,a.index),$(this,de,n.step_details.tool_calls[a.index]),v(this,de,"f")&&this._emit("toolCallCreated",v(this,de,"f")));this._emit("runStepDelta",e.data.delta,n);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":$(this,pt,void 0),e.data.step_details.type=="tool_calls"&&v(this,de,"f")&&(this._emit("toolCallDone",v(this,de,"f")),$(this,de,void 0)),this._emit("runStepDone",e.data,n);break}},ro=function(e){v(this,Kn,"f").push(e),this._emit("event",e)},ao=function(e){switch(e.event){case"thread.run.step.created":return v(this,ve,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let n=v(this,ve,"f")[e.data.id];if(!n)throw Error("Received a RunStepDelta before creation of a snapshot");let s=e.data;if(s.delta){const i=ke.accumulateDelta(n,s.delta);v(this,ve,"f")[e.data.id]=i}return v(this,ve,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":v(this,ve,"f")[e.data.id]=e.data;break}if(v(this,ve,"f")[e.data.id])return v(this,ve,"f")[e.data.id];throw new Error("No snapshot available")},lo=function(e,n){let s=[];switch(e.event){case"thread.message.created":return[e.data,s];case"thread.message.delta":if(!n)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let i=e.data;if(i.delta.content)for(const a of i.delta.content)if(a.index in n.content){let l=n.content[a.index];n.content[a.index]=v(this,ne,"m",uo).call(this,a,l)}else n.content[a.index]=a,s.push(a);return[n,s];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(n)return[n,s];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},uo=function(e,n){return ke.accumulateDelta(n,e)},co=function(e){switch($(this,Ot,e.data),e.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":$(this,De,e.data),v(this,de,"f")&&(this._emit("toolCallDone",v(this,de,"f")),$(this,de,void 0));break}};let cs=class extends B{constructor(){super(...arguments),this.steps=new Yr(this._client)}create(e,n,s){const{include:i,...a}=n;return this._client.post(M`/threads/${e}/runs`,{query:{include:i},body:a,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers]),stream:n.stream??!1})}retrieve(e,n,s){const{thread_id:i}=n;return this._client.get(M`/threads/${i}/runs/${e}`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(e,n,s){const{thread_id:i,...a}=n;return this._client.post(M`/threads/${i}/runs/${e}`,{body:a,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,n={},s){return this._client.getAPIList(M`/threads/${e}/runs`,Y,{query:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}cancel(e,n,s){const{thread_id:i}=n;return this._client.post(M`/threads/${i}/runs/${e}/cancel`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(e,n,s){const i=await this.create(e,n,s);return await this.poll(i.id,{thread_id:e},s)}createAndStream(e,n,s){return gt.createAssistantStream(e,this._client.beta.threads.runs,n,s)}async poll(e,n,s){var a;const i=U([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=s==null?void 0:s.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const{data:l,response:u}=await this.retrieve(e,n,{...s,headers:{...s==null?void 0:s.headers,...i}}).withResponse();switch(l.status){case"queued":case"in_progress":case"cancelling":let f=5e3;if(s!=null&&s.pollIntervalMs)f=s.pollIntervalMs;else{const c=u.headers.get("openai-poll-after-ms");if(c){const m=parseInt(c);isNaN(m)||(f=m)}}await vt(f);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return l}}}stream(e,n,s){return gt.createAssistantStream(e,this._client.beta.threads.runs,n,s)}submitToolOutputs(e,n,s){const{thread_id:i,...a}=n;return this._client.post(M`/threads/${i}/runs/${e}/submit_tool_outputs`,{body:a,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers]),stream:n.stream??!1})}async submitToolOutputsAndPoll(e,n,s){const i=await this.submitToolOutputs(e,n,s);return await this.poll(i.id,n,s)}submitToolOutputsStream(e,n,s){return gt.createToolAssistantStream(e,this._client.beta.threads.runs,n,s)}};cs.Steps=Yr;class dn extends B{constructor(){super(...arguments),this.runs=new cs(this._client),this.messages=new Kr(this._client)}create(e={},n){return this._client.post("/threads",{body:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,n){return this._client.get(M`/threads/${e}`,{...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,n,s){return this._client.post(M`/threads/${e}`,{body:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(e,n){return this._client.delete(M`/threads/${e}`,{...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}createAndRun(e,n){return this._client.post("/threads/runs",{body:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers]),stream:e.stream??!1})}async createAndRunPoll(e,n){const s=await this.createAndRun(e,n);return await this.runs.poll(s.id,{thread_id:s.thread_id},n)}createAndRunStream(e,n){return gt.createThreadAssistantStream(e,this._client.beta.threads,n)}}dn.Runs=cs;dn.Messages=Kr;class Tt extends B{constructor(){super(...arguments),this.realtime=new cn(this._client),this.assistants=new Or(this._client),this.threads=new dn(this._client)}}Tt.Realtime=cn;Tt.Assistants=Or;Tt.Threads=dn;class zr extends B{create(e,n){return this._client.post("/completions",{body:e,...n,stream:e.stream??!1})}}class Xr extends B{retrieve(e,n,s){const{container_id:i}=n;return this._client.get(M`/containers/${i}/files/${e}/content`,{...s,headers:U([{Accept:"application/binary"},s==null?void 0:s.headers]),__binaryResponse:!0})}}let ds=class extends B{constructor(){super(...arguments),this.content=new Xr(this._client)}create(e,n,s){return this._client.post(M`/containers/${e}/files`,Fe({body:n,...s},this._client))}retrieve(e,n,s){const{container_id:i}=n;return this._client.get(M`/containers/${i}/files/${e}`,s)}list(e,n={},s){return this._client.getAPIList(M`/containers/${e}/files`,Y,{query:n,...s})}delete(e,n,s){const{container_id:i}=n;return this._client.delete(M`/containers/${i}/files/${e}`,{...s,headers:U([{Accept:"*/*"},s==null?void 0:s.headers])})}};ds.Content=Xr;class fs extends B{constructor(){super(...arguments),this.files=new ds(this._client)}create(e,n){return this._client.post("/containers",{body:e,...n})}retrieve(e,n){return this._client.get(M`/containers/${e}`,n)}list(e={},n){return this._client.getAPIList("/containers",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/containers/${e}`,{...n,headers:U([{Accept:"*/*"},n==null?void 0:n.headers])})}}fs.Files=ds;class Qr extends B{create(e,n){const s=!!e.encoding_format;let i=s?e.encoding_format:"base64";s&&te(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);const a=this._client.post("/embeddings",{body:{...e,encoding_format:i},...n});return s?a:(te(this._client).debug("embeddings/decoding base64 embeddings from base64"),a._thenUnwrap(l=>(l&&l.data&&l.data.forEach(u=>{const f=u.embedding;u.embedding=by(f)}),l)))}}class Zr extends B{retrieve(e,n,s){const{eval_id:i,run_id:a}=n;return this._client.get(M`/evals/${i}/runs/${a}/output_items/${e}`,s)}list(e,n,s){const{eval_id:i,...a}=n;return this._client.getAPIList(M`/evals/${i}/runs/${e}/output_items`,Y,{query:a,...s})}}class ms extends B{constructor(){super(...arguments),this.outputItems=new Zr(this._client)}create(e,n,s){return this._client.post(M`/evals/${e}/runs`,{body:n,...s})}retrieve(e,n,s){const{eval_id:i}=n;return this._client.get(M`/evals/${i}/runs/${e}`,s)}list(e,n={},s){return this._client.getAPIList(M`/evals/${e}/runs`,Y,{query:n,...s})}delete(e,n,s){const{eval_id:i}=n;return this._client.delete(M`/evals/${i}/runs/${e}`,s)}cancel(e,n,s){const{eval_id:i}=n;return this._client.post(M`/evals/${i}/runs/${e}`,s)}}ms.OutputItems=Zr;class hs extends B{constructor(){super(...arguments),this.runs=new ms(this._client)}create(e,n){return this._client.post("/evals",{body:e,...n})}retrieve(e,n){return this._client.get(M`/evals/${e}`,n)}update(e,n,s){return this._client.post(M`/evals/${e}`,{body:n,...s})}list(e={},n){return this._client.getAPIList("/evals",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/evals/${e}`,n)}}hs.Runs=ms;let jr=class extends B{create(e,n){return this._client.post("/files",Fe({body:e,...n},this._client))}retrieve(e,n){return this._client.get(M`/files/${e}`,n)}list(e={},n){return this._client.getAPIList("/files",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/files/${e}`,n)}content(e,n){return this._client.get(M`/files/${e}/content`,{...n,headers:U([{Accept:"application/binary"},n==null?void 0:n.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:n=5e3,maxWait:s=30*60*1e3}={}){const i=new Set(["processed","error","deleted"]),a=Date.now();let l=await this.retrieve(e);for(;!l.status||!i.has(l.status);)if(await vt(n),l=await this.retrieve(e),Date.now()-a>s)throw new ns({message:`Giving up on waiting for file ${e} to finish processing after ${s} milliseconds.`});return l}};class ea extends B{}let ta=class extends B{run(e,n){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...n})}validate(e,n){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...n})}};class ps extends B{constructor(){super(...arguments),this.graders=new ta(this._client)}}ps.Graders=ta;class na extends B{create(e,n,s){return this._client.getAPIList(M`/fine_tuning/checkpoints/${e}/permissions`,un,{body:n,method:"post",...s})}retrieve(e,n={},s){return this._client.get(M`/fine_tuning/checkpoints/${e}/permissions`,{query:n,...s})}delete(e,n,s){const{fine_tuned_model_checkpoint:i}=n;return this._client.delete(M`/fine_tuning/checkpoints/${i}/permissions/${e}`,s)}}let gs=class extends B{constructor(){super(...arguments),this.permissions=new na(this._client)}};gs.Permissions=na;class sa extends B{list(e,n={},s){return this._client.getAPIList(M`/fine_tuning/jobs/${e}/checkpoints`,Y,{query:n,...s})}}class ys extends B{constructor(){super(...arguments),this.checkpoints=new sa(this._client)}create(e,n){return this._client.post("/fine_tuning/jobs",{body:e,...n})}retrieve(e,n){return this._client.get(M`/fine_tuning/jobs/${e}`,n)}list(e={},n){return this._client.getAPIList("/fine_tuning/jobs",Y,{query:e,...n})}cancel(e,n){return this._client.post(M`/fine_tuning/jobs/${e}/cancel`,n)}listEvents(e,n={},s){return this._client.getAPIList(M`/fine_tuning/jobs/${e}/events`,Y,{query:n,...s})}pause(e,n){return this._client.post(M`/fine_tuning/jobs/${e}/pause`,n)}resume(e,n){return this._client.post(M`/fine_tuning/jobs/${e}/resume`,n)}}ys.Checkpoints=sa;class je extends B{constructor(){super(...arguments),this.methods=new ea(this._client),this.jobs=new ys(this._client),this.checkpoints=new gs(this._client),this.alpha=new ps(this._client)}}je.Methods=ea;je.Jobs=ys;je.Checkpoints=gs;je.Alpha=ps;class ia extends B{}class xs extends B{constructor(){super(...arguments),this.graderModels=new ia(this._client)}}xs.GraderModels=ia;class oa extends B{createVariation(e,n){return this._client.post("/images/variations",Fe({body:e,...n},this._client))}edit(e,n){return this._client.post("/images/edits",Fe({body:e,...n,stream:e.stream??!1},this._client))}generate(e,n){return this._client.post("/images/generations",{body:e,...n,stream:e.stream??!1})}}class ra extends B{retrieve(e,n){return this._client.get(M`/models/${e}`,n)}list(e){return this._client.getAPIList("/models",un,e)}delete(e,n){return this._client.delete(M`/models/${e}`,n)}}class aa extends B{create(e,n){return this._client.post("/moderations",{body:e,...n})}}function My(t,e){return!e||!Ry(e)?{...t,output_parsed:null,output:t.output.map(n=>n.type==="function_call"?{...n,parsed_arguments:null}:n.type==="message"?{...n,content:n.content.map(s=>({...s,parsed:null}))}:n)}:la(t,e)}function la(t,e){const n=t.output.map(i=>{if(i.type==="function_call")return{...i,parsed_arguments:Ly(e,i)};if(i.type==="message"){const a=i.content.map(l=>l.type==="output_text"?{...l,parsed:Py(e,l.text)}:l);return{...i,content:a}}return i}),s=Object.assign({},t,{output:n});return Object.getOwnPropertyDescriptor(t,"output_text")||Yn(s),Object.defineProperty(s,"output_parsed",{enumerable:!0,get(){for(const i of s.output)if(i.type==="message"){for(const a of i.content)if(a.type==="output_text"&&a.parsed!==null)return a.parsed}return null}}),s}function Py(t,e){var n,s,i,a;return((s=(n=t.text)==null?void 0:n.format)==null?void 0:s.type)!=="json_schema"?null:"$parseRaw"in((i=t.text)==null?void 0:i.format)?((a=t.text)==null?void 0:a.format).$parseRaw(e):JSON.parse(e)}function Ry(t){var e;return!!os((e=t.text)==null?void 0:e.format)}function ky(t){return(t==null?void 0:t.$brand)==="auto-parseable-tool"}function Dy(t,e){return t.find(n=>n.type==="function"&&n.name===e)}function Ly(t,e){const n=Dy(t.tools??[],e.name);return{...e,...e,parsed_arguments:ky(n)?n.$parseRaw(e.arguments):n!=null&&n.strict?JSON.parse(e.arguments):null}}function Yn(t){const e=[];for(const n of t.output)if(n.type==="message")for(const s of n.content)s.type==="output_text"&&e.push(s.text);t.output_text=e.join("")}var Be,Rt,Ie,kt,fo,mo,ho,po;class vs extends is{constructor(e){super(),Be.add(this),Rt.set(this,void 0),Ie.set(this,void 0),kt.set(this,void 0),$(this,Rt,e)}static createResponse(e,n,s){const i=new vs(n);return i._run(()=>i._createOrRetrieveResponse(e,n,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),i}async _createOrRetrieveResponse(e,n,s){var u;const i=s==null?void 0:s.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort())),v(this,Be,"m",fo).call(this);let a,l=null;"response_id"in n?(a=await e.responses.retrieve(n.response_id,{stream:!0},{...s,signal:this.controller.signal,stream:!0}),l=n.starting_after??null):a=await e.responses.create({...n,stream:!0},{...s,signal:this.controller.signal}),this._connected();for await(const f of a)v(this,Be,"m",mo).call(this,f,l);if((u=a.controller.signal)!=null&&u.aborted)throw new me;return v(this,Be,"m",ho).call(this)}[(Rt=new WeakMap,Ie=new WeakMap,kt=new WeakMap,Be=new WeakSet,fo=function(){this.ended||$(this,Ie,void 0)},mo=function(n,s){if(this.ended)return;const i=(l,u)=>{(s==null||u.sequence_number>s)&&this._emit(l,u)},a=v(this,Be,"m",po).call(this,n);switch(i("event",n),n.type){case"response.output_text.delta":{const l=a.output[n.output_index];if(!l)throw new V(`missing output at index ${n.output_index}`);if(l.type==="message"){const u=l.content[n.content_index];if(!u)throw new V(`missing content at index ${n.content_index}`);if(u.type!=="output_text")throw new V(`expected content to be 'output_text', got ${u.type}`);i("response.output_text.delta",{...n,snapshot:u.text})}break}case"response.function_call_arguments.delta":{const l=a.output[n.output_index];if(!l)throw new V(`missing output at index ${n.output_index}`);l.type==="function_call"&&i("response.function_call_arguments.delta",{...n,snapshot:l.arguments});break}default:i(n.type,n);break}},ho=function(){if(this.ended)throw new V("stream has ended, this shouldn't happen");const n=v(this,Ie,"f");if(!n)throw new V("request ended without sending any events");$(this,Ie,void 0);const s=Fy(n,v(this,Rt,"f"));return $(this,kt,s),s},po=function(n){let s=v(this,Ie,"f");if(!s){if(n.type!=="response.created")throw new V(`When snapshot hasn't been set yet, expected 'response.created' event, got ${n.type}`);return s=$(this,Ie,n.response),s}switch(n.type){case"response.output_item.added":{s.output.push(n.item);break}case"response.content_part.added":{const i=s.output[n.output_index];if(!i)throw new V(`missing output at index ${n.output_index}`);i.type==="message"&&i.content.push(n.part);break}case"response.output_text.delta":{const i=s.output[n.output_index];if(!i)throw new V(`missing output at index ${n.output_index}`);if(i.type==="message"){const a=i.content[n.content_index];if(!a)throw new V(`missing content at index ${n.content_index}`);if(a.type!=="output_text")throw new V(`expected content to be 'output_text', got ${a.type}`);a.text+=n.delta}break}case"response.function_call_arguments.delta":{const i=s.output[n.output_index];if(!i)throw new V(`missing output at index ${n.output_index}`);i.type==="function_call"&&(i.arguments+=n.delta);break}case"response.completed":{$(this,Ie,n.response);break}}return s},Symbol.asyncIterator)](){const e=[],n=[];let s=!1;return this.on("event",i=>{const a=n.shift();a?a.resolve(i):e.push(i)}),this.on("end",()=>{s=!0;for(const i of n)i.resolve(void 0);n.length=0}),this.on("abort",i=>{s=!0;for(const a of n)a.reject(i);n.length=0}),this.on("error",i=>{s=!0;for(const a of n)a.reject(i);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((a,l)=>n.push({resolve:a,reject:l})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const e=v(this,kt,"f");if(!e)throw new V("stream ended without producing a ChatCompletion");return e}}function Fy(t,e){return My(t,e)}class ua extends B{list(e,n={},s){return this._client.getAPIList(M`/responses/${e}/input_items`,Y,{query:n,...s})}}class Cs extends B{constructor(){super(...arguments),this.inputItems=new ua(this._client)}create(e,n){return this._client.post("/responses",{body:e,...n,stream:e.stream??!1})._thenUnwrap(s=>("object"in s&&s.object==="response"&&Yn(s),s))}retrieve(e,n={},s){return this._client.get(M`/responses/${e}`,{query:n,...s,stream:(n==null?void 0:n.stream)??!1})._thenUnwrap(i=>("object"in i&&i.object==="response"&&Yn(i),i))}delete(e,n){return this._client.delete(M`/responses/${e}`,{...n,headers:U([{Accept:"*/*"},n==null?void 0:n.headers])})}parse(e,n){return this._client.responses.create(e,n)._thenUnwrap(s=>la(s,e))}stream(e,n){return vs.createResponse(this._client,e,n)}cancel(e,n){return this._client.post(M`/responses/${e}/cancel`,n)}}Cs.InputItems=ua;class ca extends B{create(e,n,s){return this._client.post(M`/uploads/${e}/parts`,Fe({body:n,...s},this._client))}}class _s extends B{constructor(){super(...arguments),this.parts=new ca(this._client)}create(e,n){return this._client.post("/uploads",{body:e,...n})}cancel(e,n){return this._client.post(M`/uploads/${e}/cancel`,n)}complete(e,n,s){return this._client.post(M`/uploads/${e}/complete`,{body:n,...s})}}_s.Parts=ca;const Uy=async t=>{const e=await Promise.allSettled(t),n=e.filter(i=>i.status==="rejected");if(n.length){for(const i of n)console.error(i.reason);throw new Error(`${n.length} promise(s) failed - see the above errors`)}const s=[];for(const i of e)i.status==="fulfilled"&&s.push(i.value);return s};class da extends B{create(e,n,s){return this._client.post(M`/vector_stores/${e}/file_batches`,{body:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(e,n,s){const{vector_store_id:i}=n;return this._client.get(M`/vector_stores/${i}/file_batches/${e}`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}cancel(e,n,s){const{vector_store_id:i}=n;return this._client.post(M`/vector_stores/${i}/file_batches/${e}/cancel`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(e,n,s){const i=await this.create(e,n);return await this.poll(e,i.id,s)}listFiles(e,n,s){const{vector_store_id:i,...a}=n;return this._client.getAPIList(M`/vector_stores/${i}/file_batches/${e}/files`,Y,{query:a,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async poll(e,n,s){var a;const i=U([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=s==null?void 0:s.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const{data:l,response:u}=await this.retrieve(n,{vector_store_id:e},{...s,headers:i}).withResponse();switch(l.status){case"in_progress":let f=5e3;if(s!=null&&s.pollIntervalMs)f=s.pollIntervalMs;else{const c=u.headers.get("openai-poll-after-ms");if(c){const m=parseInt(c);isNaN(m)||(f=m)}}await vt(f);break;case"failed":case"cancelled":case"completed":return l}}}async uploadAndPoll(e,{files:n,fileIds:s=[]},i){if(n==null||n.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const a=(i==null?void 0:i.maxConcurrency)??5,l=Math.min(a,n.length),u=this._client,f=n.values(),c=[...s];async function m(p){for(let g of p){const y=await u.files.create({file:g,purpose:"assistants"},i);c.push(y.id)}}const h=Array(l).fill(f).map(m);return await Uy(h),await this.createAndPoll(e,{file_ids:c})}}class fa extends B{create(e,n,s){return this._client.post(M`/vector_stores/${e}/files`,{body:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}retrieve(e,n,s){const{vector_store_id:i}=n;return this._client.get(M`/vector_stores/${i}/files/${e}`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}update(e,n,s){const{vector_store_id:i,...a}=n;return this._client.post(M`/vector_stores/${i}/files/${e}`,{body:a,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e,n={},s){return this._client.getAPIList(M`/vector_stores/${e}/files`,Y,{query:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}delete(e,n,s){const{vector_store_id:i}=n;return this._client.delete(M`/vector_stores/${i}/files/${e}`,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}async createAndPoll(e,n,s){const i=await this.create(e,n,s);return await this.poll(e,i.id,s)}async poll(e,n,s){var a;const i=U([s==null?void 0:s.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=s==null?void 0:s.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const l=await this.retrieve(n,{vector_store_id:e},{...s,headers:i}).withResponse(),u=l.data;switch(u.status){case"in_progress":let f=5e3;if(s!=null&&s.pollIntervalMs)f=s.pollIntervalMs;else{const c=l.response.headers.get("openai-poll-after-ms");if(c){const m=parseInt(c);isNaN(m)||(f=m)}}await vt(f);break;case"failed":case"completed":return u}}}async upload(e,n,s){const i=await this._client.files.create({file:n,purpose:"assistants"},s);return this.create(e,{file_id:i.id},s)}async uploadAndPoll(e,n,s){const i=await this.upload(e,n,s);return await this.poll(e,i.id,s)}content(e,n,s){const{vector_store_id:i}=n;return this._client.getAPIList(M`/vector_stores/${i}/files/${e}/content`,un,{...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}class fn extends B{constructor(){super(...arguments),this.files=new fa(this._client),this.fileBatches=new da(this._client)}create(e,n){return this._client.post("/vector_stores",{body:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,n){return this._client.get(M`/vector_stores/${e}`,{...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,n,s){return this._client.post(M`/vector_stores/${e}`,{body:n,...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}list(e={},n){return this._client.getAPIList("/vector_stores",Y,{query:e,...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}delete(e,n){return this._client.delete(M`/vector_stores/${e}`,{...n,headers:U([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}search(e,n,s){return this._client.getAPIList(M`/vector_stores/${e}/search`,un,{body:n,method:"post",...s,headers:U([{"OpenAI-Beta":"assistants=v2"},s==null?void 0:s.headers])})}}fn.Files=fa;fn.FileBatches=da;var Oe,ma,Ht;class ha extends B{constructor(){super(...arguments),Oe.add(this)}async unwrap(e,n,s=this._client.webhookSecret,i=300){return await this.verifySignature(e,n,s,i),JSON.parse(e)}async verifySignature(e,n,s=this._client.webhookSecret,i=300){if(typeof crypto>"u"||typeof crypto.subtle.importKey!="function"||typeof crypto.subtle.verify!="function")throw new Error("Webhook signature verification is only supported when the `crypto` global is defined");v(this,Oe,"m",ma).call(this,s);const a=U([n]).values,l=v(this,Oe,"m",Ht).call(this,a,"webhook-signature"),u=v(this,Oe,"m",Ht).call(this,a,"webhook-timestamp"),f=v(this,Oe,"m",Ht).call(this,a,"webhook-id"),c=parseInt(u,10);if(isNaN(c))throw new it("Invalid webhook timestamp format");const m=Math.floor(Date.now()/1e3);if(m-c>i)throw new it("Webhook timestamp is too old");if(c>m+i)throw new it("Webhook timestamp is too new");const h=l.split(" ").map(x=>x.startsWith("v1,")?x.substring(3):x),p=s.startsWith("whsec_")?Buffer.from(s.replace("whsec_",""),"base64"):Buffer.from(s,"utf-8"),g=f?`${f}.${u}.${e}`:`${u}.${e}`,y=await crypto.subtle.importKey("raw",p,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(const x of h)try{const T=Buffer.from(x,"base64");if(await crypto.subtle.verify("HMAC",y,T,new TextEncoder().encode(g)))return}catch{continue}throw new it("The given webhook signature does not match the expected signature")}}Oe=new WeakSet,ma=function(e){if(typeof e!="string"||e.length===0)throw new Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},Ht=function(e,n){if(!e)throw new Error("Headers are required");const s=e.get(n);if(s==null)throw new Error(`Missing required header: ${n}`);return s};var zn,Ts,Wt,pa;class q{constructor({baseURL:e=Ve("OPENAI_BASE_URL"),apiKey:n=Ve("OPENAI_API_KEY"),organization:s=Ve("OPENAI_ORG_ID")??null,project:i=Ve("OPENAI_PROJECT_ID")??null,webhookSecret:a=Ve("OPENAI_WEBHOOK_SECRET")??null,...l}={}){if(zn.add(this),Wt.set(this,void 0),this.completions=new zr(this),this.chat=new us(this),this.embeddings=new Qr(this),this.files=new jr(this),this.images=new oa(this),this.audio=new _t(this),this.moderations=new aa(this),this.models=new ra(this),this.fineTuning=new je(this),this.graders=new xs(this),this.vectorStores=new fn(this),this.webhooks=new ha(this),this.beta=new Tt(this),this.batches=new Jr(this),this.uploads=new _s(this),this.responses=new Cs(this),this.evals=new hs(this),this.containers=new fs(this),n===void 0)throw new V("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const u={apiKey:n,organization:s,project:i,webhookSecret:a,...l,baseURL:e||"https://api.openai.com/v1"};if(!u.dangerouslyAllowBrowser&&$g())throw new V(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);this.baseURL=u.baseURL,this.timeout=u.timeout??Ts.DEFAULT_TIMEOUT,this.logger=u.logger??console;const f="warn";this.logLevel=f,this.logLevel=Zi(u.logLevel,"ClientOptions.logLevel",this)??Zi(Ve("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??f,this.fetchOptions=u.fetchOptions,this.maxRetries=u.maxRetries??2,this.fetch=u.fetch??Jg(),$(this,Wt,Hg),this._options=u,this.apiKey=n,this.organization=s,this.project=i,this.webhookSecret=a}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:n}){}async authHeaders(e){return U([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return Qg(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${Ge}`}defaultIdempotencyKey(){return`stainless-node-retry-${Ko()}`}makeStatusError(e,n,s,i){return se.generate(e,n,s,i)}buildURL(e,n,s){const i=!v(this,zn,"m",pa).call(this)&&s||this.baseURL,a=Rg(e)?new URL(e):new URL(i+(i.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),l=this.defaultQuery();return Dg(l)||(n={...l,...n}),typeof n=="object"&&n&&!Array.isArray(n)&&(a.search=this.stringifyQuery(n)),a.toString()}async prepareOptions(e){}async prepareRequest(e,{url:n,options:s}){}get(e,n){return this.methodRequest("get",e,n)}post(e,n){return this.methodRequest("post",e,n)}patch(e,n){return this.methodRequest("patch",e,n)}put(e,n){return this.methodRequest("put",e,n)}delete(e,n){return this.methodRequest("delete",e,n)}methodRequest(e,n,s){return this.request(Promise.resolve(s).then(i=>({method:e,path:n,...i})))}request(e,n=null){return new ln(this,this.makeRequest(e,n,void 0))}async makeRequest(e,n,s){var I,w;const i=await e,a=i.maxRetries??this.maxRetries;n==null&&(n=a),await this.prepareOptions(i);const{req:l,url:u,timeout:f}=await this.buildRequest(i,{retryCount:a-n});await this.prepareRequest(l,{url:u,options:i});const c="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),m=s===void 0?"":`, retryOf: ${s}`,h=Date.now();if(te(this).debug(`[${c}] sending request`,Pe({retryOfRequestLogID:s,method:i.method,url:u,options:i,headers:l.headers})),(I=i.signal)!=null&&I.aborted)throw new me;const p=new AbortController,g=await this.fetchWithTimeout(u,l,f,p).catch($n),y=Date.now();if(g instanceof Error){const A=`retrying, ${n} attempts remaining`;if((w=i.signal)!=null&&w.aborted)throw new me;const _=Un(g)||/timed? ?out/i.test(String(g)+("cause"in g?String(g.cause):""));if(n)return te(this).info(`[${c}] connection ${_?"timed out":"failed"} - ${A}`),te(this).debug(`[${c}] connection ${_?"timed out":"failed"} (${A})`,Pe({retryOfRequestLogID:s,url:u,durationMs:y-h,message:g.message})),this.retryRequest(i,n,s??c);throw te(this).info(`[${c}] connection ${_?"timed out":"failed"} - error; no more retries left`),te(this).debug(`[${c}] connection ${_?"timed out":"failed"} (error; no more retries left)`,Pe({retryOfRequestLogID:s,url:u,durationMs:y-h,message:g.message})),_?new ns:new rn({cause:g})}const x=[...g.headers.entries()].filter(([A])=>A==="x-request-id").map(([A,_])=>", "+A+": "+JSON.stringify(_)).join(""),T=`[${c}${m}${x}] ${l.method} ${u} ${g.ok?"succeeded":"failed"} with status ${g.status} in ${y-h}ms`;if(!g.ok){const A=await this.shouldRetry(g);if(n&&A){const b=`retrying, ${n} attempts remaining`;return await Og(g.body),te(this).info(`${T} - ${b}`),te(this).debug(`[${c}] response error (${b})`,Pe({retryOfRequestLogID:s,url:g.url,status:g.status,headers:g.headers,durationMs:y-h})),this.retryRequest(i,n,s??c,g.headers)}const _=A?"error; no more retries left":"error; not retryable";te(this).info(`${T} - ${_}`);const C=await g.text().catch(b=>$n(b).message),E=Ug(C),N=E?void 0:C;throw te(this).debug(`[${c}] response error (${_})`,Pe({retryOfRequestLogID:s,url:g.url,status:g.status,headers:g.headers,message:N,durationMs:Date.now()-h})),this.makeStatusError(g.status,E,N,g.headers)}return te(this).info(T),te(this).debug(`[${c}] response start`,Pe({retryOfRequestLogID:s,url:g.url,status:g.status,headers:g.headers,durationMs:y-h})),{response:g,options:i,controller:p,requestLogID:c,retryOfRequestLogID:s,startTime:h}}getAPIList(e,n,s){return this.requestAPIList(n,{method:"get",path:e,...s})}requestAPIList(e,n){const s=this.makeRequest(n,null,void 0);return new ry(this,s,e)}async fetchWithTimeout(e,n,s,i){const{signal:a,method:l,...u}=n||{};a&&a.addEventListener("abort",()=>i.abort());const f=setTimeout(()=>i.abort(),s),c=globalThis.ReadableStream&&u.body instanceof globalThis.ReadableStream||typeof u.body=="object"&&u.body!==null&&Symbol.asyncIterator in u.body,m={signal:i.signal,...c?{duplex:"half"}:{},method:"GET",...u};l&&(m.method=l.toUpperCase());try{return await this.fetch.call(void 0,e,m)}finally{clearTimeout(f)}}async shouldRetry(e){const n=e.headers.get("x-should-retry");return n==="true"?!0:n==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,n,s,i){let a;const l=i==null?void 0:i.get("retry-after-ms");if(l){const f=parseFloat(l);Number.isNaN(f)||(a=f)}const u=i==null?void 0:i.get("retry-after");if(u&&!a){const f=parseFloat(u);Number.isNaN(f)?a=Date.parse(u)-Date.now():a=f*1e3}if(!(a&&0<=a&&a<60*1e3)){const f=e.maxRetries??this.maxRetries;a=this.calculateDefaultRetryTimeoutMillis(n,f)}return await vt(a),this.makeRequest(e,n-1,s)}calculateDefaultRetryTimeoutMillis(e,n){const a=n-e,l=Math.min(.5*Math.pow(2,a),8),u=1-Math.random()*.25;return l*u*1e3}async buildRequest(e,{retryCount:n=0}={}){const s={...e},{method:i,path:a,query:l,defaultBaseURL:u}=s,f=this.buildURL(a,l,u);"timeout"in s&&Fg("timeout",s.timeout),s.timeout=s.timeout??this.timeout;const{bodyHeaders:c,body:m}=this.buildBody({options:s}),h=await this.buildHeaders({options:e,method:i,bodyHeaders:c,retryCount:n});return{req:{method:i,headers:h,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&m instanceof globalThis.ReadableStream&&{duplex:"half"},...m&&{body:m},...this.fetchOptions??{},...s.fetchOptions??{}},url:f,timeout:s.timeout}}async buildHeaders({options:e,method:n,bodyHeaders:s,retryCount:i}){let a={};this.idempotencyHeader&&n!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),a[this.idempotencyHeader]=e.idempotencyKey);const l=U([a,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(i),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...Gg(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(e),this._options.defaultHeaders,s,e.headers]);return this.validateHeaders(l),l.values}buildBody({options:{body:e,headers:n}}){if(!e)return{bodyHeaders:void 0,body:void 0};const s=U([n]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||typeof e=="string"&&s.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:typeof e=="object"&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&typeof e.next=="function")?{bodyHeaders:void 0,body:or(e)}:v(this,Wt,"f").call(this,{body:e,headers:s})}}Ts=q,Wt=new WeakMap,zn=new WeakSet,pa=function(){return this.baseURL!=="https://api.openai.com/v1"};q.OpenAI=Ts;q.DEFAULT_TIMEOUT=6e5;q.OpenAIError=V;q.APIError=se;q.APIConnectionError=rn;q.APIConnectionTimeoutError=ns;q.APIUserAbortError=me;q.NotFoundError=Qo;q.ConflictError=Zo;q.RateLimitError=er;q.BadRequestError=Yo;q.AuthenticationError=zo;q.InternalServerError=tr;q.PermissionDeniedError=Xo;q.UnprocessableEntityError=jo;q.InvalidWebhookSignatureError=it;q.toFile=fy;q.Completions=zr;q.Chat=us;q.Embeddings=Qr;q.Files=jr;q.Images=oa;q.Audio=_t;q.Moderations=aa;q.Models=ra;q.FineTuning=je;q.Graders=xs;q.VectorStores=fn;q.Webhooks=ha;q.Beta=Tt;q.Batches=Jr;q.Uploads=_s;q.Responses=Cs;q.Evals=hs;q.Containers=fs;class $y{constructor(){this.config=null,this.textGeminiClient=null,this.textOpenaiClient=null,this.imageGeminiClient=null,this.imageOpenaiClient=null}async getConfig(){var e,n,s,i,a,l,u,f,c,m,h,p,g;if(!this.config)try{const y=await oe.getAIConfigForClient();if(console.log("🔍 获取到的AI配置:",{textProvider:((e=y.config)==null?void 0:e.textProvider)||((n=y.config)==null?void 0:n.provider),imageProvider:((s=y.config)==null?void 0:s.imageProvider)||((i=y.config)==null?void 0:i.provider),hasTextApiKey:!!((a=y.config)!=null&&a.textApiKey||(l=y.config)!=null&&l.apiKey),hasImageApiKey:!!((u=y.config)!=null&&u.imageApiKey||(f=y.config)!=null&&f.apiKey),textBaseUrl:(c=y.config)==null?void 0:c.textBaseUrl,imageBaseUrl:(m=y.config)==null?void 0:m.imageBaseUrl,textModel:(h=y.config)==null?void 0:h.textModel,imageModel:(p=y.config)==null?void 0:p.imageModel,isActive:(g=y.config)==null?void 0:g.isActive,rawConfig:y.config}),y.config&&y.config.isActive){const x=y.config;if(x.textProvider&&x.imageProvider){if(!x.textApiKey||!x.imageApiKey)throw console.error("❌ 新格式配置中缺少文本或图片API Key"),new Error("配置中缺少文本或图片API Key");this.config={textProvider:x.textProvider,textApiKey:x.textApiKey,textBaseUrl:x.textBaseUrl,textModel:x.textModel||"gemini-2.5-flash",imageProvider:x.imageProvider,imageApiKey:x.imageApiKey,imageBaseUrl:x.imageBaseUrl,imageModel:x.imageModel||"imagen-3.0-generate-002",isActive:x.isActive,provider:x.textProvider,apiKey:x.textApiKey,baseUrl:x.textBaseUrl},console.log(`🔧 使用分离配置 - 文本: ${this.config.textProvider}/${this.config.textModel}, 图片: ${this.config.imageProvider}/${this.config.imageModel}`)}else{if(!x.apiKey)throw console.error("❌ 旧格式配置中缺少API Key"),new Error("配置中缺少API Key");this.config={textProvider:x.provider||"gemini",textApiKey:x.apiKey,textBaseUrl:x.baseUrl,textModel:x.textModel||"gemini-2.5-flash",imageProvider:x.provider||"gemini",imageApiKey:x.apiKey,imageBaseUrl:x.baseUrl,imageModel:x.imageModel||"imagen-3.0-generate-002",isActive:x.isActive,provider:x.provider||"gemini",apiKey:x.apiKey,baseUrl:x.baseUrl},console.log(`🔧 使用统一配置 - 供应商: ${this.config.provider}, 文本模型: ${this.config.textModel}, 图片模型: ${this.config.imageModel}`)}}else throw new Error("没有活跃的AI配置")}catch(y){console.log("获取用户AI配置失败，使用默认Gemini配置:",y);const{apiKey:x}=await oe.getAIApiKey();this.config={textProvider:"gemini",textApiKey:x,textModel:"gemini-2.5-flash",imageProvider:"gemini",imageApiKey:x,imageModel:"imagen-3.0-generate-002",isActive:!0,provider:"gemini",apiKey:x}}return this.config}async initializeClients(){const e=await this.getConfig();if(!e.textApiKey||!e.imageApiKey)throw new Error("文本或图片API Key未配置或为空");if(console.log(`🔧 初始化AI客户端 - 文本: ${e.textProvider}, 图片: ${e.imageProvider}`),e.textProvider==="gemini")this.textGeminiClient||(this.textGeminiClient=new qi({apiKey:e.textApiKey}),console.log("✅ 文本Gemini客户端初始化成功"));else if(e.textProvider==="openai")try{this.textOpenaiClient=new q({apiKey:e.textApiKey,baseURL:e.textBaseUrl,dangerouslyAllowBrowser:!0}),console.log(`✅ 文本OpenAI客户端初始化成功 - baseURL: ${e.textBaseUrl}`)}catch(n){throw console.error("❌ 文本OpenAI客户端初始化失败:",n),n}if(e.imageProvider==="gemini")this.imageGeminiClient||(this.imageGeminiClient=new qi({apiKey:e.imageApiKey}),console.log("✅ 图片Gemini客户端初始化成功"));else if(e.imageProvider==="openai")try{this.imageOpenaiClient=new q({apiKey:e.imageApiKey,baseURL:e.imageBaseUrl,dangerouslyAllowBrowser:!0}),console.log(`✅ 图片OpenAI客户端初始化成功 - baseURL: ${e.imageBaseUrl}`)}catch(n){throw console.error("❌ 图片OpenAI客户端初始化失败:",n),n}}async generateText(e){var i,a;await this.initializeClients();const n=await this.getConfig(),s=e.model||n.textModel;if(console.log(`📝 使用 ${n.textProvider} 的模型 ${s} 生成文本`),n.textProvider==="gemini")return(await this.textGeminiClient.models.generateContent({model:s,contents:e.prompt,config:e.responseFormat==="json"?{responseMimeType:"application/json"}:void 0})).text||"";if(n.textProvider==="openai")return((a=(i=(await this.textOpenaiClient.chat.completions.create({model:s,messages:[{role:"user",content:e.prompt}],response_format:e.responseFormat==="json"?{type:"json_object"}:void 0})).choices[0])==null?void 0:i.message)==null?void 0:a.content)||"";throw new Error(`不支持的文本供应商: ${n.textProvider}`)}async generateImage(e){var i,a,l,u;await this.initializeClients();const n=await this.getConfig(),s=e.model||n.imageModel;if(console.log(`🎨 使用 ${n.imageProvider} 的模型 ${s} 生成图片`),n.imageProvider==="gemini")return`data:image/jpeg;base64,${((l=(a=(i=(await this.imageGeminiClient.models.generateImages({model:s,prompt:e.prompt,config:{numberOfImages:e.numberOfImages||1,outputMimeType:"image/jpeg",aspectRatio:e.aspectRatio||"1:1"}})).generatedImages)==null?void 0:i[0])==null?void 0:a.image)==null?void 0:l.imageBytes)||""}`;if(n.imageProvider==="openai")return console.log("🖼️ OpenAI图片生成参数:",{model:s,prompt:e.prompt,n:e.numberOfImages||1,size:e.size||"1024x1024",response_format:"b64_json"}),`data:image/png;base64,${((u=(await this.imageOpenaiClient.images.generate({model:s,prompt:e.prompt,n:e.numberOfImages||1,size:e.size||"1024x1024",response_format:"b64_json"})).data[0])==null?void 0:u.b64_json)||""}`;throw new Error(`不支持的图片供应商: ${n.imageProvider}`)}async testConnection(){var e,n;try{await this.initializeClients();const s=await this.getConfig();console.log(`🧪 测试文本: ${s.textProvider}, 图片: ${s.imageProvider} API连接...`);let i="";return s.textProvider==="gemini"?i=(await this.textGeminiClient.models.generateContent({model:s.textModel,contents:'Hello, this is a test message. Please respond with "Test successful".'})).text||"":s.textProvider==="openai"&&(i=((n=(e=(await this.textOpenaiClient.chat.completions.create({model:s.textModel,messages:[{role:"user",content:'Hello, this is a test message. Please respond with "Test successful".'}]})).choices[0])==null?void 0:e.message)==null?void 0:n.content)||""),i.length>0?{success:!0,message:`API测试成功！文本: ${s.textProvider}/${s.textModel}, 图片: ${s.imageProvider}/${s.imageModel}`,details:{textResponse:i.substring(0,100)+"...",textProvider:s.textProvider,imageProvider:s.imageProvider}}:{success:!1,message:`文本API测试失败: ${s.textProvider}`,details:{textResponse:i}}}catch(s){return console.error("❌ API测试失败:",s),{success:!1,message:`API测试失败: ${s instanceof Error?s.message:"未知错误"}`,details:{error:s instanceof Error?s.message:s}}}}resetConfig(){console.log("🔄 重置AI配置缓存"),this.config=null,this.geminiClient=null,this.openaiClient=null}}const go=new $y,Vy=()=>{const[t,e]=R.useState(null),[n,s]=R.useState(null),[i,a]=R.useState(!0),[l,u]=R.useState("overview"),[f,c]=R.useState(!1);R.useEffect(()=>{m()},[]);const m=async()=>{try{a(!0);const[g,y]=await Promise.all([Dt.getSystemStats(),Dt.getStorageAnalysis()]);e(g),s(y)}catch(g){console.error("加载系统统计失败:",g)}finally{a(!1)}},h=async()=>{try{c(!0),await Dt.cleanupStorage(),alert("存储清理完成！"),m()}catch(g){console.error("存储清理失败:",g),alert("存储清理失败")}finally{c(!1)}},p=g=>{const y=parseInt(g);return y<50?"bg-green-500":y<80?"bg-yellow-500":"bg-red-500"};return i?d.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:d.jsxs("div",{className:"animate-pulse",children:[d.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),d.jsxs("div",{className:"space-y-3",children:[d.jsx("div",{className:"h-3 bg-gray-200 rounded"}),d.jsx("div",{className:"h-3 bg-gray-200 rounded w-5/6"}),d.jsx("div",{className:"h-3 bg-gray-200 rounded w-4/6"})]})]})}):t?d.jsxs("div",{className:"bg-white rounded-lg shadow",children:[d.jsx("div",{className:"border-b border-gray-200",children:d.jsx("nav",{className:"flex space-x-8 px-6",children:[{key:"overview",label:"📊 系统概览"},{key:"storage",label:"💾 存储分析"},{key:"cleanup",label:"🧹 清理工具"}].map(g=>d.jsx("button",{onClick:()=>u(g.key),className:`py-4 px-1 border-b-2 font-medium text-sm ${l===g.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:g.label},g.key))})}),d.jsxs("div",{className:"p-6",children:[l==="overview"&&d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[d.jsx("h3",{className:"text-lg font-semibold mb-3",children:"💽 磁盘使用情况"}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-2xl font-bold text-blue-600",children:t.disk.total}),d.jsx("div",{className:"text-sm text-gray-600",children:"总容量"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-2xl font-bold text-orange-600",children:t.disk.used}),d.jsx("div",{className:"text-sm text-gray-600",children:"已使用"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-2xl font-bold text-green-600",children:t.disk.available}),d.jsx("div",{className:"text-sm text-gray-600",children:"可用空间"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-2xl font-bold text-red-600",children:t.disk.usagePercent}),d.jsx("div",{className:"text-sm text-gray-600",children:"使用率"})]})]}),d.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:d.jsx("div",{className:`h-3 rounded-full ${p(t.disk.usagePercent)}`,style:{width:t.disk.usagePercent}})})]}),d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[d.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🚀 应用程序占用"}),d.jsx("div",{className:"mb-4",children:d.jsxs("div",{className:"text-xl font-bold text-purple-600",children:["总大小: ",t.application.totalSize]})}),d.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:t.application.components.map((g,y)=>d.jsxs("div",{className:"bg-white rounded p-3 border",children:[d.jsx("div",{className:"font-medium",children:g.name}),d.jsx("div",{className:"text-sm text-gray-600",children:g.size})]},y))})]}),d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[d.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🎵 音频文件统计"}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-2xl font-bold text-blue-600",children:t.audio.fileCount}),d.jsx("div",{className:"text-sm text-gray-600",children:"音频文件数量"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-2xl font-bold text-green-600",children:t.audio.totalSize}),d.jsx("div",{className:"text-sm text-gray-600",children:"音频文件总大小"})]})]})]}),d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[d.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🗄️ 数据库统计"}),d.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xl font-bold text-blue-600",children:t.database.articles}),d.jsx("div",{className:"text-sm text-gray-600",children:"文章数量"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xl font-bold text-green-600",children:t.database.users}),d.jsx("div",{className:"text-sm text-gray-600",children:"用户数量"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xl font-bold text-purple-600",children:t.database.audioFiles}),d.jsx("div",{className:"text-sm text-gray-600",children:"音频文件"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xl font-bold text-orange-600",children:t.database.size}),d.jsx("div",{className:"text-sm text-gray-600",children:"数据库大小"})]})]})]}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[d.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🧠 内存使用"}),d.jsxs("div",{className:"space-y-2",children:[d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{children:"总内存:"}),d.jsx("span",{className:"font-medium",children:t.memory.total})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{children:"已使用:"}),d.jsx("span",{className:"font-medium text-orange-600",children:t.memory.used})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{children:"可用:"}),d.jsx("span",{className:"font-medium text-green-600",children:t.memory.free})]})]})]}),d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[d.jsx("h3",{className:"text-lg font-semibold mb-3",children:"⚡ 系统状态"}),d.jsxs("div",{className:"space-y-2",children:[d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{children:"CPU使用率:"}),d.jsx("span",{className:"font-medium",children:t.cpu.usage})]}),d.jsxs("div",{className:"flex justify-between",children:[d.jsx("span",{children:"系统运行时间:"}),d.jsx("span",{className:"font-medium",children:t.uptime})]})]})]})]})]}),l==="storage"&&n&&d.jsxs("div",{className:"space-y-6",children:[d.jsx("h3",{className:"text-lg font-semibold",children:"📁 详细存储分析"}),d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[d.jsx("h4",{className:"font-semibold mb-3",children:"🎵 音频文件详情"}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xl font-bold",children:n.audioFiles.totalFiles}),d.jsx("div",{className:"text-sm text-gray-600",children:"总文件数"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xl font-bold",children:n.audioFiles.totalSize}),d.jsx("div",{className:"text-sm text-gray-600",children:"总大小"})]}),d.jsxs("div",{className:"text-center",children:[d.jsx("div",{className:"text-xl font-bold",children:n.audioFiles.averageSize}),d.jsx("div",{className:"text-sm text-gray-600",children:"平均大小"})]})]}),n.audioFiles.largestFiles.length>0&&d.jsxs("div",{children:[d.jsx("h5",{className:"font-medium mb-2",children:"最大的音频文件:"}),d.jsx("div",{className:"space-y-2",children:n.audioFiles.largestFiles.map((g,y)=>d.jsx("div",{className:"bg-white rounded p-3 border",children:d.jsxs("div",{className:"flex justify-between items-center",children:[d.jsxs("div",{children:[d.jsx("div",{className:"font-medium",children:g.articleTitle}),d.jsx("div",{className:"text-sm text-gray-600",children:g.fileName})]}),d.jsxs("div",{className:"text-right",children:[d.jsx("div",{className:"font-medium",children:g.size}),d.jsx("div",{className:"text-sm text-gray-600",children:new Date(g.uploadDate).toLocaleDateString()})]})]})},y))})]})]})]}),l==="cleanup"&&d.jsxs("div",{className:"space-y-6",children:[d.jsx("h3",{className:"text-lg font-semibold",children:"🧹 存储清理工具"}),d.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:d.jsxs("div",{className:"flex items-start",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("svg",{className:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),d.jsxs("div",{className:"ml-3",children:[d.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"注意"}),d.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:d.jsx("p",{children:"清理操作将删除临时文件和缓存，这个操作不可逆。请确保您了解这些操作的影响。"})})]})]})}),d.jsxs("div",{className:"bg-white border rounded-lg p-4",children:[d.jsx("h4",{className:"font-semibold mb-3",children:"可清理的内容:"}),d.jsxs("ul",{className:"space-y-2 mb-4",children:[d.jsxs("li",{className:"flex items-center",children:[d.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"7天前的日志文件"]}),d.jsxs("li",{className:"flex items-center",children:[d.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"npm缓存文件"]}),d.jsxs("li",{className:"flex items-center",children:[d.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"临时文件"]})]}),d.jsx("button",{onClick:h,disabled:f,className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:f?"清理中...":"开始清理"})]})]})]}),d.jsx("div",{className:"border-t border-gray-200 px-6 py-3",children:d.jsx("button",{onClick:m,disabled:i,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:i?"刷新中...":"刷新数据"})})]}):d.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:d.jsx("p",{className:"text-red-600",children:"无法加载系统统计信息"})})},By=()=>{const[t,e]=R.useState([]),[n,s]=R.useState(!0),[i,a]=R.useState(!1),[l,u]=R.useState(null),[f,c]=R.useState(!1),[m,h]=R.useState({version:"",type:"minor",title:"",description:"",changes:[""]});R.useEffect(()=>{p()},[]);const p=async()=>{try{s(!0);const C=await qe.getSystemLogs({limit:50});e(C.logs)}catch(C){console.error("加载日志失败:",C)}finally{s(!1)}},g=async C=>{C.preventDefault();const E=m.changes.filter(N=>N.trim());if(E.length===0){alert("请至少添加一条变更记录");return}try{const N={...m,changes:E};l?await qe.updateSystemLog(l.id,N):await qe.createSystemLog(N),a(!1),u(null),h({version:"",type:"minor",title:"",description:"",changes:[""]}),p()}catch(N){console.error("保存日志失败:",N),alert("保存失败，请检查版本号是否重复")}},y=C=>{u(C),h({version:C.version,type:C.type,title:C.title||"",description:C.description||"",changes:C.changes.length>0?C.changes:[""]}),a(!0)},x=async C=>{if(confirm("确定要删除这条日志吗？"))try{await qe.deleteSystemLog(C),p()}catch(E){console.error("删除日志失败:",E),alert("删除失败")}},T=()=>{h(C=>({...C,changes:[...C.changes,""]}))},I=(C,E)=>{h(N=>({...N,changes:N.changes.map((P,b)=>b===C?E:P)}))},w=C=>{h(E=>({...E,changes:E.changes.filter((N,P)=>P!==C)}))},A=async()=>{if(confirm("确定要导入历史日志吗？这将添加所有历史版本记录。"))try{c(!0);const C=[{version:"v2.2.0",date:"2025-01-11",type:"minor",title:"播客功能上线",description:"为Joker期刊系统添加完整的音频播客功能，提供多媒体阅读体验",changes:["🎵 播客功能：支持为文章添加音频播客，提供完整的多媒体阅读体验","🎧 音频播放器：专业的音频播放控件，支持进度控制、音量调节、时长显示","📤 音频上传：管理员可上传最大50MB的音频文件，支持拖拽上传","🎛️ 播放控制：播放/暂停、进度跳转、音量控制等完整功能","📊 音频管理：支持音频信息编辑、删除等管理功能","🔒 权限控制：只有管理员和编辑可以上传/删除音频文件","📱 响应式设计：音频播放器适配各种屏幕尺寸"]},{version:"v2.1.3",date:"2025-01-11",type:"patch",title:"AI模型升级",description:"全面升级到Gemini 2.5 Flash模型，提升生成性能",changes:["🤖 AI模型升级：全面切换到Gemini 2.5 Flash模型，提升生成速度和质量","⚡ 性能优化：新模型响应更快，生成内容更加稳定","🔧 统一配置：前后端AI调用统一使用最新的模型版本","📝 改进生成：文章内容和图片生成都使用优化后的模型"]},{version:"v2.1.2",date:"2025-01-11",type:"patch",title:"用户去重统计",description:"优化观看次数统计，避免重复计数",changes:["👤 用户去重统计：同一用户只会被统计一次观看次数，避免重复刷新增加浏览量","🔍 智能识别：基于用户ID（已登录）+ IP地址 + 浏览器指纹进行用户识别","💾 本地缓存：使用localStorage记录已访问文章，减少不必要的API调用","📊 真实统计：新增观看记录表，提供更准确的用户行为分析","🎯 精确去重：确保统计数据的真实性和准确性"]},{version:"v2.1.1",date:"2025-01-11",type:"patch",title:"修复观看次数统计",description:"解决观看次数不更新的问题",changes:["🔧 修复观看次数统计：解决前端未调用后端API导致观看次数不更新的问题","📊 优化统计逻辑：移除重复的观看次数增加逻辑，确保统计准确性","⚡ 实时更新：进入文章页面时自动获取最新的观看次数并显示","🎯 精确计算：每次访问文章详情页都会正确增加观看次数"]},{version:"v2.1.0",date:"2025-01-11",type:"minor",title:"观看统计与批量管理",description:"新增观看次数统计和批量删除功能",changes:["📊 新增观看次数统计：文章详情页自动统计观看次数，实时显示浏览量","🗂️ 批量删除功能：管理员可多选文章进行批量删除，提升管理效率","✏️ 观看次数编辑：管理员可手动修改文章观看次数，支持数据调整","☑️ 全选功能：支持一键全选/取消全选所有文章，便于批量操作","🎯 智能选择：选中文章数量实时显示，操作状态清晰可见","🔒 权限控制：批量删除和观看次数编辑仅限管理员和编辑使用","📈 数据展示：文章列表和详情页都显示观看次数，数据透明化"]},{version:"v2.0.1",date:"2025-01-11",type:"patch",title:"UI优化与修复",description:"修复重复显示问题，优化用户体验",changes:["🔧 修复UI重复问题：移除重复的'文章内容尚未生成'提示，优化用户体验","📝 修复日志显示：解决更新日志中重复列表符号的显示问题","🎯 优化内容生成：改进prompt避免重复生成标题和摘要，从正文部分开始延续","✨ 智能内容衔接：生成详细内容时会基于已有的标题、作者、摘要信息继续写作","🎨 界面清理：移除冗余的提示信息，让界面更加简洁明了"]},{version:"v2.0.0",date:"2025-01-11",type:"major",title:"Joker 2.0 重大更新",description:"期刊正式更名为Joker，新增双模式系统",changes:["🃏 重大更名：期刊名称从'Jocker'正式更名为'Joker'，修正了所有前端显示","🎭 双模式系统：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）","🎓 学术生产力：严肃模式生成符合顶级期刊标准的专业文章，成为真正的学术工具","🎪 娱乐保留：小丑模式继续提供幽默、戏谑的学术文章","⚙️ 智能切换：根据模式自动使用不同的AI提示词模板","🔬 严谨标准：严肃模式确保科学方法论、统计分析、引用格式的正确性","🎨 全新界面：生成弹窗提供直观的模式选择体验","📚 完整更新：DOI、引用格式、PDF文件名等全面更新为新期刊名称","🔧 技术优化：修复了多个渲染和显示问题，提升系统稳定性"]},{version:"v1.8.0",date:"2025-01-11",type:"minor",title:"生成模式选择",description:"新增小丑模式和严肃模式选择",changes:["🎭 新增生成模式选择：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）","🎓 严肃模式功能：生成符合顶级期刊发表标准的专业学术文章，可作为真正的学术生产力工具","🎪 小丑模式保留：继续支持原有的戏谑、幽默学术文章生成","⚙️ 智能提示词切换：根据选择的模式自动使用不同的AI提示词模板","🔬 严谨学术标准：严肃模式确保科学方法论正确、统计分析合理、引用格式标准","🎨 直观模式选择：生成和重新生成弹窗中提供清晰的模式选择界面"]},{version:"v1.7.6",date:"2025-01-11",type:"patch",title:"图片生成优化",description:"修复图片生成问题，优化错误处理",changes:["🔧 修复图片生成问题：限制AI提示词长度在400字符以内，避免413错误","⚡ 优化错误处理：图片生成失败时保存提示词并跳过到下一张，避免进度卡死","🎨 修复后台布局：解决文章标题过长时编辑删除按钮被挤出容器的问题","📱 改进响应式设计：后台文章管理界面按钮现在垂直排列，确保始终可点击","🖼️ 修复失败图片显示：失败的图片现在会在Figures画廊中显示，可以点击重新生成","💡 改进用户提示：文章中的失败占位符会提示用户到Figures画廊重新生成","📝 修复列表渲染问题：解决有序列表和无序列表在Markdown渲染时丢失的问题","🔧 优化Markdown解析：改进marked配置和列表格式处理，确保列表正确显示"]},{version:"v1.7.5",date:"2025-01-11",type:"minor",title:"文章编辑功能",description:"新增文章编辑功能和权限管理",changes:["✏️ 新增文章编辑功能：管理员和编辑可以直接编辑文章的Markdown内容","🎯 权限分级管理：管理员可生成和重新生成文章，编辑只能编辑现有内容","📝 实时编辑界面：提供专业的Markdown编辑器，支持语法提示和格式指导","🔄 自动重新渲染：编辑保存后自动重新渲染文章内容，包括图片和引用","🛡️ 角色权限控制：基于用户角色显示不同的操作按钮和功能","💾 即时保存功能：编辑内容可即时保存到数据库并更新显示"]},{version:"v1.7.4",date:"2025-01-11",type:"patch",title:"文献引用优化",description:"优化文献引用格式和PDF导出",changes:["📚 文献引用上标化：文献引用现在显示为学术期刊标准的上标格式，保留中括号","🔗 引用点击跳转：点击文献引用可平滑滚动到对应参考文献并高亮显示","📄 优化PDF导出：移除PDF中多余的'Peer Reviewed'和分类标签行","🔧 修复加粗渲染：改进紧贴数字和Figure标题的加粗标记处理","✨ 引用格式支持：支持[1], [1,2], [1-3], [1,3-5]等多种引用格式","🎨 视觉优化：引用点击后临时高亮效果，提升用户体验"]},{version:"v1.7.3",date:"2025-01-11",type:"minor",title:"文章生成流程优化",description:"分离基本信息生成和详细内容生成",changes:["🎯 优化文章生成流程：分离基本信息生成和详细内容生成","📝 首页生成文章：现在只需输入主题，生成标题、作者、摘要和封面","⚙️ 详情页生成正文：在文章详情页可自定义字数、图片数量和参考文献数量","🎨 新增生成参数弹窗：用户可在生成正文时设置文章长度、图片数量和参考文献数量","📚 新增参考文献数量控制：支持自定义1-10篇参考文献，默认3-5篇","🔄 改进重新生成功能：支持自定义参数重新生成文章内容","📄 优化PDF导出：文件名现在使用DOI格式","📊 新增生成进度显示：实时显示文章生成进度"]}];await qe.importHistoryLogs(C),alert("历史日志导入成功！"),p()}catch(C){console.error("导入历史日志失败:",C),alert("导入失败，请检查网络连接")}finally{c(!1)}},_=C=>{switch(C){case"major":return"bg-red-100 text-red-800";case"minor":return"bg-blue-100 text-blue-800";case"patch":return"bg-green-100 text-green-800";case"hotfix":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex justify-between items-center",children:[d.jsx("h2",{className:"text-2xl font-bold",children:"📋 日志管理"}),d.jsxs("div",{className:"flex space-x-3",children:[d.jsx("button",{onClick:A,disabled:f,className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:bg-gray-400",children:f?"导入中...":"📥 导入历史日志"}),d.jsx("button",{onClick:()=>{a(!0),u(null),h({version:"",type:"minor",title:"",description:"",changes:[""]})},className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"➕ 添加日志"})]})]}),i&&d.jsxs("div",{className:"bg-white border rounded-lg p-6",children:[d.jsx("h3",{className:"text-lg font-semibold mb-4",children:l?"编辑日志":"创建新日志"}),d.jsxs("form",{onSubmit:g,className:"space-y-4",children:[d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"版本号 *"}),d.jsx("input",{type:"text",value:m.version,onChange:C=>h(E=>({...E,version:C.target.value})),placeholder:"如: v2.3.0",className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型 *"}),d.jsxs("select",{value:m.type,onChange:C=>h(E=>({...E,type:C.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[d.jsx("option",{value:"major",children:"🚀 重大更新"}),d.jsx("option",{value:"minor",children:"✨ 功能更新"}),d.jsx("option",{value:"patch",children:"🔧 修复更新"}),d.jsx("option",{value:"hotfix",children:"🚨 紧急修复"})]})]})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"标题"}),d.jsx("input",{type:"text",value:m.title,onChange:C=>h(E=>({...E,title:C.target.value})),placeholder:"可选的更新标题",className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"描述"}),d.jsx("textarea",{value:m.description,onChange:C=>h(E=>({...E,description:C.target.value})),placeholder:"可选的详细描述",rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"变更列表 *"}),m.changes.map((C,E)=>d.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[d.jsx("input",{type:"text",value:C,onChange:N=>I(E,N.target.value),placeholder:"输入变更内容",className:"flex-1 border border-gray-300 rounded-md px-3 py-2"}),m.changes.length>1&&d.jsx("button",{type:"button",onClick:()=>w(E),className:"text-red-600 hover:text-red-800",children:"❌"})]},E)),d.jsx("button",{type:"button",onClick:T,className:"text-blue-600 hover:text-blue-800 text-sm",children:"➕ 添加变更项"})]}),d.jsxs("div",{className:"flex space-x-3",children:[d.jsx("button",{type:"submit",className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:l?"更新":"创建"}),d.jsx("button",{type:"button",onClick:()=>{a(!1),u(null)},className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400",children:"取消"})]})]})]}),d.jsxs("div",{className:"bg-white rounded-lg shadow",children:[d.jsx("div",{className:"p-4 border-b border-gray-200",children:d.jsx("h3",{className:"text-lg font-semibold",children:"现有日志"})}),n?d.jsx("div",{className:"p-6 text-center",children:"加载中..."}):t.length===0?d.jsx("div",{className:"p-6 text-center text-gray-500",children:"暂无日志记录"}):d.jsx("div",{className:"divide-y divide-gray-200",children:t.map(C=>d.jsx("div",{className:"p-4 hover:bg-gray-50",children:d.jsxs("div",{className:"flex justify-between items-start",children:[d.jsxs("div",{className:"flex-1",children:[d.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[d.jsx("span",{className:"font-mono text-lg",children:C.version}),d.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${_(C.type)}`,children:C.type}),d.jsx("span",{className:"text-sm text-gray-500",children:new Date(C.date).toLocaleDateString()})]}),C.title&&d.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:C.title}),d.jsxs("div",{className:"text-sm text-gray-600",children:[C.changes.slice(0,2).map((E,N)=>d.jsxs("div",{children:["• ",E]},N)),C.changes.length>2&&d.jsxs("div",{className:"text-gray-400",children:["... 还有 ",C.changes.length-2," 项"]})]})]}),d.jsxs("div",{className:"flex space-x-2 ml-4",children:[d.jsx("button",{onClick:()=>y(C),className:"text-blue-600 hover:text-blue-800 text-sm",children:"编辑"}),d.jsx("button",{onClick:()=>x(C.id),className:"text-red-600 hover:text-red-800 text-sm",children:"删除"})]})]})},C.id))})]})]})},qy=({onUploadSuccess:t,onUploadError:e})=>{const[n,s]=R.useState(!1),[i,a]=R.useState(null),[l,u]=R.useState(""),[f,c]=R.useState(""),m=R.useRef(null),h=y=>{var I;const x=(I=y.target.files)==null?void 0:I[0];if(!x)return;if(!x.type.startsWith("image/")){e==null||e("请选择图片文件");return}if(x.size>10*1024*1024){e==null||e("图片文件不能超过10MB");return}const T=new FileReader;T.onload=w=>{var _;const A=(_=w.target)==null?void 0:_.result;a(A)},T.readAsDataURL(x)},p=async()=>{if(!i){e==null||e("请先选择图片");return}s(!0);try{const y=await wa.uploadWebsiteCover({coverUrl:i,title:l||"网站封面",description:f||"网站主页封面图片"});t==null||t(y),a(null),u(""),c(""),m.current&&(m.current.value="")}catch(y){e==null||e(y instanceof Error?y.message:"上传失败")}finally{s(!1)}},g=()=>{a(null),u(""),c(""),m.current&&(m.current.value="")};return d.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[d.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"🖼️ 网站封面上传"}),d.jsxs("div",{className:"mb-4",children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择封面图片"}),d.jsx("input",{ref:m,type:"file",accept:"image/*",onChange:h,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"}),d.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"支持 JPG、PNG、GIF 格式，最大 10MB"})]}),i&&d.jsxs("div",{className:"mb-4",children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"预览效果"}),d.jsxs("div",{className:"relative",children:[d.jsx("img",{src:i,alt:"封面预览",className:"w-full h-64 object-cover rounded-lg border border-gray-200",style:{objectPosition:"center center"}}),d.jsx("div",{className:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md",children:d.jsxs("div",{className:"flex items-start",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("svg",{className:"h-5 w-5 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),d.jsxs("div",{className:"ml-3",children:[d.jsx("h4",{className:"text-sm font-medium text-blue-800",children:"💡 竖图优化建议"}),d.jsxs("p",{className:"mt-1 text-sm text-blue-700",children:["系统会自动使用 ",d.jsx("code",{children:"object-fit: cover"})," 来优化显示效果，确保图片在不同屏幕尺寸下都能美观展示。 竖图会自动居中裁剪，重点内容请放在图片中央区域。"]})]})]})})]})]}),i&&d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"mb-4",children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标题 (可选)"}),d.jsx("input",{type:"text",value:l,onChange:y=>u(y.target.value),placeholder:"网站封面",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"})]}),d.jsxs("div",{className:"mb-6",children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"描述 (可选)"}),d.jsx("textarea",{value:f,onChange:y=>c(y.target.value),placeholder:"网站主页封面图片",rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"})]}),d.jsxs("div",{className:"flex space-x-3",children:[d.jsx("button",{onClick:p,disabled:n,className:"flex-1 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:n?"上传中...":"🚀 上传封面"}),d.jsx("button",{onClick:g,disabled:n,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors",children:"取消"})]})]})]})},Gy=({onUploadSuccess:t,onUploadError:e})=>{const[n,s]=R.useState(!1),[i,a]=R.useState(null),[l,u]=R.useState(""),[f,c]=R.useState(""),[m,h]=R.useState(""),p=y=>{var I;const x=(I=y.target.files)==null?void 0:I[0];if(!x)return;if(!x.type.startsWith("image/")){e("请选择图片文件");return}if(x.size>10*1024*1024){e("图片文件不能超过10MB");return}const T=new FileReader;T.onload=w=>{var _;const A=(_=w.target)==null?void 0:_.result;a(A)},T.readAsDataURL(x)},g=async()=>{if(!i){e("请先选择图片");return}if(!l.trim()){e("请输入广告标题");return}s(!0);try{const y=await oe.uploadAdvertisementCover({imageUrl:i,title:l.trim(),description:f.trim(),linkUrl:m.trim()||"#"});t(y),a(null),u(""),c(""),h("");const x=document.getElementById("ad-cover-input");x&&(x.value="")}catch(y){console.error("上传广告封面失败:",y),e(y instanceof Error?y.message:"上传失败")}finally{s(!1)}};return d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{children:[d.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"📢 广告封面设置"}),d.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"设置侧边栏广告区域的封面图片和相关信息。"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择广告图片"}),d.jsx("input",{id:"ad-cover-input",type:"file",accept:"image/*",onChange:p,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"支持 JPG、PNG、GIF 格式，最大 10MB"})]}),i&&d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片预览"}),d.jsx("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:d.jsx("img",{src:i,alt:"广告预览",className:"max-w-full h-auto max-h-64 mx-auto rounded-md shadow-sm"})})]}),d.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"广告标题 *"}),d.jsx("input",{type:"text",value:l,onChange:y=>u(y.target.value),placeholder:"例如：Confused by Science?",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"广告描述"}),d.jsx("input",{type:"text",value:f,onChange:y=>c(y.target.value),placeholder:"例如：You're not alone. Try our new coffee mug.",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"点击链接"}),d.jsx("input",{type:"url",value:m,onChange:y=>h(y.target.value),placeholder:"例如：https://example.com/shop",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),d.jsx("div",{className:"flex justify-end",children:d.jsx("button",{onClick:g,disabled:n||!i||!l.trim(),className:`px-6 py-2 rounded-md font-medium transition-colors ${n||!i||!l.trim()?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:n?"上传中...":"上传广告封面"})})]})},Jy=({onUpdateSuccess:t,onUpdateError:e})=>{const[n,s]=R.useState("gemini"),[i,a]=R.useState(""),[l,u]=R.useState(""),[f,c]=R.useState("gemini-2.0-flash-exp"),[m,h]=R.useState("gemini"),[p,g]=R.useState(""),[y,x]=R.useState(""),[T,I]=R.useState("imagen-3.0-generate-001"),[w,A]=R.useState(!0),[_,C]=R.useState(!1),[E,N]=R.useState(!1),[P,b]=R.useState(null),[L,k]=R.useState(!1),[K,fe]=R.useState(!1),[z,X]=R.useState(null),[ee,et]=R.useState("gemini"),[be,St]=R.useState(""),[Me,wt]=R.useState("");R.useEffect(()=>{S()},[]),R.useEffect(()=>{n==="gemini"?(c("gemini-2.0-flash-exp"),u("")):n==="openai"&&(c("gpt-4o"),u("https://api.openai.com/v1"))},[n]),R.useEffect(()=>{m==="gemini"?(I("imagen-3.0-generate-001"),x("")):m==="openai"&&(I("dall-e-3"),x("https://api.openai.com/v1"))},[m]);const S=async()=>{try{const D=await oe.getAIConfig();b(D.config),D.config&&(D.config.textProvider&&D.config.imageProvider?(C(!0),s(D.config.textProvider),u(D.config.textBaseUrl||""),h(D.config.imageProvider),x(D.config.imageBaseUrl||"")):(C(!1),et(D.config.provider||"gemini"),wt(D.config.baseUrl||""),s(D.config.provider||"gemini"),u(D.config.baseUrl||""),h(D.config.provider||"gemini"),x(D.config.baseUrl||"")),A(D.config.isActive),c(D.config.textModel),I(D.config.imageModel))}catch(D){console.error("加载AI配置失败:",D)}},O=async D=>{if(D.preventDefault(),_){if(!i.trim()||!p.trim()){e("请输入文本和图片的API Key");return}if(n==="openai"&&!l.trim()){e("文本OpenAI格式需要提供基础URL");return}if(m==="openai"&&!y.trim()){e("图片OpenAI格式需要提供基础URL");return}}else{if(!be.trim()){e("请输入API Key");return}if(ee==="openai"&&!Me.trim()){e("OpenAI格式需要提供基础URL");return}}if(!f.trim()){e("请输入文本生成模型");return}if(!T.trim()){e("请输入图片生成模型");return}N(!0);try{const Ae=_?{textProvider:n,textApiKey:i.trim(),textBaseUrl:n==="openai"?l.trim():void 0,textModel:f.trim(),imageProvider:m,imageApiKey:p.trim(),imageBaseUrl:m==="openai"?y.trim():void 0,imageModel:T.trim(),isActive:w}:{provider:ee,apiKey:be.trim(),baseUrl:ee==="openai"?Me.trim():void 0,isActive:w,textModel:f.trim(),imageModel:T.trim()},ga=await oe.setAIConfig(Ae);t("AI API配置设置成功！"),b(ga.config),_?(a(""),g("")):St("")}catch(Ae){console.error("设置AI配置失败:",Ae),e(Ae instanceof Error?Ae.message:"设置AI配置失败")}finally{N(!1)}},H=async()=>{if(P){N(!0);try{const D=await oe.setAIConfig({provider:P.provider,apiKey:P.apiKeyPreview.replace("...",""),baseUrl:P.baseUrl,isActive:!w,textModel:P.textModel,imageModel:P.imageModel});A(!w),b(D.config),t(`AI配置已${w?"禁用":"启用"}`)}catch(D){console.error("切换AI配置状态失败:",D),e("切换状态失败")}finally{N(!1)}}},ye=async()=>{if(!P){X({success:!1,message:"请先保存配置后再测试"});return}fe(!0),X(null);try{go.resetConfig();const D=await go.testConnection();X(D),D.success?t(D.message):e(D.message)}catch(D){const Ae=D instanceof Error?D.message:"测试连接失败";X({success:!1,message:Ae,details:{error:D}}),e(Ae)}finally{fe(!1)}};return d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{children:[d.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"🤖 AI API 配置管理"}),d.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"设置自定义的AI API配置。支持Gemini和OpenAI格式的API。当设置了自定义配置时，系统将优先使用您提供的配置而不是服务器默认配置。"})]}),P&&d.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:[d.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"当前配置"}),d.jsxs("div",{className:"space-y-2 text-sm",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"配置模式:"}),d.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${P.textProvider&&P.imageProvider?"bg-purple-100 text-purple-800":"bg-blue-100 text-blue-800"}`,children:P.textProvider&&P.imageProvider?"分离配置":"统一配置"})]}),P.textProvider&&P.imageProvider?d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"bg-green-50 border border-green-200 rounded p-2 mt-2",children:[d.jsx("div",{className:"text-xs font-medium text-green-800 mb-1",children:"📝 文本生成"}),d.jsxs("div",{className:"space-y-1",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"供应商:"}),d.jsx("span",{className:`px-1 py-0.5 rounded text-xs font-medium ${P.textProvider==="gemini"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:P.textProvider==="gemini"?"Gemini":"OpenAI"})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"模型:"}),d.jsx("span",{className:"font-mono text-gray-900 text-xs",children:P.textModel})]}),P.textBaseUrl&&d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"URL:"}),d.jsx("span",{className:"font-mono text-gray-900 text-xs truncate max-w-32",children:P.textBaseUrl})]})]})]}),d.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded p-2",children:[d.jsx("div",{className:"text-xs font-medium text-purple-800 mb-1",children:"🎨 图片生成"}),d.jsxs("div",{className:"space-y-1",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"供应商:"}),d.jsx("span",{className:`px-1 py-0.5 rounded text-xs font-medium ${P.imageProvider==="gemini"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:P.imageProvider==="gemini"?"Gemini":"OpenAI"})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"模型:"}),d.jsx("span",{className:"font-mono text-gray-900 text-xs",children:P.imageModel})]}),P.imageBaseUrl&&d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"URL:"}),d.jsx("span",{className:"font-mono text-gray-900 text-xs truncate max-w-32",children:P.imageBaseUrl})]})]})]})]}):d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"供应商:"}),d.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${P.provider==="gemini"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:P.provider==="gemini"?"Google Gemini":"OpenAI"})]}),P.baseUrl&&d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"基础URL:"}),d.jsx("span",{className:"font-mono text-gray-900 text-xs",children:P.baseUrl})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"文本模型:"}),d.jsx("span",{className:"font-mono text-gray-900 text-sm",children:P.textModel})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"图片模型:"}),d.jsx("span",{className:"font-mono text-gray-900 text-sm",children:P.imageModel})]})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"状态:"}),d.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${P.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:P.isActive?"已启用":"已禁用"})]}),d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("span",{className:"text-gray-600",children:"更新时间:"}),d.jsx("span",{className:"text-gray-900",children:new Date(P.updatedAt).toLocaleString("zh-CN")})]})]}),d.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200 flex gap-2",children:[d.jsx("button",{onClick:H,disabled:E,className:`px-3 py-1 rounded-md text-sm font-medium transition-colors ${P.isActive?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"} disabled:opacity-50 disabled:cursor-not-allowed`,children:E?"处理中...":P.isActive?"禁用":"启用"}),d.jsx("button",{onClick:ye,disabled:K||!P.isActive,className:"px-3 py-1 rounded-md text-sm font-medium transition-colors bg-blue-100 text-blue-700 hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed",children:K?"测试中...":"🧪 测试连接"})]})]}),z&&d.jsx("div",{className:`rounded-lg p-4 border ${z.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:d.jsxs("div",{className:"flex items-start",children:[d.jsx("div",{className:`flex-shrink-0 ${z.success?"text-green-400":"text-red-400"}`,children:z.success?d.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):d.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:d.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),d.jsxs("div",{className:"ml-3",children:[d.jsx("h3",{className:`text-sm font-medium ${z.success?"text-green-800":"text-red-800"}`,children:z.success?"✅ 连接测试成功":"❌ 连接测试失败"}),d.jsxs("div",{className:`mt-2 text-sm ${z.success?"text-green-700":"text-red-700"}`,children:[d.jsx("p",{children:z.message}),z.details&&d.jsxs("details",{className:"mt-2",children:[d.jsx("summary",{className:"cursor-pointer font-medium",children:"查看详细信息"}),d.jsx("pre",{className:"mt-1 text-xs bg-white bg-opacity-50 p-2 rounded border overflow-auto",children:JSON.stringify(z.details,null,2)})]})]})]})]})}),d.jsxs("form",{onSubmit:O,className:"space-y-4",children:[d.jsx("div",{children:d.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[P?"更新":"设置"," AI API配置"]})}),d.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"配置模式"}),d.jsxs("div",{className:"space-y-2",children:[d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"radio",name:"configMode",checked:!_,onChange:()=>C(!1),className:"mr-2"}),d.jsx("span",{className:"text-sm",children:"统一配置（文本和图片使用相同供应商）"})]}),d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"radio",name:"configMode",checked:_,onChange:()=>C(!0),className:"mr-2"}),d.jsx("span",{className:"text-sm",children:"分离配置（文本和图片使用不同供应商）"})]})]}),d.jsx("p",{className:"text-xs text-blue-600 mt-2",children:"💡 分离配置可以让您使用便宜的API生成文本，用高质量API生成图片"})]}),_?d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[d.jsx("h4",{className:"text-sm font-medium text-green-800 mb-3",children:"📝 文本生成配置"}),d.jsxs("div",{className:"space-y-3",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"文本供应商"}),d.jsxs("select",{value:n,onChange:D=>s(D.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500",children:[d.jsx("option",{value:"gemini",children:"Google Gemini"}),d.jsx("option",{value:"openai",children:"OpenAI 格式"})]})]}),n==="openai"&&d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"文本API基础URL"}),d.jsx("input",{type:"text",value:l,onChange:D=>u(D.target.value),placeholder:"例如: https://api.openai.com/v1",className:"w-full border border-green-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 font-mono text-sm"}),d.jsx("p",{className:"text-xs text-green-600 mt-1",children:"💡 支持第三方兼容OpenAI格式的API（如Claude、通义千问等）"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"文本生成模型"}),d.jsx("input",{type:"text",value:f,onChange:D=>c(D.target.value),placeholder:n==="gemini"?"例如: gemini-2.0-flash-exp":"例如: gpt-4o",className:"w-full border border-green-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 font-mono text-sm"}),d.jsx("p",{className:"text-xs text-green-600 mt-1",children:"用于生成文章内容的模型"})]})]})]}),d.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[d.jsx("h4",{className:"text-sm font-medium text-purple-800 mb-3",children:"🎨 图片生成配置"}),d.jsxs("div",{className:"space-y-3",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片供应商"}),d.jsxs("select",{value:m,onChange:D=>h(D.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500",children:[d.jsx("option",{value:"gemini",children:"Google Gemini"}),d.jsx("option",{value:"openai",children:"OpenAI 格式"})]})]}),m==="openai"&&d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片API基础URL"}),d.jsx("input",{type:"text",value:y,onChange:D=>x(D.target.value),placeholder:"例如: https://api.openai.com/v1",className:"w-full border border-purple-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"}),d.jsx("p",{className:"text-xs text-purple-600 mt-1",children:"💡 支持第三方兼容OpenAI格式的API（如DALL-E兼容服务等）"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片生成模型"}),d.jsx("input",{type:"text",value:T,onChange:D=>I(D.target.value),placeholder:m==="gemini"?"例如: imagen-3.0-generate-001":"例如: dall-e-3",className:"w-full border border-purple-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"}),d.jsx("p",{className:"text-xs text-purple-600 mt-1",children:"用于生成图片的模型"})]})]})]})]}):d.jsxs(d.Fragment,{children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"AI 供应商"}),d.jsxs("select",{value:ee,onChange:D=>et(D.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[d.jsx("option",{value:"gemini",children:"Google Gemini"}),d.jsx("option",{value:"openai",children:"OpenAI 格式"})]}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"选择您要使用的AI服务供应商（文本和图片共用）"})]}),ee==="openai"&&d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API基础URL"}),d.jsx("input",{type:"text",value:Me,onChange:D=>wt(D.target.value),placeholder:"例如: https://api.openai.com/v1",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"💡 支持第三方兼容OpenAI格式的API（如Claude、通义千问、DALL-E兼容服务等）"})]}),d.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"文本生成模型"}),d.jsx("input",{type:"text",value:f,onChange:D=>c(D.target.value),placeholder:ee==="gemini"?"例如: gemini-2.0-flash-exp":"例如: gpt-4o",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"用于生成文章内容的模型"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片生成模型"}),d.jsx("input",{type:"text",value:T,onChange:D=>I(D.target.value),placeholder:ee==="gemini"?"例如: imagen-3.0-generate-001":"例如: dall-e-3",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"用于生成图片的模型"})]})]})]}),_?d.jsxs(d.Fragment,{children:[d.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[d.jsx("label",{className:"block text-sm font-medium text-green-800 mb-2",children:"📝 文本生成 API Key"}),d.jsxs("div",{className:"relative",children:[d.jsx("input",{type:L?"text":"password",value:i,onChange:D=>a(D.target.value),placeholder:n==="gemini"?"输入文本生成的Gemini API Key":"输入文本生成的OpenAI API Key",className:"w-full border border-green-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-green-500 font-mono text-sm"}),d.jsx("button",{type:"button",onClick:()=>k(!L),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:L?d.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):d.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),d.jsx("p",{className:"text-xs text-green-600 mt-1",children:"💰 推荐使用便宜的API供应商生成文本内容"})]}),d.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[d.jsx("label",{className:"block text-sm font-medium text-purple-800 mb-2",children:"🎨 图片生成 API Key"}),d.jsxs("div",{className:"relative",children:[d.jsx("input",{type:L?"text":"password",value:p,onChange:D=>g(D.target.value),placeholder:m==="gemini"?"输入图片生成的Gemini API Key":"输入图片生成的OpenAI API Key",className:"w-full border border-purple-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"}),d.jsx("button",{type:"button",onClick:()=>k(!L),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:L?d.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):d.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),d.jsx("p",{className:"text-xs text-purple-600 mt-1",children:"🎨 推荐使用高质量的API供应商生成精美图片"})]})]}):d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),d.jsxs("div",{className:"relative",children:[d.jsx("input",{type:L?"text":"password",value:be,onChange:D=>St(D.target.value),placeholder:ee==="gemini"?"输入您的Gemini API Key (例如: AIzaSy...)":"输入您的OpenAI API Key (例如: sk-...)",className:"w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),d.jsx("button",{type:"button",onClick:()=>k(!L),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:L?d.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):d.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),d.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:ee==="gemini"?d.jsxs(d.Fragment,{children:["💡 您可以在 ",d.jsx("a",{href:"https://aistudio.google.com/app/apikey",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"Google AI Studio"})," 获取免费的API Key"]}):d.jsxs(d.Fragment,{children:["💡 您可以在 ",d.jsx("a",{href:"https://platform.openai.com/api-keys",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"OpenAI Platform"})," 获取API Key，或使用兼容OpenAI格式的第三方API"]})})]}),d.jsxs("div",{className:"flex items-center",children:[d.jsx("input",{type:"checkbox",id:"isActive",checked:w,onChange:D=>A(D.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),d.jsx("label",{htmlFor:"isActive",className:"ml-2 block text-sm text-gray-700",children:"立即启用此配置"})]}),d.jsx("div",{className:"flex justify-end",children:d.jsx("button",{type:"submit",disabled:E||!f.trim()||!T.trim()||(_?!i.trim()||!p.trim()||n==="openai"&&!l.trim()||m==="openai"&&!y.trim():!be.trim()||ee==="openai"&&!Me.trim()),className:`px-4 py-2 rounded-md font-medium transition-colors ${E||!f.trim()||!T.trim()||(_?!i.trim()||!p.trim()||n==="openai"&&!l.trim()||m==="openai"&&!y.trim():!be.trim()||ee==="openai"&&!Me.trim())?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:E?"设置中...":P?"更新配置":"设置配置"})})]}),d.jsxs("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:[d.jsx("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"📋 使用说明"}),d.jsxs("ul",{className:"text-sm text-blue-800 space-y-1",children:[d.jsx("li",{children:"• 支持Google Gemini和OpenAI格式的API"}),d.jsxs("li",{children:["• ",d.jsx("strong",{children:"🆕 支持分离配置：文本和图片可以使用不同的供应商和API Key"})]}),d.jsx("li",{children:"• 分离配置可以让您用便宜的API生成文本，用高质量API生成图片"}),d.jsx("li",{children:"• 设置自定义配置后，系统将优先使用您的配置进行AI生成"}),d.jsx("li",{children:"• 文本模型用于生成文章内容，图片模型用于生成文章配图"}),d.jsx("li",{children:"• OpenAI格式支持第三方兼容API（如Claude、通义千问等）"}),d.jsx("li",{children:"• 如果配置失效，系统会自动回退到服务器默认配置"}),d.jsx("li",{children:"• 您可以随时禁用自定义配置，系统将使用服务器默认设置"}),d.jsx("li",{children:"• 所有配置会安全存储，只有管理员可以查看和修改"})]})]})]})},Oy=({onUpdateSuccess:t,onUpdateError:e})=>{const[n,s]=R.useState("🤡"),[i,a]=R.useState(""),[l,u]=R.useState(!1),[f,c]=R.useState(!1),[m,h]=R.useState(!1),[p,g]=R.useState("emoji"),y=["🤡","🎭","🎪","🎨","🔬","📚","📖","📝","🧪","🔍","💡","🎯","🚀","⚡","🌟","💎","🎲","🎳","🎮","🎸","🎺","🎻","🎤","🎧"],x=async()=>{u(!0);try{const _=await(await fetch("/api/admin/journal-icon")).json();if(_.success){const C=_.data.icon;s(C),a(C),g(C.startsWith("data:image/")?"image":"emoji")}}catch(A){console.error("加载期刊图标失败:",A),e==null||e("加载期刊图标失败")}finally{u(!1)}},T=async()=>{if(!i.trim()){e==null||e("请输入或选择一个图标");return}c(!0);try{await oe.setJournalIcon(i.trim()),s(i.trim()),t==null||t("期刊图标更新成功！")}catch(A){console.error("保存期刊图标失败:",A),e==null||e("保存期刊图标失败")}finally{c(!1)}},I=()=>{a(n),g(n.startsWith("data:image/")?"image":"emoji")},w=async A=>{var C;const _=(C=A.target.files)==null?void 0:C[0];if(_){if(!_.type.startsWith("image/")){e==null||e("请选择图片文件");return}if(_.size>2*1024*1024){e==null||e("图片文件大小不能超过2MB");return}h(!0);try{const E=new FileReader;E.onload=N=>{var b;const P=(b=N.target)==null?void 0:b.result;a(P),g("image"),h(!1)},E.onerror=()=>{e==null||e("图片读取失败"),h(!1)},E.readAsDataURL(_)}catch(E){console.error("图片上传失败:",E),e==null||e("图片上传失败"),h(!1)}}};return R.useEffect(()=>{x()},[]),d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"期刊图标管理"}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("span",{className:"text-sm text-gray-500",children:"当前图标:"}),d.jsx("span",{className:"text-2xl",children:n})]})]}),l?d.jsx("div",{className:"flex items-center justify-center py-8",children:d.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):d.jsxs("div",{className:"space-y-6",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"选择图标类型"}),d.jsxs("div",{className:"flex space-x-4",children:[d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"radio",value:"emoji",checked:p==="emoji",onChange:A=>g(A.target.value),className:"mr-2"}),d.jsx("span",{className:"text-sm text-gray-700",children:"Emoji 表情"})]}),d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"radio",value:"image",checked:p==="image",onChange:A=>g(A.target.value),className:"mr-2"}),d.jsx("span",{className:"text-sm text-gray-700",children:"上传图片"})]})]})]}),p==="emoji"?d.jsxs(d.Fragment,{children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"自定义 Emoji"}),d.jsxs("div",{className:"flex space-x-2",children:[d.jsx("input",{type:"text",value:i.startsWith("data:image/")?"":i,onChange:A=>a(A.target.value),placeholder:"输入 emoji...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),d.jsx("div",{className:"flex items-center justify-center w-12 h-10 border border-gray-300 rounded-md bg-gray-50",children:i&&!i.startsWith("data:image/")&&d.jsx("span",{className:"text-xl",children:i})})]})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"或选择预设 Emoji"}),d.jsx("div",{className:"grid grid-cols-8 gap-2",children:y.map((A,_)=>d.jsx("button",{onClick:()=>a(A),className:`w-12 h-12 flex items-center justify-center text-xl border-2 rounded-lg transition-colors ${i===A?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,children:A},_))})]})]}):d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"上传图标图片"}),d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs("div",{className:"flex-1",children:[d.jsx("input",{type:"file",accept:"image/*",onChange:w,disabled:m,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50"}),d.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"支持 JPG、PNG、GIF 格式，建议尺寸 64x64px，文件大小不超过 2MB"})]}),d.jsx("div",{className:"flex items-center justify-center w-16 h-16 border border-gray-300 rounded-md bg-gray-50",children:m?d.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}):i.startsWith("data:image/")?d.jsx("img",{src:i,alt:"图标预览",className:"w-12 h-12 object-contain rounded"}):d.jsx("span",{className:"text-xs text-gray-400",children:"预览"})})]})]}),d.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[d.jsx("button",{onClick:I,disabled:f,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:"重置"}),d.jsxs("button",{onClick:T,disabled:f||i===n,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[f&&d.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),d.jsx("span",{children:f?"保存中...":"保存图标"})]})]}),d.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:[d.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"使用说明"}),d.jsxs("ul",{className:"text-sm text-blue-800 space-y-1",children:[d.jsxs("li",{children:["• ",d.jsx("strong",{children:"Emoji 模式"}),"：可以使用任何 Emoji 表情作为期刊图标，如 🤡 🎭 🔬 等"]}),d.jsxs("li",{children:["• ",d.jsx("strong",{children:"图片模式"}),"：上传自定义图片作为期刊图标，建议使用正方形图片，尺寸 64x64px"]}),d.jsx("li",{children:"• 图标会显示在网站标题、PDF导出、浏览器标签页等位置"}),d.jsx("li",{children:"• 建议选择与期刊主题相关的图标，保持专业性"}),d.jsx("li",{children:"• 图片文件会转换为 base64 格式存储，无需外部链接"})]})]})]})]})},Hy=({onNavigateHome:t})=>{const[e,n]=R.useState([]),[s,i]=R.useState(!0),[a,l]=R.useState(null),[u,f]=R.useState(null),[c,m]=R.useState("articles"),[h,p]=R.useState(new Set),[g,y]=R.useState(!1),[x,T]=R.useState({}),[I,w]=R.useState([]),[A,_]=R.useState(!1);R.useEffect(()=>{C()},[]),R.useEffect(()=>{c==="users"&&fe()},[c]);const C=async()=>{try{i(!0);const S=await oe.getArticles({limit:50});n(S.data)}catch(S){l("获取文章列表失败"),console.error(S)}finally{i(!1)}},E=S=>{f({...S})},N=async()=>{if(u)try{const S=await wn.updateArticle(u.id,{title:u.title,author:u.author,category:u.category,excerpt:u.excerpt,content:u.content,published:u.published,featured:u.featured});n(O=>O.map(H=>H.id===u.id?S:H)),f(null),alert("文章保存成功！")}catch(S){alert("保存文章失败"),console.error(S)}},P=async S=>{if(confirm("确定要删除这篇文章吗？"))try{await wn.deleteArticle(S),n(O=>O.filter(H=>H.id!==S)),alert("文章删除成功！")}catch(O){alert("删除文章失败"),console.error(O)}},b=async()=>{if(h.size===0){alert("请先选择要删除的文章");return}if(confirm(`确定要删除选中的 ${h.size} 篇文章吗？此操作不可恢复！`)){y(!0);try{const S=Array.from(h);await oe.batchDeleteArticles(S),n(O=>O.filter(H=>!h.has(H.id))),p(new Set),alert(`成功删除 ${S.length} 篇文章！`)}catch(S){alert("批量删除失败"),console.error(S)}finally{y(!1)}}},L=S=>{p(O=>{const H=new Set(O);return H.has(S)?H.delete(S):H.add(S),H})},k=()=>{h.size===e.length?p(new Set):p(new Set(e.map(S=>S.id)))},K=async(S,O)=>{if(O<0){alert("观看次数不能为负数");return}try{await oe.updateArticleViews(S,O),n(H=>H.map(ye=>ye.id===S?{...ye,views:O}:ye)),T(H=>{const ye={...H};return delete ye[S],ye}),alert("观看次数更新成功！")}catch(H){alert("更新观看次数失败"),console.error(H)}},fe=async()=>{console.log("开始加载用户列表..."),console.log("adminApi:",oe),_(!0);try{const S=await oe.getAllUsers();console.log("用户列表响应:",S),w(S.users)}catch(S){console.error("加载用户列表失败:",S),alert("加载用户列表失败: "+(S instanceof Error?S.message:"未知错误"))}finally{_(!1)}},z=async(S,O,H)=>{try{switch(O){case"status":await oe.updateUserStatus(S,H);break;case"role":await oe.updateUserRole(S,H);break;case"delete":if(confirm("确定要删除这个用户吗？此操作不可恢复。"))await oe.deleteUser(S);else return;break}await fe(),alert("操作成功")}catch(ye){console.error("操作失败:",ye),alert("操作失败，请重试")}},X=S=>{switch(S){case"ADMIN":return"bg-red-100 text-red-800";case"EDITOR":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},ee=S=>{switch(S){case"ACTIVE":return"bg-green-100 text-green-800";case"DISABLED":return"bg-red-100 text-red-800";case"SUSPENDED":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},et=S=>new Date(S).toLocaleString("zh-CN"),be=()=>u?d.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:d.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[d.jsx("h3",{className:"text-xl font-bold mb-4",children:"编辑文章"}),d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"标题"}),d.jsx("input",{type:"text",value:u.title,onChange:S=>f({...u,title:S.target.value}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"作者"}),d.jsx("input",{type:"text",value:u.author,onChange:S=>f({...u,author:S.target.value}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"分类"}),d.jsx("input",{type:"text",value:u.category,onChange:S=>f({...u,category:S.target.value}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"摘要"}),d.jsx("textarea",{value:u.excerpt,onChange:S=>f({...u,excerpt:S.target.value}),rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("div",{children:[d.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"内容"}),d.jsx("textarea",{value:u.content||"",onChange:S=>f({...u,content:S.target.value}),rows:8,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"checkbox",checked:u.published,onChange:S=>f({...u,published:S.target.checked}),className:"mr-2"}),"已发布"]}),d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"checkbox",checked:u.featured,onChange:S=>f({...u,featured:S.target.checked}),className:"mr-2"}),"推荐文章"]})]})]}),d.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[d.jsx("button",{onClick:()=>f(null),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"取消"}),d.jsx("button",{onClick:N,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"保存"})]})]})}):null,St=()=>d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{className:"flex justify-between items-center",children:[d.jsx("h2",{className:"text-2xl font-bold",children:"文章管理"}),d.jsxs("div",{className:"flex space-x-2",children:[h.size>0&&d.jsx("button",{onClick:b,disabled:g,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50",children:g?"删除中...":`删除选中 (${h.size})`}),d.jsx("button",{onClick:C,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"刷新列表"})]})]}),e.length>0&&d.jsxs("div",{className:"flex items-center space-x-4 bg-gray-50 p-3 rounded-md",children:[d.jsxs("label",{className:"flex items-center",children:[d.jsx("input",{type:"checkbox",checked:h.size===e.length&&e.length>0,onChange:k,className:"mr-2"}),"全选 (",e.length," 篇文章)"]}),h.size>0&&d.jsxs("span",{className:"text-sm text-gray-600",children:["已选择 ",h.size," 篇文章"]})]}),s?d.jsx("div",{className:"text-center py-8",children:"加载中..."}):a?d.jsx("div",{className:"text-center py-8 text-red-600",children:a}):d.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:d.jsx("ul",{className:"divide-y divide-gray-200",children:e.map(S=>d.jsx("li",{className:"px-6 py-4",children:d.jsxs("div",{className:"flex items-start gap-4",children:[d.jsx("div",{className:"flex-shrink-0 pt-1",children:d.jsx("input",{type:"checkbox",checked:h.has(S.id),onChange:()=>L(S.id),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})}),d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsxs("div",{className:"flex items-start justify-between gap-2",children:[d.jsx("h3",{className:"text-lg font-medium text-gray-900 break-words flex-1",children:S.title}),d.jsxs("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[S.published&&d.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"已发布"}),S.featured&&d.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"推荐"})]})]}),d.jsxs("div",{className:"mt-1 text-sm text-gray-600 flex items-center space-x-4",children:[d.jsxs("span",{children:["作者: ",S.author]}),d.jsxs("span",{children:["分类: ",S.category]}),d.jsxs("div",{className:"flex items-center space-x-1",children:[d.jsx("span",{children:"浏览:"}),x[S.id]!==void 0?d.jsxs("div",{className:"flex items-center space-x-1",children:[d.jsx("input",{type:"number",value:x[S.id],onChange:O=>T(H=>({...H,[S.id]:parseInt(O.target.value)||0})),className:"w-20 px-1 py-0.5 text-xs border border-gray-300 rounded",min:"0"}),d.jsx("button",{onClick:()=>K(S.id,x[S.id]),className:"px-2 py-0.5 bg-green-600 text-white text-xs rounded hover:bg-green-700",children:"保存"}),d.jsx("button",{onClick:()=>T(O=>{const H={...O};return delete H[S.id],H}),className:"px-2 py-0.5 bg-gray-600 text-white text-xs rounded hover:bg-gray-700",children:"取消"})]}):d.jsx("button",{onClick:()=>T(O=>({...O,[S.id]:S.views||0})),className:"text-blue-600 hover:text-blue-800 underline",children:S.views||0})]}),d.jsxs("span",{children:["点赞: ",S.likes]})]}),d.jsx("p",{className:"mt-1 text-sm text-gray-500 line-clamp-2",children:S.excerpt})]}),d.jsxs("div",{className:"flex flex-col space-y-2 flex-shrink-0",children:[d.jsx("button",{onClick:()=>E(S),className:"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 whitespace-nowrap",children:"编辑"}),d.jsx("button",{onClick:()=>P(S.id),className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 whitespace-nowrap",children:"删除"})]})]})},S.id))})})]}),Me=()=>d.jsxs("div",{className:"space-y-4",children:[d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"用户管理"}),d.jsxs("div",{className:"text-sm text-gray-500",children:["共 ",I.length," 个用户"]})]}),A?d.jsx("div",{className:"flex items-center justify-center py-12",children:d.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):d.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:d.jsx("ul",{className:"divide-y divide-gray-200",children:I.map(S=>d.jsx("li",{className:"px-6 py-4",children:d.jsxs("div",{className:"flex items-center justify-between",children:[d.jsxs("div",{className:"flex items-center space-x-4",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:d.jsx("span",{className:"text-sm font-medium text-gray-700",children:S.username.charAt(0).toUpperCase()})})}),d.jsxs("div",{className:"flex-1 min-w-0",children:[d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:S.username}),d.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${X(S.role)}`,children:S.role}),d.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${ee(S.status)}`,children:S.status})]}),d.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[d.jsx("p",{className:"text-sm text-gray-500",children:S.email}),d.jsxs("p",{className:"text-sm text-gray-500",children:["文章: ",S._count.articles]}),d.jsxs("p",{className:"text-sm text-gray-500",children:["注册: ",et(S.createdAt)]}),S.lastLogin&&d.jsxs("p",{className:"text-sm text-gray-500",children:["最后登录: ",et(S.lastLogin)]})]})]})]}),d.jsxs("div",{className:"flex items-center space-x-2",children:[d.jsxs("select",{value:S.status,onChange:O=>z(S.id,"status",O.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[d.jsx("option",{value:"ACTIVE",children:"激活"}),d.jsx("option",{value:"DISABLED",children:"禁用"}),d.jsx("option",{value:"SUSPENDED",children:"暂停"})]}),d.jsxs("select",{value:S.role,onChange:O=>z(S.id,"role",O.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[d.jsx("option",{value:"USER",children:"用户"}),d.jsx("option",{value:"EDITOR",children:"编辑"}),d.jsx("option",{value:"ADMIN",children:"管理员"})]}),d.jsx("button",{onClick:()=>z(S.id,"delete"),className:"text-red-600 hover:text-red-800 text-sm px-2 py-1 border border-red-300 rounded hover:bg-red-50",children:"删除"})]})]})},S.id))})})]}),wt=()=>d.jsxs("div",{className:"space-y-6",children:[d.jsx("div",{className:"bg-white shadow rounded-lg",children:d.jsxs("div",{className:"px-4 py-5 sm:p-6",children:[d.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"🌐 网站管理"}),d.jsx("p",{className:"text-sm text-gray-600 mb-6",children:"管理网站的外观和设置，包括主页封面图片等。"}),d.jsx(qy,{onUploadSuccess:S=>{console.log("封面上传成功:",S),alert("封面上传成功！刷新页面查看效果。")},onUploadError:S=>{console.error("封面上传失败:",S),alert("封面上传失败: "+S)}})]})}),d.jsx("div",{className:"bg-white shadow rounded-lg",children:d.jsx("div",{className:"px-4 py-5 sm:p-6",children:d.jsx(Gy,{onUploadSuccess:S=>{console.log("广告封面上传成功:",S),alert("广告封面上传成功！刷新页面查看效果。")},onUploadError:S=>{console.error("广告封面上传失败:",S),alert("广告封面上传失败: "+S)}})})}),d.jsx("div",{className:"bg-white shadow rounded-lg",children:d.jsx("div",{className:"px-4 py-5 sm:p-6",children:d.jsx(Oy,{onUpdateSuccess:S=>{console.log("期刊图标更新成功:",S),alert(S)},onUpdateError:S=>{console.error("期刊图标更新失败:",S),alert("期刊图标更新失败: "+S)}})})}),d.jsx("div",{className:"bg-white shadow rounded-lg",children:d.jsx("div",{className:"px-4 py-5 sm:p-6",children:d.jsx(Jy,{onUpdateSuccess:S=>{console.log("AI配置更新成功:",S),alert(S)},onUpdateError:S=>{console.error("AI配置更新失败:",S),alert("AI配置更新失败: "+S)}})})})]});return d.jsxs("div",{className:"min-h-screen bg-gray-50",children:[d.jsx("nav",{className:"bg-white shadow",children:d.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:d.jsxs("div",{className:"flex justify-between h-16",children:[d.jsx("div",{className:"flex items-center",children:d.jsx("button",{onClick:t,className:"text-xl font-bold text-gray-900 hover:text-gray-700",children:"← 返回首页"})}),d.jsx("div",{className:"flex items-center space-x-4",children:d.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"Jocker 管理后台"})})]})})}),d.jsxs("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:[d.jsx("div",{className:"mb-6",children:d.jsxs("nav",{className:"flex space-x-8",children:[d.jsx("button",{onClick:()=>m("articles"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="articles"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"文章管理"}),d.jsx("button",{onClick:()=>m("users"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="users"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"用户管理"}),d.jsx("button",{onClick:()=>m("system"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="system"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"📊 系统监控"}),d.jsx("button",{onClick:()=>m("logs"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="logs"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"📋 日志管理"}),d.jsx("button",{onClick:()=>m("website"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="website"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"网站管理"})]})}),c==="articles"?St():c==="users"?Me():c==="system"?d.jsx(Vy,{}):c==="logs"?d.jsx(By,{}):wt()]}),be()]})},rx=Object.freeze(Object.defineProperty({__proto__:null,AdminPage:Hy},Symbol.toStringTag,{value:"Module"})),Wy=({onNavigateHome:t,onLoginSuccess:e})=>{const[n,s]=R.useState(!1),[i,a]=R.useState(""),[l,u]=R.useState(""),[f,c]=R.useState(""),[m,h]=R.useState(""),[p,g]=R.useState(""),[y,x]=R.useState(""),[T,I]=R.useState(!1),w=async _=>{_.preventDefault(),I(!0),g(""),x("");try{if(n){if(l!==m){g("密码确认不匹配"),I(!1);return}if(l.length<6){g("密码长度至少为 6 位"),I(!1);return}const{token:C,user:E}=await An.register({email:i,password:l,username:f});x("注册成功！您现在是普通用户，可以浏览文章。"),localStorage.setItem("jocker_admin_token",C),localStorage.setItem("jocker_admin_logged_in","false"),localStorage.setItem("jocker_admin_user",JSON.stringify(E)),localStorage.setItem("jocker_user_role",E.role||"USER"),e(),setTimeout(()=>{t()},3e3)}else{const{token:C,user:E}=await An.login({email:i,password:l}),N=E.role==="ADMIN";localStorage.setItem("jocker_admin_token",C),localStorage.setItem("jocker_admin_logged_in",N?"true":"false"),localStorage.setItem("jocker_admin_user",JSON.stringify(E)),localStorage.setItem("jocker_user_role",E.role||"USER"),e(),N||(x("登录成功！您现在可以浏览文章。"),setTimeout(()=>{t()},2e3))}}catch(C){console.error(n?"注册失败:":"登录失败:",C),g(C instanceof Error?C.message:n?"注册失败，请重试":"登录失败，请重试")}finally{I(!1)}},A=()=>{s(!n),g(""),x(""),a(""),u(""),c(""),h("")};return d.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[d.jsx("div",{className:"absolute top-4 left-4",children:d.jsx("button",{onClick:t,className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:"← 返回首页"})}),d.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[d.jsxs("div",{className:"text-center",children:[d.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Joker"}),d.jsx("p",{className:"text-sm text-gray-600",children:"The Journal of Outrageous Claims and Kooky Experiments Research"})]}),d.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:n?"用户注册":"用户登录"}),d.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:n?"创建新账户以浏览文章和使用功能":"登录您的账户（管理员可访问后台管理）"})]}),d.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:d.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[d.jsxs("form",{className:"space-y-6",onSubmit:w,children:[n&&d.jsxs("div",{children:[d.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:"用户名"}),d.jsx("div",{className:"mt-1",children:d.jsx("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,value:f,onChange:_=>c(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:"请输入用户名"})})]}),d.jsxs("div",{children:[d.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"邮箱"}),d.jsx("div",{className:"mt-1",children:d.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:i,onChange:_=>a(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:"请输入邮箱地址"})})]}),d.jsxs("div",{children:[d.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"密码"}),d.jsx("div",{className:"mt-1",children:d.jsx("input",{id:"password",name:"password",type:"password",autoComplete:n?"new-password":"current-password",required:!0,value:l,onChange:_=>u(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:n?"请输入密码（至少6位）":"请输入密码"})})]}),n&&d.jsxs("div",{children:[d.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"确认密码"}),d.jsx("div",{className:"mt-1",children:d.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:m,onChange:_=>h(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:"请再次输入密码"})})]}),p&&d.jsx("div",{className:"rounded-md bg-red-50 p-4",children:d.jsx("div",{className:"flex",children:d.jsxs("div",{className:"ml-3",children:[d.jsx("h3",{className:"text-sm font-medium text-red-800",children:n?"注册失败":"登录失败"}),d.jsx("div",{className:"mt-2 text-sm text-red-700",children:d.jsx("p",{children:p})})]})})}),y&&d.jsx("div",{className:"rounded-md bg-green-50 p-4",children:d.jsx("div",{className:"flex",children:d.jsxs("div",{className:"ml-3",children:[d.jsx("h3",{className:"text-sm font-medium text-green-800",children:"成功"}),d.jsx("div",{className:"mt-2 text-sm text-green-700",children:d.jsx("p",{children:y})})]})})}),d.jsx("div",{children:d.jsx("button",{type:"submit",disabled:T,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed",children:T?n?"注册中...":"登录中...":n?"注册":"登录"})})]}),d.jsxs("div",{className:"mt-6",children:[d.jsxs("div",{className:"relative",children:[d.jsx("div",{className:"absolute inset-0 flex items-center",children:d.jsx("div",{className:"w-full border-t border-gray-300"})}),d.jsx("div",{className:"relative flex justify-center text-sm",children:d.jsx("span",{className:"px-2 bg-white text-gray-500",children:n?"已有账户？":"没有账户？"})})]}),d.jsx("div",{className:"mt-6 text-center",children:d.jsx("button",{type:"button",onClick:A,className:"text-purple-600 hover:text-purple-500 font-medium",children:n?"点击登录":"点击注册"})}),d.jsx("div",{className:"mt-4",children:d.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:d.jsxs("div",{className:"flex",children:[d.jsx("div",{className:"flex-shrink-0",children:d.jsx("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:d.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),d.jsxs("div",{className:"ml-3",children:[d.jsx("h3",{className:"text-sm font-medium text-blue-800",children:n?"注册说明":"登录说明"}),d.jsx("div",{className:"mt-2 text-sm text-blue-700",children:n?d.jsxs(d.Fragment,{children:[d.jsx("p",{children:"注册后您将成为普通用户，可以浏览所有文章"}),d.jsx("p",{children:"管理员账户可以生成和管理文章"})]}):d.jsxs(d.Fragment,{children:[d.jsx("p",{children:"管理员账户可以访问文章生成功能"}),d.jsx("p",{children:"普通用户可以浏览所有文章内容"})]})})]})]})})})]})]})})]})},ax=Object.freeze(Object.defineProperty({__proto__:null,LoginPage:Wy},Symbol.toStringTag,{value:"Module"}));export{rx as A,ax as L,Yy as R,wa as a,oe as b,go as c,wn as d,Ta as e,_a as f,d as j,qe as l,R as r};
