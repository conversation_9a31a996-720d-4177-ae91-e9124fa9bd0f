import React, { useState, useEffect } from 'react';
import { Article } from '../types';
import { BackArrowIcon } from './icons';
import { GoogleGenAI, GenerateContentResponse } from '@google/genai';
import { adminApi } from '../src/services/api';

// Declare the globals from the scripts loaded in index.html
declare var marked: { parse: (markdown: string) => string };
declare var DOMPurify: { sanitize: (html: string) => string };

interface ArticlePageProps {
  article: Article;
  updateArticleContent: (id: number, content: string) => void;
  onNavigateHome: () => void;
}

const ArticleSkeleton: React.FC = () => (
    <div className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-8"></div>
        <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-full mt-6"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
        </div>
    </div>
);


export const ArticlePage: React.FC<ArticlePageProps> = ({ article, updateArticleContent, onNavigateHome }) => {
  const [content, setContent] = useState<string | null>(article.content || null);
  const [isLoading, setIsLoading] = useState<boolean>(!article.content);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const generateFullArticle = async () => {
      if (article.content) {
          setContent(article.content);
          setIsLoading(false);
          return;
      }

      setIsLoading(true);
      setError(null);
      
      try {
        // 获取用户配置的模型
        let aiConfig;
        try {
          aiConfig = await adminApi.getAIConfig();
        } catch (error) {
          console.log('获取用户AI配置失败，使用默认配置');
          aiConfig = { config: null };
        }

        const textModel = aiConfig.config?.textModel || 'gemini-2.5-flash';
        const useApiKey = aiConfig.config?.apiKey || "AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY";

        console.log(`🔧 前端使用配置 - 文本模型: ${textModel}`);

        // 使用前端的 Google AI 生成文章内容
        const ai = new GoogleGenAI({ apiKey: useApiKey });

        const prompt = `Write a complete satirical scientific research paper for the journal "Jocker". Use an extremely serious, academic tone while discussing absurd topics. The paper should be approximately 500 words and include these sections with headers: "Introduction", "Methodology", "Results", and "Conclusion". Use professional jargon and make it witty.

IMPORTANT: Return ONLY the article content in Markdown format. Do not include any explanatory text, comments, or meta-commentary about the article.

Title: "${article.title}"
Author: "${article.author}"
Category: "${article.category}"
Abstract: "${article.excerpt}"

Format with Markdown headers (## Section Name), use **bold** and *italics* for emphasis.`;

        console.log(`📝 前端使用模型 ${textModel} 生成文章内容`);
        const response: GenerateContentResponse = await ai.models.generateContent({
          model: textModel,
          contents: prompt,
        });

        const fullText = response.text || '';
        setContent(fullText);
        updateArticleContent(article.id, fullText);

      } catch (e) {
        console.error("Failed to generate article content:", e);
        setError("Our AI writer is currently experiencing stage fright. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    generateFullArticle();
  }, [article, updateArticleContent]);

  return (
    <article className="max-w-4xl mx-auto">
        <button onClick={onNavigateHome} className="inline-flex items-center text-purple-700 hover:text-purple-900 mb-6 group">
            <BackArrowIcon className="w-5 h-5 mr-2 transition-transform group-hover:-translate-x-1" />
            Back to all articles
        </button>
        
        <p className="font-bold uppercase tracking-wider text-purple-700">{article.category}</p>
        <h1 className="mt-2 text-4xl md:text-5xl font-extrabold text-gray-900 leading-tight">
            {article.title}
        </h1>
        <p className="mt-4 text-lg text-gray-500 italic">By {article.author}</p>

        <img src={article.imageUrl} alt={article.title} className="w-full h-auto max-h-[500px] object-cover rounded-lg shadow-lg my-8" />
        
        <div className="prose prose-lg max-w-none prose-h2:font-serif prose-h2:text-3xl prose-h2:border-b prose-h2:pb-2 prose-h2:mb-4">
            {isLoading && <ArticleSkeleton />}
            {error && <p className="text-red-600">{error}</p>}
            {content && (
                <div
                    dangerouslySetInnerHTML={{
                        __html: DOMPurify.sanitize(marked.parse(content)),
                    }}
                />
            )}
        </div>
    </article>
  );
};