import React, { createContext, useContext, useState, useEffect } from 'react';
import { api } from '../services/api';

interface WebsiteCover {
  id: number;
  name: string;
  title: string;
  description: string;
  avatarUrl?: string;
}

interface WebsiteCoverContextType {
  websiteCover: WebsiteCover | null;
  loading: boolean;
  refreshCover: () => Promise<void>;
}

const WebsiteCoverContext = createContext<WebsiteCoverContextType | undefined>(undefined);

export const useWebsiteCover = () => {
  const context = useContext(WebsiteCoverContext);
  if (context === undefined) {
    throw new Error('useWebsiteCover must be used within a WebsiteCoverProvider');
  }
  return context;
};

interface WebsiteCoverProviderProps {
  children: React.ReactNode;
}

export const WebsiteCoverProvider: React.FC<WebsiteCoverProviderProps> = ({ children }) => {
  const [websiteCover, setWebsiteCover] = useState<WebsiteCover | null>(null);
  const [loading, setLoading] = useState(true);

  const loadWebsiteCover = async () => {
    try {
      setLoading(true);
      const coverData = await api.getWebsiteCover();
      setWebsiteCover(coverData.websiteConfig);
    } catch (error) {
      console.log('获取网站封面失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const refreshCover = async () => {
    await loadWebsiteCover();
  };

  useEffect(() => {
    loadWebsiteCover();
  }, []);

  const value = {
    websiteCover,
    loading,
    refreshCover,
  };

  return (
    <WebsiteCoverContext.Provider value={value}>
      {children}
    </WebsiteCoverContext.Provider>
  );
};
