{"version": "v2.7.0", "type": "minor", "title": "🎭 数学公式渲染优化 & 账户管理 & 恶搞页面大放送", "description": "重构数学公式渲染系统，新增用户账户管理功能，并推出三个一本正经胡说八道的恶搞页面，让学术界更加有趣！", "changes": ["🔢 数学公式渲染革命：完全重构LaTeX公式处理流程，解决复杂公式显示问题", "📐 专业公式样式：块级公式现在拥有学术期刊级别的专业外观，包括自动编号、居中显示和精美边框", "🛡️ 公式保护机制：创新的数学内容保护系统，防止Markdown处理和文献引用功能破坏LaTeX语法", "🔍 智能冲突检测：文献引用处理器现在能识别并避开数学公式中的方括号，不再误伤科学表达式", "👤 用户信息卡片：将静态的'欢迎管理员'文本升级为可交互的用户信息组件，显示头像、权限和账户详情", "🔒 安全密码管理：新增密码修改功能，支持当前密码验证、新密码强度检查和实时反馈", "🎨 界面交互优化：用户卡片采用现代化设计，支持点击外部关闭、流畅动画和响应式布局", "🤡 Privacy Policy恶搞页：一本正经地声明'我们不知道你是谁，我们也不在乎，但我们的饼干很在乎'", "📜 Terms of Confusion：混乱条款页面，要求用户'不能起诉我们，除非你用押韵的方式'", "♿️ Accessibility Statement：可访问性声明页面，支持深色模式、小丑模式和存在主义绝望模式", "🎪 恶搞页面互动：每个恶搞页面都包含真实的交互元素，如模式切换器、动态样式和幽默的用户指南", "🔧 API架构完善：后端新增密码修改API，支持bcrypt加密、权限验证和详细错误处理", "📊 调试功能增强：数学公式渲染过程现在提供详细的控制台日志，便于问题排查和性能监控", "🎯 用户体验提升：所有新功能都采用渐进式设计，确保向后兼容性和平滑的用户过渡", "🚀 性能优化：数学公式处理采用异步渲染，用户界面响应更加流畅", "🎭 学术幽默升级：恶搞页面内容经过精心设计，在保持专业外观的同时注入大量学术界内部笑话"]}