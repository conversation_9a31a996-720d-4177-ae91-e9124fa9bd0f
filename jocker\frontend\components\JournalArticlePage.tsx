import React, { useState, useEffect, useRef } from 'react';
import { Article } from '../types';
import { BackArrowIcon } from './icons';
import { adminApi, articleApi, audioApi } from '../src/services/api';
import { aiService } from '../src/services/aiService';
import {
  AuthorCard,
  AbstractSection,
  ArticleMetrics,
  JournalHeader,
  PeerReviewBadge,
  JournalFooter
} from './JournalComponents';
import { FigureList, Figure } from './FigureComponents';
import { FigureGallery } from './FigureGallery';
import { MathRenderer } from './MathRenderer';
import { AcademicMarkdownRenderer } from './EnhancedMarkdownRenderer';
import { PDFDownloader } from './PDFDownloader';
import { ReferencesSection, removeReferencesFromContent, hasReferences } from './ReferencesSection';
import { AudioPlayer } from './AudioPlayer';
import { protectMath, restoreMath, clearMathProtection, getMathStats } from '../src/utils/mathProtection';
import { AudioUploader } from './AudioUploader';
import { aiService } from '../src/services/aiService';
import { ArticleComments } from './ArticleComments';
import { isUserLoggedIn, getUserRole } from '../src/utils/authUtils';

// Declare the globals from the scripts loaded in index.html
declare var marked: { parse: (markdown: string) => string };
declare var DOMPurify: { sanitize: (html: string) => string };

interface JournalArticlePageProps {
  article: Article;
  updateArticleContent: (id: number, content: string) => void;
  onNavigateHome: () => void;
}

// 数学公式样式现在在mathProtection中处理

// 内容渲染组件，处理KaTeX渲染
const ContentWithMath: React.FC<{ content: string }> = ({ content }) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current && window.renderMathInElement) {
      // 设置HTML内容
      containerRef.current.innerHTML = content;

      // 渲染数学公式
      window.renderMathInElement(containerRef.current, {
        delimiters: [
          { left: '$$', right: '$$', display: true },
          { left: '$', right: '$', display: false },
          { left: '\\[', right: '\\]', display: true },
          { left: '\\(', right: '\\)', display: false }
        ],
        throwOnError: false,
        errorColor: '#cc0000',
        strict: false,
        trust: false,
        macros: {
          '\\RR': '\\mathbb{R}',
          '\\NN': '\\mathbb{N}',
          '\\ZZ': '\\mathbb{Z}',
          '\\QQ': '\\mathbb{Q}',
          '\\CC': '\\mathbb{C}',
        }
      });

      console.log('✅ KaTeX渲染完成');
    }
  }, [content]);

  return (
    <div
      ref={containerRef}
      className="prose prose-lg max-w-none article-content prose-headings:font-serif prose-h2:text-2xl prose-h2:border-b prose-h2:border-gray-200 prose-h2:pb-2 prose-h2:mb-6 prose-h2:mt-8 prose-p:text-gray-700 prose-p:leading-relaxed"
    />
  );
};

// Citation formats
const generateCitations = (article: Article) => {
  const currentDate = new Date().toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
  
  const year = new Date().getFullYear();
  
  return {
    apa: `${article.author} (${year}). ${article.title}. *Joker: Journal of Satirical Science*, Retrieved ${currentDate}, from http://joker-journal.com/articles/${article.id}`,
    mla: `${article.author}. "${article.title}." *Joker: Journal of Satirical Science*, ${currentDate}, http://joker-journal.com/articles/${article.id}.`,
    chicago: `${article.author}. "${article.title}." Joker: Journal of Satirical Science. Accessed ${currentDate}. http://joker-journal.com/articles/${article.id}.`,
    ieee: `${article.author}, "${article.title}," *Joker: Journal of Satirical Science*, ${currentDate}. [Online]. Available: http://joker-journal.com/articles/${article.id}`
  };
};

const CitationBox: React.FC<{ article: Article }> = ({ article }) => {
  const [selectedFormat, setSelectedFormat] = useState<'apa' | 'mla' | 'chicago' | 'ieee'>('apa');
  const [copied, setCopied] = useState(false);
  const citations = generateCitations(article);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy citation:', err);
    }
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Cite This Article</h3>
      
      <div className="flex flex-wrap gap-2 mb-4">
        {Object.keys(citations).map((format) => (
          <button
            key={format}
            onClick={() => setSelectedFormat(format as any)}
            className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
              selectedFormat === format
                ? 'bg-purple-600 text-white'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
            }`}
          >
            {format.toUpperCase()}
          </button>
        ))}
      </div>
      
      <div className="bg-white border border-gray-200 rounded p-4 mb-3">
        <p className="text-sm text-gray-800 leading-relaxed">
          {citations[selectedFormat]}
        </p>
      </div>
      
      <button
        onClick={() => copyToClipboard(citations[selectedFormat])}
        className="inline-flex items-center px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 transition-colors"
      >
        {copied ? (
          <>
            <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
            Copied!
          </>
        ) : (
          <>
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Copy Citation
          </>
        )}
      </button>
    </div>
  );
};

const ArticleSkeleton: React.FC = () => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
    <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
    <div className="h-4 bg-gray-200 rounded w-5/6 mb-4"></div>
    <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
  </div>
);

export const JournalArticlePage: React.FC<JournalArticlePageProps> = ({
  article,
  updateArticleContent,
  onNavigateHome
}) => {
  const [currentArticle, setCurrentArticle] = useState<Article>(article);
  const [content, setContent] = useState<string | null>(article.content || null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [figures, setFigures] = useState<Figure[]>([]);
  const [figuresLoading, setFiguresLoading] = useState<boolean>(false);

  // 生成内容弹窗相关状态
  const [showGenerateModal, setShowGenerateModal] = useState<boolean>(false);
  const [showRegenerateModal, setShowRegenerateModal] = useState<boolean>(false);
  const [wordCount, setWordCount] = useState<string>('');
  const [figureCount, setFigureCount] = useState<string>('');
  const [referenceCount, setReferenceCount] = useState<string>('');
  const [supplementaryMaterial, setSupplementaryMaterial] = useState<string>('');
  const [generationMode, setGenerationMode] = useState<'satirical' | 'serious'>('satirical');

  // 检查是否为新闻类型
  const isNewsArticle = article.category === 'News & Comment';

  // 生成进度相关状态
  const [generationProgress, setGenerationProgress] = useState<{
    show: boolean;
    step: string;
    current: number;
    total: number;
    details: string;
  }>({
    show: false,
    step: '',
    current: 0,
    total: 0,
    details: ''
  });

  // 编辑模式相关状态
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [editingContent, setEditingContent] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);

  // 音频相关状态
  const [audioData, setAudioData] = useState<any>(null);
  const [audioLoading, setAudioLoading] = useState<boolean>(false);
  const [showAudioUploader, setShowAudioUploader] = useState<boolean>(false);

  // 期刊图标状态
  const [journalIcon, setJournalIcon] = useState<string>('🤡');

  // 组件卸载时清理数学保护状态
  useEffect(() => {
    return () => {
      clearMathProtection();
    };
  }, []);

  // 加载期刊图标
  useEffect(() => {
    const loadJournalIcon = async () => {
      try {
        const response = await adminApi.getJournalIcon();
        if (response.icon) {
          setJournalIcon(response.icon);
        }
      } catch (error) {
        console.log('使用默认期刊图标');
      }
    };

    loadJournalIcon();
  }, []);

  // 检查用户是否为管理员或编辑 - 使用统一的权限检查
  const isLoggedIn = isUserLoggedIn();
  const userRole = getUserRole() || '';
  const canEdit = isLoggedIn && (userRole === 'ADMIN' || userRole === 'EDITOR');

  console.log('📄 JournalArticlePage 权限状态:', {
    isLoggedIn,
    userRole,
    canEdit
  });

  // 添加全局的scrollToReference函数
  useEffect(() => {
    // 将函数添加到window对象，供HTML中的onclick使用
    (window as any).scrollToReference = (refNumber: number) => {
      const element = document.getElementById(`ref-${refNumber}`);
      if (element) {
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
        // 高亮引用
        element.style.backgroundColor = '#fef3c7';
        setTimeout(() => {
          element.style.backgroundColor = '';
        }, 2000);
      }
    };

    return () => {
      // 清理函数
      delete (window as any).scrollToReference;
    };
  }, []);

  // 为失败的图片占位符添加重新生成按钮
  useEffect(() => {
    if (!isLoggedIn) return;

    const addRegenerateButtons = () => {
      const placeholders = document.querySelectorAll('.figure-placeholder[data-figure-id]');

      placeholders.forEach(placeholder => {
        const figureId = placeholder.getAttribute('data-figure-id');
        const figureNumber = placeholder.getAttribute('data-figure-number');

        if (!figureId) return;

        const figureData = figures.find(f => f.id === figureId);
        if (!figureData || figureData.status === 'completed') return;

        // 检查是否已经添加了按钮
        if (placeholder.querySelector('.regenerate-button')) return;

        // 创建重新生成按钮
        const button = document.createElement('button');
        button.className = 'regenerate-button mt-3 px-4 py-2 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors';
        button.textContent = figureData.status === 'generating' ? '生成中...' : '重新生成图片';
        button.disabled = figureData.status === 'generating';

        button.onclick = () => {
          if (confirm(`确定要重新生成图片 ${figureNumber} 吗？`)) {
            handleRegenerateFigure(figureId);
          }
        };

        // 添加按钮到占位符
        const contentDiv = placeholder.querySelector('div');
        if (contentDiv) {
          contentDiv.appendChild(button);
        }
      });
    };

    // 延迟执行，确保DOM已更新
    const timer = setTimeout(addRegenerateButtons, 100);

    return () => clearTimeout(timer);
  }, [content, figures, isLoggedIn]);

  // 处理图片占位符并生成图片
  const processAndGenerateFigures = async (content: string, article: any): Promise<string> => {
    const figures: Array<{number: number, title: string, description: string, placeholder: string}> = [];

    // 多种占位符格式的正则表达式（按优先级排序）
    const figurePatterns = [
      // 标准格式：[Figure 1: Title]
      /\[Figure (\d+): ([^\]]+)\]/g,
      // 旧格式：[FIGURE:1:description]
      /\[FIGURE:(\d+):([^\]]+)\]/g,
      // 变体格式：[figure 1: title]
      /\[figure (\d+): ([^\]]+)\]/gi,
      // 简化格式：[Fig 1: title]
      /\[Fig (\d+): ([^\]]+)\]/gi,
      // 带破折号：[Figure 1 - title]
      /\[Figure (\d+) - ([^\]]+)\]/gi
    ];

    // 尝试每种格式，找到所有占位符
    for (const pattern of figurePatterns) {
      let match;
      const tempContent = content; // 重置正则表达式
      pattern.lastIndex = 0; // 重置正则表达式状态

      while ((match = pattern.exec(tempContent)) !== null) {
        const figureNumber = parseInt(match[1]);
        const titleOrDesc = match[2].trim();

        // 检查是否已经添加过这个图片编号
        if (!figures.find(f => f.number === figureNumber)) {
          figures.push({
            number: figureNumber,
            title: titleOrDesc,
            description: '', // 将从 Figures 部分提取
            placeholder: match[0]
          });
        }
      }
    }

    // 按图片编号排序
    figures.sort((a, b) => a.number - b.number);

    // 从文末的 Figures 部分提取详细描述
    const figuresSection = content.match(/## Figures\s*([\s\S]*?)(?=##|$)/);
    if (figuresSection) {
      const figureDescRegex = /\*\*Figure (\d+):\*\* ([^\n]+)/g;
      let descMatch;
      while ((descMatch = figureDescRegex.exec(figuresSection[1])) !== null) {
        const figureNum = parseInt(descMatch[1]);
        const description = descMatch[2].trim();
        const figure = figures.find(f => f.number === figureNum);
        if (figure) {
          figure.description = description;
        }
      }
    }

    console.log(`🎨 发现 ${figures.length} 个图片占位符，开始生成图片...`);

    // 更新总进度数（基础3步 + 图片数量）
    setGenerationProgress(prev => ({
      ...prev,
      total: 3 + figures.length,
      details: `发现 ${figures.length} 个图片，准备生成...`
    }));

    let processedContent = content;

    // 为每个图片生成内容
    for (let i = 0; i < figures.length; i++) {
      const figure = figures[i];
      // 先创建基础的图片记录，确保即使生成失败也能在列表中显示
      const baseFigureData = {
        articleId: article.id,
        figureNumber: figure.number,
        title: figure.title,
        description: figure.description || figure.title,
        caption: `Figure ${figure.number}: ${(figure.description || figure.title).charAt(0).toUpperCase() + (figure.description || figure.title).slice(1)}.`,
        imagePrompt: '',
        imageUrl: '',
        status: 'pending'
      };

      // 更新进度：当前生成的图片
      setGenerationProgress(prev => ({
        ...prev,
        step: `生成图片 ${figure.number}`,
        current: 3 + i,
        details: `正在生成图片 ${figure.number}: ${figure.title}`
      }));

      try {
        console.log(`🎨 正在生成图片 ${figure.number}: ${figure.title}`);

        // 1. 先保存pending状态的记录
        await adminApi.saveFigure(baseFigureData);
        console.log(`📝 图片 ${figure.number} 记录已创建（pending状态）`);

        // 2. 生成图片提示词（限制长度避免413错误）
        const imagePromptText = `Create a concise prompt (max 400 words) for an academic figure:

Title: "${figure.title}"
Description: "${figure.description || figure.title}"
Category: "${article.category}"

Generate a brief, professional prompt for a scientific illustration. Keep it under 400 words. Focus on key visual elements only.

Return ONLY the image prompt, no explanations.`;

        const promptResponse = await aiService.generateText({
          prompt: imagePromptText
        });

        let imagePrompt = promptResponse.trim() || figure.description || figure.title;

        // 限制提示词长度，避免413错误
        if (imagePrompt.length > 400) {
          imagePrompt = imagePrompt.substring(0, 400) + '...';
          console.log(`⚠️ 图片 ${figure.number} 提示词过长，已截断到400字符`);
        }

        // 3. 生成图片
        console.log(`🖼️ 使用提示词生成图片: ${imagePrompt}`);

        try {
          const generatedImageUrl = await aiService.generateImage({
            prompt: imagePrompt + '. Professional academic illustration style, clean and clear, suitable for scientific publication, high quality, detailed, professional composition.',
            numberOfImages: 1,
            aspectRatio: '1:1'
          });

          if (generatedImageUrl) {

            // 4. 更新为成功状态
            const completedFigureData = {
              ...baseFigureData,
              imagePrompt: imagePrompt,
              imageUrl: generatedImageUrl,
              status: 'completed'
            };

            await adminApi.saveFigure(completedFigureData);

            // 5. 替换占位符为图片组件标记
            const figureComponent = `[FIGURE_DISPLAY:${figure.number}]`;
            processedContent = processedContent.replace(figure.placeholder, figureComponent);

            console.log(`✅ 图片 ${figure.number} 生成并保存成功`);
          } else {
            // 图片生成失败，保存提示词并标记为failed
            const failedFigureData = {
              ...baseFigureData,
              imagePrompt: imagePrompt,
              status: 'failed'
            };
            await adminApi.saveFigure(failedFigureData);
            console.warn(`⚠️ 图片 ${figure.number} 生成失败，已保存提示词并标记为failed状态，保留文章中的占位符`);
            // 注意：不替换占位符，让它保留在文章中，这样用户能看到有图片缺失
          }
        } catch (imageGenError) {
          // 图片生成API调用失败（如413错误），保存提示词并跳过
          console.error(`❌ 图片 ${figure.number} 生成API调用失败:`, imageGenError);

          const failedFigureData = {
            ...baseFigureData,
            imagePrompt: imagePrompt,
            status: 'failed'
          };

          try {
            await adminApi.saveFigure(failedFigureData);
            console.log(`📝 图片 ${figure.number} 提示词已保存，跳过到下一张图片，保留文章中的占位符`);
            // 注意：不替换占位符，让它保留在文章中
          } catch (saveError) {
            console.error(`❌ 保存失败图片记录失败:`, saveError);
          }
        }

      } catch (error) {
        console.error(`❌ 图片 ${figure.number} 生成失败:`, error);

        // 即使生成失败，也要确保数据库中有记录（failed状态）
        try {
          const failedFigureData = {
            ...baseFigureData,
            status: 'failed'
          };
          await adminApi.saveFigure(failedFigureData);
          console.log(`📝 图片 ${figure.number} 已标记为failed状态`);
        } catch (saveError) {
          console.error(`❌ 保存失败状态记录失败:`, saveError);
        }
      }
    }

    return processedContent;
  };

  // 显示重新生成内容弹窗
  const handleRegenerateContent = () => {
    setShowRegenerateModal(true);
  };

  // 执行重新生成文章内容
  const executeRegenerateContent = async (wordCount?: string, figureCount?: string, referenceCount?: string, mode?: 'satirical' | 'serious') => {
    if (!confirm('确定要重新生成文章内容吗？这将覆盖现有内容和所有图片。')) {
      return;
    }

    setIsGenerating(true);
    setError(null);
    setShowRegenerateModal(false);

    try {
      console.log('🔄 开始重新生成文章内容...');

      // 1. 先清理旧的图片记录
      try {
        console.log('🗑️ 清理旧图片记录...');
        await adminApi.clearArticleFigures(article.id);
        console.log('✅ 旧图片记录清理完成');

        // 清空本地图片状态
        setFigures([]);
      } catch (clearError) {
        console.warn('⚠️ 清理旧图片记录失败，继续生成:', clearError);
      }

      // 2. 重新生成内容
      await generateArticleContent(true, wordCount, figureCount, referenceCount, supplementaryMaterial, mode); // 强制重新生成
    } catch (e) {
      console.error("Failed to regenerate article content:", e);
      setError("重新生成文章内容失败，请稍后重试。");
    } finally {
      setIsGenerating(false);
      setGenerationProgress(prev => ({ ...prev, show: false }));
    }
  };

  // 显示生成内容弹窗
  const handleGenerateContent = () => {
    setShowGenerateModal(true);
  };

  // 执行生成文章内容
  const executeGenerateContent = async (wordCount?: string, figureCount?: string, referenceCount?: string, supplementary?: string, mode?: 'satirical' | 'serious') => {
    setIsGenerating(true);
    setError(null);
    setShowGenerateModal(false);

    try {
      await generateArticleContent(false, wordCount, figureCount, referenceCount, supplementary, mode); // 正常生成
    } catch (e) {
      console.error("Failed to generate article content:", e);
      if (e instanceof Error && e.message.includes('认证')) {
        setError("权限验证失败，请重新登录。");
      } else {
        setError("Our AI writer is currently experiencing stage fright. Please try again later.");
      }
    } finally {
      setIsGenerating(false);
      setGenerationProgress(prev => ({ ...prev, show: false }));
    }
  };

  // 统一的文章生成逻辑
  const generateArticleContent = async (forceRegenerate: boolean = false, wordCount?: string, figureCount?: string, referenceCount?: string, supplementary?: string, mode: 'satirical' | 'serious' = 'satirical') => {
    // 显示进度
    setGenerationProgress({
      show: true,
      step: '准备生成',
      current: 0,
      total: 3, // 基础步骤：获取API Key、生成文字、生成图片
      details: '正在获取API密钥...'
    });

    // 1. 重置AI服务配置缓存，确保使用最新配置
    aiService.resetConfig();

    // 3. 构建提示词，包含用户自定义的参数
    const wordCountText = wordCount?.trim() ? `approximately ${wordCount} words` : '600-800 words';
    const figureCountText = figureCount?.trim() ? `EXACTLY ${figureCount} figures` : '2-3 figures';
    const referenceCountText = referenceCount?.trim() ? `EXACTLY ${referenceCount} references` : '3-5 references';
    const supplementaryText = supplementary?.trim() ? `\n\nSUPPLEMENTARY INFORMATION: ${supplementary}` : '';

    // 更新进度：开始生成文字
    setGenerationProgress(prev => ({
      ...prev,
      step: '生成文字内容',
      current: 1,
      details: `正在生成${wordCountText}的${mode === 'serious' ? '严肃学术' : '戏谑'}文章...`
    }));

    // 根据模式和类型选择不同的prompt
    const prompt = isNewsArticle
      ? `Write a detailed satirical news article for the "Jocker Journal News & Comment" section using the INVERTED PYRAMID structure. The article already has a headline, reporter, and summary. Now write the full news story.

EXISTING NEWS INFO:
- Headline: "${article.title}"
- Category: "${article.category}"
- Reporter: "${article.author}"
- Summary/Lead: "${article.excerpt}"${supplementaryText}

TASK: Write the complete news article (${wordCountText}) using INVERTED PYRAMID STRUCTURE:

## LEAD PARAGRAPH (Most Important)
Start with the most crucial information - expand on the 5W1H (Who, What, When, Where, Why, How) from the summary. This should be the most attention-grabbing and informative part.

## BODY PARAGRAPHS (Important Details)
- **Immediate Details**: Key facts, witness accounts, official statements
- **Background Context**: What led to this situation, relevant history
- **Expert Reactions**: Quotes from academics, officials, witnesses (make them believable but absurd)
- **Current Impact**: How this affects the academic community

## SUPPORTING INFORMATION (Less Critical)
- **Additional Context**: Secondary details, related incidents
- **Future Implications**: What might happen next, ongoing investigations
- **Historical Perspective**: Similar past events (if relevant)

FORMATTING REQUIREMENTS:
1. Use SHORT PARAGRAPHS (2-3 sentences max) - typical news style
2. Include ${figureCountText} photos using: [FIGURE:X:description]
   - Example: [FIGURE:1:Scene showing the chaotic laboratory aftermath]
   - Follow with: "**Photo X:** [Caption describing the scene]"
3. Include DIRECT QUOTES from at least 3 different sources
4. Use present tense for recent events, past tense for background
5. Include ${referenceCountText} news sources at the end:
   --- SOURCES ---
   [1] University Press Release, Academic Affairs Office (2024)
   [2] Interview with Dr. Smith, Department of Satirical Studies (2024)
   --- END SOURCES ---

Write in professional journalism style with short, punchy paragraphs. Make it absurd but believable academic news.`

      : mode === 'serious'
      ? `Continue writing the main body of a professional scientific research paper. The article already has a title, author, and abstract. Now write the detailed content starting from the Introduction section.

EXISTING ARTICLE INFO:
- Title: "${article.title}"
- Category: "${article.category}"
- Author: "${article.author}"
- Abstract: "${article.excerpt}"${supplementaryText}

TASK: Write the main body content (${wordCountText}) with these sections: "Introduction", "Methodology", "Results", and "Conclusion". Do NOT repeat the title, author, or abstract.

IMPORTANT FORMATTING REQUIREMENTS:
1. Start directly with "## Introduction" - do not repeat title, author, or abstract
2. Return ONLY the main body content in Markdown format
3. Do not include any explanatory text, comments, or meta-commentary
4. Include ${figureCountText} (no more, no less) in your article using this EXACT format: [FIGURE:X:description]
   - Replace X with the figure number (1, 2, 3, etc.)
   - Replace description with a detailed, professional figure caption
   - Example: [FIGURE:1:Schematic diagram of the experimental setup showing the measurement apparatus and sample positioning]
5. Use proper academic writing style with formal language and technical precision
6. Include appropriate statistical analysis and data interpretation
7. Ensure all claims are supported by logical reasoning and proper methodology
8. Use LaTeX mathematical notation for equations:
   - For inline math: use $equation$ (single dollar signs)
   - For display math (centered, numbered): use $$equation$$ (double dollar signs)
   - Example inline: "The correlation coefficient $r = 0.95$ indicates..."
   - Example display: "$$F = ma$$" or "$$\\sigma = \\sqrt{\\frac{\\sum(x_i - \\mu)^2}{N}}$$"
   - Reference equations in text like "as shown in Equation 1" or "(Eq. 2)"
9. Make equations scientifically accurate and mathematically sound
10. Include ${referenceCountText} real-style academic references at the end using this EXACT format:
    --- REFERENCES ---
    [1] Smith, J., & Johnson, A. (2023). Advanced methodologies in experimental research. Nature Methods, 15(3), 42-58.
    [2] Brown, M. et al. (2022). Statistical analysis of complex datasets. Science, 378(6621), 123-145.
    --- END REFERENCES ---
    - Create realistic-sounding journal names, authors, and citations that could exist in real academic literature
    - Use proper academic citation format with journal names, volume numbers, page numbers
    - Make references relevant to the research topic and methodology

Write content that demonstrates deep understanding of the subject matter, employs rigorous scientific methodology, and maintains the professional standards expected in academic publishing.`

      : `Continue writing the main body of a satirical scientific research paper for the journal "Joker". The article already has a title, author, and abstract. Now write the detailed content starting from the Introduction section.

EXISTING ARTICLE INFO:
- Title: "${article.title}"
- Category: "${article.category}"
- Author: "${article.author}"
- Abstract: "${article.excerpt}"${supplementaryText}

TASK: Write the main body content (${wordCountText}) with these sections: "Introduction", "Methodology", "Results", and "Conclusion". Use an extremely serious, academic tone while discussing absurd topics. Do NOT repeat the title, author, or abstract.

IMPORTANT FORMATTING REQUIREMENTS:
1. Return ONLY the article content in Markdown format
2. Do not include any explanatory text, comments, or meta-commentary
3. Include ${figureCountText} (no more, no less) in your article using this EXACT format: [FIGURE:X:description]
   - X should be the figure number (1, 2, or 3)
   - description should be a brief description of what the figure shows
   - Example: [FIGURE:1:Experimental setup showing the absurd measurement apparatus]
4. After each figure placeholder, add a proper figure caption on the next line starting with "**Figure X:**"
5. Reference figures in the text using standard academic format like "as shown in Figure 1" or "(Figure 2)"
6. Distribute figures logically: typically one in Methodology, one in Results, and optionally one in Introduction or Conclusion
7. Include 2-4 mathematical equations using LaTeX format:
   - For inline math: use $equation$ (single dollar signs)
   - For display math (centered, numbered): use $$equation$$ (double dollar signs)
   - Example inline: "The correlation coefficient $r = 0.95$ indicates..."
   - Example display: "$$P(coffee) = \\frac{productivity^2}{absurdity \\cdot \\sqrt{caffeine}}$$"
   - Reference equations in text like "as shown in Equation 1" or "(Eq. 2)"
8. Make equations satirical but mathematically plausible (use real mathematical notation)
9. Include ${referenceCountText} satirical academic references at the end using this EXACT format:
   --- REFERENCES ---
   [1] Smith, J., & Johnson, A. (2023). The Art of Academic Procrastination. Journal of Satirical Science, 15(3), 42-58.
   [2] Brown, M. et al. (2022). Coffee Consumption and Productivity: A Longitudinal Study. Proceedings of Caffeine Research, 8, 123-145.
   --- END REFERENCES ---
   - Create fake but realistic-sounding journal names, authors, and citations
   - Use proper academic citation format (Author, Year, Title, Journal, Volume, Pages)
   - Make references humorous but believable
   - Reference these citations in the text using [1], [2], etc.

Title: "${article.title}"
Author: "${article.author}"
Category: "${article.category}"
Abstract: "${article.excerpt}"

Format with Markdown headers (## Section Name), use **bold** and *italics* for emphasis. Make sure to include relevant figures and references that would enhance the satirical scientific narrative.`;

    console.log(forceRegenerate ? '🔄 开始重新生成文章内容...' : '🤖 开始生成文章内容...');

    const response = await aiService.generateText({
      prompt
    });

    const generatedContent = response || '';

    if (!generatedContent) {
      throw new Error('AI 未返回文章内容');
    }

    // 更新进度：开始生成图片
    setGenerationProgress(prev => ({
      ...prev,
      step: '生成图片',
      current: 2,
      details: '正在分析文章内容并生成图片...'
    }));

    // 3. 处理图片占位符并生成图片
    const processedContent = await processAndGenerateFigures(generatedContent, article);

    // 更新进度：保存内容
    setGenerationProgress(prev => ({
      ...prev,
      step: '保存内容',
      current: 3,
      details: '正在保存文章内容到数据库...'
    }));

    // 4. 将处理后的内容保存到后端
    await adminApi.saveArticleContent(article.id, processedContent);

    setContent(processedContent);
    updateArticleContent(article.id, processedContent);

    // 5. 重新加载图片列表
    loadArticleFigures();

    // 隐藏进度显示
    setGenerationProgress(prev => ({ ...prev, show: false }));

    console.log(forceRegenerate ? '✅ 文章内容重新生成并保存成功' : '✅ 文章内容和图片生成并保存成功');
  };

  // 为旧文章创建缺失的图片记录
  const createMissingFigureRecords = async () => {
    if (!content || !isLoggedIn) return;

    try {
      // 从文章内容中提取图片占位符
      const figurePatterns = [
        /\[Figure (\d+): ([^\]]+)\]/g,
        /\[FIGURE:(\d+):([^\]]+)\]/g,
        /\[figure (\d+): ([^\]]+)\]/gi,
        /\[Fig (\d+): ([^\]]+)\]/gi,
        /\[Figure (\d+) - ([^\]]+)\]/gi
      ];

      const foundPlaceholders = [];
      for (const pattern of figurePatterns) {
        let match;
        pattern.lastIndex = 0;
        while ((match = pattern.exec(content)) !== null) {
          const figureNumber = parseInt(match[1]);
          const titleOrDesc = match[2].trim();

          if (!foundPlaceholders.find(f => f.number === figureNumber)) {
            foundPlaceholders.push({
              number: figureNumber,
              title: titleOrDesc,
              description: titleOrDesc
            });
          }
        }
      }

      // 检查哪些占位符没有对应的数据库记录
      const existingFigures = figures;
      const missingFigures = foundPlaceholders.filter(placeholder =>
        !existingFigures.find(fig => fig.figureNumber === placeholder.number)
      );

      // 为缺失的图片创建failed状态的记录
      for (const missingFigure of missingFigures) {
        try {
          const figureData = {
            articleId: article.id,
            figureNumber: missingFigure.number,
            title: missingFigure.title,
            description: missingFigure.description,
            caption: `Figure ${missingFigure.number}: ${missingFigure.description}`,
            imagePrompt: `Academic illustration for: ${missingFigure.description}`,
            imageUrl: '',
            status: 'failed'
          };

          await adminApi.saveFigure(figureData);
          console.log(`📝 为旧文章创建图片记录: Figure ${missingFigure.number}`);
        } catch (error) {
          console.warn(`创建图片记录失败: Figure ${missingFigure.number}`, error);
        }
      }

      // 如果创建了新记录，重新加载图片列表
      if (missingFigures.length > 0) {
        console.log(`✅ 为旧文章创建了 ${missingFigures.length} 个缺失的图片记录`);
        await loadArticleFigures();
      }
    } catch (error) {
      console.warn('创建缺失图片记录失败:', error);
    }
  };

  // 生成浏览器指纹
  const generateFingerprint = () => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Browser fingerprint', 2, 2);
    }

    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|');

    // 生成简单的hash
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(36);
  };

  // 获取文章详情并增加观看次数
  const fetchArticleDetails = async () => {
    try {
      // 生成浏览器指纹
      const fingerprint = generateFingerprint();

      // 检查localStorage中是否已经访问过这篇文章
      const viewedArticles = JSON.parse(localStorage.getItem('joker_viewed_articles') || '[]');
      const articleKey = `${article.id}_${fingerprint}`;

      if (viewedArticles.includes(articleKey)) {
        console.log('📊 本地记录显示已访问过此文章，跳过观看次数统计');
        // 仍然获取文章数据，但不增加观看次数
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || '/api'}/articles/${article.id}?skip_view=true`);
        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            setCurrentArticle(result.data);
            // 如果文章有内容，设置content状态
            if (result.data.content && result.data.content.trim() !== '') {
              setContent(result.data.content);
            }
          }
        }
        return;
      }

      // 调用API并传递指纹
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || '/api'}/articles/${article.id}?fingerprint=${fingerprint}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          setCurrentArticle(result.data);
          console.log('📊 文章观看次数已更新:', result.data.views);

          // 如果文章有内容，设置content状态
          if (result.data.content && result.data.content.trim() !== '') {
            setContent(result.data.content);
          }

          // 记录到localStorage
          viewedArticles.push(articleKey);
          localStorage.setItem('joker_viewed_articles', JSON.stringify(viewedArticles));
        }
      }
    } catch (error) {
      console.error('获取文章详情失败:', error);
    }
  };

  // 加载文章音频
  const loadArticleAudio = async () => {
    setAudioLoading(true);
    try {
      const audio = await audioApi.getArticleAudio(article.id);
      console.log('🎵 音频数据加载成功:', audio);
      if (audio) {
        const fullAudioUrl = `${window.location.origin}${audio.audioUrl}`;
        console.log('🎵 完整音频URL:', fullAudioUrl);
      }
      setAudioData(audio);
    } catch (error) {
      console.log('该文章没有音频文件:', error);
      setAudioData(null);
    } finally {
      setAudioLoading(false);
    }
  };

  // 音频上传成功处理
  const handleAudioUploadSuccess = (audio: any) => {
    setAudioData(audio);
    setShowAudioUploader(false);
    alert('音频上传成功！');
  };

  // 音频上传错误处理
  const handleAudioUploadError = (error: string) => {
    alert(`上传失败：${error}`);
  };

  // 删除音频
  const handleDeleteAudio = async () => {
    if (!confirm('确定要删除这个音频文件吗？')) return;

    try {
      await audioApi.deleteArticleAudio(article.id);
      setAudioData(null);
      alert('音频删除成功！');
    } catch (error) {
      alert('删除音频失败');
      console.error(error);
    }
  };

  // 加载文章图片（所有用户都可以查看图片）
  const loadArticleFigures = async () => {
    setFiguresLoading(true);
    try {
      // 使用公共 API 获取图片（不需要管理员权限）
      const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || '/api'}/articles/${article.id}/figures`);
      if (response.ok) {
        const result = await response.json();
        const figures = result.data?.figures || [];
        setFigures(figures);
        console.log('📸 已加载图片列表:', figures.length);

        // 为旧文章创建缺失的图片记录（仅管理员）
        if (isLoggedIn && figures.length === 0 && content) {
          await createMissingFigureRecords();
        }
      } else {
        console.warn('获取图片失败:', response.status);
        setFigures([]);
      }
    } catch (error) {
      console.error('加载图片失败:', error);
      setFigures([]);
    } finally {
      setFiguresLoading(false);
    }
  };

  // 重新生成图片
  const handleRegenerateFigure = async (figureId: string) => {
    try {
      console.log('🔄 开始重新生成图片:', figureId);

      // 找到要重新生成的图片
      const figure = figures.find(fig => fig.id === figureId);
      if (!figure) {
        console.error('找不到图片:', figureId);
        return;
      }

      // 更新图片状态为生成中
      setFigures(prev => prev.map(fig =>
        fig.id === figureId
          ? { ...fig, status: 'generating' as const, imageUrl: '' }
          : fig
      ));

      // 使用aiService生成图片（和文章生成使用相同的配置逻辑）
      const imageUrl = await aiService.generateImage({
        prompt: figure.imagePrompt || figure.description || figure.title,
        numberOfImages: 1,
        aspectRatio: '1:1'
      });

      console.log('🖼️ 图片生成成功');

      // 保存图片到数据库
      await adminApi.saveFigure({
        id: figureId,
        articleId: article?.id,
        figureNumber: figure.figureNumber,
        title: figure.title,
        description: figure.description,
        caption: figure.caption,
        imagePrompt: figure.imagePrompt,
        imageUrl: imageUrl,
        status: 'completed'
      });

      // 更新本地状态
      setFigures(prev => prev.map(fig =>
        fig.id === figureId
          ? { ...fig, status: 'completed' as const, imageUrl: imageUrl }
          : fig
      ));

      console.log('✅ 图片重新生成成功');

    } catch (error) {
      console.error('❌ 重新生成图片失败:', error);

      // 更新状态为失败
      setFigures(prev => prev.map(fig =>
        fig.id === figureId
          ? { ...fig, status: 'failed' as const }
          : fig
      ));

      // 显示错误信息给用户
      alert(`图片重新生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  };



  // 简化的列表格式修复（避免过度处理）
  const fixListFormatting = (content: string): string => {
    let fixed = content;

    // 只做最基本的修复：确保列表项格式正确
    fixed = fixed.replace(/^(\d+)\.([^\s])/gm, '$1. $2');

    return fixed;
  };

  // 渲染带有数学公式的内容
  const renderContentWithMath = (content: string) => {
    console.log('🔢 开始渲染数学内容，公式统计:', getMathStats(content));

    // 1. 首先保护所有数学公式
    let processedContent = protectMath(content);
    console.log('🛡️ 数学公式已保护');

    // 2. 移除文末的 Figures 部分（不在主文中显示）
    processedContent = processedContent.replace(/## Figures\s*[\s\S]*$/, '');

    // 4. 配置marked选项以正确处理列表（简化配置，避免自定义renderer问题）
    if (typeof marked !== 'undefined' && marked.setOptions) {
      marked.setOptions({
        gfm: true,          // GitHub Flavored Markdown
        breaks: false,      // 不要将单个换行转换为<br>
        pedantic: false,    // 不要严格遵循原始markdown.pl
        sanitize: false,    // 不要清理HTML（我们用DOMPurify）
        smartLists: true,   // 使用更智能的列表行为
        smartypants: false, // 不要使用智能标点
        headerIds: false,   // 不要自动生成header ID
        mangle: false       // 不要混淆邮箱地址
      });
    }

    // 5. 将 Markdown 转换为 HTML
    let htmlContent = marked.parse(processedContent);
    console.log('📝 Markdown转HTML完成');

    // 6. 在 HTML 中替换图片占位符
    htmlContent = replaceImagePlaceholdersInHTML(htmlContent);

    // 7. 为标题添加 ID，用于目录导航
    htmlContent = addHeadingIds(htmlContent);

    // 8. 处理文献引用为上标样式（在数学公式被保护的状态下进行）
    htmlContent = processReferenceCitations(htmlContent);
    console.log('📚 文献引用处理完成');

    // 9. 恢复数学公式（在所有其他处理完成后）
    htmlContent = restoreMath(htmlContent);
    console.log('🔓 数学公式已恢复');

    // 10. 数学公式样式已在保护阶段处理
    console.log('🎨 公式样式已在保护阶段应用');

    // 11. 清理 HTML（但要保留数学公式相关标签）
    const cleanHtml = DOMPurify.sanitize(htmlContent, {
      ADD_TAGS: ['math', 'semantics', 'mrow', 'mi', 'mo', 'mn', 'msup', 'msub', 'mfrac', 'mtext', 'munder', 'mover'],
      ADD_ATTR: ['class', 'style', 'data-katex', 'xmlns']
    });

    // 直接返回处理好的HTML，不再使用MathRenderer（避免重复处理）
    return <ContentWithMath content={cleanHtml} />;
  };

  // 为标题添加 ID，用于目录导航
  const addHeadingIds = (htmlContent: string): string => {
    // 为 h2 标题添加 ID
    htmlContent = htmlContent.replace(/<h2([^>]*)>(.*?)<\/h2>/gi, (match, attributes, content) => {
      const id = content.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '') // 移除特殊字符
        .replace(/\s+/g, '-') // 空格替换为连字符
        .trim();
      return `<h2${attributes} id="${id}">${content}</h2>`;
    });

    // 为 h3 标题添加 ID
    htmlContent = htmlContent.replace(/<h3([^>]*)>(.*?)<\/h3>/gi, (match, attributes, content) => {
      const id = content.toLowerCase()
        .replace(/[^a-z0-9\s]/g, '') // 移除特殊字符
        .replace(/\s+/g, '-') // 空格替换为连字符
        .trim();
      return `<h3${attributes} id="${id}">${content}</h3>`;
    });

    return htmlContent;
  };

  // 处理文献引用为上标样式并添加点击跳转
  const processReferenceCitations = (htmlContent: string): string => {
    // 在数学公式被保护的状态下，我们需要找出数学占位符的位置
    const mathPlaceholders: Array<{start: number, end: number}> = [];

    // 匹配数学公式占位符 <!--MATH_BLOCK_xxx-->
    const placeholderRegex = /<!--MATH_BLOCK_\d+-->/g;
    let match;
    while ((match = placeholderRegex.exec(htmlContent)) !== null) {
      mathPlaceholders.push({start: match.index, end: match.index + match[0].length});
    }

    // 按位置排序
    mathPlaceholders.sort((a, b) => a.start - b.start);

    // 检查位置是否在数学占位符中
    const isInMathPlaceholder = (position: number): boolean => {
      return mathPlaceholders.some(block => position >= block.start && position <= block.end);
    };

    // 匹配文献引用格式：[1], [2], [1,2], [1-3], [1,3-5], [9, 10], [9-10] 等
    return htmlContent.replace(/\[(\d+(?:[-,\s]*\d+)*)\]/g, (match, citations, offset) => {
      // 检查是否在数学占位符中，如果是则不处理
      if (isInMathPlaceholder(offset)) {
        return match; // 保持原样
      }

      // 解析引用编号
      const citationNumbers = parseCitationNumbers(citations);

      // 生成上标链接，保留中括号
      const citationLinks = citationNumbers.map(num =>
        `<sup><a href="#ref-${num}" class="text-blue-600 hover:text-blue-800 no-underline cursor-pointer" onclick="scrollToReference(${num})">[${num}]</a></sup>`
      ).join('');

      return citationLinks;
    });
  };

  // 解析引用编号字符串，支持 "1", "1,2", "1-3", "1,3-5", "9, 10", "9-10" 等格式
  const parseCitationNumbers = (citations: string): number[] => {
    const numbers: number[] = [];
    // 先处理空格，统一格式
    const normalizedCitations = citations.replace(/\s+/g, '');
    const parts = normalizedCitations.split(',');

    parts.forEach(part => {
      part = part.trim();
      if (part.includes('-')) {
        // 处理范围，如 "1-3" 或 "9-10"
        const [start, end] = part.split('-').map(n => parseInt(n.trim()));
        if (!isNaN(start) && !isNaN(end)) {
          for (let i = start; i <= end; i++) {
            numbers.push(i);
          }
        }
      } else {
        // 处理单个数字
        const num = parseInt(part);
        if (!isNaN(num)) {
          numbers.push(num);
        }
      }
    });

    return [...new Set(numbers)].sort((a, b) => a - b); // 去重并排序
  };

  // 平滑滚动到指定部分
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  // 进入编辑模式
  const handleEditContent = () => {
    setEditingContent(content || '');
    setIsEditing(true);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditingContent('');
  };

  // 保存编辑内容
  const handleSaveEdit = async () => {
    if (!editingContent.trim()) {
      alert('文章内容不能为空');
      return;
    }

    setIsSaving(true);
    try {
      // 保存到后端
      await adminApi.saveArticleContent(article.id, editingContent);

      // 更新本地状态
      setContent(editingContent);
      updateArticleContent(article.id, editingContent);

      // 重新加载图片列表（因为内容可能包含新的图片占位符）
      loadArticleFigures();

      // 退出编辑模式
      setIsEditing(false);
      setEditingContent('');

      alert('文章保存成功！');
    } catch (error) {
      console.error('保存文章失败:', error);
      alert('保存文章失败，请稍后重试。');
    } finally {
      setIsSaving(false);
    }
  };

  // 在 HTML 中替换图片占位符
  const replaceImagePlaceholdersInHTML = (htmlContent: string): string => {
    // 替换 [FIGURE_DISPLAY:number] 为实际图片显示
    htmlContent = htmlContent.replace(/\[FIGURE_DISPLAY:(\d+)\]/g, (match, figureNumber) => {
      const figureData = figures.find(f => f.figureNumber === parseInt(figureNumber));
      if (figureData && figureData.imageUrl) {
        return `
          <div class="figure-container my-8">
            <img src="${figureData.imageUrl}" alt="${figureData.title}" class="w-full max-w-2xl mx-auto rounded-lg shadow-lg" />
            <p class="text-sm text-gray-600 text-center mt-2 italic">${figureData.caption}</p>
          </div>
        `;
      } else {
        return `
          <div class="figure-placeholder bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center my-8">
            <div class="text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z"></path>
              </svg>
              <p class="font-medium">Figure ${figureNumber}</p>
              <p class="text-sm">Image loading...</p>
            </div>
          </div>
        `;
      }
    });

    // 处理多种占位符格式（按优先级替换）
    const figurePatterns = [
      // 标准格式：[Figure 1: Title]
      { pattern: /\[Figure (\d+): ([^\]]+)\]/g, format: (num: string, title: string) => `Figure ${num}: ${title}` },
      // 旧格式：[FIGURE:1:description]
      { pattern: /\[FIGURE:(\d+):([^\]]+)\]/g, format: (num: string, desc: string) => `Figure ${num}` },
      // 变体格式：[figure 1: title]
      { pattern: /\[figure (\d+): ([^\]]+)\]/gi, format: (num: string, title: string) => `Figure ${num}: ${title}` },
      // 简化格式：[Fig 1: title]
      { pattern: /\[Fig (\d+): ([^\]]+)\]/gi, format: (num: string, title: string) => `Figure ${num}: ${title}` },
      // 带破折号：[Figure 1 - title]
      { pattern: /\[Figure (\d+) - ([^\]]+)\]/gi, format: (num: string, title: string) => `Figure ${num}: ${title}` }
    ];

    // 替换所有格式的占位符为图片显示
    for (const { pattern, format } of figurePatterns) {
      htmlContent = htmlContent.replace(pattern, (match, figureNumber, titleOrDesc) => {
        const figNum = parseInt(figureNumber);
        const figureData = figures.find(f => f.figureNumber === figNum);

        if (figureData && figureData.imageUrl) {
          return `
            <div class="figure-container my-8">
              <img src="${figureData.imageUrl}" alt="${figureData.title}" class="w-full max-w-2xl mx-auto rounded-lg shadow-lg" />
              <p class="text-sm text-gray-600 text-center mt-2 italic">${figureData.caption}</p>
            </div>
          `;
        } else {
          const displayText = format(figureNumber, titleOrDesc.trim());
          const figureStatus = figureData?.status || 'unknown';
          const statusText = figureStatus === 'failed' ? 'Generation failed - Check Figures gallery to regenerate' :
                           figureStatus === 'pending' ? 'Generating...' :
                           figureStatus === 'generating' ? 'Generating...' : 'Image loading...';
          const statusColor = figureStatus === 'failed' ? 'text-red-500' : 'text-gray-500';
          const borderColor = figureStatus === 'failed' ? 'border-red-300 bg-red-50' : 'border-gray-300 bg-gray-100';

          return `
            <div class="figure-placeholder ${borderColor} border-2 border-dashed rounded-lg p-8 text-center my-8" data-figure-id="${figureData?.id || ''}" data-figure-number="${figureNumber}">
              <div class="${statusColor}">
                <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 002 2z"></path>
                </svg>
                <p class="font-medium">${displayText}</p>
                <p class="text-sm">${statusText}</p>
                ${figureStatus === 'failed' ? '<p class="text-xs mt-2 text-blue-600">💡 Tip: Use the Figures gallery below to regenerate this image</p>' : ''}
              </div>
            </div>
          `;
        }
      });
    }

    return htmlContent;
  };

  // 移除了自动加粗修复逻辑，让用户完全控制Markdown格式

  useEffect(() => {
    // 获取文章详情并增加观看次数
    fetchArticleDetails();

    // 加载音频信息
    loadArticleAudio();

    // 如果文章已有内容，直接显示（兼容旧的传入方式）
    if (article.content && article.content.trim() !== '') {
      setContent(article.content);
    }

    // 总是尝试加载图片（所有用户都可以查看图片）
    loadArticleFigures();
  }, [article]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with navigation */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 lg:px-8 py-4">
          <button
            onClick={onNavigateHome}
            className="inline-flex items-center text-purple-600 hover:text-purple-800 transition-colors"
          >
            <BackArrowIcon className="w-5 h-5 mr-2" />
            Back to Journal
          </button>
        </div>
      </div>

      {/* Main content container with responsive layout */}
      <div className="max-w-[1600px] mx-auto px-4 lg:px-8 py-8">
        <div className="xl:grid xl:grid-cols-12 xl:gap-10">
          {/* Main article content - takes more space on desktop */}
          <article className="xl:col-span-9 bg-white rounded-lg shadow-sm border border-gray-200 p-6 lg:p-10">
        {/* Journal header */}
        <JournalHeader
          articleNumber={article.id}
          doi={`10.1000/joker.${new Date().getFullYear()}.${article.id}`}
        />

        {/* Article metadata */}
        <div className="mb-8">
          {isNewsArticle ? (
            /* 新闻样式 */
            <div className="border-l-4 border-red-600 bg-red-50 p-6 mb-6">
              <div className="flex items-center space-x-3 mb-3">
                <span className="bg-red-600 text-white px-3 py-1 rounded-full text-xs font-bold uppercase tracking-wider">
                  🚨 BREAKING NEWS
                </span>
                <span className="text-red-700 text-sm font-medium">
                  {new Date().toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              </div>
              <p className="text-sm font-semibold text-red-800 uppercase tracking-wider mb-2">
                {article.category}
              </p>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight mb-4 font-sans">
                {article.title}
              </h1>
              <div className="text-sm text-red-700">
                <span className="font-medium">Reported by:</span> {article.author}
              </div>
            </div>
          ) : (
            /* 学术期刊样式 */
            <div className="bg-purple-50 border-l-4 border-purple-600 p-6 mb-6">
              <PeerReviewBadge />
              <p className="text-sm font-semibold text-purple-800 uppercase tracking-wider mb-2">
                {article.category}
              </p>
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 leading-tight mb-6 font-serif">
                {article.title}
              </h1>
            </div>
          )}

          {/* Author information */}
          <div className="mb-6">
            <AuthorCard author={article.author} />
          </div>

          {/* Article metrics */}
          <ArticleMetrics />

          {/* Abstract/Summary with keywords */}
          <div id="abstract">
            {isNewsArticle ? (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-8">
                <h2 className="text-lg font-bold text-gray-900 mb-3 flex items-center">
                  📰 News Summary
                </h2>
                <p className="text-gray-700 leading-relaxed text-base">
                  {article.excerpt}
                </p>
                <div className="mt-4 flex flex-wrap gap-2">
                  {['breaking news', 'academic affairs', article.category.toLowerCase(), 'campus report'].map((tag, index) => (
                    <span key={index} className="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            ) : (
              <AbstractSection
                abstract={article.excerpt}
                keywords={['satirical science', 'methodology', article.category.toLowerCase(), 'peer review', 'academic humor']}
              />
            )}
          </div>

          {/* Audio Player Section */}
          {audioData && (
            <div className="mt-8">
              <AudioPlayer
                audioUrl={`${window.location.origin}${audioData.audioUrl}`}
                title={audioData.title}
                description={audioData.description}
                className="mb-6"
              />
              {canEdit && (
                <div className="flex justify-end mt-2">
                  <button
                    onClick={handleDeleteAudio}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    删除音频
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Audio Upload Section (Admin Only) */}
          {canEdit && !audioData && (
            <div className="mt-8">
              {!showAudioUploader ? (
                <div className="text-center py-6 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                  <svg className="w-12 h-12 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                  </svg>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">添加播客音频</h3>
                  <p className="text-gray-600 mb-4">为这篇文章添加音频播客，让读者可以收听内容</p>
                  <button
                    onClick={() => setShowAudioUploader(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    上传音频文件
                  </button>
                </div>
              ) : (
                <AudioUploader
                  articleId={article.id}
                  onUploadSuccess={handleAudioUploadSuccess}
                  onUploadError={handleAudioUploadError}
                  className="mb-6"
                />
              )}
            </div>
          )}
        </div>

        {/* Article image */}
        <div className="mb-8">
          <img 
            src={article.imageUrl} 
            alt={article.title} 
            className="w-full h-auto max-h-[400px] object-cover rounded-lg shadow-lg" 
          />
          <p className="text-sm text-gray-500 mt-2 text-center italic">
            Figure 1: Visual representation of the research methodology
          </p>
        </div>

        {/* Article content */}
        <div className="prose prose-xl max-w-none prose-headings:font-serif prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-h2:border-b prose-h2:border-gray-200 prose-h2:pb-3 prose-h2:mb-8 prose-h2:mt-10 prose-p:text-gray-700 prose-p:leading-relaxed prose-p:text-lg prose-p:mb-6">
          {isGenerating && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-blue-600">AI 正在生成文章内容，请稍候...</p>
            </div>
          )}

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-600">{error}</p>
              {isLoggedIn && (
                <button
                  onClick={handleGenerateContent}
                  className="mt-3 px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 transition-colors"
                >
                  重试生成
                </button>
              )}
            </div>
          )}

          {!content && !isGenerating && !error && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
              <div className="mb-4">
                <svg className="w-16 h-16 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">文章内容尚未生成</h3>
              <p className="text-gray-600 mb-4">
                这篇文章的完整内容还没有生成。
                {isLoggedIn ? '点击下方按钮生成内容。' : '请联系管理员生成内容。'}
              </p>
              {isLoggedIn && (
                <button
                  onClick={handleGenerateContent}
                  disabled={isGenerating}
                  className="inline-flex items-center px-6 py-3 bg-purple-600 text-white font-medium rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  生成文章内容
                </button>
              )}
            </div>
          )}

          {content && !isEditing && (
            <>
              {/* 渲染带有数学公式的文章内容 */}
              {renderContentWithMath(removeReferencesFromContent(content))}

              {/* 参考文献部分 */}
              {hasReferences(content) && (
                <div id="references">
                  <ReferencesSection content={content} />
                </div>
              )}

              {/* 专业图片画廊 */}
              {figures.length > 0 && (
                <div id="figures" className="mt-12 mb-8">
                  <div className="flex items-center justify-between mb-2">
                    <h2 className="text-2xl font-bold text-gray-900 font-serif">Figures</h2>
                    {isLoggedIn && (
                      <div className="text-sm text-blue-600 bg-blue-50 px-3 py-1 rounded-lg">
                        💡 管理员：悬停图片可重新生成
                      </div>
                    )}
                  </div>
                  <div className="w-16 h-0.5 bg-purple-600 mb-8"></div>
                  <FigureGallery
                    figures={figures}
                    showAdmin={isLoggedIn}
                    onRegenerate={handleRegenerateFigure}
                  />
                </div>
              )}

              {/* 图片加载状态 */}
              {figuresLoading && (
                <div className="mt-8 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto mb-2"></div>
                  <p className="text-gray-600 text-sm">正在加载图片...</p>
                </div>
              )}
            </>
          )}

          {/* 编辑模式界面 */}
          {isEditing && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">编辑文章内容</h3>
                <div className="text-sm text-gray-500">
                  支持 Markdown 格式 | 图片占位符格式：[FIGURE:数字:描述]
                </div>
              </div>

              <div className="mb-4">
                <textarea
                  value={editingContent}
                  onChange={(e) => setEditingContent(e.target.value)}
                  className="w-full h-96 p-4 border border-gray-300 rounded-md font-mono text-sm resize-vertical focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="在这里编辑文章的 Markdown 内容..."
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  <strong>提示：</strong>
                  <ul className="mt-1 space-y-1">
                    <li>• 使用 **文字** 来加粗</li>
                    <li>• 使用 [FIGURE:1:图片描述] 来插入图片占位符</li>
                    <li>• 使用 [1], [2] 等来引用参考文献</li>
                    <li>• 保存后将自动重新渲染内容</li>
                  </ul>
                </div>

                <div className="flex space-x-3">
                  <button
                    onClick={handleCancelEdit}
                    disabled={isSaving}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    取消
                  </button>
                  <button
                    onClick={handleSaveEdit}
                    disabled={isSaving || !editingContent.trim()}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? '保存中...' : '保存更改'}
                  </button>
                </div>
              </div>
            </div>
          )}


        </div>

            {/* Citation box */}
            <CitationBox article={article} />

            {/* Journal footer */}
            <JournalFooter />

            {/* Post-Publication Peer Review */}
            <div className="mt-8 pt-8 border-t border-gray-200">
              <ArticleComments
                articleId={article.id}
                isLoggedIn={isLoggedIn}
                article={article}
                content={content}
              />
            </div>
          </article>

          {/* Sidebar - only visible on desktop */}
          <aside className="xl:col-span-3 mt-8 xl:mt-0 space-y-6">
            {/* Article Info Card */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Article Information</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Article ID:</span>
                  <span className="ml-2 text-gray-600">#{article.id}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Category:</span>
                  <span className="ml-2 text-purple-600 font-medium">{article.category}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Author:</span>
                  <span className="ml-2 text-gray-600">{article.author}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Views:</span>
                  <span className="ml-2 text-gray-600">{currentArticle.views || 0}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-700">DOI:</span>
                  <span className="ml-2 text-gray-600 text-xs">10.1000/joker.{new Date().getFullYear()}.{article.id}</span>
                </div>
              </div>
            </div>

            {/* PDF Download - Available to all users */}
            {content && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Download</h3>
                <PDFDownloader article={article} content={content} journalIcon={journalIcon} />
              </div>
            )}

            {/* Quick Actions - Admin/Editor only */}
            {isLoggedIn && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {userRole === 'ADMIN' ? 'Admin Actions' : 'Editor Actions'}
                </h3>
                <div className="space-y-3">
                  {!content && userRole === 'ADMIN' && (
                    <button
                      onClick={handleGenerateContent}
                      disabled={isGenerating}
                      className="w-full px-4 py-2 bg-purple-600 text-white text-sm font-medium rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isGenerating ? '生成中...' : '生成文章内容'}
                    </button>
                  )}

                  {/* 编辑按钮 - 管理员和编辑都可以使用 */}
                  {content && canEdit && (
                    <button
                      onClick={handleEditContent}
                      disabled={isEditing || isGenerating}
                      className="w-full px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      📝 编辑文章内容
                    </button>
                  )}

                  {/* 重新生成按钮 - 只有管理员可以使用 */}
                  {content && userRole === 'ADMIN' && (
                    <button
                      onClick={handleRegenerateContent}
                      disabled={isGenerating || isEditing}
                      className="w-full px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isGenerating ? '重新生成中...' : '重新生成文章'}
                    </button>
                  )}
                </div>
              </div>
            )}

            {/* Figure Gallery - if figures exist */}
            {figures.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Figures</h3>
                <FigureGallery
                  figures={figures}
                  showAdmin={isLoggedIn}
                  onRegenerate={isLoggedIn ? handleRegenerateFigure : undefined}
                  compact={true}
                />
              </div>
            )}

            {/* Table of Contents - if content exists */}
            {content && (
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">
                  {isNewsArticle ? '📰 Article Sections' : 'Table of Contents'}
                </h3>
                <nav className="space-y-2 text-sm">
                  <button onClick={() => scrollToSection('abstract')} className={`block text-left w-full text-gray-600 hover:${isNewsArticle ? 'text-red-600' : 'text-purple-600'} transition-colors`}>
                    {isNewsArticle ? 'News Summary' : 'Abstract'}
                  </button>
                  {isNewsArticle ? (
                    <>
                      <button onClick={() => scrollToSection('lead')} className="block text-left w-full text-gray-600 hover:text-red-600 transition-colors">Lead Story</button>
                      <button onClick={() => scrollToSection('details')} className="block text-left w-full text-gray-600 hover:text-red-600 transition-colors">Key Details</button>
                      <button onClick={() => scrollToSection('background')} className="block text-left w-full text-gray-600 hover:text-red-600 transition-colors">Background</button>
                      <button onClick={() => scrollToSection('reactions')} className="block text-left w-full text-gray-600 hover:text-red-600 transition-colors">Expert Reactions</button>
                      <button onClick={() => scrollToSection('impact')} className="block text-left w-full text-gray-600 hover:text-red-600 transition-colors">Impact Analysis</button>
                    </>
                  ) : (
                    <>
                      <button onClick={() => scrollToSection('introduction')} className="block text-left w-full text-gray-600 hover:text-purple-600 transition-colors">Introduction</button>
                      <button onClick={() => scrollToSection('methodology')} className="block text-left w-full text-gray-600 hover:text-purple-600 transition-colors">Methodology</button>
                      <button onClick={() => scrollToSection('results')} className="block text-left w-full text-gray-600 hover:text-purple-600 transition-colors">Results</button>
                      <button onClick={() => scrollToSection('discussion')} className="block text-left w-full text-gray-600 hover:text-purple-600 transition-colors">Discussion</button>
                      <button onClick={() => scrollToSection('conclusion')} className="block text-left w-full text-gray-600 hover:text-purple-600 transition-colors">Conclusion</button>
                    </>
                  )}
                  {figures.length > 0 && (
                    <button onClick={() => scrollToSection('figures')} className={`block text-left w-full text-gray-600 hover:${isNewsArticle ? 'text-red-600' : 'text-purple-600'} transition-colors`}>
                      {isNewsArticle ? 'Photos' : 'Figures'}
                    </button>
                  )}
                  {hasReferences(content || '') && (
                    <button onClick={() => scrollToSection('references')} className={`block text-left w-full text-gray-600 hover:${isNewsArticle ? 'text-red-600' : 'text-purple-600'} transition-colors`}>
                      {isNewsArticle ? 'Sources' : 'References'}
                    </button>
                  )}
                </nav>
              </div>
            )}

            {/* Related Articles */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Articles</h3>
              <div className="space-y-3">
                <div className="border-l-4 border-purple-200 pl-3">
                  <h4 className="text-sm font-medium text-gray-900">The Science of Procrastination</h4>
                  <p className="text-xs text-gray-600 mt-1">A comprehensive study on academic delays</p>
                </div>
                <div className="border-l-4 border-purple-200 pl-3">
                  <h4 className="text-sm font-medium text-gray-900">Coffee Consumption in Research</h4>
                  <p className="text-xs text-gray-600 mt-1">Correlation between caffeine and productivity</p>
                </div>
                <div className="border-l-4 border-purple-200 pl-3">
                  <h4 className="text-sm font-medium text-gray-900">Peer Review Psychology</h4>
                  <p className="text-xs text-gray-600 mt-1">Understanding reviewer behavior patterns</p>
                </div>
              </div>
            </div>


          </aside>
        </div>
      </div>

      {/* 生成文章内容弹窗 */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              生成{isNewsArticle ? '新闻' : '文章'}内容
            </h3>

            <div className="mb-4">
              {isNewsArticle ? (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-red-600">📰</span>
                    <span className="text-sm font-semibold text-red-800">新闻报道模式</span>
                  </div>
                  <p className="text-sm text-red-700">
                    即将为新闻《{article.title}》生成详细报道内容。将使用倒金字塔结构，包含导语、正文和背景信息。
                  </p>
                </div>
              ) : (
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-purple-600">🔬</span>
                    <span className="text-sm font-semibold text-purple-800">学术论文模式</span>
                  </div>
                  <p className="text-sm text-purple-700">
                    即将为文章《{article.title}》生成详细内容。将包含引言、方法、结果和结论等学术章节。
                  </p>
                </div>
              )}
            </div>

            {/* 模式选择 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                生成模式
              </label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setGenerationMode('satirical')}
                  className={`p-4 border-2 rounded-lg text-left transition-all ${
                    generationMode === 'satirical'
                      ? 'border-purple-500 bg-purple-50 text-purple-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">🎭 小丑模式</div>
                  <div className="text-sm text-gray-600 mt-1">
                    生成戏谑、幽默的学术文章，保持严肃的学术语调但讨论荒诞话题
                  </div>
                </button>
                <button
                  onClick={() => setGenerationMode('serious')}
                  className={`p-4 border-2 rounded-lg text-left transition-all ${
                    generationMode === 'serious'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">🎓 严肃模式</div>
                  <div className="text-sm text-gray-600 mt-1">
                    生成严谨、专业的学术文章，符合顶级期刊发表标准
                  </div>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-6">
              <div>
                <label htmlFor="wordCount" className="block text-sm font-medium text-gray-700 mb-2">
                  文字数量 (可选)
                </label>
                <input
                  id="wordCount"
                  type="number"
                  value={wordCount}
                  onChange={(e) => setWordCount(e.target.value)}
                  placeholder="600-800"
                  min="300"
                  max="3000"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
                <p className="mt-1 text-xs text-gray-500">默认: 600-800 词</p>
              </div>

              <div>
                <label htmlFor="figureCount" className="block text-sm font-medium text-gray-700 mb-2">
                  图片数量 (可选)
                </label>
                <input
                  id="figureCount"
                  type="number"
                  value={figureCount}
                  onChange={(e) => setFigureCount(e.target.value)}
                  placeholder="2-3"
                  min="0"
                  max="5"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
                <p className="mt-1 text-xs text-gray-500">默认: 2-3 张</p>
              </div>

              <div>
                <label htmlFor="referenceCount" className="block text-sm font-medium text-gray-700 mb-2">
                  参考文献数量 (可选)
                </label>
                <input
                  id="referenceCount"
                  type="number"
                  value={referenceCount}
                  onChange={(e) => setReferenceCount(e.target.value)}
                  placeholder="3-5"
                  min="1"
                  max="10"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                />
                <p className="mt-1 text-xs text-gray-500">默认: 3-5 篇</p>
              </div>
            </div>

            {/* 补充材料输入框 */}
            <div className="mb-6">
              <label htmlFor="supplementaryMaterial" className="block text-sm font-medium text-gray-700 mb-2">
                补充材料 (可选)
              </label>
              <textarea
                id="supplementaryMaterial"
                value={supplementaryMaterial}
                onChange={(e) => setSupplementaryMaterial(e.target.value)}
                placeholder="对这个课题的补充说明、特殊要求、研究角度、方法论细节等..."
                rows={3}
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
              <p className="mt-1 text-xs text-gray-500">
                🔬 提供额外的背景信息、研究方向或特殊要求，帮助AI更准确地生成符合您期望的内容
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowGenerateModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => executeGenerateContent(wordCount, figureCount, referenceCount, supplementaryMaterial, generationMode)}
                disabled={isGenerating}
                className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? '生成中...' : `开始生成 (${generationMode === 'serious' ? '严肃模式' : '小丑模式'})`}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 重新生成文章内容弹窗 */}
      {showRegenerateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              重新生成{isNewsArticle ? '新闻' : '文章'}内容
            </h3>

            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-4">
                ⚠️ <strong>注意：</strong>重新生成将覆盖现有内容和所有图片。您可以自定义以下参数：
              </p>
            </div>

            {/* 模式选择 */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-3">
                生成模式
              </label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setGenerationMode('satirical')}
                  className={`p-4 border-2 rounded-lg text-left transition-all ${
                    generationMode === 'satirical'
                      ? 'border-orange-500 bg-orange-50 text-orange-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">🎭 小丑模式</div>
                  <div className="text-sm text-gray-600 mt-1">
                    生成戏谑、幽默的学术文章
                  </div>
                </button>
                <button
                  onClick={() => setGenerationMode('serious')}
                  className={`p-4 border-2 rounded-lg text-left transition-all ${
                    generationMode === 'serious'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="font-medium">🎓 严肃模式</div>
                  <div className="text-sm text-gray-600 mt-1">
                    生成严谨、专业的学术文章
                  </div>
                </button>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-4 mb-6">
              <div>
                <label htmlFor="regenWordCount" className="block text-sm font-medium text-gray-700 mb-2">
                  文字数量 (可选)
                </label>
                <input
                  id="regenWordCount"
                  type="number"
                  value={wordCount}
                  onChange={(e) => setWordCount(e.target.value)}
                  placeholder="600-800"
                  min="300"
                  max="3000"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
                <p className="mt-1 text-xs text-gray-500">默认: 600-800 词</p>
              </div>

              <div>
                <label htmlFor="regenFigureCount" className="block text-sm font-medium text-gray-700 mb-2">
                  图片数量 (可选)
                </label>
                <input
                  id="regenFigureCount"
                  type="number"
                  value={figureCount}
                  onChange={(e) => setFigureCount(e.target.value)}
                  placeholder="2-3"
                  min="0"
                  max="5"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
                <p className="mt-1 text-xs text-gray-500">默认: 2-3 张</p>
              </div>

              <div>
                <label htmlFor="regenReferenceCount" className="block text-sm font-medium text-gray-700 mb-2">
                  参考文献数量 (可选)
                </label>
                <input
                  id="regenReferenceCount"
                  type="number"
                  value={referenceCount}
                  onChange={(e) => setReferenceCount(e.target.value)}
                  placeholder="3-5"
                  min="1"
                  max="10"
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                />
                <p className="mt-1 text-xs text-gray-500">默认: 3-5 篇</p>
              </div>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowRegenerateModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => executeRegenerateContent(wordCount, figureCount, referenceCount, generationMode)}
                disabled={isGenerating}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isGenerating ? '重新生成中...' : `确认重新生成 (${generationMode === 'serious' ? '严肃模式' : '小丑模式'})`}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 生成进度弹窗 */}
      {generationProgress.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">正在生成文章内容</h3>

            {/* 进度条 */}
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>{generationProgress.step}</span>
                <span>{generationProgress.current}/{generationProgress.total}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-purple-600 h-2 rounded-full transition-all duration-300 ease-out"
                  style={{ width: `${(generationProgress.current / generationProgress.total) * 100}%` }}
                ></div>
              </div>
            </div>

            {/* 详细信息 */}
            <div className="text-sm text-gray-600 mb-4">
              {generationProgress.details}
            </div>

            {/* 动画图标 */}
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>

            <div className="mt-4 text-xs text-gray-500 text-center">
              请耐心等待，AI正在为您创作精彩内容...
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
