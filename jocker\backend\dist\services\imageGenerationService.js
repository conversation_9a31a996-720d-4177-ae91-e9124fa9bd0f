"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateImagesForArticle = exports.generateImageWithGemini = exports.updateFigureStatus = exports.DEFAULT_CONFIG = void 0;
const database_1 = __importDefault(require("../config/database"));
exports.DEFAULT_CONFIG = {
    aspectRatio: '1:1',
    quality: 'standard',
};
const updateFigureStatus = async (figureId, status, imageUrl, errorMessage) => {
    try {
        await database_1.default.figure.update({
            where: { id: figureId },
            data: {
                status,
                imageUrl: imageUrl || undefined,
                errorMsg: errorMessage || undefined,
                updatedAt: new Date(),
            },
        });
    }
    catch (error) {
        console.error(`更新图片状态失败 (ID: ${figureId}):`, error);
    }
};
exports.updateFigureStatus = updateFigureStatus;
const generateImageWithGemini = async (prompt, config = exports.DEFAULT_CONFIG) => {
    throw new Error('图片生成功能已迁移到前端，请使用前端的图片生成功能');
};
exports.generateImageWithGemini = generateImageWithGemini;
const generateImagesForArticle = async (articleId, prompts) => {
    throw new Error('图片生成功能已迁移到前端，请使用前端的图片生成功能');
};
exports.generateImagesForArticle = generateImagesForArticle;
//# sourceMappingURL=imageGenerationService.js.map