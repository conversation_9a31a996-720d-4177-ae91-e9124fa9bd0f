import { Router } from 'express';
import {
  getAdminArticleList,
  getA<PERSON>pi<PERSON>ey,
  generateAndSaveArticleContent,
  saveArticleContent,
  getArticleFiguresList,
  regenerateFigureImage,
  saveFigure,
  clearArticleFigures,
  uploadFounderAvatar,
  getFounderAvatar,
  uploadWebsiteCover,
  getWebsiteCover,
  uploadAdvertisementCover,
  getAdvertisementCover,
  setGeminiKey,
  getGeminiKey,
  setAIConfig,
  getAIConfig,
  getAIConfigForClient,
  changePassword,
  setJournalIcon,
  getJournalIcon,
  getAllUsers,
  updateUserStatus,
  updateUserRole,
  deleteUser,
  uploadMerchandiseImage,
  getMerchandiseImages
} from '../controllers/adminController';
import {
  generateAIComments,
  deleteComment,
  batchDeleteComments
} from '../controllers/commentController';
import { authenticateToken, requireAdmin } from '../middleware/auth';

const router = Router();

/**
 * @route   GET /api/admin/articles
 * @desc    获取文章列表（管理员专用，包含content字段）
 * @access  Private (Admin only)
 */
router.get('/articles', authenticateToken, requireAdmin, getAdminArticleList);

/**
 * @route   GET /api/admin/ai-key
 * @desc    获取 Google AI API Key（仅管理员）
 * @access  Private (Admin only)
 */
router.get('/ai-key', authenticateToken, requireAdmin, getAIApiKey);

/**
 * @route   POST /api/admin/articles/:id/generate-content
 * @desc    生成并保存文章内容（仅管理员）
 * @access  Private (Admin only)
 */
router.post('/articles/:id/generate-content', authenticateToken, requireAdmin, generateAndSaveArticleContent);

/**
 * @route   PUT /api/admin/articles/:id/content
 * @desc    保存文章内容（仅管理员）
 * @access  Private (Admin only)
 */
router.put('/articles/:id/content', authenticateToken, requireAdmin, saveArticleContent);

/**
 * @route   GET /api/admin/articles/:id/figures
 * @desc    获取文章图片列表（仅管理员）
 * @access  Private (Admin only)
 */
router.get('/articles/:id/figures', authenticateToken, requireAdmin, getArticleFiguresList);

/**
 * @route   POST /api/admin/figures/:id/regenerate
 * @desc    重新生成图片（仅管理员）
 * @access  Private (Admin only)
 */
router.post('/figures/:id/regenerate', authenticateToken, requireAdmin, regenerateFigureImage);

/**
 * @route   POST /api/admin/figures
 * @desc    保存图片（仅管理员）
 * @access  Private (Admin only)
 */
router.post('/figures', authenticateToken, requireAdmin, saveFigure);

/**
 * @route   DELETE /api/admin/articles/:id/figures
 * @desc    清理文章的所有图片记录（仅管理员）
 * @access  Private (Admin only)
 */
router.delete('/articles/:id/figures', authenticateToken, requireAdmin, clearArticleFigures);

/**
 * @route   POST /api/admin/founder-avatar
 * @desc    上传创始人头像（仅管理员）
 * @access  Private (Admin only)
 */
router.post('/founder-avatar', authenticateToken, requireAdmin, uploadFounderAvatar);

/**
 * @route   GET /api/admin/founder-avatar
 * @desc    获取创始人头像（所有人可访问）
 * @access  Public
 */
router.get('/founder-avatar', getFounderAvatar);

/**
 * @route   POST /api/admin/website-cover
 * @desc    上传网站封面（仅管理员）
 * @access  Private (Admin only)
 */
router.post('/website-cover', authenticateToken, requireAdmin, uploadWebsiteCover);

/**
 * @route   GET /api/admin/website-cover
 * @desc    获取网站封面（所有人可访问）
 * @access  Public
 */
router.get('/website-cover', getWebsiteCover);

/**
 * @route   GET /api/admin/users
 * @desc    获取所有用户列表（仅管理员）
 * @access  Private (Admin only)
 */
router.get('/users', authenticateToken, requireAdmin, getAllUsers);

/**
 * @route   PUT /api/admin/users/:id/status
 * @desc    更新用户状态（启用/禁用）（仅管理员）
 * @access  Private (Admin only)
 */
router.put('/users/:id/status', authenticateToken, requireAdmin, updateUserStatus);

/**
 * @route   PUT /api/admin/users/:id/role
 * @desc    更新用户角色（仅管理员）
 * @access  Private (Admin only)
 */
router.put('/users/:id/role', authenticateToken, requireAdmin, updateUserRole);

/**
 * @route   DELETE /api/admin/users/:id
 * @desc    删除用户（仅管理员）
 * @access  Private (Admin only)
 */
router.delete('/users/:id', authenticateToken, requireAdmin, deleteUser);

/**
 * @route   POST /api/admin/advertisement-cover
 * @desc    上传广告封面（仅管理员）
 * @access  Private (Admin only)
 */
router.post('/advertisement-cover', authenticateToken, requireAdmin, uploadAdvertisementCover);

/**
 * @route   GET /api/admin/advertisement-cover
 * @desc    获取广告封面（所有人可访问）
 * @access  Public
 */
router.get('/advertisement-cover', getAdvertisementCover);

/**
 * @route   POST /api/admin/ai-config
 * @desc    设置AI API配置（仅管理员）
 * @access  Private (Admin only)
 */
router.post('/ai-config', authenticateToken, requireAdmin, setAIConfig);

/**
 * @route   GET /api/admin/ai-config
 * @desc    获取AI API配置（仅管理员）
 * @access  Private (Admin only)
 */
router.get('/ai-config', authenticateToken, requireAdmin, getAIConfig);

/**
 * @route   GET /api/admin/ai-config-for-client
 * @desc    获取AI API配置用于前端调用（包含完整API Key）
 * @access  Private (Admin only)
 */
router.get('/ai-config-for-client', authenticateToken, requireAdmin, getAIConfigForClient);

/**
 * @route   POST /api/admin/change-password
 * @desc    更改用户密码
 * @access  Private (Admin only)
 */
router.post('/change-password', authenticateToken, requireAdmin, changePassword);

/**
 * @route   POST /api/admin/journal-icon
 * @desc    设置期刊图标
 * @access  Private (Admin only)
 */
router.post('/journal-icon', authenticateToken, requireAdmin, setJournalIcon);

/**
 * @route   GET /api/admin/journal-icon
 * @desc    获取期刊图标
 * @access  Public
 */
router.get('/journal-icon', getJournalIcon);

/**
 * @route   POST /api/admin/gemini-key
 * @desc    设置Gemini API Key（仅管理员）- 向后兼容
 * @access  Private (Admin only)
 */
router.post('/gemini-key', authenticateToken, requireAdmin, setGeminiKey);

/**
 * @route   GET /api/admin/gemini-key
 * @desc    获取Gemini API Key配置（仅管理员）- 向后兼容
 * @access  Private (Admin only)
 */
router.get('/gemini-key', authenticateToken, requireAdmin, getGeminiKey);

/**
 * @route   POST /api/admin/articles/:id/generate-comments
 * @desc    生成AI评论
 * @access  Private (Admin only)
 */
router.post('/articles/:id/generate-comments', authenticateToken, requireAdmin, generateAIComments);

/**
 * @route   DELETE /api/admin/comments/batch
 * @desc    批量删除评论
 * @access  Private (Admin only)
 */
router.delete('/comments/batch', authenticateToken, requireAdmin, batchDeleteComments);

/**
 * @route   DELETE /api/admin/comments/:id
 * @desc    删除评论
 * @access  Private (Admin only)
 */
router.delete('/comments/:id', authenticateToken, requireAdmin, deleteComment);

/**
 * @route   POST /api/admin/merchandise-image
 * @desc    上传周边商品图片
 * @access  Private (Admin only)
 */
router.post('/merchandise-image', authenticateToken, requireAdmin, uploadMerchandiseImage);

/**
 * @route   GET /api/admin/merchandise-images
 * @desc    获取周边商品图片
 * @access  Public
 */
router.get('/merchandise-images', getMerchandiseImages);

export default router;
