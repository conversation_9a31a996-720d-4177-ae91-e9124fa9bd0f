import React, { useState, useEffect } from 'react';
import { adminApi } from '../src/services/api';
import { aiService } from '../src/services/aiService';

interface AIConfigManagerProps {
  onUpdateSuccess: (message: string) => void;
  onUpdateError: (error: string) => void;
}

interface AIConfig {
  id: string;
  provider: 'gemini' | 'openai';
  baseUrl?: string;
  isActive: boolean;
  textModel: string;
  imageModel: string;
  updatedAt: string;
  apiKeyPreview: string;
}

export const AIConfigManager: React.FC<AIConfigManagerProps> = ({
  onUpdateSuccess,
  onUpdateError
}) => {
  const [provider, setProvider] = useState<'gemini' | 'openai'>('gemini');
  const [apiKey, setApiKey] = useState('');
  const [baseUrl, setBaseUrl] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [textModel, setTextModel] = useState('gemini-2.0-flash-exp');
  const [imageModel, setImageModel] = useState('imagen-3.0-generate-001');
  const [isLoading, setIsLoading] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<AIConfig | null>(null);
  const [showKey, setShowKey] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string; details?: any } | null>(null);

  // 加载当前配置
  useEffect(() => {
    loadCurrentConfig();
  }, []);

  // 当供应商改变时，更新默认模型
  useEffect(() => {
    if (provider === 'gemini') {
      setTextModel('gemini-2.0-flash-exp');
      setImageModel('imagen-3.0-generate-001');
      setBaseUrl('');
    } else if (provider === 'openai') {
      setTextModel('gpt-4o');
      setImageModel('dall-e-3');
      setBaseUrl('https://api.openai.com/v1');
    }
  }, [provider]);

  const loadCurrentConfig = async () => {
    try {
      const result = await adminApi.getAIConfig();
      setCurrentConfig(result.config);
      if (result.config) {
        setProvider(result.config.provider);
        setBaseUrl(result.config.baseUrl || '');
        setIsActive(result.config.isActive);
        setTextModel(result.config.textModel);
        setImageModel(result.config.imageModel);
      }
    } catch (error) {
      console.error('加载AI配置失败:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!apiKey.trim()) {
      onUpdateError('请输入API Key');
      return;
    }

    if (!textModel.trim()) {
      onUpdateError('请输入文本生成模型');
      return;
    }

    if (!imageModel.trim()) {
      onUpdateError('请输入图片生成模型');
      return;
    }

    if (provider === 'openai' && !baseUrl.trim()) {
      onUpdateError('OpenAI格式需要提供基础URL');
      return;
    }

    setIsLoading(true);

    try {
      const result = await adminApi.setAIConfig({
        provider,
        apiKey: apiKey.trim(),
        baseUrl: provider === 'openai' ? baseUrl.trim() : undefined,
        isActive,
        textModel: textModel.trim(),
        imageModel: imageModel.trim()
      });

      onUpdateSuccess('AI API配置设置成功！');
      setCurrentConfig(result.config);
      setApiKey(''); // 清空输入框
      
    } catch (error) {
      console.error('设置AI配置失败:', error);
      onUpdateError(error instanceof Error ? error.message : '设置AI配置失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleActive = async () => {
    if (!currentConfig) return;

    setIsLoading(true);
    try {
      const result = await adminApi.setAIConfig({
        provider: currentConfig.provider,
        apiKey: currentConfig.apiKeyPreview.replace('...', ''), // 这里需要完整的key，实际应该从服务器获取
        baseUrl: currentConfig.baseUrl,
        isActive: !isActive,
        textModel: currentConfig.textModel,
        imageModel: currentConfig.imageModel
      });

      setIsActive(!isActive);
      setCurrentConfig(result.config);
      onUpdateSuccess(`AI配置已${!isActive ? '启用' : '禁用'}`);

    } catch (error) {
      console.error('切换AI配置状态失败:', error);
      onUpdateError('切换状态失败');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestConnection = async () => {
    if (!currentConfig) {
      setTestResult({
        success: false,
        message: '请先保存配置后再测试'
      });
      return;
    }

    setIsTesting(true);
    setTestResult(null);

    try {
      // 重置AI服务配置，确保使用最新配置
      aiService.resetConfig();

      const result = await aiService.testConnection();
      setTestResult(result);

      if (result.success) {
        onUpdateSuccess(result.message);
      } else {
        onUpdateError(result.message);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '测试连接失败';
      setTestResult({
        success: false,
        message: errorMessage,
        details: { error }
      });
      onUpdateError(errorMessage);
    } finally {
      setIsTesting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h4 className="text-lg font-medium text-gray-900 mb-4">🤖 AI API 配置管理</h4>
        <p className="text-sm text-gray-600 mb-4">
          设置自定义的AI API配置。支持Gemini和OpenAI格式的API。当设置了自定义配置时，系统将优先使用您提供的配置而不是服务器默认配置。
        </p>
      </div>

      {/* 当前配置状态 */}
      {currentConfig && (
        <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
          <h5 className="text-sm font-medium text-gray-900 mb-2">当前配置</h5>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-gray-600">供应商:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                currentConfig.provider === 'gemini' 
                  ? 'bg-blue-100 text-blue-800' 
                  : 'bg-green-100 text-green-800'
              }`}>
                {currentConfig.provider === 'gemini' ? 'Google Gemini' : 'OpenAI'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">API Key:</span>
              <span className="font-mono text-gray-900">{currentConfig.apiKeyPreview}</span>
            </div>
            {currentConfig.baseUrl && (
              <div className="flex items-center justify-between">
                <span className="text-gray-600">基础URL:</span>
                <span className="font-mono text-gray-900 text-xs">{currentConfig.baseUrl}</span>
              </div>
            )}
            <div className="flex items-center justify-between">
              <span className="text-gray-600">状态:</span>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                currentConfig.isActive 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {currentConfig.isActive ? '已启用' : '已禁用'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">文本模型:</span>
              <span className="font-mono text-gray-900 text-sm">{currentConfig.textModel}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">图片模型:</span>
              <span className="font-mono text-gray-900 text-sm">{currentConfig.imageModel}</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-600">更新时间:</span>
              <span className="text-gray-900">
                {new Date(currentConfig.updatedAt).toLocaleString('zh-CN')}
              </span>
            </div>
          </div>
          
          {/* 启用/禁用和测试按钮 */}
          <div className="mt-3 pt-3 border-t border-gray-200 flex gap-2">
            <button
              onClick={handleToggleActive}
              disabled={isLoading}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                currentConfig.isActive
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
              } disabled:opacity-50 disabled:cursor-not-allowed`}
            >
              {isLoading ? '处理中...' : (currentConfig.isActive ? '禁用' : '启用')}
            </button>

            <button
              onClick={handleTestConnection}
              disabled={isTesting || !currentConfig.isActive}
              className="px-3 py-1 rounded-md text-sm font-medium transition-colors bg-blue-100 text-blue-700 hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isTesting ? '测试中...' : '🧪 测试连接'}
            </button>
          </div>
        </div>
      )}

      {/* 测试结果显示 */}
      {testResult && (
        <div className={`rounded-lg p-4 border ${
          testResult.success
            ? 'bg-green-50 border-green-200'
            : 'bg-red-50 border-red-200'
        }`}>
          <div className="flex items-start">
            <div className={`flex-shrink-0 ${
              testResult.success ? 'text-green-400' : 'text-red-400'
            }`}>
              {testResult.success ? (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <h3 className={`text-sm font-medium ${
                testResult.success ? 'text-green-800' : 'text-red-800'
              }`}>
                {testResult.success ? '✅ 连接测试成功' : '❌ 连接测试失败'}
              </h3>
              <div className={`mt-2 text-sm ${
                testResult.success ? 'text-green-700' : 'text-red-700'
              }`}>
                <p>{testResult.message}</p>
                {testResult.details && (
                  <details className="mt-2">
                    <summary className="cursor-pointer font-medium">查看详细信息</summary>
                    <pre className="mt-1 text-xs bg-white bg-opacity-50 p-2 rounded border overflow-auto">
                      {JSON.stringify(testResult.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 设置新的AI配置 */}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {currentConfig ? '更新' : '设置'} AI API配置
          </label>
        </div>

        {/* 供应商选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            AI 供应商
          </label>
          <select
            value={provider}
            onChange={(e) => setProvider(e.target.value as 'gemini' | 'openai')}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="gemini">Google Gemini</option>
            <option value="openai">OpenAI 格式</option>
          </select>
          <p className="text-xs text-gray-500 mt-1">
            选择您要使用的AI服务供应商
          </p>
        </div>

        {/* API Key输入 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            API Key
          </label>
          <div className="relative">
            <input
              type={showKey ? 'text' : 'password'}
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder={provider === 'gemini' ? '输入您的Gemini API Key (例如: AIzaSy...)' : '输入您的OpenAI API Key (例如: sk-...)'}
              className="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
            <button
              type="button"
              onClick={() => setShowKey(!showKey)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
            >
              {showKey ? (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                </svg>
              ) : (
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              )}
            </button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {provider === 'gemini' ? (
              <>💡 您可以在 <a href="https://aistudio.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">Google AI Studio</a> 获取免费的API Key</>
            ) : (
              <>💡 您可以在 <a href="https://platform.openai.com/api-keys" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">OpenAI Platform</a> 获取API Key，或使用兼容OpenAI格式的第三方API</>
            )}
          </p>
        </div>

        {/* OpenAI基础URL */}
        {provider === 'openai' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              基础URL
            </label>
            <input
              type="text"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              placeholder="例如: https://api.openai.com/v1"
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              OpenAI API的基础URL，支持第三方兼容API
            </p>
          </div>
        )}

        {/* 模型选择 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              文本生成模型
            </label>
            <input
              type="text"
              value={textModel}
              onChange={(e) => setTextModel(e.target.value)}
              placeholder={provider === 'gemini' ? '例如: gemini-2.0-flash-exp' : '例如: gpt-4o'}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              用于文章内容生成的模型
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              图片生成模型
            </label>
            <input
              type="text"
              value={imageModel}
              onChange={(e) => setImageModel(e.target.value)}
              placeholder={provider === 'gemini' ? '例如: imagen-3.0-generate-001' : '例如: dall-e-3'}
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              用于图片生成的模型
            </p>
          </div>
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="isActive"
            checked={isActive}
            onChange={(e) => setIsActive(e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
            立即启用此配置
          </label>
        </div>

        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isLoading || !apiKey.trim() || !textModel.trim() || !imageModel.trim() || (provider === 'openai' && !baseUrl.trim())}
            className={`px-4 py-2 rounded-md font-medium transition-colors ${
              isLoading || !apiKey.trim() || !textModel.trim() || !imageModel.trim() || (provider === 'openai' && !baseUrl.trim())
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isLoading ? '设置中...' : (currentConfig ? '更新配置' : '设置配置')}
          </button>
        </div>
      </form>

      {/* 使用说明 */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
        <h5 className="text-sm font-medium text-blue-900 mb-2">📋 使用说明</h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• 支持Google Gemini和OpenAI格式的API</li>
          <li>• 设置自定义配置后，系统将优先使用您的配置进行AI生成</li>
          <li>• 文本模型用于生成文章内容，图片模型用于生成文章配图</li>
          <li>• OpenAI格式支持第三方兼容API（如Claude、通义千问等）</li>
          <li>• 如果配置失效，系统会自动回退到服务器默认配置</li>
          <li>• 您可以随时禁用自定义配置，系统将使用服务器默认设置</li>
          <li>• 所有配置会安全存储，只有管理员可以查看和修改</li>
        </ul>
      </div>
    </div>
  );
};
