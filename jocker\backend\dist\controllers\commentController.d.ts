import { Request, Response } from 'express';
export declare const getArticleComments: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const submitComment: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const deleteComment: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const batchDeleteComments: (req: Request, res: Response, next: import("express").NextFunction) => void;
export declare const generateAIComments: (req: Request, res: Response, next: import("express").NextFunction) => void;
//# sourceMappingURL=commentController.d.ts.map