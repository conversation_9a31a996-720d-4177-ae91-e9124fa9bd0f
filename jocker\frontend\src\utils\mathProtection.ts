/**
 * 数学公式保护工具
 * 在内容处理过程中保护LaTeX公式不被破坏
 */

interface MathBlock {
  id: string;
  content: string;
  type: 'display' | 'inline';
  placeholder: string;
}

class MathProtector {
  private mathBlocks: Map<string, MathBlock> = new Map();
  private counter = 0;

  /**
   * 提取并保护数学公式
   */
  protect(content: string): string {
    this.mathBlocks.clear();
    this.counter = 0;

    let protectedContent = content;

    // 1. 保护块级公式 $$...$$
    protectedContent = this.protectPattern(
      protectedContent,
      /\$\$([\s\S]*?)\$\$/g,
      'display'
    );

    // 2. 保护块级公式 \[...\]
    protectedContent = this.protectPattern(
      protectedContent,
      /\\\[([\s\S]*?)\\\]/g,
      'display'
    );

    // 3. 保护行内公式 $...$（但要避免误匹配）
    protectedContent = this.protectInlineMath(protectedContent);

    // 4. 保护行内公式 \(...\)
    protectedContent = this.protectPattern(
      protectedContent,
      /\\\(([\s\S]*?)\\\)/g,
      'inline'
    );

    console.log(`🛡️ 保护了 ${this.mathBlocks.size} 个数学公式`);
    return protectedContent;
  }

  /**
   * 恢复被保护的数学公式
   */
  restore(content: string): string {
    let restoredContent = content;

    // 按照ID顺序恢复所有数学公式
    for (const [id, mathBlock] of this.mathBlocks) {
      restoredContent = restoredContent.replace(
        mathBlock.placeholder,
        mathBlock.content
      );
    }

    console.log(`🔓 恢复了 ${this.mathBlocks.size} 个数学公式`);
    return restoredContent;
  }

  /**
   * 获取所有保护的数学块（用于调试）
   */
  getProtectedBlocks(): MathBlock[] {
    return Array.from(this.mathBlocks.values());
  }

  /**
   * 清理保护的数学块
   */
  clear(): void {
    this.mathBlocks.clear();
    this.counter = 0;
  }

  private protectPattern(
    content: string,
    pattern: RegExp,
    type: 'display' | 'inline'
  ): string {
    return content.replace(pattern, (match, mathContent) => {
      const id = `MATH_BLOCK_${this.counter++}`;
      const placeholder = `<!--${id}-->`;
      
      const mathBlock: MathBlock = {
        id,
        content: match, // 保存完整的原始内容，包括定界符
        type,
        placeholder
      };

      this.mathBlocks.set(id, mathBlock);
      return placeholder;
    });
  }

  private protectInlineMath(content: string): string {
    // 更智能的行内数学公式检测，避免误匹配
    // 匹配 $...$ 但排除一些常见的非数学用法
    const inlineMathPattern = /\$([^$\n]+?)\$/g;
    
    return content.replace(inlineMathPattern, (match, mathContent) => {
      // 简单的启发式检查：如果包含数学符号，则认为是数学公式
      const hasMathSymbols = /[\\{}^_=+\-*/()[\]|<>≤≥∞∑∏∫∂∇α-ωΑ-Ω]/.test(mathContent);
      const hasCommands = /\\[a-zA-Z]+/.test(mathContent);
      
      if (hasMathSymbols || hasCommands) {
        const id = `MATH_BLOCK_${this.counter++}`;
        const placeholder = `<!--${id}-->`;
        
        const mathBlock: MathBlock = {
          id,
          content: match,
          type: 'inline',
          placeholder
        };

        this.mathBlocks.set(id, mathBlock);
        return placeholder;
      }

      // 不是数学公式，保持原样
      return match;
    });
  }
}

// 导出单例实例
export const mathProtector = new MathProtector();

/**
 * 便捷函数：保护数学公式
 */
export function protectMath(content: string): string {
  return mathProtector.protect(content);
}

/**
 * 便捷函数：恢复数学公式
 */
export function restoreMath(content: string): string {
  return mathProtector.restore(content);
}

/**
 * 便捷函数：清理保护状态
 */
export function clearMathProtection(): void {
  mathProtector.clear();
}

/**
 * 检查内容中是否包含数学公式
 */
export function hasMathContent(content: string): boolean {
  const patterns = [
    /\$\$[\s\S]*?\$\$/,  // 块级公式 $$...$$
    /\\\[[\s\S]*?\\\]/,  // 块级公式 \[...\]
    /\$[^$\n]+?\$/,      // 行内公式 $...$
    /\\\([\s\S]*?\\\)/   // 行内公式 \(...\)
  ];

  return patterns.some(pattern => pattern.test(content));
}

/**
 * 获取内容中的数学公式统计
 */
export function getMathStats(content: string): {
  displayMath: number;
  inlineMath: number;
  total: number;
} {
  const displayMath = (content.match(/\$\$[\s\S]*?\$\$|\\\[[\s\S]*?\\\]/g) || []).length;
  const inlineMath = (content.match(/\$[^$\n]+?\$|\\\([\s\S]*?\\\)/g) || []).length;
  
  return {
    displayMath,
    inlineMath,
    total: displayMath + inlineMath
  };
}
