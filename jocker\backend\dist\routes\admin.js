"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const adminController_1 = require("../controllers/adminController");
const auth_1 = require("../middleware/auth");
const router = (0, express_1.Router)();
router.get('/articles', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.getAdminArticleList);
router.get('/ai-key', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.getAIApiKey);
router.post('/articles/:id/generate-content', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.generateAndSaveArticleContent);
router.put('/articles/:id/content', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.saveArticleContent);
router.get('/articles/:id/figures', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.getArticleFiguresList);
router.post('/figures/:id/regenerate', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.regenerateFigureImage);
router.post('/figures', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.saveFigure);
router.delete('/articles/:id/figures', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.clearArticleFigures);
router.post('/founder-avatar', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.uploadFounderAvatar);
router.get('/founder-avatar', adminController_1.getFounderAvatar);
router.post('/website-cover', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.uploadWebsiteCover);
router.get('/website-cover', adminController_1.getWebsiteCover);
router.get('/users', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.getAllUsers);
router.put('/users/:id/status', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.updateUserStatus);
router.put('/users/:id/role', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.updateUserRole);
router.delete('/users/:id', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.deleteUser);
router.post('/advertisement-cover', auth_1.authenticateToken, auth_1.requireAdmin, adminController_1.uploadAdvertisementCover);
router.get('/advertisement-cover', adminController_1.getAdvertisementCover);
exports.default = router;
//# sourceMappingURL=admin.js.map