"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.generatePeerReview = exports.generateArticleContent = exports.generateArticleContentWithFigures = exports.generateContent = exports.generateReview = exports.generateSummary = exports.generateArticles = void 0;
const database_1 = __importDefault(require("../config/database"));
const logAIGeneration = async (type, prompt, response, success, errorMsg, duration) => {
    try {
        await database_1.default.aIGenerationLog.create({
            data: {
                type,
                prompt,
                response,
                success,
                errorMsg,
                duration,
            },
        });
    }
    catch (error) {
        console.error('记录 AI 生成日志失败:', error);
    }
};
const generateArticles = async (request) => {
    throw new Error('文章生成功能已迁移到前端，请使用前端的文章生成功能');
};
exports.generateArticles = generateArticles;
const generateSummary = async (articleId) => {
    throw new Error('文章摘要生成功能已迁移到前端，请使用前端的文章生成功能');
};
exports.generateSummary = generateSummary;
const generateReview = async (request) => {
    throw new Error('文章评审生成功能已迁移到前端，请使用前端的文章生成功能');
};
exports.generateReview = generateReview;
const generateContent = async (article) => {
    throw new Error('文章内容生成功能已迁移到前端，请使用前端的文章生成功能');
};
exports.generateContent = generateContent;
const generateArticleContentWithFigures = async (article) => {
    throw new Error('文章内容生成功能已迁移到前端，请使用前端的文章生成功能');
};
exports.generateArticleContentWithFigures = generateArticleContentWithFigures;
const generateArticleContent = async (request) => {
    throw new Error('文章内容生成功能已迁移到前端，请使用前端的文章生成功能');
};
exports.generateArticleContent = generateArticleContent;
const generatePeerReview = async (author, title, abstract) => {
    throw new Error('同行评议生成功能已迁移到前端，请使用前端的文章生成功能');
};
exports.generatePeerReview = generatePeerReview;
//# sourceMappingURL=aiService.js.map