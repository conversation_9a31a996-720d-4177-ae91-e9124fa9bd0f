import{r as ya,g as xa}from"./react-vendor-DJG_os-6.js";var mn={exports:{}},et={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Si;function va(){if(Si)return et;Si=1;var t=Symbol.for("react.transitional.element"),e=Symbol.for("react.fragment");function n(i,s,a){var l=null;if(a!==void 0&&(l=""+a),s.key!==void 0&&(l=""+s.key),"key"in s){a={};for(var u in s)u!=="key"&&(a[u]=s[u])}else a=s;return s=a.ref,{$$typeof:t,type:i,key:l,ref:s!==void 0?s:null,props:a}}return et.Fragment=e,et.jsx=n,et.jsxs=n,et}var wi;function Ca(){return wi||(wi=1,mn.exports=va()),mn.exports}var f=Ca(),P=ya();const Yy=xa(P),Te="http://47.79.90.239/api";console.log("API_BASE_URL:",Te);console.log("VITE_API_BASE_URL:","http://47.79.90.239/api");async function se(t,e={}){const n=`${Te}${t}`,s={...{headers:{"Content-Type":"application/json"}},...e};try{const l=await(await fetch(n,s)).json();if(!l.success)throw new Error(l.message||"请求失败");return l.data}catch(a){throw console.error("API 请求失败:",a),a}}async function G(t,e={}){const n=localStorage.getItem("jocker_admin_token");if(!n)throw new Error("未找到认证 Token，请先登录");const i=`${Te}${t}`,a={...{headers:{"Content-Type":"application/json",Authorization:`Bearer ${n}`}},...e};try{const u=await(await fetch(i,a)).json();if(!u.success)throw new Error(u.message||"请求失败");return u.data}catch(l){throw console.error("认证请求失败:",l),l}}const wn={getArticles:async t=>{const e=new URLSearchParams;t&&Object.entries(t).forEach(([i,s])=>{s!==void 0&&e.append(i,String(s))});const n=e.toString();return se(`/articles${n?`?${n}`:""}`)},getArticle:async t=>se(`/articles/${t}`),getTrending:async(t=5)=>se(`/articles/trending?limit=${t}`),getFeatured:async(t=3)=>se(`/articles/featured?limit=${t}`),likeArticle:async t=>se(`/articles/${t}/like`,{method:"POST"}),createArticle:async t=>se("/articles",{method:"POST",body:JSON.stringify(t)}),updateArticle:async(t,e)=>se(`/articles/${t}`,{method:"PUT",body:JSON.stringify(e)}),deleteArticle:async t=>se(`/articles/${t}`,{method:"DELETE"}),getComments:async t=>se(`/articles/${t}/comments`),submitComment:async(t,e)=>G(`/articles/${t}/comments`,{method:"POST",body:JSON.stringify(e)})},_a={generateReview:async t=>se("/ai/generate-review",{method:"POST",body:JSON.stringify(t)}),testConnection:async()=>se("/ai/test-connection",{method:"GET"})},An={register:async t=>se("/auth/register",{method:"POST",body:JSON.stringify(t)}),login:async t=>se("/auth/login",{method:"POST",body:JSON.stringify(t)}),verifyToken:async()=>G("/auth/verify"),getProfile:async()=>G("/auth/profile")},oe={getArticles:async t=>{const e=new URLSearchParams;t&&Object.entries(t).forEach(([i,s])=>{s!==void 0&&e.append(i,String(s))});const n=e.toString();return G(`/admin/articles${n?`?${n}`:""}`)},getAIApiKey:async()=>G("/admin/ai-key"),saveArticleContent:async(t,e)=>G(`/admin/articles/${t}/content`,{method:"PUT",body:JSON.stringify({content:e})}),getArticleFigures:async t=>G(`/admin/articles/${t}/figures`),regenerateFigure:async t=>G(`/admin/figures/${t}/regenerate`,{method:"POST"}),saveFigure:async t=>G("/admin/figures",{method:"POST",body:JSON.stringify(t)}),clearArticleFigures:async t=>G(`/admin/articles/${t}/figures`,{method:"DELETE"}),uploadFounderAvatar:async t=>G("/admin/founder-avatar",{method:"POST",body:JSON.stringify(t)}),getFounderAvatar:async()=>se("/admin/founder-avatar"),uploadAdvertisementCover:async t=>G("/admin/advertisement-cover",{method:"POST",body:JSON.stringify(t)}),getAdvertisementCover:async()=>se("/admin/advertisement-cover"),setAIConfig:async t=>G("/admin/ai-config",{method:"POST",body:JSON.stringify(t)}),getAIConfig:async()=>G("/admin/ai-config"),getAIConfigForClient:async()=>G("/admin/ai-config-for-client"),changePassword:async t=>G("/admin/change-password",{method:"POST",body:JSON.stringify(t)}),setGeminiKey:async t=>G("/admin/gemini-key",{method:"POST",body:JSON.stringify(t)}),getGeminiKey:async()=>G("/admin/gemini-key"),getAllUsers:async()=>G("/admin/users"),updateUserStatus:async(t,e)=>G(`/admin/users/${t}/status`,{method:"PUT",body:JSON.stringify({status:e})}),updateUserRole:async(t,e)=>G(`/admin/users/${t}/role`,{method:"PUT",body:JSON.stringify({role:e})}),deleteUser:async t=>G(`/admin/users/${t}`,{method:"DELETE"}),batchDeleteArticles:async t=>G("/articles/batch",{method:"DELETE",body:JSON.stringify({articleIds:t})}),updateArticleViews:async(t,e)=>G(`/articles/${t}/views`,{method:"PUT",body:JSON.stringify({views:e})}),setJournalIcon:async t=>G("/admin/journal-icon",{method:"POST",body:JSON.stringify({icon:t})}),getJournalIcon:async()=>{const e=await(await fetch(`${Te}/admin/journal-icon`)).json();if(!e.success)throw new Error(e.message||"获取期刊图标失败");return e.data},uploadMerchandiseImage:async t=>G("/admin/merchandise-image",{method:"POST",body:JSON.stringify(t)}),getMerchandiseImages:async()=>{const e=await(await fetch(`${Te}/admin/merchandise-images`)).json();if(!e.success)throw new Error(e.message||"获取周边商品图片失败");return e.data}},Ta={getArticleAudio:async t=>{const n=await(await fetch(`${Te}/audio/articles/${t}`)).json();if(!n.success)throw new Error(n.message||"获取音频信息失败");return n.data},deleteArticleAudio:async t=>G(`/audio/articles/${t}`,{method:"DELETE"}),updateArticleAudio:async(t,e)=>G(`/audio/articles/${t}`,{method:"PUT",body:JSON.stringify(e)})},kt={getSystemStats:async()=>G("/system/stats"),getStorageAnalysis:async()=>G("/system/storage"),cleanupStorage:async()=>G("/system/cleanup",{method:"POST"})},qe={getSystemLogs:async t=>{const e=new URLSearchParams;t!=null&&t.page&&e.append("page",t.page.toString()),t!=null&&t.limit&&e.append("limit",t.limit.toString()),t!=null&&t.type&&e.append("type",t.type);const n=`/logs${e.toString()?"?"+e.toString():""}`,s=await(await fetch(`${Te}${n}`)).json();if(!s.success)throw new Error(s.message||"获取日志失败");return s.data},createSystemLog:async t=>G("/logs",{method:"POST",body:JSON.stringify(t)}),updateSystemLog:async(t,e)=>G(`/logs/${t}`,{method:"PUT",body:JSON.stringify(e)}),deleteSystemLog:async t=>G(`/logs/${t}`,{method:"DELETE"}),getLogStats:async()=>{const e=await(await fetch(`${Te}/logs/stats`)).json();if(!e.success)throw new Error(e.message||"获取日志统计失败");return e.data},importHistoryLogs:async t=>G("/logs/import",{method:"POST",body:JSON.stringify({logs:t})}),uploadWebsiteCover:async t=>G("/admin/website-cover",{method:"POST",body:JSON.stringify(t)}),getWebsiteCover:async()=>{const e=await(await fetch(`${Te}/admin/website-cover`)).json();if(!e.success)throw new Error(e.message||"获取网站封面失败");return e.data}},Sa=async()=>se("/health"),wa={...wn,..._a,...An,...oe,...Ta,...kt,...qe,healthCheck:Sa};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let Aa,Ea;function Ia(){return{geminiUrl:Aa,vertexUrl:Ea}}function Na(t,e,n){var i,s,a;if(!(!((i=t.httpOptions)===null||i===void 0)&&i.baseUrl)){const l=Ia();return t.vertexai?(s=l.vertexUrl)!==null&&s!==void 0?s:e:(a=l.geminiUrl)!==null&&a!==void 0?a:n}return t.httpOptions.baseUrl}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Ue{}function L(t,e){const n=/\{([^}]+)\}/g;return t.replace(n,(i,s)=>{if(Object.prototype.hasOwnProperty.call(e,s)){const a=e[s];return a!=null?String(a):""}else throw new Error(`Key '${s}' not found in valueMap.`)})}function r(t,e,n){for(let a=0;a<e.length-1;a++){const l=e[a];if(l.endsWith("[]")){const u=l.slice(0,-2);if(!(u in t))if(Array.isArray(n))t[u]=Array.from({length:n.length},()=>({}));else throw new Error(`Value must be a list given an array path ${l}`);if(Array.isArray(t[u])){const d=t[u];if(Array.isArray(n))for(let c=0;c<d.length;c++){const m=d[c];r(m,e.slice(a+1),n[c])}else for(const c of d)r(c,e.slice(a+1),n)}return}else if(l.endsWith("[0]")){const u=l.slice(0,-3);u in t||(t[u]=[{}]);const d=t[u];r(d[0],e.slice(a+1),n);return}(!t[l]||typeof t[l]!="object")&&(t[l]={}),t=t[l]}const i=e[e.length-1],s=t[i];if(s!==void 0){if(!n||typeof n=="object"&&Object.keys(n).length===0||n===s)return;if(typeof s=="object"&&typeof n=="object"&&s!==null&&n!==null)Object.assign(s,n);else throw new Error(`Cannot set value for an existing key. Key: ${i}`)}else t[i]=n}function o(t,e){try{if(e.length===1&&e[0]==="_self")return t;for(let n=0;n<e.length;n++){if(typeof t!="object"||t===null)return;const i=e[n];if(i.endsWith("[]")){const s=i.slice(0,-2);if(s in t){const a=t[s];return Array.isArray(a)?a.map(l=>o(l,e.slice(n+1))):void 0}else return}else t=t[i]}return t}catch(n){if(n instanceof TypeError)return;throw n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var Ai;(function(t){t.OUTCOME_UNSPECIFIED="OUTCOME_UNSPECIFIED",t.OUTCOME_OK="OUTCOME_OK",t.OUTCOME_FAILED="OUTCOME_FAILED",t.OUTCOME_DEADLINE_EXCEEDED="OUTCOME_DEADLINE_EXCEEDED"})(Ai||(Ai={}));var Ei;(function(t){t.LANGUAGE_UNSPECIFIED="LANGUAGE_UNSPECIFIED",t.PYTHON="PYTHON"})(Ei||(Ei={}));var Ne;(function(t){t.TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",t.STRING="STRING",t.NUMBER="NUMBER",t.INTEGER="INTEGER",t.BOOLEAN="BOOLEAN",t.ARRAY="ARRAY",t.OBJECT="OBJECT",t.NULL="NULL"})(Ne||(Ne={}));var Ii;(function(t){t.HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",t.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",t.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",t.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",t.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",t.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY",t.HARM_CATEGORY_IMAGE_HATE="HARM_CATEGORY_IMAGE_HATE",t.HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT="HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT",t.HARM_CATEGORY_IMAGE_HARASSMENT="HARM_CATEGORY_IMAGE_HARASSMENT",t.HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT="HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT"})(Ii||(Ii={}));var Ni;(function(t){t.HARM_BLOCK_METHOD_UNSPECIFIED="HARM_BLOCK_METHOD_UNSPECIFIED",t.SEVERITY="SEVERITY",t.PROBABILITY="PROBABILITY"})(Ni||(Ni={}));var bi;(function(t){t.HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE",t.OFF="OFF"})(bi||(bi={}));var Mi;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(Mi||(Mi={}));var Pi;(function(t){t.AUTH_TYPE_UNSPECIFIED="AUTH_TYPE_UNSPECIFIED",t.NO_AUTH="NO_AUTH",t.API_KEY_AUTH="API_KEY_AUTH",t.HTTP_BASIC_AUTH="HTTP_BASIC_AUTH",t.GOOGLE_SERVICE_ACCOUNT_AUTH="GOOGLE_SERVICE_ACCOUNT_AUTH",t.OAUTH="OAUTH",t.OIDC_AUTH="OIDC_AUTH"})(Pi||(Pi={}));var Ri;(function(t){t.API_SPEC_UNSPECIFIED="API_SPEC_UNSPECIFIED",t.SIMPLE_SEARCH="SIMPLE_SEARCH",t.ELASTIC_SEARCH="ELASTIC_SEARCH"})(Ri||(Ri={}));var ki;(function(t){t.ENVIRONMENT_UNSPECIFIED="ENVIRONMENT_UNSPECIFIED",t.ENVIRONMENT_BROWSER="ENVIRONMENT_BROWSER"})(ki||(ki={}));var Di;(function(t){t.URL_RETRIEVAL_STATUS_UNSPECIFIED="URL_RETRIEVAL_STATUS_UNSPECIFIED",t.URL_RETRIEVAL_STATUS_SUCCESS="URL_RETRIEVAL_STATUS_SUCCESS",t.URL_RETRIEVAL_STATUS_ERROR="URL_RETRIEVAL_STATUS_ERROR"})(Di||(Di={}));var Li;(function(t){t.FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",t.STOP="STOP",t.MAX_TOKENS="MAX_TOKENS",t.SAFETY="SAFETY",t.RECITATION="RECITATION",t.LANGUAGE="LANGUAGE",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.SPII="SPII",t.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",t.IMAGE_SAFETY="IMAGE_SAFETY",t.UNEXPECTED_TOOL_CALL="UNEXPECTED_TOOL_CALL"})(Li||(Li={}));var Fi;(function(t){t.HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",t.NEGLIGIBLE="NEGLIGIBLE",t.LOW="LOW",t.MEDIUM="MEDIUM",t.HIGH="HIGH"})(Fi||(Fi={}));var Ui;(function(t){t.HARM_SEVERITY_UNSPECIFIED="HARM_SEVERITY_UNSPECIFIED",t.HARM_SEVERITY_NEGLIGIBLE="HARM_SEVERITY_NEGLIGIBLE",t.HARM_SEVERITY_LOW="HARM_SEVERITY_LOW",t.HARM_SEVERITY_MEDIUM="HARM_SEVERITY_MEDIUM",t.HARM_SEVERITY_HIGH="HARM_SEVERITY_HIGH"})(Ui||(Ui={}));var $i;(function(t){t.BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",t.SAFETY="SAFETY",t.OTHER="OTHER",t.BLOCKLIST="BLOCKLIST",t.PROHIBITED_CONTENT="PROHIBITED_CONTENT",t.IMAGE_SAFETY="IMAGE_SAFETY"})($i||($i={}));var Vi;(function(t){t.TRAFFIC_TYPE_UNSPECIFIED="TRAFFIC_TYPE_UNSPECIFIED",t.ON_DEMAND="ON_DEMAND",t.PROVISIONED_THROUGHPUT="PROVISIONED_THROUGHPUT"})(Vi||(Vi={}));var Wt;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.AUDIO="AUDIO"})(Wt||(Wt={}));var Bi;(function(t){t.MEDIA_RESOLUTION_UNSPECIFIED="MEDIA_RESOLUTION_UNSPECIFIED",t.MEDIA_RESOLUTION_LOW="MEDIA_RESOLUTION_LOW",t.MEDIA_RESOLUTION_MEDIUM="MEDIA_RESOLUTION_MEDIUM",t.MEDIA_RESOLUTION_HIGH="MEDIA_RESOLUTION_HIGH"})(Bi||(Bi={}));var En;(function(t){t.JOB_STATE_UNSPECIFIED="JOB_STATE_UNSPECIFIED",t.JOB_STATE_QUEUED="JOB_STATE_QUEUED",t.JOB_STATE_PENDING="JOB_STATE_PENDING",t.JOB_STATE_RUNNING="JOB_STATE_RUNNING",t.JOB_STATE_SUCCEEDED="JOB_STATE_SUCCEEDED",t.JOB_STATE_FAILED="JOB_STATE_FAILED",t.JOB_STATE_CANCELLING="JOB_STATE_CANCELLING",t.JOB_STATE_CANCELLED="JOB_STATE_CANCELLED",t.JOB_STATE_PAUSED="JOB_STATE_PAUSED",t.JOB_STATE_EXPIRED="JOB_STATE_EXPIRED",t.JOB_STATE_UPDATING="JOB_STATE_UPDATING",t.JOB_STATE_PARTIALLY_SUCCEEDED="JOB_STATE_PARTIALLY_SUCCEEDED"})(En||(En={}));var qi;(function(t){t.ADAPTER_SIZE_UNSPECIFIED="ADAPTER_SIZE_UNSPECIFIED",t.ADAPTER_SIZE_ONE="ADAPTER_SIZE_ONE",t.ADAPTER_SIZE_TWO="ADAPTER_SIZE_TWO",t.ADAPTER_SIZE_FOUR="ADAPTER_SIZE_FOUR",t.ADAPTER_SIZE_EIGHT="ADAPTER_SIZE_EIGHT",t.ADAPTER_SIZE_SIXTEEN="ADAPTER_SIZE_SIXTEEN",t.ADAPTER_SIZE_THIRTY_TWO="ADAPTER_SIZE_THIRTY_TWO"})(qi||(qi={}));var Gi;(function(t){t.FEATURE_SELECTION_PREFERENCE_UNSPECIFIED="FEATURE_SELECTION_PREFERENCE_UNSPECIFIED",t.PRIORITIZE_QUALITY="PRIORITIZE_QUALITY",t.BALANCED="BALANCED",t.PRIORITIZE_COST="PRIORITIZE_COST"})(Gi||(Gi={}));var Ji;(function(t){t.UNSPECIFIED="UNSPECIFIED",t.BLOCKING="BLOCKING",t.NON_BLOCKING="NON_BLOCKING"})(Ji||(Ji={}));var Oi;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.MODE_DYNAMIC="MODE_DYNAMIC"})(Oi||(Oi={}));var Hi;(function(t){t.MODE_UNSPECIFIED="MODE_UNSPECIFIED",t.AUTO="AUTO",t.ANY="ANY",t.NONE="NONE"})(Hi||(Hi={}));var Wi;(function(t){t.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",t.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",t.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",t.BLOCK_NONE="BLOCK_NONE"})(Wi||(Wi={}));var Ki;(function(t){t.DONT_ALLOW="DONT_ALLOW",t.ALLOW_ADULT="ALLOW_ADULT",t.ALLOW_ALL="ALLOW_ALL"})(Ki||(Ki={}));var Yi;(function(t){t.auto="auto",t.en="en",t.ja="ja",t.ko="ko",t.hi="hi",t.zh="zh",t.pt="pt",t.es="es"})(Yi||(Yi={}));var zi;(function(t){t.MASK_MODE_DEFAULT="MASK_MODE_DEFAULT",t.MASK_MODE_USER_PROVIDED="MASK_MODE_USER_PROVIDED",t.MASK_MODE_BACKGROUND="MASK_MODE_BACKGROUND",t.MASK_MODE_FOREGROUND="MASK_MODE_FOREGROUND",t.MASK_MODE_SEMANTIC="MASK_MODE_SEMANTIC"})(zi||(zi={}));var Xi;(function(t){t.CONTROL_TYPE_DEFAULT="CONTROL_TYPE_DEFAULT",t.CONTROL_TYPE_CANNY="CONTROL_TYPE_CANNY",t.CONTROL_TYPE_SCRIBBLE="CONTROL_TYPE_SCRIBBLE",t.CONTROL_TYPE_FACE_MESH="CONTROL_TYPE_FACE_MESH"})(Xi||(Xi={}));var Qi;(function(t){t.SUBJECT_TYPE_DEFAULT="SUBJECT_TYPE_DEFAULT",t.SUBJECT_TYPE_PERSON="SUBJECT_TYPE_PERSON",t.SUBJECT_TYPE_ANIMAL="SUBJECT_TYPE_ANIMAL",t.SUBJECT_TYPE_PRODUCT="SUBJECT_TYPE_PRODUCT"})(Qi||(Qi={}));var Zi;(function(t){t.EDIT_MODE_DEFAULT="EDIT_MODE_DEFAULT",t.EDIT_MODE_INPAINT_REMOVAL="EDIT_MODE_INPAINT_REMOVAL",t.EDIT_MODE_INPAINT_INSERTION="EDIT_MODE_INPAINT_INSERTION",t.EDIT_MODE_OUTPAINT="EDIT_MODE_OUTPAINT",t.EDIT_MODE_CONTROLLED_EDITING="EDIT_MODE_CONTROLLED_EDITING",t.EDIT_MODE_STYLE="EDIT_MODE_STYLE",t.EDIT_MODE_BGSWAP="EDIT_MODE_BGSWAP",t.EDIT_MODE_PRODUCT_IMAGE="EDIT_MODE_PRODUCT_IMAGE"})(Zi||(Zi={}));var ji;(function(t){t.OPTIMIZED="OPTIMIZED",t.LOSSLESS="LOSSLESS"})(ji||(ji={}));var es;(function(t){t.STATE_UNSPECIFIED="STATE_UNSPECIFIED",t.PROCESSING="PROCESSING",t.ACTIVE="ACTIVE",t.FAILED="FAILED"})(es||(es={}));var ts;(function(t){t.SOURCE_UNSPECIFIED="SOURCE_UNSPECIFIED",t.UPLOADED="UPLOADED",t.GENERATED="GENERATED"})(ts||(ts={}));var ns;(function(t){t.MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",t.TEXT="TEXT",t.IMAGE="IMAGE",t.VIDEO="VIDEO",t.AUDIO="AUDIO",t.DOCUMENT="DOCUMENT"})(ns||(ns={}));var is;(function(t){t.START_SENSITIVITY_UNSPECIFIED="START_SENSITIVITY_UNSPECIFIED",t.START_SENSITIVITY_HIGH="START_SENSITIVITY_HIGH",t.START_SENSITIVITY_LOW="START_SENSITIVITY_LOW"})(is||(is={}));var ss;(function(t){t.END_SENSITIVITY_UNSPECIFIED="END_SENSITIVITY_UNSPECIFIED",t.END_SENSITIVITY_HIGH="END_SENSITIVITY_HIGH",t.END_SENSITIVITY_LOW="END_SENSITIVITY_LOW"})(ss||(ss={}));var os;(function(t){t.ACTIVITY_HANDLING_UNSPECIFIED="ACTIVITY_HANDLING_UNSPECIFIED",t.START_OF_ACTIVITY_INTERRUPTS="START_OF_ACTIVITY_INTERRUPTS",t.NO_INTERRUPTION="NO_INTERRUPTION"})(os||(os={}));var rs;(function(t){t.TURN_COVERAGE_UNSPECIFIED="TURN_COVERAGE_UNSPECIFIED",t.TURN_INCLUDES_ONLY_ACTIVITY="TURN_INCLUDES_ONLY_ACTIVITY",t.TURN_INCLUDES_ALL_INPUT="TURN_INCLUDES_ALL_INPUT"})(rs||(rs={}));var as;(function(t){t.SCHEDULING_UNSPECIFIED="SCHEDULING_UNSPECIFIED",t.SILENT="SILENT",t.WHEN_IDLE="WHEN_IDLE",t.INTERRUPT="INTERRUPT"})(as||(as={}));var ls;(function(t){t.SCALE_UNSPECIFIED="SCALE_UNSPECIFIED",t.C_MAJOR_A_MINOR="C_MAJOR_A_MINOR",t.D_FLAT_MAJOR_B_FLAT_MINOR="D_FLAT_MAJOR_B_FLAT_MINOR",t.D_MAJOR_B_MINOR="D_MAJOR_B_MINOR",t.E_FLAT_MAJOR_C_MINOR="E_FLAT_MAJOR_C_MINOR",t.E_MAJOR_D_FLAT_MINOR="E_MAJOR_D_FLAT_MINOR",t.F_MAJOR_D_MINOR="F_MAJOR_D_MINOR",t.G_FLAT_MAJOR_E_FLAT_MINOR="G_FLAT_MAJOR_E_FLAT_MINOR",t.G_MAJOR_E_MINOR="G_MAJOR_E_MINOR",t.A_FLAT_MAJOR_F_MINOR="A_FLAT_MAJOR_F_MINOR",t.A_MAJOR_G_FLAT_MINOR="A_MAJOR_G_FLAT_MINOR",t.B_FLAT_MAJOR_G_MINOR="B_FLAT_MAJOR_G_MINOR",t.B_MAJOR_A_FLAT_MINOR="B_MAJOR_A_FLAT_MINOR"})(ls||(ls={}));var He;(function(t){t.PLAYBACK_CONTROL_UNSPECIFIED="PLAYBACK_CONTROL_UNSPECIFIED",t.PLAY="PLAY",t.PAUSE="PAUSE",t.STOP="STOP",t.RESET_CONTEXT="RESET_CONTEXT"})(He||(He={}));class In{constructor(e){const n={};for(const i of e.headers.entries())n[i[0]]=i[1];this.headers=n,this.responseInternal=e}json(){return this.responseInternal.json()}}class tt{get text(){var e,n,i,s,a,l,u,d;if(((s=(i=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||i===void 0?void 0:i.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning text from the first one.");let c="",m=!1;const h=[];for(const p of(d=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)!==null&&d!==void 0?d:[]){for(const[g,y]of Object.entries(p))g!=="text"&&g!=="thought"&&(y!==null||y!==void 0)&&h.push(g);if(typeof p.text=="string"){if(typeof p.thought=="boolean"&&p.thought)continue;m=!0,c+=p.text}}return h.length>0&&console.warn(`there are non-text parts ${h} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),m?c:void 0}get data(){var e,n,i,s,a,l,u,d;if(((s=(i=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||i===void 0?void 0:i.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning data from the first one.");let c="";const m=[];for(const h of(d=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)!==null&&d!==void 0?d:[]){for(const[p,g]of Object.entries(h))p!=="inlineData"&&(g!==null||g!==void 0)&&m.push(p);h.inlineData&&typeof h.inlineData.data=="string"&&(c+=atob(h.inlineData.data))}return m.length>0&&console.warn(`there are non-data parts ${m} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),c.length>0?btoa(c):void 0}get functionCalls(){var e,n,i,s,a,l,u,d;if(((s=(i=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||i===void 0?void 0:i.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning function calls from the first one.");const c=(d=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)===null||d===void 0?void 0:d.filter(m=>m.functionCall).map(m=>m.functionCall).filter(m=>m!==void 0);if((c==null?void 0:c.length)!==0)return c}get executableCode(){var e,n,i,s,a,l,u,d,c;if(((s=(i=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||i===void 0?void 0:i.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning executable code from the first one.");const m=(d=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)===null||d===void 0?void 0:d.filter(h=>h.executableCode).map(h=>h.executableCode).filter(h=>h!==void 0);if((m==null?void 0:m.length)!==0)return(c=m==null?void 0:m[0])===null||c===void 0?void 0:c.code}get codeExecutionResult(){var e,n,i,s,a,l,u,d,c;if(((s=(i=(n=(e=this.candidates)===null||e===void 0?void 0:e[0])===null||n===void 0?void 0:n.content)===null||i===void 0?void 0:i.parts)===null||s===void 0?void 0:s.length)===0)return;this.candidates&&this.candidates.length>1&&console.warn("there are multiple candidates in the response, returning code execution result from the first one.");const m=(d=(u=(l=(a=this.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content)===null||u===void 0?void 0:u.parts)===null||d===void 0?void 0:d.filter(h=>h.codeExecutionResult).map(h=>h.codeExecutionResult).filter(h=>h!==void 0);if((m==null?void 0:m.length)!==0)return(c=m==null?void 0:m[0])===null||c===void 0?void 0:c.output}}class us{}class cs{}class ba{}class Ma{}class ds{}class fs{}class ms{}class Pa{}class hs{}class ps{}class gs{}class Ra{}class ka{}class Da{}class ys{}class La{get text(){var e,n,i;let s="",a=!1;const l=[];for(const u of(i=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&i!==void 0?i:[]){for(const[d,c]of Object.entries(u))d!=="text"&&d!=="thought"&&c!==null&&l.push(d);if(typeof u.text=="string"){if(typeof u.thought=="boolean"&&u.thought)continue;a=!0,s+=u.text}}return l.length>0&&console.warn(`there are non-text parts ${l} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),a?s:void 0}get data(){var e,n,i;let s="";const a=[];for(const l of(i=(n=(e=this.serverContent)===null||e===void 0?void 0:e.modelTurn)===null||n===void 0?void 0:n.parts)!==null&&i!==void 0?i:[]){for(const[u,d]of Object.entries(l))u!=="inlineData"&&d!==null&&a.push(u);l.inlineData&&typeof l.inlineData.data=="string"&&(s+=atob(l.inlineData.data))}return a.length>0&&console.warn(`there are non-data parts ${a} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),s.length>0?btoa(s):void 0}}class Fa{get audioChunk(){if(this.serverContent&&this.serverContent.audioChunks&&this.serverContent.audioChunks.length>0)return this.serverContent.audioChunks[0]}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function J(t,e){if(!e||typeof e!="string")throw new Error("model is required and must be a string");if(t.isVertexAI()){if(e.startsWith("publishers/")||e.startsWith("projects/")||e.startsWith("models/"))return e;if(e.indexOf("/")>=0){const n=e.split("/",2);return`publishers/${n[0]}/models/${n[1]}`}else return`publishers/google/models/${e}`}else return e.startsWith("models/")||e.startsWith("tunedModels/")?e:`models/${e}`}function yo(t,e){const n=J(t,e);return n?n.startsWith("publishers/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}`:n.startsWith("models/")&&t.isVertexAI()?`projects/${t.getProject()}/locations/${t.getLocation()}/publishers/google/${n}`:n:""}function xo(t){return Array.isArray(t)?t.map(e=>Kt(e)):[Kt(t)]}function Kt(t){if(typeof t=="object"&&t!==null)return t;throw new Error(`Could not parse input as Blob. Unsupported blob type: ${typeof t}`)}function vo(t){const e=Kt(t);if(e.mimeType&&e.mimeType.startsWith("image/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function Co(t){const e=Kt(t);if(e.mimeType&&e.mimeType.startsWith("audio/"))return e;throw new Error(`Unsupported mime type: ${e.mimeType}`)}function xs(t){if(t==null)throw new Error("PartUnion is required");if(typeof t=="object")return t;if(typeof t=="string")return{text:t};throw new Error(`Unsupported part type: ${typeof t}`)}function _o(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("PartListUnion is required");return Array.isArray(t)?t.map(e=>xs(e)):[xs(t)]}function Nn(t){return t!=null&&typeof t=="object"&&"parts"in t&&Array.isArray(t.parts)}function vs(t){return t!=null&&typeof t=="object"&&"functionCall"in t}function Cs(t){return t!=null&&typeof t=="object"&&"functionResponse"in t}function re(t){if(t==null)throw new Error("ContentUnion is required");return Nn(t)?t:{role:"user",parts:_o(t)}}function To(t,e){if(!e)return[];if(t.isVertexAI()&&Array.isArray(e))return e.flatMap(n=>{const i=re(n);return i.parts&&i.parts.length>0&&i.parts[0].text!==void 0?[i.parts[0].text]:[]});if(t.isVertexAI()){const n=re(e);return n.parts&&n.parts.length>0&&n.parts[0].text!==void 0?[n.parts[0].text]:[]}return Array.isArray(e)?e.map(n=>re(n)):[re(e)]}function he(t){if(t==null||Array.isArray(t)&&t.length===0)throw new Error("contents are required");if(!Array.isArray(t)){if(vs(t)||Cs(t))throw new Error("To specify functionCall or functionResponse parts, please wrap them in a Content object, specifying the role for them");return[re(t)]}const e=[],n=[],i=Nn(t[0]);for(const s of t){const a=Nn(s);if(a!=i)throw new Error("Mixing Content and Parts is not supported, please group the parts into a the appropriate Content objects and specify the roles for them");if(a)e.push(s);else{if(vs(s)||Cs(s))throw new Error("To specify functionCall or functionResponse parts, please wrap them, and any other parts, in Content objects as appropriate, specifying the role for them");n.push(s)}}return i||e.push({role:"user",parts:_o(n)}),e}function Ua(t,e){t.includes("null")&&(e.nullable=!0);const n=t.filter(i=>i!=="null");if(n.length===1)e.type=Object.values(Ne).includes(n[0].toUpperCase())?n[0].toUpperCase():Ne.TYPE_UNSPECIFIED;else{e.anyOf=[];for(const i of n)e.anyOf.push({type:Object.values(Ne).includes(i.toUpperCase())?i.toUpperCase():Ne.TYPE_UNSPECIFIED})}}function Ke(t){const e={},n=["items"],i=["anyOf"],s=["properties"];if(t.type&&t.anyOf)throw new Error("type and anyOf cannot be both populated.");const a=t.anyOf;a!=null&&a.length==2&&(a[0].type==="null"?(e.nullable=!0,t=a[1]):a[1].type==="null"&&(e.nullable=!0,t=a[0])),t.type instanceof Array&&Ua(t.type,e);for(const[l,u]of Object.entries(t))if(u!=null)if(l=="type"){if(u==="null")throw new Error("type: null can not be the only possible type for the field.");if(u instanceof Array)continue;e.type=Object.values(Ne).includes(u.toUpperCase())?u.toUpperCase():Ne.TYPE_UNSPECIFIED}else if(n.includes(l))e[l]=Ke(u);else if(i.includes(l)){const d=[];for(const c of u){if(c.type=="null"){e.nullable=!0;continue}d.push(Ke(c))}e[l]=d}else if(s.includes(l)){const d={};for(const[c,m]of Object.entries(u))d[c]=Ke(m);e[l]=d}else{if(l==="additionalProperties")continue;e[l]=u}return e}function Xn(t){return Ke(t)}function Qn(t){if(typeof t=="object")return t;if(typeof t=="string")return{voiceConfig:{prebuiltVoiceConfig:{voiceName:t}}};throw new Error(`Unsupported speechConfig type: ${typeof t}`)}function Zn(t){if("multiSpeakerVoiceConfig"in t)throw new Error("multiSpeakerVoiceConfig is not supported in the live API.");return t}function ze(t){if(t.functionDeclarations)for(const e of t.functionDeclarations)e.parameters&&(Object.keys(e.parameters).includes("$schema")?e.parametersJsonSchema||(e.parametersJsonSchema=e.parameters,delete e.parameters):e.parameters=Ke(e.parameters)),e.response&&(Object.keys(e.response).includes("$schema")?e.responseJsonSchema||(e.responseJsonSchema=e.response,delete e.response):e.response=Ke(e.response));return t}function Xe(t){if(t==null)throw new Error("tools is required");if(!Array.isArray(t))throw new Error("tools is required and must be an array of Tools");const e=[];for(const n of t)e.push(n);return e}function $a(t,e,n,i=1){const s=!e.startsWith(`${n}/`)&&e.split("/").length===i;return t.isVertexAI()?e.startsWith("projects/")?e:e.startsWith("locations/")?`projects/${t.getProject()}/${e}`:e.startsWith(`${n}/`)?`projects/${t.getProject()}/locations/${t.getLocation()}/${e}`:s?`projects/${t.getProject()}/locations/${t.getLocation()}/${n}/${e}`:e:s?`${n}/${e}`:e}function Se(t,e){if(typeof e!="string")throw new Error("name must be a string");return $a(t,e,"cachedContents")}function So(t){switch(t){case"STATE_UNSPECIFIED":return"JOB_STATE_UNSPECIFIED";case"CREATING":return"JOB_STATE_RUNNING";case"ACTIVE":return"JOB_STATE_SUCCEEDED";case"FAILED":return"JOB_STATE_FAILED";default:return t}}function we(t){if(typeof t!="string")throw new Error("fromImageBytes must be a string");return t}function Va(t){return t!=null&&typeof t=="object"&&"name"in t}function Ba(t){return t!=null&&typeof t=="object"&&"video"in t}function qa(t){return t!=null&&typeof t=="object"&&"uri"in t}function wo(t){var e;let n;if(Va(t)&&(n=t.name),!(qa(t)&&(n=t.uri,n===void 0))&&!(Ba(t)&&(n=(e=t.video)===null||e===void 0?void 0:e.uri,n===void 0))){if(typeof t=="string"&&(n=t),n===void 0)throw new Error("Could not extract file name from the provided input.");if(n.startsWith("https://")){const s=n.split("files/")[1].match(/[a-z0-9]+/);if(s===null)throw new Error(`Could not extract file name from URI ${n}`);n=s[0]}else n.startsWith("files/")&&(n=n.split("files/")[1]);return n}}function Ao(t,e){let n;return t.isVertexAI()?n=e?"publishers/google/models":"models":n=e?"models":"tunedModels",n}function Eo(t){for(const e of["models","tunedModels","publisherModels"])if(Ga(t,e))return t[e];return[]}function Ga(t,e){return t!==null&&typeof t=="object"&&e in t}function Ja(t,e={}){const n=t,i={name:n.name,description:n.description,parametersJsonSchema:n.inputSchema};return e.behavior&&(i.behavior=e.behavior),{functionDeclarations:[i]}}function Oa(t,e={}){const n=[],i=new Set;for(const s of t){const a=s.name;if(i.has(a))throw new Error(`Duplicate function name ${a} found in MCP tools. Please ensure function names are unique.`);i.add(a);const l=Ja(s,e);l.functionDeclarations&&n.push(...l.functionDeclarations)}return{functionDeclarations:n}}function Io(t,e){if(typeof e!="string"&&!Array.isArray(e)){if(t&&t.isVertexAI()){if(e.gcsUri&&e.bigqueryUri)throw new Error("Only one of `gcsUri` or `bigqueryUri` can be set.");if(!e.gcsUri&&!e.bigqueryUri)throw new Error("One of `gcsUri` or `bigqueryUri` must be set.")}else{if(e.inlinedRequests&&e.fileName)throw new Error("Only one of `inlinedRequests` or `fileName` can be set.");if(!e.inlinedRequests&&!e.fileName)throw new Error("One of `inlinedRequests` or `fileName` must be set.")}return e}else{if(Array.isArray(e))return{inlinedRequests:e};if(typeof e=="string"){if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:[e]};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};if(e.startsWith("files/"))return{fileName:e}}}throw new Error(`Unsupported source: ${e}`)}function Ha(t){const e=t;if(e.startsWith("gs://"))return{format:"jsonl",gcsUri:e};if(e.startsWith("bq://"))return{format:"bigquery",bigqueryUri:e};throw new Error(`Unsupported destination: ${e}`)}function Qe(t,e){const n=e;if(!t.isVertexAI()){if(/batches\/[^/]+$/.test(n))return n.split("/").pop();throw new Error(`Invalid batch job name: ${n}.`)}if(/^projects\/[^/]+\/locations\/[^/]+\/batchPredictionJobs\/[^/]+$/.test(n))return n.split("/").pop();if(/^\d+$/.test(n))return n;throw new Error(`Invalid batch job name: ${n}.`)}function No(t){const e=t;return e==="BATCH_STATE_UNSPECIFIED"?"JOB_STATE_UNSPECIFIED":e==="BATCH_STATE_PENDING"?"JOB_STATE_PENDING":e==="BATCH_STATE_SUCCEEDED"?"JOB_STATE_SUCCEEDED":e==="BATCH_STATE_FAILED"?"JOB_STATE_FAILED":e==="BATCH_STATE_CANCELLED"?"JOB_STATE_CANCELLED":e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Wa(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Ka(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ya(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function za(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Wa(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Ka(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ya(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bo(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>za(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Xa(t){const e={},n=o(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const i=o(t,["default"]);i!=null&&r(e,["default"],i);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const a=o(t,["enum"]);a!=null&&r(e,["enum"],a);const l=o(t,["example"]);l!=null&&r(e,["example"],l);const u=o(t,["format"]);u!=null&&r(e,["format"],u);const d=o(t,["items"]);d!=null&&r(e,["items"],d);const c=o(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const m=o(t,["maxLength"]);m!=null&&r(e,["maxLength"],m);const h=o(t,["maxProperties"]);h!=null&&r(e,["maxProperties"],h);const p=o(t,["maximum"]);p!=null&&r(e,["maximum"],p);const g=o(t,["minItems"]);g!=null&&r(e,["minItems"],g);const y=o(t,["minLength"]);y!=null&&r(e,["minLength"],y);const x=o(t,["minProperties"]);x!=null&&r(e,["minProperties"],x);const T=o(t,["minimum"]);T!=null&&r(e,["minimum"],T);const I=o(t,["nullable"]);I!=null&&r(e,["nullable"],I);const w=o(t,["pattern"]);w!=null&&r(e,["pattern"],w);const A=o(t,["properties"]);A!=null&&r(e,["properties"],A);const _=o(t,["propertyOrdering"]);_!=null&&r(e,["propertyOrdering"],_);const C=o(t,["required"]);C!=null&&r(e,["required"],C);const E=o(t,["title"]);E!=null&&r(e,["title"],E);const N=o(t,["type"]);return N!=null&&r(e,["type"],N),e}function Qa(t){const e={};if(o(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=o(t,["category"]);n!=null&&r(e,["category"],n);const i=o(t,["threshold"]);return i!=null&&r(e,["threshold"],i),e}function Za(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const d=o(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function ja(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function el(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],ja(n)),e}function tl(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function nl(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],tl(n)),e}function il(){return{}}function sl(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(c=>Za(c))),r(e,["functionDeclarations"],d)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],el(i));const s=o(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],nl(s)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],il());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function ol(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["allowedFunctionNames"]);return i!=null&&r(e,["allowedFunctionNames"],i),e}function rl(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const i=o(t,["longitude"]);return i!=null&&r(e,["longitude"],i),e}function al(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],rl(n));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function ll(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],ol(n));const i=o(t,["retrievalConfig"]);return i!=null&&r(e,["retrievalConfig"],al(i)),e}function ul(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Mo(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],ul(n)),e}function cl(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const i=o(t,["voiceConfig"]);return i!=null&&r(e,["voiceConfig"],Mo(i)),e}function dl(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>cl(s))),r(e,["speakerVoiceConfigs"],i)}return e}function fl(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Mo(n));const i=o(t,["multiSpeakerVoiceConfig"]);i!=null&&r(e,["multiSpeakerVoiceConfig"],dl(i));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function ml(t){const e={},n=o(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const i=o(t,["thinkingBudget"]);return i!=null&&r(e,["thinkingBudget"],i),e}function hl(t,e,n){const i={},s=o(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],bo(re(s)));const a=o(e,["temperature"]);a!=null&&r(i,["temperature"],a);const l=o(e,["topP"]);l!=null&&r(i,["topP"],l);const u=o(e,["topK"]);u!=null&&r(i,["topK"],u);const d=o(e,["candidateCount"]);d!=null&&r(i,["candidateCount"],d);const c=o(e,["maxOutputTokens"]);c!=null&&r(i,["maxOutputTokens"],c);const m=o(e,["stopSequences"]);m!=null&&r(i,["stopSequences"],m);const h=o(e,["responseLogprobs"]);h!=null&&r(i,["responseLogprobs"],h);const p=o(e,["logprobs"]);p!=null&&r(i,["logprobs"],p);const g=o(e,["presencePenalty"]);g!=null&&r(i,["presencePenalty"],g);const y=o(e,["frequencyPenalty"]);y!=null&&r(i,["frequencyPenalty"],y);const x=o(e,["seed"]);x!=null&&r(i,["seed"],x);const T=o(e,["responseMimeType"]);T!=null&&r(i,["responseMimeType"],T);const I=o(e,["responseSchema"]);I!=null&&r(i,["responseSchema"],Xa(Xn(I)));const w=o(e,["responseJsonSchema"]);if(w!=null&&r(i,["responseJsonSchema"],w),o(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(o(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const A=o(e,["safetySettings"]);if(n!==void 0&&A!=null){let R=A;Array.isArray(R)&&(R=R.map(K=>Qa(K))),r(n,["safetySettings"],R)}const _=o(e,["tools"]);if(n!==void 0&&_!=null){let R=Xe(_);Array.isArray(R)&&(R=R.map(K=>sl(ze(K)))),r(n,["tools"],R)}const C=o(e,["toolConfig"]);if(n!==void 0&&C!=null&&r(n,["toolConfig"],ll(C)),o(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const E=o(e,["cachedContent"]);n!==void 0&&E!=null&&r(n,["cachedContent"],Se(t,E));const N=o(e,["responseModalities"]);N!=null&&r(i,["responseModalities"],N);const D=o(e,["mediaResolution"]);D!=null&&r(i,["mediaResolution"],D);const b=o(e,["speechConfig"]);if(b!=null&&r(i,["speechConfig"],fl(Qn(b))),o(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const k=o(e,["thinkingConfig"]);return k!=null&&r(i,["thinkingConfig"],ml(k)),i}function pl(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["request","model"],J(t,i));const s=o(e,["contents"]);if(s!=null){let l=he(s);Array.isArray(l)&&(l=l.map(u=>bo(u))),r(n,["request","contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["request","generationConfig"],hl(t,a,n)),n}function gl(t,e){const n={};if(o(e,["format"])!==void 0)throw new Error("format parameter is not supported in Gemini API.");if(o(e,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");if(o(e,["bigqueryUri"])!==void 0)throw new Error("bigqueryUri parameter is not supported in Gemini API.");const i=o(e,["fileName"]);i!=null&&r(n,["fileName"],i);const s=o(e,["inlinedRequests"]);if(s!=null){let a=s;Array.isArray(a)&&(a=a.map(l=>pl(t,l))),r(n,["requests","requests"],a)}return n}function yl(t,e){const n={},i=o(t,["displayName"]);if(e!==void 0&&i!=null&&r(e,["batch","displayName"],i),o(t,["dest"])!==void 0)throw new Error("dest parameter is not supported in Gemini API.");return n}function xl(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["src"]);s!=null&&r(n,["batch","inputConfig"],gl(t,Io(t,s)));const a=o(e,["config"]);return a!=null&&r(n,["config"],yl(a,n)),n}function vl(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Qe(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Cl(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Qe(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function _l(t,e){const n={},i=o(t,["pageSize"]);e!==void 0&&i!=null&&r(e,["_query","pageSize"],i);const s=o(t,["pageToken"]);if(e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),o(t,["filter"])!==void 0)throw new Error("filter parameter is not supported in Gemini API.");return n}function Tl(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],_l(n,e)),e}function Sl(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Qe(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function wl(t){const e={},n=o(t,["format"]);n!=null&&r(e,["instancesFormat"],n);const i=o(t,["gcsUri"]);i!=null&&r(e,["gcsSource","uris"],i);const s=o(t,["bigqueryUri"]);if(s!=null&&r(e,["bigquerySource","inputUri"],s),o(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(o(t,["inlinedRequests"])!==void 0)throw new Error("inlinedRequests parameter is not supported in Vertex AI.");return e}function Al(t){const e={},n=o(t,["format"]);n!=null&&r(e,["predictionsFormat"],n);const i=o(t,["gcsUri"]);i!=null&&r(e,["gcsDestination","outputUriPrefix"],i);const s=o(t,["bigqueryUri"]);if(s!=null&&r(e,["bigqueryDestination","outputUri"],s),o(t,["fileName"])!==void 0)throw new Error("fileName parameter is not supported in Vertex AI.");if(o(t,["inlinedResponses"])!==void 0)throw new Error("inlinedResponses parameter is not supported in Vertex AI.");return e}function El(t,e){const n={},i=o(t,["displayName"]);e!==void 0&&i!=null&&r(e,["displayName"],i);const s=o(t,["dest"]);return e!==void 0&&s!=null&&r(e,["outputConfig"],Al(Ha(s))),n}function Il(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["model"],J(t,i));const s=o(e,["src"]);s!=null&&r(n,["inputConfig"],wl(Io(t,s)));const a=o(e,["config"]);return a!=null&&r(n,["config"],El(a,n)),n}function Nl(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Qe(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function bl(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Qe(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ml(t,e){const n={},i=o(t,["pageSize"]);e!==void 0&&i!=null&&r(e,["_query","pageSize"],i);const s=o(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const a=o(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function Pl(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],Ml(n,e)),e}function Rl(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Qe(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Po(){return{}}function kl(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Dl(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ll(t){const e={},n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Fl(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],kl(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Dl(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ll(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Ul(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Fl(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function $l(t){const e={},n=o(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function Vl(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const i=o(t,["urlRetrievalStatus"]);return i!=null&&r(e,["urlRetrievalStatus"],i),e}function Bl(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Vl(s))),r(e,["urlMetadata"],i)}return e}function ql(t){const e={},n=o(t,["content"]);n!=null&&r(e,["content"],Ul(n));const i=o(t,["citationMetadata"]);i!=null&&r(e,["citationMetadata"],$l(i));const s=o(t,["tokenCount"]);s!=null&&r(e,["tokenCount"],s);const a=o(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=o(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],Bl(l));const u=o(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const d=o(t,["groundingMetadata"]);d!=null&&r(e,["groundingMetadata"],d);const c=o(t,["index"]);c!=null&&r(e,["index"],c);const m=o(t,["logprobsResult"]);m!=null&&r(e,["logprobsResult"],m);const h=o(t,["safetyRatings"]);return h!=null&&r(e,["safetyRatings"],h),e}function Gl(t){const e={},n=o(t,["candidates"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(u=>ql(u))),r(e,["candidates"],l)}const i=o(t,["modelVersion"]);i!=null&&r(e,["modelVersion"],i);const s=o(t,["promptFeedback"]);s!=null&&r(e,["promptFeedback"],s);const a=o(t,["usageMetadata"]);return a!=null&&r(e,["usageMetadata"],a),e}function Jl(t){const e={},n=o(t,["response"]);return n!=null&&r(e,["response"],Gl(n)),o(t,["error"])!=null&&r(e,["error"],Po()),e}function Ol(t){const e={},n=o(t,["responsesFile"]);n!=null&&r(e,["fileName"],n);const i=o(t,["inlinedResponses","inlinedResponses"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>Jl(a))),r(e,["inlinedResponses"],s)}return e}function bn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["metadata","displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["metadata","state"]);s!=null&&r(e,["state"],No(s));const a=o(t,["metadata","createTime"]);a!=null&&r(e,["createTime"],a);const l=o(t,["metadata","endTime"]);l!=null&&r(e,["endTime"],l);const u=o(t,["metadata","updateTime"]);u!=null&&r(e,["updateTime"],u);const d=o(t,["metadata","model"]);d!=null&&r(e,["model"],d);const c=o(t,["metadata","output"]);return c!=null&&r(e,["dest"],Ol(c)),e}function Hl(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["operations"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>bn(a))),r(e,["batchJobs"],s)}return e}function Wl(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["done"]);return i!=null&&r(e,["done"],i),o(t,["error"])!=null&&r(e,["error"],Po()),e}function Ro(t){const e={},n=o(t,["details"]);n!=null&&r(e,["details"],n);const i=o(t,["code"]);i!=null&&r(e,["code"],i);const s=o(t,["message"]);return s!=null&&r(e,["message"],s),e}function Kl(t){const e={},n=o(t,["instancesFormat"]);n!=null&&r(e,["format"],n);const i=o(t,["gcsSource","uris"]);i!=null&&r(e,["gcsUri"],i);const s=o(t,["bigquerySource","inputUri"]);return s!=null&&r(e,["bigqueryUri"],s),e}function Yl(t){const e={},n=o(t,["predictionsFormat"]);n!=null&&r(e,["format"],n);const i=o(t,["gcsDestination","outputUriPrefix"]);i!=null&&r(e,["gcsUri"],i);const s=o(t,["bigqueryDestination","outputUri"]);return s!=null&&r(e,["bigqueryUri"],s),e}function Mn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["state"]);s!=null&&r(e,["state"],No(s));const a=o(t,["error"]);a!=null&&r(e,["error"],Ro(a));const l=o(t,["createTime"]);l!=null&&r(e,["createTime"],l);const u=o(t,["startTime"]);u!=null&&r(e,["startTime"],u);const d=o(t,["endTime"]);d!=null&&r(e,["endTime"],d);const c=o(t,["updateTime"]);c!=null&&r(e,["updateTime"],c);const m=o(t,["model"]);m!=null&&r(e,["model"],m);const h=o(t,["inputConfig"]);h!=null&&r(e,["src"],Kl(h));const p=o(t,["outputConfig"]);return p!=null&&r(e,["dest"],Yl(p)),e}function zl(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["batchPredictionJobs"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>Mn(a))),r(e,["batchJobs"],s)}return e}function Xl(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["done"]);i!=null&&r(e,["done"],i);const s=o(t,["error"]);return s!=null&&r(e,["error"],Ro(s)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */var Le;(function(t){t.PAGED_ITEM_BATCH_JOBS="batchJobs",t.PAGED_ITEM_MODELS="models",t.PAGED_ITEM_TUNING_JOBS="tuningJobs",t.PAGED_ITEM_FILES="files",t.PAGED_ITEM_CACHED_CONTENTS="cachedContents"})(Le||(Le={}));class yt{constructor(e,n,i,s){this.pageInternal=[],this.paramsInternal={},this.requestInternal=n,this.init(e,i,s)}init(e,n,i){var s,a;this.nameInternal=e,this.pageInternal=n[this.nameInternal]||[],this.idxInternal=0;let l={config:{}};i?typeof i=="object"?l=Object.assign({},i):l=i:l={config:{}},l.config&&(l.config.pageToken=n.nextPageToken),this.paramsInternal=l,this.pageInternalSize=(a=(s=l.config)===null||s===void 0?void 0:s.pageSize)!==null&&a!==void 0?a:this.pageInternal.length}initNextPage(e){this.init(this.nameInternal,e,this.paramsInternal)}get page(){return this.pageInternal}get name(){return this.nameInternal}get pageSize(){return this.pageInternalSize}get params(){return this.paramsInternal}get pageLength(){return this.pageInternal.length}getItem(e){return this.pageInternal[e]}[Symbol.asyncIterator](){return{next:async()=>{if(this.idxInternal>=this.pageLength)if(this.hasNextPage())await this.nextPage();else return{value:void 0,done:!0};const e=this.getItem(this.idxInternal);return this.idxInternal+=1,{value:e,done:!1}},return:async()=>({value:void 0,done:!0})}}async nextPage(){if(!this.hasNextPage())throw new Error("No more pages to fetch.");const e=await this.requestInternal(this.params);return this.initNextPage(e),this.page}hasNextPage(){var e;return((e=this.params.config)===null||e===void 0?void 0:e.pageToken)!==void 0}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let Ql=class extends Ue{constructor(e){super(),this.apiClient=e,this.create=async n=>{if(this.apiClient.isVertexAI()){const s=Date.now().toString();if(Array.isArray(n.src))throw new Error("InlinedRequest[] is not supported in Vertex AI. Please use Google Cloud Storage URI or BigQuery URI instead.");if(n.config=n.config||{},n.config.displayName===void 0&&(n.config.displayName="genaiBatchJob_${timestampStr}"),n.config.dest===void 0&&typeof n.src=="string")if(n.src.startsWith("gs://")&&n.src.endsWith(".jsonl"))n.config.dest=`${n.src.slice(0,-6)}/dest`;else if(n.src.startsWith("bq://"))n.config.dest=`${n.src}_dest_${s}`;else throw new Error("Unsupported source:"+n.src)}return await this.createInternal(n)},this.list=async(n={})=>new yt(Le.PAGED_ITEM_BATCH_JOBS,i=>this.listInternal(i),await this.listInternal(n),n)}async createInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Il(this.apiClient,e);return u=L("batchPredictionJobs",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Mn(m))}else{const c=xl(this.apiClient,e);return u=L("{model}:batchGenerateContent",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>bn(m))}}async get(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Nl(this.apiClient,e);return u=L("batchPredictionJobs/{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Mn(m))}else{const c=vl(this.apiClient,e);return u=L("batches/{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>bn(m))}}async cancel(e){var n,i,s,a;let l="",u={};if(this.apiClient.isVertexAI()){const d=bl(this.apiClient,e);l=L("batchPredictionJobs/{name}:cancel",d._url),u=d._query,delete d.config,delete d._url,delete d._query,await this.apiClient.request({path:l,queryParams:u,body:JSON.stringify(d),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal})}else{const d=Cl(this.apiClient,e);l=L("batches/{name}:cancel",d._url),u=d._query,delete d.config,delete d._url,delete d._query,await this.apiClient.request({path:l,queryParams:u,body:JSON.stringify(d),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal})}}async listInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Pl(e);return u=L("batchPredictionJobs",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=zl(m),p=new ys;return Object.assign(p,h),p})}else{const c=Tl(e);return u=L("batches",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Hl(m),p=new ys;return Object.assign(p,h),p})}}async delete(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Rl(this.apiClient,e);return u=L("batchPredictionJobs/{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Xl(m))}else{const c=Sl(this.apiClient,e);return u=L("batches/{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Wl(m))}}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Zl(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function jl(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function eu(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function tu(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Zl(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],jl(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],eu(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function _s(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>tu(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function nu(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const d=o(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function iu(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function su(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],iu(n)),e}function ou(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function ru(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],ou(n)),e}function au(){return{}}function lu(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(c=>nu(c))),r(e,["functionDeclarations"],d)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],su(i));const s=o(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],ru(s)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],au());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function uu(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["allowedFunctionNames"]);return i!=null&&r(e,["allowedFunctionNames"],i),e}function cu(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const i=o(t,["longitude"]);return i!=null&&r(e,["longitude"],i),e}function du(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],cu(n));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function fu(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],uu(n));const i=o(t,["retrievalConfig"]);return i!=null&&r(e,["retrievalConfig"],du(i)),e}function mu(t,e){const n={},i=o(t,["ttl"]);e!==void 0&&i!=null&&r(e,["ttl"],i);const s=o(t,["expireTime"]);e!==void 0&&s!=null&&r(e,["expireTime"],s);const a=o(t,["displayName"]);e!==void 0&&a!=null&&r(e,["displayName"],a);const l=o(t,["contents"]);if(e!==void 0&&l!=null){let m=he(l);Array.isArray(m)&&(m=m.map(h=>_s(h))),r(e,["contents"],m)}const u=o(t,["systemInstruction"]);e!==void 0&&u!=null&&r(e,["systemInstruction"],_s(re(u)));const d=o(t,["tools"]);if(e!==void 0&&d!=null){let m=d;Array.isArray(m)&&(m=m.map(h=>lu(h))),r(e,["tools"],m)}const c=o(t,["toolConfig"]);if(e!==void 0&&c!=null&&r(e,["toolConfig"],fu(c)),o(t,["kmsKeyName"])!==void 0)throw new Error("kmsKeyName parameter is not supported in Gemini API.");return n}function hu(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["model"],yo(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],mu(s,n)),n}function pu(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Se(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function gu(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Se(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function yu(t,e){const n={},i=o(t,["ttl"]);e!==void 0&&i!=null&&r(e,["ttl"],i);const s=o(t,["expireTime"]);return e!==void 0&&s!=null&&r(e,["expireTime"],s),n}function xu(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Se(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],yu(s,n)),n}function vu(t,e){const n={},i=o(t,["pageSize"]);e!==void 0&&i!=null&&r(e,["_query","pageSize"],i);const s=o(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function Cu(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],vu(n,e)),e}function _u(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Tu(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["data"]);i!=null&&r(e,["data"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Su(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["fileUri"]);i!=null&&r(e,["fileUri"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function wu(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],_u(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Tu(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Su(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Ts(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>wu(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Au(t){const e={};if(o(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=o(t,["description"]);n!=null&&r(e,["description"],n);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const s=o(t,["parameters"]);s!=null&&r(e,["parameters"],s);const a=o(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=o(t,["response"]);l!=null&&r(e,["response"],l);const u=o(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function Eu(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function Iu(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Eu(n)),e}function Nu(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function bu(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Nu(n)),e}function Mu(){return{}}function Pu(t){const e={},n=o(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Ru(t){const e={},n=o(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Pu(n));const i=o(t,["authType"]);i!=null&&r(e,["authType"],i);const s=o(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const a=o(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=o(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const u=o(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function ku(t){const e={},n=o(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Ru(n)),e}function Du(){return{}}function Lu(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let h=n;Array.isArray(h)&&(h=h.map(p=>Au(p))),r(e,["functionDeclarations"],h)}const i=o(t,["retrieval"]);i!=null&&r(e,["retrieval"],i);const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],Iu(s));const a=o(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],bu(a)),o(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Mu());const u=o(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],ku(u)),o(t,["urlContext"])!=null&&r(e,["urlContext"],Du());const c=o(t,["codeExecution"]);c!=null&&r(e,["codeExecution"],c);const m=o(t,["computerUse"]);return m!=null&&r(e,["computerUse"],m),e}function Fu(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["allowedFunctionNames"]);return i!=null&&r(e,["allowedFunctionNames"],i),e}function Uu(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const i=o(t,["longitude"]);return i!=null&&r(e,["longitude"],i),e}function $u(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],Uu(n));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function Vu(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],Fu(n));const i=o(t,["retrievalConfig"]);return i!=null&&r(e,["retrievalConfig"],$u(i)),e}function Bu(t,e){const n={},i=o(t,["ttl"]);e!==void 0&&i!=null&&r(e,["ttl"],i);const s=o(t,["expireTime"]);e!==void 0&&s!=null&&r(e,["expireTime"],s);const a=o(t,["displayName"]);e!==void 0&&a!=null&&r(e,["displayName"],a);const l=o(t,["contents"]);if(e!==void 0&&l!=null){let h=he(l);Array.isArray(h)&&(h=h.map(p=>Ts(p))),r(e,["contents"],h)}const u=o(t,["systemInstruction"]);e!==void 0&&u!=null&&r(e,["systemInstruction"],Ts(re(u)));const d=o(t,["tools"]);if(e!==void 0&&d!=null){let h=d;Array.isArray(h)&&(h=h.map(p=>Lu(p))),r(e,["tools"],h)}const c=o(t,["toolConfig"]);e!==void 0&&c!=null&&r(e,["toolConfig"],Vu(c));const m=o(t,["kmsKeyName"]);return e!==void 0&&m!=null&&r(e,["encryption_spec","kmsKeyName"],m),n}function qu(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["model"],yo(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],Bu(s,n)),n}function Gu(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Se(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ju(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Se(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Ou(t,e){const n={},i=o(t,["ttl"]);e!==void 0&&i!=null&&r(e,["ttl"],i);const s=o(t,["expireTime"]);return e!==void 0&&s!=null&&r(e,["expireTime"],s),n}function Hu(t,e){const n={},i=o(e,["name"]);i!=null&&r(n,["_url","name"],Se(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],Ou(s,n)),n}function Wu(t,e){const n={},i=o(t,["pageSize"]);e!==void 0&&i!=null&&r(e,["_query","pageSize"],i);const s=o(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function Ku(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],Wu(n,e)),e}function Dt(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["model"]);s!=null&&r(e,["model"],s);const a=o(t,["createTime"]);a!=null&&r(e,["createTime"],a);const l=o(t,["updateTime"]);l!=null&&r(e,["updateTime"],l);const u=o(t,["expireTime"]);u!=null&&r(e,["expireTime"],u);const d=o(t,["usageMetadata"]);return d!=null&&r(e,["usageMetadata"],d),e}function Yu(){return{}}function zu(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["cachedContents"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>Dt(a))),r(e,["cachedContents"],s)}return e}function Lt(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["model"]);s!=null&&r(e,["model"],s);const a=o(t,["createTime"]);a!=null&&r(e,["createTime"],a);const l=o(t,["updateTime"]);l!=null&&r(e,["updateTime"],l);const u=o(t,["expireTime"]);u!=null&&r(e,["expireTime"],u);const d=o(t,["usageMetadata"]);return d!=null&&r(e,["usageMetadata"],d),e}function Xu(){return{}}function Qu(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["cachedContents"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>Lt(a))),r(e,["cachedContents"],s)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Zu extends Ue{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new yt(Le.PAGED_ITEM_CACHED_CONTENTS,i=>this.listInternal(i),await this.listInternal(n),n)}async create(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=qu(this.apiClient,e);return u=L("cachedContents",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Lt(m))}else{const c=hu(this.apiClient,e);return u=L("cachedContents",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Dt(m))}}async get(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Gu(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Lt(m))}else{const c=pu(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Dt(m))}}async delete(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Ju(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(()=>{const m=Xu(),h=new ps;return Object.assign(h,m),h})}else{const c=gu(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(()=>{const m=Yu(),h=new ps;return Object.assign(h,m),h})}}async update(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Hu(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Lt(m))}else{const c=xu(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Dt(m))}}async listInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Ku(e);return u=L("cachedContents",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Qu(m),p=new gs;return Object.assign(p,h),p})}else{const c=Cu(e);return u=L("cachedContents",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=zu(m),p=new gs;return Object.assign(p,h),p})}}}function Ss(t){var e=typeof Symbol=="function"&&Symbol.iterator,n=e&&t[e],i=0;if(n)return n.call(t);if(t&&typeof t.length=="number")return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function W(t){return this instanceof W?(this.v=t,this):new W(t)}function Ye(t,e,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i=n.apply(t,e||[]),s,a=[];return s=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",l),s[Symbol.asyncIterator]=function(){return this},s;function l(g){return function(y){return Promise.resolve(y).then(g,h)}}function u(g,y){i[g]&&(s[g]=function(x){return new Promise(function(T,I){a.push([g,x,T,I])>1||d(g,x)})},y&&(s[g]=y(s[g])))}function d(g,y){try{c(i[g](y))}catch(x){p(a[0][3],x)}}function c(g){g.value instanceof W?Promise.resolve(g.value.v).then(m,h):p(a[0][2],g)}function m(g){d("next",g)}function h(g){d("throw",g)}function p(g,y){g(y),a.shift(),a.length&&d(a[0][0],a[0][1])}}function ft(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e=t[Symbol.asyncIterator],n;return e?e.call(t):(t=typeof Ss=="function"?Ss(t):t[Symbol.iterator](),n={},i("next"),i("throw"),i("return"),n[Symbol.asyncIterator]=function(){return this},n);function i(a){n[a]=t[a]&&function(l){return new Promise(function(u,d){l=t[a](l),s(u,d,l.done,l.value)})}}function s(a,l,u,d){Promise.resolve(d).then(function(c){a({value:c,done:u})},l)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function ju(t){var e;if(t.candidates==null||t.candidates.length===0)return!1;const n=(e=t.candidates[0])===null||e===void 0?void 0:e.content;return n===void 0?!1:ko(n)}function ko(t){if(t.parts===void 0||t.parts.length===0)return!1;for(const e of t.parts)if(e===void 0||Object.keys(e).length===0||!e.thought&&e.text!==void 0&&e.text==="")return!1;return!0}function ec(t){if(t.length!==0){for(const e of t)if(e.role!=="user"&&e.role!=="model")throw new Error(`Role must be user or model, but got ${e.role}.`)}}function ws(t){if(t===void 0||t.length===0)return[];const e=[],n=t.length;let i=0;for(;i<n;)if(t[i].role==="user")e.push(t[i]),i++;else{const s=[];let a=!0;for(;i<n&&t[i].role==="model";)s.push(t[i]),a&&!ko(t[i])&&(a=!1),i++;a?e.push(...s):e.pop()}return e}class tc{constructor(e,n){this.modelsModule=e,this.apiClient=n}create(e){return new nc(this.apiClient,this.modelsModule,e.model,e.config,structuredClone(e.history))}}let nc=class{constructor(e,n,i,s={},a=[]){this.apiClient=e,this.modelsModule=n,this.model=i,this.config=s,this.history=a,this.sendPromise=Promise.resolve(),ec(a)}async sendMessage(e){var n;await this.sendPromise;const i=re(e.message),s=this.modelsModule.generateContent({model:this.model,contents:this.getHistory(!0).concat(i),config:(n=e.config)!==null&&n!==void 0?n:this.config});return this.sendPromise=(async()=>{var a,l,u;const d=await s,c=(l=(a=d.candidates)===null||a===void 0?void 0:a[0])===null||l===void 0?void 0:l.content,m=d.automaticFunctionCallingHistory,h=this.getHistory(!0).length;let p=[];m!=null&&(p=(u=m.slice(h))!==null&&u!==void 0?u:[]);const g=c?[c]:[];this.recordHistory(i,g,p)})(),await this.sendPromise.catch(()=>{this.sendPromise=Promise.resolve()}),s}async sendMessageStream(e){var n;await this.sendPromise;const i=re(e.message),s=this.modelsModule.generateContentStream({model:this.model,contents:this.getHistory(!0).concat(i),config:(n=e.config)!==null&&n!==void 0?n:this.config});this.sendPromise=s.then(()=>{}).catch(()=>{});const a=await s;return this.processStreamResponse(a,i)}getHistory(e=!1){const n=e?ws(this.history):this.history;return structuredClone(n)}processStreamResponse(e,n){var i,s;return Ye(this,arguments,function*(){var l,u,d,c;const m=[];try{for(var h=!0,p=ft(e),g;g=yield W(p.next()),l=g.done,!l;h=!0){c=g.value,h=!1;const y=c;if(ju(y)){const x=(s=(i=y.candidates)===null||i===void 0?void 0:i[0])===null||s===void 0?void 0:s.content;x!==void 0&&m.push(x)}yield yield W(y)}}catch(y){u={error:y}}finally{try{!h&&!l&&(d=p.return)&&(yield W(d.call(p)))}finally{if(u)throw u.error}}this.recordHistory(n,m)})}recordHistory(e,n,i){let s=[];n.length>0&&n.every(a=>a.role!==void 0)?s=n:s.push({role:"model",parts:[]}),i&&i.length>0?this.history.push(...ws(i)):this.history.push(e),this.history.push(...s)}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class tn extends Error{constructor(e){super(e.message),this.name="ApiError",this.status=e.status,Object.setPrototypeOf(this,tn.prototype)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function ic(t,e){const n={},i=o(t,["pageSize"]);e!==void 0&&i!=null&&r(e,["_query","pageSize"],i);const s=o(t,["pageToken"]);return e!==void 0&&s!=null&&r(e,["_query","pageToken"],s),n}function sc(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],ic(n,e)),e}function oc(t){const e={},n=o(t,["details"]);n!=null&&r(e,["details"],n);const i=o(t,["message"]);i!=null&&r(e,["message"],i);const s=o(t,["code"]);return s!=null&&r(e,["code"],s),e}function rc(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["mimeType"]);s!=null&&r(e,["mimeType"],s);const a=o(t,["sizeBytes"]);a!=null&&r(e,["sizeBytes"],a);const l=o(t,["createTime"]);l!=null&&r(e,["createTime"],l);const u=o(t,["expirationTime"]);u!=null&&r(e,["expirationTime"],u);const d=o(t,["updateTime"]);d!=null&&r(e,["updateTime"],d);const c=o(t,["sha256Hash"]);c!=null&&r(e,["sha256Hash"],c);const m=o(t,["uri"]);m!=null&&r(e,["uri"],m);const h=o(t,["downloadUri"]);h!=null&&r(e,["downloadUri"],h);const p=o(t,["state"]);p!=null&&r(e,["state"],p);const g=o(t,["source"]);g!=null&&r(e,["source"],g);const y=o(t,["videoMetadata"]);y!=null&&r(e,["videoMetadata"],y);const x=o(t,["error"]);return x!=null&&r(e,["error"],oc(x)),e}function ac(t){const e={},n=o(t,["file"]);n!=null&&r(e,["file"],rc(n));const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function lc(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","file"],wo(n));const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function uc(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","file"],wo(n));const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function cc(t){const e={},n=o(t,["details"]);n!=null&&r(e,["details"],n);const i=o(t,["message"]);i!=null&&r(e,["message"],i);const s=o(t,["code"]);return s!=null&&r(e,["code"],s),e}function Pn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["mimeType"]);s!=null&&r(e,["mimeType"],s);const a=o(t,["sizeBytes"]);a!=null&&r(e,["sizeBytes"],a);const l=o(t,["createTime"]);l!=null&&r(e,["createTime"],l);const u=o(t,["expirationTime"]);u!=null&&r(e,["expirationTime"],u);const d=o(t,["updateTime"]);d!=null&&r(e,["updateTime"],d);const c=o(t,["sha256Hash"]);c!=null&&r(e,["sha256Hash"],c);const m=o(t,["uri"]);m!=null&&r(e,["uri"],m);const h=o(t,["downloadUri"]);h!=null&&r(e,["downloadUri"],h);const p=o(t,["state"]);p!=null&&r(e,["state"],p);const g=o(t,["source"]);g!=null&&r(e,["source"],g);const y=o(t,["videoMetadata"]);y!=null&&r(e,["videoMetadata"],y);const x=o(t,["error"]);return x!=null&&r(e,["error"],cc(x)),e}function dc(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["files"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>Pn(a))),r(e,["files"],s)}return e}function fc(){return{}}function mc(){return{}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let hc=class extends Ue{constructor(e){super(),this.apiClient=e,this.list=async(n={})=>new yt(Le.PAGED_ITEM_FILES,i=>this.listInternal(i),await this.listInternal(n),n)}async upload(e){if(this.apiClient.isVertexAI())throw new Error("Vertex AI does not support uploading files. You can share files through a GCS bucket.");return this.apiClient.uploadFile(e.file,e.config).then(n=>Pn(n))}async download(e){await this.apiClient.downloadFile(e)}async listInternal(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=sc(e);return a=L("files",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>{const c=dc(d),m=new Ra;return Object.assign(m,c),m})}}async createInternal(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=ac(e);return a=L("upload/v1beta/files",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(()=>{const d=fc(),c=new ka;return Object.assign(c,d),c})}}async get(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=lc(e);return a=L("files/{file}",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>Pn(d))}}async delete(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=uc(e);return a=L("files/{file}",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(()=>{const d=mc(),c=new Da;return Object.assign(c,d),c})}}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function pc(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function gc(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Do(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],pc(n)),e}function yc(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],gc(n)),e}function xc(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const i=o(t,["voiceConfig"]);return i!=null&&r(e,["voiceConfig"],Do(i)),e}function vc(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>xc(s))),r(e,["speakerVoiceConfigs"],i)}return e}function Cc(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Do(n));const i=o(t,["multiSpeakerVoiceConfig"]);i!=null&&r(e,["multiSpeakerVoiceConfig"],vc(i));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function _c(t){const e={},n=o(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],yc(n)),o(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function Tc(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Sc(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function wc(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ac(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["data"]);i!=null&&r(e,["data"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ec(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ic(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["fileUri"]);i!=null&&r(e,["fileUri"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Nc(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Tc(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],wc(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ec(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bc(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Sc(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Ac(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ic(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Mc(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Nc(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Pc(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>bc(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Rc(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const d=o(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function kc(t){const e={};if(o(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=o(t,["description"]);n!=null&&r(e,["description"],n);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const s=o(t,["parameters"]);s!=null&&r(e,["parameters"],s);const a=o(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=o(t,["response"]);l!=null&&r(e,["response"],l);const u=o(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function Dc(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function Lc(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function Fc(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Dc(n)),e}function Uc(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Lc(n)),e}function $c(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function Vc(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function Bc(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],$c(n)),e}function qc(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],Vc(n)),e}function Gc(){return{}}function Jc(t){const e={},n=o(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function Oc(t){const e={},n=o(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],Jc(n));const i=o(t,["authType"]);i!=null&&r(e,["authType"],i);const s=o(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const a=o(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=o(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const u=o(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function Hc(t){const e={},n=o(t,["authConfig"]);return n!=null&&r(e,["authConfig"],Oc(n)),e}function Wc(){return{}}function Kc(){return{}}function Yc(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(c=>Rc(c))),r(e,["functionDeclarations"],d)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],Fc(i));const s=o(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Bc(s)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],Wc());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function zc(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let h=n;Array.isArray(h)&&(h=h.map(p=>kc(p))),r(e,["functionDeclarations"],h)}const i=o(t,["retrieval"]);i!=null&&r(e,["retrieval"],i);const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],Uc(s));const a=o(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],qc(a)),o(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],Gc());const u=o(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],Hc(u)),o(t,["urlContext"])!=null&&r(e,["urlContext"],Kc());const c=o(t,["codeExecution"]);c!=null&&r(e,["codeExecution"],c);const m=o(t,["computerUse"]);return m!=null&&r(e,["computerUse"],m),e}function Xc(t){const e={},n=o(t,["handle"]);if(n!=null&&r(e,["handle"],n),o(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function Qc(t){const e={},n=o(t,["handle"]);n!=null&&r(e,["handle"],n);const i=o(t,["transparent"]);return i!=null&&r(e,["transparent"],i),e}function As(){return{}}function Es(){return{}}function Zc(t){const e={},n=o(t,["disabled"]);n!=null&&r(e,["disabled"],n);const i=o(t,["startOfSpeechSensitivity"]);i!=null&&r(e,["startOfSpeechSensitivity"],i);const s=o(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const a=o(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=o(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function jc(t){const e={},n=o(t,["disabled"]);n!=null&&r(e,["disabled"],n);const i=o(t,["startOfSpeechSensitivity"]);i!=null&&r(e,["startOfSpeechSensitivity"],i);const s=o(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const a=o(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=o(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function ed(t){const e={},n=o(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],Zc(n));const i=o(t,["activityHandling"]);i!=null&&r(e,["activityHandling"],i);const s=o(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function td(t){const e={},n=o(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],jc(n));const i=o(t,["activityHandling"]);i!=null&&r(e,["activityHandling"],i);const s=o(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function nd(t){const e={},n=o(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function id(t){const e={},n=o(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function sd(t){const e={},n=o(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const i=o(t,["slidingWindow"]);return i!=null&&r(e,["slidingWindow"],nd(i)),e}function od(t){const e={},n=o(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const i=o(t,["slidingWindow"]);return i!=null&&r(e,["slidingWindow"],id(i)),e}function rd(t){const e={},n=o(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function ad(t){const e={},n=o(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function ld(t,e){const n={},i=o(t,["generationConfig"]);e!==void 0&&i!=null&&r(e,["setup","generationConfig"],i);const s=o(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const a=o(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=o(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const u=o(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const d=o(t,["maxOutputTokens"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","maxOutputTokens"],d);const c=o(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const m=o(t,["seed"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","seed"],m);const h=o(t,["speechConfig"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","speechConfig"],Cc(Zn(h)));const p=o(t,["enableAffectiveDialog"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],p);const g=o(t,["systemInstruction"]);e!==void 0&&g!=null&&r(e,["setup","systemInstruction"],Mc(re(g)));const y=o(t,["tools"]);if(e!==void 0&&y!=null){let C=Xe(y);Array.isArray(C)&&(C=C.map(E=>Yc(ze(E)))),r(e,["setup","tools"],C)}const x=o(t,["sessionResumption"]);e!==void 0&&x!=null&&r(e,["setup","sessionResumption"],Xc(x));const T=o(t,["inputAudioTranscription"]);e!==void 0&&T!=null&&r(e,["setup","inputAudioTranscription"],As());const I=o(t,["outputAudioTranscription"]);e!==void 0&&I!=null&&r(e,["setup","outputAudioTranscription"],As());const w=o(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],ed(w));const A=o(t,["contextWindowCompression"]);e!==void 0&&A!=null&&r(e,["setup","contextWindowCompression"],sd(A));const _=o(t,["proactivity"]);return e!==void 0&&_!=null&&r(e,["setup","proactivity"],rd(_)),n}function ud(t,e){const n={},i=o(t,["generationConfig"]);e!==void 0&&i!=null&&r(e,["setup","generationConfig"],i);const s=o(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const a=o(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=o(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const u=o(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const d=o(t,["maxOutputTokens"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","maxOutputTokens"],d);const c=o(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const m=o(t,["seed"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","seed"],m);const h=o(t,["speechConfig"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","speechConfig"],_c(Zn(h)));const p=o(t,["enableAffectiveDialog"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],p);const g=o(t,["systemInstruction"]);e!==void 0&&g!=null&&r(e,["setup","systemInstruction"],Pc(re(g)));const y=o(t,["tools"]);if(e!==void 0&&y!=null){let C=Xe(y);Array.isArray(C)&&(C=C.map(E=>zc(ze(E)))),r(e,["setup","tools"],C)}const x=o(t,["sessionResumption"]);e!==void 0&&x!=null&&r(e,["setup","sessionResumption"],Qc(x));const T=o(t,["inputAudioTranscription"]);e!==void 0&&T!=null&&r(e,["setup","inputAudioTranscription"],Es());const I=o(t,["outputAudioTranscription"]);e!==void 0&&I!=null&&r(e,["setup","outputAudioTranscription"],Es());const w=o(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],td(w));const A=o(t,["contextWindowCompression"]);e!==void 0&&A!=null&&r(e,["setup","contextWindowCompression"],od(A));const _=o(t,["proactivity"]);return e!==void 0&&_!=null&&r(e,["setup","proactivity"],ad(_)),n}function cd(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["setup","model"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],ld(s,n)),n}function dd(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["setup","model"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],ud(s,n)),n}function fd(){return{}}function md(){return{}}function hd(){return{}}function pd(){return{}}function gd(t){const e={},n=o(t,["media"]);n!=null&&r(e,["mediaChunks"],xo(n));const i=o(t,["audio"]);i!=null&&r(e,["audio"],Co(i));const s=o(t,["audioStreamEnd"]);s!=null&&r(e,["audioStreamEnd"],s);const a=o(t,["video"]);a!=null&&r(e,["video"],vo(a));const l=o(t,["text"]);return l!=null&&r(e,["text"],l),o(t,["activityStart"])!=null&&r(e,["activityStart"],fd()),o(t,["activityEnd"])!=null&&r(e,["activityEnd"],hd()),e}function yd(t){const e={},n=o(t,["media"]);n!=null&&r(e,["mediaChunks"],xo(n));const i=o(t,["audio"]);i!=null&&r(e,["audio"],Co(i));const s=o(t,["audioStreamEnd"]);s!=null&&r(e,["audioStreamEnd"],s);const a=o(t,["video"]);a!=null&&r(e,["video"],vo(a));const l=o(t,["text"]);return l!=null&&r(e,["text"],l),o(t,["activityStart"])!=null&&r(e,["activityStart"],md()),o(t,["activityEnd"])!=null&&r(e,["activityEnd"],pd()),e}function Lo(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const i=o(t,["weight"]);return i!=null&&r(e,["weight"],i),e}function xd(t){const e={},n=o(t,["weightedPrompts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Lo(s))),r(e,["weightedPrompts"],i)}return e}function Fo(t){const e={},n=o(t,["temperature"]);n!=null&&r(e,["temperature"],n);const i=o(t,["topK"]);i!=null&&r(e,["topK"],i);const s=o(t,["seed"]);s!=null&&r(e,["seed"],s);const a=o(t,["guidance"]);a!=null&&r(e,["guidance"],a);const l=o(t,["bpm"]);l!=null&&r(e,["bpm"],l);const u=o(t,["density"]);u!=null&&r(e,["density"],u);const d=o(t,["brightness"]);d!=null&&r(e,["brightness"],d);const c=o(t,["scale"]);c!=null&&r(e,["scale"],c);const m=o(t,["muteBass"]);m!=null&&r(e,["muteBass"],m);const h=o(t,["muteDrums"]);h!=null&&r(e,["muteDrums"],h);const p=o(t,["onlyBassAndDrums"]);return p!=null&&r(e,["onlyBassAndDrums"],p),e}function vd(t){const e={},n=o(t,["musicGenerationConfig"]);return n!=null&&r(e,["musicGenerationConfig"],Fo(n)),e}function Uo(t){const e={},n=o(t,["model"]);return n!=null&&r(e,["model"],n),e}function $o(t){const e={},n=o(t,["weightedPrompts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Lo(s))),r(e,["weightedPrompts"],i)}return e}function Rn(t){const e={},n=o(t,["setup"]);n!=null&&r(e,["setup"],Uo(n));const i=o(t,["clientContent"]);i!=null&&r(e,["clientContent"],$o(i));const s=o(t,["musicGenerationConfig"]);s!=null&&r(e,["musicGenerationConfig"],Fo(s));const a=o(t,["playbackControl"]);return a!=null&&r(e,["playbackControl"],a),e}function Cd(){return{}}function _d(t){const e={},n=o(t,["sessionId"]);return n!=null&&r(e,["sessionId"],n),e}function Td(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Sd(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function wd(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ad(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["data"]);i!=null&&r(e,["data"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Ed(t){const e={},n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Id(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["fileUri"]);i!=null&&r(e,["fileUri"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Nd(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Td(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],wd(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ed(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bd(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Sd(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Ad(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Id(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Md(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Nd(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Pd(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>bd(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Is(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const i=o(t,["finished"]);return i!=null&&r(e,["finished"],i),e}function Ns(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const i=o(t,["finished"]);return i!=null&&r(e,["finished"],i),e}function Rd(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const i=o(t,["urlRetrievalStatus"]);return i!=null&&r(e,["urlRetrievalStatus"],i),e}function kd(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Rd(s))),r(e,["urlMetadata"],i)}return e}function Dd(t){const e={},n=o(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],Md(n));const i=o(t,["turnComplete"]);i!=null&&r(e,["turnComplete"],i);const s=o(t,["interrupted"]);s!=null&&r(e,["interrupted"],s);const a=o(t,["groundingMetadata"]);a!=null&&r(e,["groundingMetadata"],a);const l=o(t,["generationComplete"]);l!=null&&r(e,["generationComplete"],l);const u=o(t,["inputTranscription"]);u!=null&&r(e,["inputTranscription"],Is(u));const d=o(t,["outputTranscription"]);d!=null&&r(e,["outputTranscription"],Is(d));const c=o(t,["urlContextMetadata"]);return c!=null&&r(e,["urlContextMetadata"],kd(c)),e}function Ld(t){const e={},n=o(t,["modelTurn"]);n!=null&&r(e,["modelTurn"],Pd(n));const i=o(t,["turnComplete"]);i!=null&&r(e,["turnComplete"],i);const s=o(t,["interrupted"]);s!=null&&r(e,["interrupted"],s);const a=o(t,["groundingMetadata"]);a!=null&&r(e,["groundingMetadata"],a);const l=o(t,["generationComplete"]);l!=null&&r(e,["generationComplete"],l);const u=o(t,["inputTranscription"]);u!=null&&r(e,["inputTranscription"],Ns(u));const d=o(t,["outputTranscription"]);return d!=null&&r(e,["outputTranscription"],Ns(d)),e}function Fd(t){const e={},n=o(t,["id"]);n!=null&&r(e,["id"],n);const i=o(t,["args"]);i!=null&&r(e,["args"],i);const s=o(t,["name"]);return s!=null&&r(e,["name"],s),e}function Ud(t){const e={},n=o(t,["args"]);n!=null&&r(e,["args"],n);const i=o(t,["name"]);return i!=null&&r(e,["name"],i),e}function $d(t){const e={},n=o(t,["functionCalls"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Fd(s))),r(e,["functionCalls"],i)}return e}function Vd(t){const e={},n=o(t,["functionCalls"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Ud(s))),r(e,["functionCalls"],i)}return e}function Bd(t){const e={},n=o(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function qd(t){const e={},n=o(t,["ids"]);return n!=null&&r(e,["ids"],n),e}function wt(t){const e={},n=o(t,["modality"]);n!=null&&r(e,["modality"],n);const i=o(t,["tokenCount"]);return i!=null&&r(e,["tokenCount"],i),e}function At(t){const e={},n=o(t,["modality"]);n!=null&&r(e,["modality"],n);const i=o(t,["tokenCount"]);return i!=null&&r(e,["tokenCount"],i),e}function Gd(t){const e={},n=o(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const i=o(t,["cachedContentTokenCount"]);i!=null&&r(e,["cachedContentTokenCount"],i);const s=o(t,["responseTokenCount"]);s!=null&&r(e,["responseTokenCount"],s);const a=o(t,["toolUsePromptTokenCount"]);a!=null&&r(e,["toolUsePromptTokenCount"],a);const l=o(t,["thoughtsTokenCount"]);l!=null&&r(e,["thoughtsTokenCount"],l);const u=o(t,["totalTokenCount"]);u!=null&&r(e,["totalTokenCount"],u);const d=o(t,["promptTokensDetails"]);if(d!=null){let p=d;Array.isArray(p)&&(p=p.map(g=>wt(g))),r(e,["promptTokensDetails"],p)}const c=o(t,["cacheTokensDetails"]);if(c!=null){let p=c;Array.isArray(p)&&(p=p.map(g=>wt(g))),r(e,["cacheTokensDetails"],p)}const m=o(t,["responseTokensDetails"]);if(m!=null){let p=m;Array.isArray(p)&&(p=p.map(g=>wt(g))),r(e,["responseTokensDetails"],p)}const h=o(t,["toolUsePromptTokensDetails"]);if(h!=null){let p=h;Array.isArray(p)&&(p=p.map(g=>wt(g))),r(e,["toolUsePromptTokensDetails"],p)}return e}function Jd(t){const e={},n=o(t,["promptTokenCount"]);n!=null&&r(e,["promptTokenCount"],n);const i=o(t,["cachedContentTokenCount"]);i!=null&&r(e,["cachedContentTokenCount"],i);const s=o(t,["candidatesTokenCount"]);s!=null&&r(e,["responseTokenCount"],s);const a=o(t,["toolUsePromptTokenCount"]);a!=null&&r(e,["toolUsePromptTokenCount"],a);const l=o(t,["thoughtsTokenCount"]);l!=null&&r(e,["thoughtsTokenCount"],l);const u=o(t,["totalTokenCount"]);u!=null&&r(e,["totalTokenCount"],u);const d=o(t,["promptTokensDetails"]);if(d!=null){let g=d;Array.isArray(g)&&(g=g.map(y=>At(y))),r(e,["promptTokensDetails"],g)}const c=o(t,["cacheTokensDetails"]);if(c!=null){let g=c;Array.isArray(g)&&(g=g.map(y=>At(y))),r(e,["cacheTokensDetails"],g)}const m=o(t,["candidatesTokensDetails"]);if(m!=null){let g=m;Array.isArray(g)&&(g=g.map(y=>At(y))),r(e,["responseTokensDetails"],g)}const h=o(t,["toolUsePromptTokensDetails"]);if(h!=null){let g=h;Array.isArray(g)&&(g=g.map(y=>At(y))),r(e,["toolUsePromptTokensDetails"],g)}const p=o(t,["trafficType"]);return p!=null&&r(e,["trafficType"],p),e}function Od(t){const e={},n=o(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function Hd(t){const e={},n=o(t,["timeLeft"]);return n!=null&&r(e,["timeLeft"],n),e}function Wd(t){const e={},n=o(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const i=o(t,["resumable"]);i!=null&&r(e,["resumable"],i);const s=o(t,["lastConsumedClientMessageIndex"]);return s!=null&&r(e,["lastConsumedClientMessageIndex"],s),e}function Kd(t){const e={},n=o(t,["newHandle"]);n!=null&&r(e,["newHandle"],n);const i=o(t,["resumable"]);i!=null&&r(e,["resumable"],i);const s=o(t,["lastConsumedClientMessageIndex"]);return s!=null&&r(e,["lastConsumedClientMessageIndex"],s),e}function Yd(t){const e={};o(t,["setupComplete"])!=null&&r(e,["setupComplete"],Cd());const i=o(t,["serverContent"]);i!=null&&r(e,["serverContent"],Dd(i));const s=o(t,["toolCall"]);s!=null&&r(e,["toolCall"],$d(s));const a=o(t,["toolCallCancellation"]);a!=null&&r(e,["toolCallCancellation"],Bd(a));const l=o(t,["usageMetadata"]);l!=null&&r(e,["usageMetadata"],Gd(l));const u=o(t,["goAway"]);u!=null&&r(e,["goAway"],Od(u));const d=o(t,["sessionResumptionUpdate"]);return d!=null&&r(e,["sessionResumptionUpdate"],Wd(d)),e}function zd(t){const e={},n=o(t,["setupComplete"]);n!=null&&r(e,["setupComplete"],_d(n));const i=o(t,["serverContent"]);i!=null&&r(e,["serverContent"],Ld(i));const s=o(t,["toolCall"]);s!=null&&r(e,["toolCall"],Vd(s));const a=o(t,["toolCallCancellation"]);a!=null&&r(e,["toolCallCancellation"],qd(a));const l=o(t,["usageMetadata"]);l!=null&&r(e,["usageMetadata"],Jd(l));const u=o(t,["goAway"]);u!=null&&r(e,["goAway"],Hd(u));const d=o(t,["sessionResumptionUpdate"]);return d!=null&&r(e,["sessionResumptionUpdate"],Kd(d)),e}function Xd(){return{}}function Qd(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const i=o(t,["weight"]);return i!=null&&r(e,["weight"],i),e}function Zd(t){const e={},n=o(t,["weightedPrompts"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Qd(s))),r(e,["weightedPrompts"],i)}return e}function jd(t){const e={},n=o(t,["temperature"]);n!=null&&r(e,["temperature"],n);const i=o(t,["topK"]);i!=null&&r(e,["topK"],i);const s=o(t,["seed"]);s!=null&&r(e,["seed"],s);const a=o(t,["guidance"]);a!=null&&r(e,["guidance"],a);const l=o(t,["bpm"]);l!=null&&r(e,["bpm"],l);const u=o(t,["density"]);u!=null&&r(e,["density"],u);const d=o(t,["brightness"]);d!=null&&r(e,["brightness"],d);const c=o(t,["scale"]);c!=null&&r(e,["scale"],c);const m=o(t,["muteBass"]);m!=null&&r(e,["muteBass"],m);const h=o(t,["muteDrums"]);h!=null&&r(e,["muteDrums"],h);const p=o(t,["onlyBassAndDrums"]);return p!=null&&r(e,["onlyBassAndDrums"],p),e}function ef(t){const e={},n=o(t,["clientContent"]);n!=null&&r(e,["clientContent"],Zd(n));const i=o(t,["musicGenerationConfig"]);return i!=null&&r(e,["musicGenerationConfig"],jd(i)),e}function tf(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);i!=null&&r(e,["mimeType"],i);const s=o(t,["sourceMetadata"]);return s!=null&&r(e,["sourceMetadata"],ef(s)),e}function nf(t){const e={},n=o(t,["audioChunks"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>tf(s))),r(e,["audioChunks"],i)}return e}function sf(t){const e={},n=o(t,["text"]);n!=null&&r(e,["text"],n);const i=o(t,["filteredReason"]);return i!=null&&r(e,["filteredReason"],i),e}function of(t){const e={};o(t,["setupComplete"])!=null&&r(e,["setupComplete"],Xd());const i=o(t,["serverContent"]);i!=null&&r(e,["serverContent"],nf(i));const s=o(t,["filteredPrompt"]);return s!=null&&r(e,["filteredPrompt"],sf(s)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function rf(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function af(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function lf(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function uf(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],rf(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],af(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],lf(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function nn(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>uf(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function cf(t){const e={},n=o(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const i=o(t,["default"]);i!=null&&r(e,["default"],i);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const a=o(t,["enum"]);a!=null&&r(e,["enum"],a);const l=o(t,["example"]);l!=null&&r(e,["example"],l);const u=o(t,["format"]);u!=null&&r(e,["format"],u);const d=o(t,["items"]);d!=null&&r(e,["items"],d);const c=o(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const m=o(t,["maxLength"]);m!=null&&r(e,["maxLength"],m);const h=o(t,["maxProperties"]);h!=null&&r(e,["maxProperties"],h);const p=o(t,["maximum"]);p!=null&&r(e,["maximum"],p);const g=o(t,["minItems"]);g!=null&&r(e,["minItems"],g);const y=o(t,["minLength"]);y!=null&&r(e,["minLength"],y);const x=o(t,["minProperties"]);x!=null&&r(e,["minProperties"],x);const T=o(t,["minimum"]);T!=null&&r(e,["minimum"],T);const I=o(t,["nullable"]);I!=null&&r(e,["nullable"],I);const w=o(t,["pattern"]);w!=null&&r(e,["pattern"],w);const A=o(t,["properties"]);A!=null&&r(e,["properties"],A);const _=o(t,["propertyOrdering"]);_!=null&&r(e,["propertyOrdering"],_);const C=o(t,["required"]);C!=null&&r(e,["required"],C);const E=o(t,["title"]);E!=null&&r(e,["title"],E);const N=o(t,["type"]);return N!=null&&r(e,["type"],N),e}function df(t){const e={};if(o(t,["method"])!==void 0)throw new Error("method parameter is not supported in Gemini API.");const n=o(t,["category"]);n!=null&&r(e,["category"],n);const i=o(t,["threshold"]);return i!=null&&r(e,["threshold"],i),e}function ff(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const d=o(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function mf(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function hf(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],mf(n)),e}function pf(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function gf(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],pf(n)),e}function yf(){return{}}function xf(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(c=>ff(c))),r(e,["functionDeclarations"],d)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],hf(i));const s=o(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],gf(s)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],yf());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function vf(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["allowedFunctionNames"]);return i!=null&&r(e,["allowedFunctionNames"],i),e}function Cf(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const i=o(t,["longitude"]);return i!=null&&r(e,["longitude"],i),e}function _f(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],Cf(n));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function Tf(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],vf(n));const i=o(t,["retrievalConfig"]);return i!=null&&r(e,["retrievalConfig"],_f(i)),e}function Sf(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Vo(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],Sf(n)),e}function wf(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const i=o(t,["voiceConfig"]);return i!=null&&r(e,["voiceConfig"],Vo(i)),e}function Af(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>wf(s))),r(e,["speakerVoiceConfigs"],i)}return e}function Ef(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Vo(n));const i=o(t,["multiSpeakerVoiceConfig"]);i!=null&&r(e,["multiSpeakerVoiceConfig"],Af(i));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function If(t){const e={},n=o(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const i=o(t,["thinkingBudget"]);return i!=null&&r(e,["thinkingBudget"],i),e}function Nf(t,e,n){const i={},s=o(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],nn(re(s)));const a=o(e,["temperature"]);a!=null&&r(i,["temperature"],a);const l=o(e,["topP"]);l!=null&&r(i,["topP"],l);const u=o(e,["topK"]);u!=null&&r(i,["topK"],u);const d=o(e,["candidateCount"]);d!=null&&r(i,["candidateCount"],d);const c=o(e,["maxOutputTokens"]);c!=null&&r(i,["maxOutputTokens"],c);const m=o(e,["stopSequences"]);m!=null&&r(i,["stopSequences"],m);const h=o(e,["responseLogprobs"]);h!=null&&r(i,["responseLogprobs"],h);const p=o(e,["logprobs"]);p!=null&&r(i,["logprobs"],p);const g=o(e,["presencePenalty"]);g!=null&&r(i,["presencePenalty"],g);const y=o(e,["frequencyPenalty"]);y!=null&&r(i,["frequencyPenalty"],y);const x=o(e,["seed"]);x!=null&&r(i,["seed"],x);const T=o(e,["responseMimeType"]);T!=null&&r(i,["responseMimeType"],T);const I=o(e,["responseSchema"]);I!=null&&r(i,["responseSchema"],cf(Xn(I)));const w=o(e,["responseJsonSchema"]);if(w!=null&&r(i,["responseJsonSchema"],w),o(e,["routingConfig"])!==void 0)throw new Error("routingConfig parameter is not supported in Gemini API.");if(o(e,["modelSelectionConfig"])!==void 0)throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const A=o(e,["safetySettings"]);if(n!==void 0&&A!=null){let R=A;Array.isArray(R)&&(R=R.map(K=>df(K))),r(n,["safetySettings"],R)}const _=o(e,["tools"]);if(n!==void 0&&_!=null){let R=Xe(_);Array.isArray(R)&&(R=R.map(K=>xf(ze(K)))),r(n,["tools"],R)}const C=o(e,["toolConfig"]);if(n!==void 0&&C!=null&&r(n,["toolConfig"],Tf(C)),o(e,["labels"])!==void 0)throw new Error("labels parameter is not supported in Gemini API.");const E=o(e,["cachedContent"]);n!==void 0&&E!=null&&r(n,["cachedContent"],Se(t,E));const N=o(e,["responseModalities"]);N!=null&&r(i,["responseModalities"],N);const D=o(e,["mediaResolution"]);D!=null&&r(i,["mediaResolution"],D);const b=o(e,["speechConfig"]);if(b!=null&&r(i,["speechConfig"],Ef(Qn(b))),o(e,["audioTimestamp"])!==void 0)throw new Error("audioTimestamp parameter is not supported in Gemini API.");const k=o(e,["thinkingConfig"]);return k!=null&&r(i,["thinkingConfig"],If(k)),i}function bs(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["contents"]);if(s!=null){let l=he(s);Array.isArray(l)&&(l=l.map(u=>nn(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["generationConfig"],Nf(t,a,n)),n}function bf(t,e){const n={},i=o(t,["taskType"]);e!==void 0&&i!=null&&r(e,["requests[]","taskType"],i);const s=o(t,["title"]);e!==void 0&&s!=null&&r(e,["requests[]","title"],s);const a=o(t,["outputDimensionality"]);if(e!==void 0&&a!=null&&r(e,["requests[]","outputDimensionality"],a),o(t,["mimeType"])!==void 0)throw new Error("mimeType parameter is not supported in Gemini API.");if(o(t,["autoTruncate"])!==void 0)throw new Error("autoTruncate parameter is not supported in Gemini API.");return n}function Mf(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["contents"]);s!=null&&r(n,["requests[]","content"],To(t,s));const a=o(e,["config"]);a!=null&&r(n,["config"],bf(a,n));const l=o(e,["model"]);return l!==void 0&&r(n,["requests[]","model"],J(t,l)),n}function Pf(t,e){const n={};if(o(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(o(t,["negativePrompt"])!==void 0)throw new Error("negativePrompt parameter is not supported in Gemini API.");const i=o(t,["numberOfImages"]);e!==void 0&&i!=null&&r(e,["parameters","sampleCount"],i);const s=o(t,["aspectRatio"]);e!==void 0&&s!=null&&r(e,["parameters","aspectRatio"],s);const a=o(t,["guidanceScale"]);if(e!==void 0&&a!=null&&r(e,["parameters","guidanceScale"],a),o(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const l=o(t,["safetyFilterLevel"]);e!==void 0&&l!=null&&r(e,["parameters","safetySetting"],l);const u=o(t,["personGeneration"]);e!==void 0&&u!=null&&r(e,["parameters","personGeneration"],u);const d=o(t,["includeSafetyAttributes"]);e!==void 0&&d!=null&&r(e,["parameters","includeSafetyAttributes"],d);const c=o(t,["includeRaiReason"]);e!==void 0&&c!=null&&r(e,["parameters","includeRaiReason"],c);const m=o(t,["language"]);e!==void 0&&m!=null&&r(e,["parameters","language"],m);const h=o(t,["outputMimeType"]);e!==void 0&&h!=null&&r(e,["parameters","outputOptions","mimeType"],h);const p=o(t,["outputCompressionQuality"]);if(e!==void 0&&p!=null&&r(e,["parameters","outputOptions","compressionQuality"],p),o(t,["addWatermark"])!==void 0)throw new Error("addWatermark parameter is not supported in Gemini API.");if(o(t,["enhancePrompt"])!==void 0)throw new Error("enhancePrompt parameter is not supported in Gemini API.");return n}function Rf(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=o(e,["config"]);return a!=null&&r(n,["config"],Pf(a,n)),n}function kf(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","name"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Df(t,e,n){const i={},s=o(e,["pageSize"]);n!==void 0&&s!=null&&r(n,["_query","pageSize"],s);const a=o(e,["pageToken"]);n!==void 0&&a!=null&&r(n,["_query","pageToken"],a);const l=o(e,["filter"]);n!==void 0&&l!=null&&r(n,["_query","filter"],l);const u=o(e,["queryBase"]);return n!==void 0&&u!=null&&r(n,["_url","models_url"],Ao(t,u)),i}function Lf(t,e){const n={},i=o(e,["config"]);return i!=null&&r(n,["config"],Df(t,i,n)),n}function Ff(t,e){const n={},i=o(t,["displayName"]);e!==void 0&&i!=null&&r(e,["displayName"],i);const s=o(t,["description"]);e!==void 0&&s!=null&&r(e,["description"],s);const a=o(t,["defaultCheckpointId"]);return e!==void 0&&a!=null&&r(e,["defaultCheckpointId"],a),n}function Uf(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","name"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],Ff(s,n)),n}function $f(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","name"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Vf(t){const e={};if(o(t,["systemInstruction"])!==void 0)throw new Error("systemInstruction parameter is not supported in Gemini API.");if(o(t,["tools"])!==void 0)throw new Error("tools parameter is not supported in Gemini API.");if(o(t,["generationConfig"])!==void 0)throw new Error("generationConfig parameter is not supported in Gemini API.");return e}function Bf(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["contents"]);if(s!=null){let l=he(s);Array.isArray(l)&&(l=l.map(u=>nn(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["config"],Vf(a)),n}function qf(t){const e={};if(o(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");const n=o(t,["imageBytes"]);n!=null&&r(e,["bytesBase64Encoded"],we(n));const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Gf(t,e){const n={},i=o(t,["numberOfVideos"]);if(e!==void 0&&i!=null&&r(e,["parameters","sampleCount"],i),o(t,["outputGcsUri"])!==void 0)throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(o(t,["fps"])!==void 0)throw new Error("fps parameter is not supported in Gemini API.");const s=o(t,["durationSeconds"]);if(e!==void 0&&s!=null&&r(e,["parameters","durationSeconds"],s),o(t,["seed"])!==void 0)throw new Error("seed parameter is not supported in Gemini API.");const a=o(t,["aspectRatio"]);if(e!==void 0&&a!=null&&r(e,["parameters","aspectRatio"],a),o(t,["resolution"])!==void 0)throw new Error("resolution parameter is not supported in Gemini API.");const l=o(t,["personGeneration"]);if(e!==void 0&&l!=null&&r(e,["parameters","personGeneration"],l),o(t,["pubsubTopic"])!==void 0)throw new Error("pubsubTopic parameter is not supported in Gemini API.");const u=o(t,["negativePrompt"]);e!==void 0&&u!=null&&r(e,["parameters","negativePrompt"],u);const d=o(t,["enhancePrompt"]);if(e!==void 0&&d!=null&&r(e,["parameters","enhancePrompt"],d),o(t,["generateAudio"])!==void 0)throw new Error("generateAudio parameter is not supported in Gemini API.");if(o(t,["lastFrame"])!==void 0)throw new Error("lastFrame parameter is not supported in Gemini API.");if(o(t,["compressionQuality"])!==void 0)throw new Error("compressionQuality parameter is not supported in Gemini API.");return n}function Jf(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=o(e,["image"]);if(a!=null&&r(n,["instances[0]","image"],qf(a)),o(e,["video"])!==void 0)throw new Error("video parameter is not supported in Gemini API.");const l=o(e,["config"]);return l!=null&&r(n,["config"],Gf(l,n)),n}function Of(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Hf(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["data"]);i!=null&&r(e,["data"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Wf(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["fileUri"]);i!=null&&r(e,["fileUri"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Kf(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Of(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Hf(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Wf(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Ze(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Kf(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Yf(t){const e={},n=o(t,["anyOf"]);n!=null&&r(e,["anyOf"],n);const i=o(t,["default"]);i!=null&&r(e,["default"],i);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const a=o(t,["enum"]);a!=null&&r(e,["enum"],a);const l=o(t,["example"]);l!=null&&r(e,["example"],l);const u=o(t,["format"]);u!=null&&r(e,["format"],u);const d=o(t,["items"]);d!=null&&r(e,["items"],d);const c=o(t,["maxItems"]);c!=null&&r(e,["maxItems"],c);const m=o(t,["maxLength"]);m!=null&&r(e,["maxLength"],m);const h=o(t,["maxProperties"]);h!=null&&r(e,["maxProperties"],h);const p=o(t,["maximum"]);p!=null&&r(e,["maximum"],p);const g=o(t,["minItems"]);g!=null&&r(e,["minItems"],g);const y=o(t,["minLength"]);y!=null&&r(e,["minLength"],y);const x=o(t,["minProperties"]);x!=null&&r(e,["minProperties"],x);const T=o(t,["minimum"]);T!=null&&r(e,["minimum"],T);const I=o(t,["nullable"]);I!=null&&r(e,["nullable"],I);const w=o(t,["pattern"]);w!=null&&r(e,["pattern"],w);const A=o(t,["properties"]);A!=null&&r(e,["properties"],A);const _=o(t,["propertyOrdering"]);_!=null&&r(e,["propertyOrdering"],_);const C=o(t,["required"]);C!=null&&r(e,["required"],C);const E=o(t,["title"]);E!=null&&r(e,["title"],E);const N=o(t,["type"]);return N!=null&&r(e,["type"],N),e}function zf(t){const e={},n=o(t,["featureSelectionPreference"]);return n!=null&&r(e,["featureSelectionPreference"],n),e}function Xf(t){const e={},n=o(t,["method"]);n!=null&&r(e,["method"],n);const i=o(t,["category"]);i!=null&&r(e,["category"],i);const s=o(t,["threshold"]);return s!=null&&r(e,["threshold"],s),e}function Qf(t){const e={};if(o(t,["behavior"])!==void 0)throw new Error("behavior parameter is not supported in Vertex AI.");const n=o(t,["description"]);n!=null&&r(e,["description"],n);const i=o(t,["name"]);i!=null&&r(e,["name"],i);const s=o(t,["parameters"]);s!=null&&r(e,["parameters"],s);const a=o(t,["parametersJsonSchema"]);a!=null&&r(e,["parametersJsonSchema"],a);const l=o(t,["response"]);l!=null&&r(e,["response"],l);const u=o(t,["responseJsonSchema"]);return u!=null&&r(e,["responseJsonSchema"],u),e}function Zf(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function jf(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Zf(n)),e}function em(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function tm(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],em(n)),e}function nm(){return{}}function im(t){const e={},n=o(t,["apiKeyString"]);return n!=null&&r(e,["apiKeyString"],n),e}function sm(t){const e={},n=o(t,["apiKeyConfig"]);n!=null&&r(e,["apiKeyConfig"],im(n));const i=o(t,["authType"]);i!=null&&r(e,["authType"],i);const s=o(t,["googleServiceAccountConfig"]);s!=null&&r(e,["googleServiceAccountConfig"],s);const a=o(t,["httpBasicAuthConfig"]);a!=null&&r(e,["httpBasicAuthConfig"],a);const l=o(t,["oauthConfig"]);l!=null&&r(e,["oauthConfig"],l);const u=o(t,["oidcConfig"]);return u!=null&&r(e,["oidcConfig"],u),e}function om(t){const e={},n=o(t,["authConfig"]);return n!=null&&r(e,["authConfig"],sm(n)),e}function rm(){return{}}function Bo(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let h=n;Array.isArray(h)&&(h=h.map(p=>Qf(p))),r(e,["functionDeclarations"],h)}const i=o(t,["retrieval"]);i!=null&&r(e,["retrieval"],i);const s=o(t,["googleSearch"]);s!=null&&r(e,["googleSearch"],jf(s));const a=o(t,["googleSearchRetrieval"]);a!=null&&r(e,["googleSearchRetrieval"],tm(a)),o(t,["enterpriseWebSearch"])!=null&&r(e,["enterpriseWebSearch"],nm());const u=o(t,["googleMaps"]);u!=null&&r(e,["googleMaps"],om(u)),o(t,["urlContext"])!=null&&r(e,["urlContext"],rm());const c=o(t,["codeExecution"]);c!=null&&r(e,["codeExecution"],c);const m=o(t,["computerUse"]);return m!=null&&r(e,["computerUse"],m),e}function am(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["allowedFunctionNames"]);return i!=null&&r(e,["allowedFunctionNames"],i),e}function lm(t){const e={},n=o(t,["latitude"]);n!=null&&r(e,["latitude"],n);const i=o(t,["longitude"]);return i!=null&&r(e,["longitude"],i),e}function um(t){const e={},n=o(t,["latLng"]);n!=null&&r(e,["latLng"],lm(n));const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function cm(t){const e={},n=o(t,["functionCallingConfig"]);n!=null&&r(e,["functionCallingConfig"],am(n));const i=o(t,["retrievalConfig"]);return i!=null&&r(e,["retrievalConfig"],um(i)),e}function dm(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function fm(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],dm(n)),e}function mm(t){const e={},n=o(t,["voiceConfig"]);if(n!=null&&r(e,["voiceConfig"],fm(n)),o(t,["multiSpeakerVoiceConfig"])!==void 0)throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const i=o(t,["languageCode"]);return i!=null&&r(e,["languageCode"],i),e}function hm(t){const e={},n=o(t,["includeThoughts"]);n!=null&&r(e,["includeThoughts"],n);const i=o(t,["thinkingBudget"]);return i!=null&&r(e,["thinkingBudget"],i),e}function pm(t,e,n){const i={},s=o(e,["systemInstruction"]);n!==void 0&&s!=null&&r(n,["systemInstruction"],Ze(re(s)));const a=o(e,["temperature"]);a!=null&&r(i,["temperature"],a);const l=o(e,["topP"]);l!=null&&r(i,["topP"],l);const u=o(e,["topK"]);u!=null&&r(i,["topK"],u);const d=o(e,["candidateCount"]);d!=null&&r(i,["candidateCount"],d);const c=o(e,["maxOutputTokens"]);c!=null&&r(i,["maxOutputTokens"],c);const m=o(e,["stopSequences"]);m!=null&&r(i,["stopSequences"],m);const h=o(e,["responseLogprobs"]);h!=null&&r(i,["responseLogprobs"],h);const p=o(e,["logprobs"]);p!=null&&r(i,["logprobs"],p);const g=o(e,["presencePenalty"]);g!=null&&r(i,["presencePenalty"],g);const y=o(e,["frequencyPenalty"]);y!=null&&r(i,["frequencyPenalty"],y);const x=o(e,["seed"]);x!=null&&r(i,["seed"],x);const T=o(e,["responseMimeType"]);T!=null&&r(i,["responseMimeType"],T);const I=o(e,["responseSchema"]);I!=null&&r(i,["responseSchema"],Yf(Xn(I)));const w=o(e,["responseJsonSchema"]);w!=null&&r(i,["responseJsonSchema"],w);const A=o(e,["routingConfig"]);A!=null&&r(i,["routingConfig"],A);const _=o(e,["modelSelectionConfig"]);_!=null&&r(i,["modelConfig"],zf(_));const C=o(e,["safetySettings"]);if(n!==void 0&&C!=null){let X=C;Array.isArray(X)&&(X=X.map(ee=>Xf(ee))),r(n,["safetySettings"],X)}const E=o(e,["tools"]);if(n!==void 0&&E!=null){let X=Xe(E);Array.isArray(X)&&(X=X.map(ee=>Bo(ze(ee)))),r(n,["tools"],X)}const N=o(e,["toolConfig"]);n!==void 0&&N!=null&&r(n,["toolConfig"],cm(N));const D=o(e,["labels"]);n!==void 0&&D!=null&&r(n,["labels"],D);const b=o(e,["cachedContent"]);n!==void 0&&b!=null&&r(n,["cachedContent"],Se(t,b));const k=o(e,["responseModalities"]);k!=null&&r(i,["responseModalities"],k);const R=o(e,["mediaResolution"]);R!=null&&r(i,["mediaResolution"],R);const K=o(e,["speechConfig"]);K!=null&&r(i,["speechConfig"],mm(Qn(K)));const fe=o(e,["audioTimestamp"]);fe!=null&&r(i,["audioTimestamp"],fe);const z=o(e,["thinkingConfig"]);return z!=null&&r(i,["thinkingConfig"],hm(z)),i}function Ms(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["contents"]);if(s!=null){let l=he(s);Array.isArray(l)&&(l=l.map(u=>Ze(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["generationConfig"],pm(t,a,n)),n}function gm(t,e){const n={},i=o(t,["taskType"]);e!==void 0&&i!=null&&r(e,["instances[]","task_type"],i);const s=o(t,["title"]);e!==void 0&&s!=null&&r(e,["instances[]","title"],s);const a=o(t,["outputDimensionality"]);e!==void 0&&a!=null&&r(e,["parameters","outputDimensionality"],a);const l=o(t,["mimeType"]);e!==void 0&&l!=null&&r(e,["instances[]","mimeType"],l);const u=o(t,["autoTruncate"]);return e!==void 0&&u!=null&&r(e,["parameters","autoTruncate"],u),n}function ym(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["contents"]);s!=null&&r(n,["instances[]","content"],To(t,s));const a=o(e,["config"]);return a!=null&&r(n,["config"],gm(a,n)),n}function xm(t,e){const n={},i=o(t,["outputGcsUri"]);e!==void 0&&i!=null&&r(e,["parameters","storageUri"],i);const s=o(t,["negativePrompt"]);e!==void 0&&s!=null&&r(e,["parameters","negativePrompt"],s);const a=o(t,["numberOfImages"]);e!==void 0&&a!=null&&r(e,["parameters","sampleCount"],a);const l=o(t,["aspectRatio"]);e!==void 0&&l!=null&&r(e,["parameters","aspectRatio"],l);const u=o(t,["guidanceScale"]);e!==void 0&&u!=null&&r(e,["parameters","guidanceScale"],u);const d=o(t,["seed"]);e!==void 0&&d!=null&&r(e,["parameters","seed"],d);const c=o(t,["safetyFilterLevel"]);e!==void 0&&c!=null&&r(e,["parameters","safetySetting"],c);const m=o(t,["personGeneration"]);e!==void 0&&m!=null&&r(e,["parameters","personGeneration"],m);const h=o(t,["includeSafetyAttributes"]);e!==void 0&&h!=null&&r(e,["parameters","includeSafetyAttributes"],h);const p=o(t,["includeRaiReason"]);e!==void 0&&p!=null&&r(e,["parameters","includeRaiReason"],p);const g=o(t,["language"]);e!==void 0&&g!=null&&r(e,["parameters","language"],g);const y=o(t,["outputMimeType"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","mimeType"],y);const x=o(t,["outputCompressionQuality"]);e!==void 0&&x!=null&&r(e,["parameters","outputOptions","compressionQuality"],x);const T=o(t,["addWatermark"]);e!==void 0&&T!=null&&r(e,["parameters","addWatermark"],T);const I=o(t,["enhancePrompt"]);return e!==void 0&&I!=null&&r(e,["parameters","enhancePrompt"],I),n}function vm(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=o(e,["config"]);return a!=null&&r(n,["config"],xm(a,n)),n}function sn(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const i=o(t,["imageBytes"]);i!=null&&r(e,["bytesBase64Encoded"],we(i));const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Cm(t){const e={},n=o(t,["maskMode"]);n!=null&&r(e,["maskMode"],n);const i=o(t,["segmentationClasses"]);i!=null&&r(e,["maskClasses"],i);const s=o(t,["maskDilation"]);return s!=null&&r(e,["dilation"],s),e}function _m(t){const e={},n=o(t,["controlType"]);n!=null&&r(e,["controlType"],n);const i=o(t,["enableControlImageComputation"]);return i!=null&&r(e,["computeControl"],i),e}function Tm(t){const e={},n=o(t,["styleDescription"]);return n!=null&&r(e,["styleDescription"],n),e}function Sm(t){const e={},n=o(t,["subjectType"]);n!=null&&r(e,["subjectType"],n);const i=o(t,["subjectDescription"]);return i!=null&&r(e,["subjectDescription"],i),e}function wm(t){const e={},n=o(t,["referenceImage"]);n!=null&&r(e,["referenceImage"],sn(n));const i=o(t,["referenceId"]);i!=null&&r(e,["referenceId"],i);const s=o(t,["referenceType"]);s!=null&&r(e,["referenceType"],s);const a=o(t,["maskImageConfig"]);a!=null&&r(e,["maskImageConfig"],Cm(a));const l=o(t,["controlImageConfig"]);l!=null&&r(e,["controlImageConfig"],_m(l));const u=o(t,["styleImageConfig"]);u!=null&&r(e,["styleImageConfig"],Tm(u));const d=o(t,["subjectImageConfig"]);return d!=null&&r(e,["subjectImageConfig"],Sm(d)),e}function Am(t,e){const n={},i=o(t,["outputGcsUri"]);e!==void 0&&i!=null&&r(e,["parameters","storageUri"],i);const s=o(t,["negativePrompt"]);e!==void 0&&s!=null&&r(e,["parameters","negativePrompt"],s);const a=o(t,["numberOfImages"]);e!==void 0&&a!=null&&r(e,["parameters","sampleCount"],a);const l=o(t,["aspectRatio"]);e!==void 0&&l!=null&&r(e,["parameters","aspectRatio"],l);const u=o(t,["guidanceScale"]);e!==void 0&&u!=null&&r(e,["parameters","guidanceScale"],u);const d=o(t,["seed"]);e!==void 0&&d!=null&&r(e,["parameters","seed"],d);const c=o(t,["safetyFilterLevel"]);e!==void 0&&c!=null&&r(e,["parameters","safetySetting"],c);const m=o(t,["personGeneration"]);e!==void 0&&m!=null&&r(e,["parameters","personGeneration"],m);const h=o(t,["includeSafetyAttributes"]);e!==void 0&&h!=null&&r(e,["parameters","includeSafetyAttributes"],h);const p=o(t,["includeRaiReason"]);e!==void 0&&p!=null&&r(e,["parameters","includeRaiReason"],p);const g=o(t,["language"]);e!==void 0&&g!=null&&r(e,["parameters","language"],g);const y=o(t,["outputMimeType"]);e!==void 0&&y!=null&&r(e,["parameters","outputOptions","mimeType"],y);const x=o(t,["outputCompressionQuality"]);e!==void 0&&x!=null&&r(e,["parameters","outputOptions","compressionQuality"],x);const T=o(t,["editMode"]);e!==void 0&&T!=null&&r(e,["parameters","editMode"],T);const I=o(t,["baseSteps"]);return e!==void 0&&I!=null&&r(e,["parameters","editConfig","baseSteps"],I),n}function Em(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=o(e,["referenceImages"]);if(a!=null){let u=a;Array.isArray(u)&&(u=u.map(d=>wm(d))),r(n,["instances[0]","referenceImages"],u)}const l=o(e,["config"]);return l!=null&&r(n,["config"],Am(l,n)),n}function Im(t,e){const n={},i=o(t,["includeRaiReason"]);e!==void 0&&i!=null&&r(e,["parameters","includeRaiReason"],i);const s=o(t,["outputMimeType"]);e!==void 0&&s!=null&&r(e,["parameters","outputOptions","mimeType"],s);const a=o(t,["outputCompressionQuality"]);e!==void 0&&a!=null&&r(e,["parameters","outputOptions","compressionQuality"],a);const l=o(t,["enhanceInputImage"]);e!==void 0&&l!=null&&r(e,["parameters","upscaleConfig","enhanceInputImage"],l);const u=o(t,["imagePreservationFactor"]);e!==void 0&&u!=null&&r(e,["parameters","upscaleConfig","imagePreservationFactor"],u);const d=o(t,["numberOfImages"]);e!==void 0&&d!=null&&r(e,["parameters","sampleCount"],d);const c=o(t,["mode"]);return e!==void 0&&c!=null&&r(e,["parameters","mode"],c),n}function Nm(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["image"]);s!=null&&r(n,["instances[0]","image"],sn(s));const a=o(e,["upscaleFactor"]);a!=null&&r(n,["parameters","upscaleConfig","upscaleFactor"],a);const l=o(e,["config"]);return l!=null&&r(n,["config"],Im(l,n)),n}function bm(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","name"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Mm(t,e,n){const i={},s=o(e,["pageSize"]);n!==void 0&&s!=null&&r(n,["_query","pageSize"],s);const a=o(e,["pageToken"]);n!==void 0&&a!=null&&r(n,["_query","pageToken"],a);const l=o(e,["filter"]);n!==void 0&&l!=null&&r(n,["_query","filter"],l);const u=o(e,["queryBase"]);return n!==void 0&&u!=null&&r(n,["_url","models_url"],Ao(t,u)),i}function Pm(t,e){const n={},i=o(e,["config"]);return i!=null&&r(n,["config"],Mm(t,i,n)),n}function Rm(t,e){const n={},i=o(t,["displayName"]);e!==void 0&&i!=null&&r(e,["displayName"],i);const s=o(t,["description"]);e!==void 0&&s!=null&&r(e,["description"],s);const a=o(t,["defaultCheckpointId"]);return e!==void 0&&a!=null&&r(e,["defaultCheckpointId"],a),n}function km(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],Rm(s,n)),n}function Dm(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","name"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],s),n}function Lm(t,e){const n={},i=o(t,["systemInstruction"]);e!==void 0&&i!=null&&r(e,["systemInstruction"],Ze(re(i)));const s=o(t,["tools"]);if(e!==void 0&&s!=null){let l=s;Array.isArray(l)&&(l=l.map(u=>Bo(u))),r(e,["tools"],l)}const a=o(t,["generationConfig"]);return e!==void 0&&a!=null&&r(e,["generationConfig"],a),n}function Fm(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["contents"]);if(s!=null){let l=he(s);Array.isArray(l)&&(l=l.map(u=>Ze(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["config"],Lm(a,n)),n}function Um(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["contents"]);if(s!=null){let l=he(s);Array.isArray(l)&&(l=l.map(u=>Ze(u))),r(n,["contents"],l)}const a=o(e,["config"]);return a!=null&&r(n,["config"],a),n}function $m(t){const e={},n=o(t,["uri"]);n!=null&&r(e,["gcsUri"],n);const i=o(t,["videoBytes"]);i!=null&&r(e,["bytesBase64Encoded"],we(i));const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Vm(t,e){const n={},i=o(t,["numberOfVideos"]);e!==void 0&&i!=null&&r(e,["parameters","sampleCount"],i);const s=o(t,["outputGcsUri"]);e!==void 0&&s!=null&&r(e,["parameters","storageUri"],s);const a=o(t,["fps"]);e!==void 0&&a!=null&&r(e,["parameters","fps"],a);const l=o(t,["durationSeconds"]);e!==void 0&&l!=null&&r(e,["parameters","durationSeconds"],l);const u=o(t,["seed"]);e!==void 0&&u!=null&&r(e,["parameters","seed"],u);const d=o(t,["aspectRatio"]);e!==void 0&&d!=null&&r(e,["parameters","aspectRatio"],d);const c=o(t,["resolution"]);e!==void 0&&c!=null&&r(e,["parameters","resolution"],c);const m=o(t,["personGeneration"]);e!==void 0&&m!=null&&r(e,["parameters","personGeneration"],m);const h=o(t,["pubsubTopic"]);e!==void 0&&h!=null&&r(e,["parameters","pubsubTopic"],h);const p=o(t,["negativePrompt"]);e!==void 0&&p!=null&&r(e,["parameters","negativePrompt"],p);const g=o(t,["enhancePrompt"]);e!==void 0&&g!=null&&r(e,["parameters","enhancePrompt"],g);const y=o(t,["generateAudio"]);e!==void 0&&y!=null&&r(e,["parameters","generateAudio"],y);const x=o(t,["lastFrame"]);e!==void 0&&x!=null&&r(e,["instances[0]","lastFrame"],sn(x));const T=o(t,["compressionQuality"]);return e!==void 0&&T!=null&&r(e,["parameters","compressionQuality"],T),n}function Bm(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["_url","model"],J(t,i));const s=o(e,["prompt"]);s!=null&&r(n,["instances[0]","prompt"],s);const a=o(e,["image"]);a!=null&&r(n,["instances[0]","image"],sn(a));const l=o(e,["video"]);l!=null&&r(n,["instances[0]","video"],$m(l));const u=o(e,["config"]);return u!=null&&r(n,["config"],Vm(u,n)),n}function qm(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Gm(t){const e={},n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Jm(t){const e={},n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Om(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],qm(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Gm(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Jm(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function Hm(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Om(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Wm(t){const e={},n=o(t,["citationSources"]);return n!=null&&r(e,["citations"],n),e}function Km(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const i=o(t,["urlRetrievalStatus"]);return i!=null&&r(e,["urlRetrievalStatus"],i),e}function Ym(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Km(s))),r(e,["urlMetadata"],i)}return e}function zm(t){const e={},n=o(t,["content"]);n!=null&&r(e,["content"],Hm(n));const i=o(t,["citationMetadata"]);i!=null&&r(e,["citationMetadata"],Wm(i));const s=o(t,["tokenCount"]);s!=null&&r(e,["tokenCount"],s);const a=o(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=o(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],Ym(l));const u=o(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const d=o(t,["groundingMetadata"]);d!=null&&r(e,["groundingMetadata"],d);const c=o(t,["index"]);c!=null&&r(e,["index"],c);const m=o(t,["logprobsResult"]);m!=null&&r(e,["logprobsResult"],m);const h=o(t,["safetyRatings"]);return h!=null&&r(e,["safetyRatings"],h),e}function Ps(t){const e={},n=o(t,["candidates"]);if(n!=null){let l=n;Array.isArray(l)&&(l=l.map(u=>zm(u))),r(e,["candidates"],l)}const i=o(t,["modelVersion"]);i!=null&&r(e,["modelVersion"],i);const s=o(t,["promptFeedback"]);s!=null&&r(e,["promptFeedback"],s);const a=o(t,["usageMetadata"]);return a!=null&&r(e,["usageMetadata"],a),e}function Xm(t){const e={},n=o(t,["values"]);return n!=null&&r(e,["values"],n),e}function Qm(){return{}}function Zm(t){const e={},n=o(t,["embeddings"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Xm(a))),r(e,["embeddings"],s)}return o(t,["metadata"])!=null&&r(e,["metadata"],Qm()),e}function jm(t){const e={},n=o(t,["bytesBase64Encoded"]);n!=null&&r(e,["imageBytes"],we(n));const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function qo(t){const e={},n=o(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const i=o(t,["safetyAttributes","scores"]);i!=null&&r(e,["scores"],i);const s=o(t,["contentType"]);return s!=null&&r(e,["contentType"],s),e}function eh(t){const e={},n=o(t,["_self"]);n!=null&&r(e,["image"],jm(n));const i=o(t,["raiFilteredReason"]);i!=null&&r(e,["raiFilteredReason"],i);const s=o(t,["_self"]);return s!=null&&r(e,["safetyAttributes"],qo(s)),e}function th(t){const e={},n=o(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>eh(a))),r(e,["generatedImages"],s)}const i=o(t,["positivePromptSafetyAttributes"]);return i!=null&&r(e,["positivePromptSafetyAttributes"],qo(i)),e}function nh(t){const e={},n=o(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const i=o(t,["createTime"]);i!=null&&r(e,["createTime"],i);const s=o(t,["updateTime"]);return s!=null&&r(e,["updateTime"],s),e}function kn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const a=o(t,["version"]);a!=null&&r(e,["version"],a);const l=o(t,["_self"]);l!=null&&r(e,["tunedModelInfo"],nh(l));const u=o(t,["inputTokenLimit"]);u!=null&&r(e,["inputTokenLimit"],u);const d=o(t,["outputTokenLimit"]);d!=null&&r(e,["outputTokenLimit"],d);const c=o(t,["supportedGenerationMethods"]);return c!=null&&r(e,["supportedActions"],c),e}function ih(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["_self"]);if(i!=null){let s=Eo(i);Array.isArray(s)&&(s=s.map(a=>kn(a))),r(e,["models"],s)}return e}function sh(){return{}}function oh(t){const e={},n=o(t,["totalTokens"]);n!=null&&r(e,["totalTokens"],n);const i=o(t,["cachedContentTokenCount"]);return i!=null&&r(e,["cachedContentTokenCount"],i),e}function rh(t){const e={},n=o(t,["video","uri"]);n!=null&&r(e,["uri"],n);const i=o(t,["video","encodedVideo"]);i!=null&&r(e,["videoBytes"],we(i));const s=o(t,["encoding"]);return s!=null&&r(e,["mimeType"],s),e}function ah(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],rh(n)),e}function lh(t){const e={},n=o(t,["generatedSamples"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>ah(l))),r(e,["generatedVideos"],a)}const i=o(t,["raiMediaFilteredCount"]);i!=null&&r(e,["raiMediaFilteredCount"],i);const s=o(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function uh(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["metadata"]);i!=null&&r(e,["metadata"],i);const s=o(t,["done"]);s!=null&&r(e,["done"],s);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response","generateVideoResponse"]);return l!=null&&r(e,["response"],lh(l)),e}function ch(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function dh(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["data"]);i!=null&&r(e,["data"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function fh(t){const e={},n=o(t,["displayName"]);n!=null&&r(e,["displayName"],n);const i=o(t,["fileUri"]);i!=null&&r(e,["fileUri"],i);const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function mh(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],ch(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],dh(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],fh(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function hh(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>mh(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function ph(t){const e={},n=o(t,["citations"]);return n!=null&&r(e,["citations"],n),e}function gh(t){const e={},n=o(t,["retrievedUrl"]);n!=null&&r(e,["retrievedUrl"],n);const i=o(t,["urlRetrievalStatus"]);return i!=null&&r(e,["urlRetrievalStatus"],i),e}function yh(t){const e={},n=o(t,["urlMetadata"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>gh(s))),r(e,["urlMetadata"],i)}return e}function xh(t){const e={},n=o(t,["content"]);n!=null&&r(e,["content"],hh(n));const i=o(t,["citationMetadata"]);i!=null&&r(e,["citationMetadata"],ph(i));const s=o(t,["finishMessage"]);s!=null&&r(e,["finishMessage"],s);const a=o(t,["finishReason"]);a!=null&&r(e,["finishReason"],a);const l=o(t,["urlContextMetadata"]);l!=null&&r(e,["urlContextMetadata"],yh(l));const u=o(t,["avgLogprobs"]);u!=null&&r(e,["avgLogprobs"],u);const d=o(t,["groundingMetadata"]);d!=null&&r(e,["groundingMetadata"],d);const c=o(t,["index"]);c!=null&&r(e,["index"],c);const m=o(t,["logprobsResult"]);m!=null&&r(e,["logprobsResult"],m);const h=o(t,["safetyRatings"]);return h!=null&&r(e,["safetyRatings"],h),e}function Rs(t){const e={},n=o(t,["candidates"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(c=>xh(c))),r(e,["candidates"],d)}const i=o(t,["createTime"]);i!=null&&r(e,["createTime"],i);const s=o(t,["responseId"]);s!=null&&r(e,["responseId"],s);const a=o(t,["modelVersion"]);a!=null&&r(e,["modelVersion"],a);const l=o(t,["promptFeedback"]);l!=null&&r(e,["promptFeedback"],l);const u=o(t,["usageMetadata"]);return u!=null&&r(e,["usageMetadata"],u),e}function vh(t){const e={},n=o(t,["truncated"]);n!=null&&r(e,["truncated"],n);const i=o(t,["token_count"]);return i!=null&&r(e,["tokenCount"],i),e}function Ch(t){const e={},n=o(t,["values"]);n!=null&&r(e,["values"],n);const i=o(t,["statistics"]);return i!=null&&r(e,["statistics"],vh(i)),e}function _h(t){const e={},n=o(t,["billableCharacterCount"]);return n!=null&&r(e,["billableCharacterCount"],n),e}function Th(t){const e={},n=o(t,["predictions[]","embeddings"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Ch(a))),r(e,["embeddings"],s)}const i=o(t,["metadata"]);return i!=null&&r(e,["metadata"],_h(i)),e}function Sh(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["gcsUri"],n);const i=o(t,["bytesBase64Encoded"]);i!=null&&r(e,["imageBytes"],we(i));const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Go(t){const e={},n=o(t,["safetyAttributes","categories"]);n!=null&&r(e,["categories"],n);const i=o(t,["safetyAttributes","scores"]);i!=null&&r(e,["scores"],i);const s=o(t,["contentType"]);return s!=null&&r(e,["contentType"],s),e}function jn(t){const e={},n=o(t,["_self"]);n!=null&&r(e,["image"],Sh(n));const i=o(t,["raiFilteredReason"]);i!=null&&r(e,["raiFilteredReason"],i);const s=o(t,["_self"]);s!=null&&r(e,["safetyAttributes"],Go(s));const a=o(t,["prompt"]);return a!=null&&r(e,["enhancedPrompt"],a),e}function wh(t){const e={},n=o(t,["predictions"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>jn(a))),r(e,["generatedImages"],s)}const i=o(t,["positivePromptSafetyAttributes"]);return i!=null&&r(e,["positivePromptSafetyAttributes"],Go(i)),e}function Ah(t){const e={},n=o(t,["predictions"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>jn(s))),r(e,["generatedImages"],i)}return e}function Eh(t){const e={},n=o(t,["predictions"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>jn(s))),r(e,["generatedImages"],i)}return e}function Ih(t){const e={},n=o(t,["endpoint"]);n!=null&&r(e,["name"],n);const i=o(t,["deployedModelId"]);return i!=null&&r(e,["deployedModelId"],i),e}function Nh(t){const e={},n=o(t,["labels","google-vertex-llm-tuning-base-model-id"]);n!=null&&r(e,["baseModel"],n);const i=o(t,["createTime"]);i!=null&&r(e,["createTime"],i);const s=o(t,["updateTime"]);return s!=null&&r(e,["updateTime"],s),e}function bh(t){const e={},n=o(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const i=o(t,["epoch"]);i!=null&&r(e,["epoch"],i);const s=o(t,["step"]);return s!=null&&r(e,["step"],s),e}function Dn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["displayName"]);i!=null&&r(e,["displayName"],i);const s=o(t,["description"]);s!=null&&r(e,["description"],s);const a=o(t,["versionId"]);a!=null&&r(e,["version"],a);const l=o(t,["deployedModels"]);if(l!=null){let h=l;Array.isArray(h)&&(h=h.map(p=>Ih(p))),r(e,["endpoints"],h)}const u=o(t,["labels"]);u!=null&&r(e,["labels"],u);const d=o(t,["_self"]);d!=null&&r(e,["tunedModelInfo"],Nh(d));const c=o(t,["defaultCheckpointId"]);c!=null&&r(e,["defaultCheckpointId"],c);const m=o(t,["checkpoints"]);if(m!=null){let h=m;Array.isArray(h)&&(h=h.map(p=>bh(p))),r(e,["checkpoints"],h)}return e}function Mh(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["_self"]);if(i!=null){let s=Eo(i);Array.isArray(s)&&(s=s.map(a=>Dn(a))),r(e,["models"],s)}return e}function Ph(){return{}}function Rh(t){const e={},n=o(t,["totalTokens"]);return n!=null&&r(e,["totalTokens"],n),e}function kh(t){const e={},n=o(t,["tokensInfo"]);return n!=null&&r(e,["tokensInfo"],n),e}function Dh(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const i=o(t,["bytesBase64Encoded"]);i!=null&&r(e,["videoBytes"],we(i));const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function Lh(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],Dh(n)),e}function Fh(t){const e={},n=o(t,["videos"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>Lh(l))),r(e,["generatedVideos"],a)}const i=o(t,["raiMediaFilteredCount"]);i!=null&&r(e,["raiMediaFilteredCount"],i);const s=o(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function Uh(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["metadata"]);i!=null&&r(e,["metadata"],i);const s=o(t,["done"]);s!=null&&r(e,["done"],s);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response"]);return l!=null&&r(e,["response"],Fh(l)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const $h="Content-Type",Vh="X-Server-Timeout",Bh="User-Agent",Ln="x-goog-api-client",qh="1.9.0",Gh=`google-genai-sdk/${qh}`,Jh="v1beta1",Oh="v1beta",ks=/^data: (.*)(?:\n\n|\r\r|\r\n\r\n)/;class Hh{constructor(e){var n,i;this.clientOptions=Object.assign(Object.assign({},e),{project:e.project,location:e.location,apiKey:e.apiKey,vertexai:e.vertexai});const s={};this.clientOptions.vertexai?(s.apiVersion=(n=this.clientOptions.apiVersion)!==null&&n!==void 0?n:Jh,s.baseUrl=this.baseUrlFromProjectLocation(),this.normalizeAuthParameters()):(s.apiVersion=(i=this.clientOptions.apiVersion)!==null&&i!==void 0?i:Oh,s.baseUrl="https://generativelanguage.googleapis.com/"),s.headers=this.getDefaultHeaders(),this.clientOptions.httpOptions=s,e.httpOptions&&(this.clientOptions.httpOptions=this.patchHttpOptions(s,e.httpOptions))}baseUrlFromProjectLocation(){return this.clientOptions.project&&this.clientOptions.location&&this.clientOptions.location!=="global"?`https://${this.clientOptions.location}-aiplatform.googleapis.com/`:"https://aiplatform.googleapis.com/"}normalizeAuthParameters(){if(this.clientOptions.project&&this.clientOptions.location){this.clientOptions.apiKey=void 0;return}this.clientOptions.project=void 0,this.clientOptions.location=void 0}isVertexAI(){var e;return(e=this.clientOptions.vertexai)!==null&&e!==void 0?e:!1}getProject(){return this.clientOptions.project}getLocation(){return this.clientOptions.location}getApiVersion(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.apiVersion!==void 0)return this.clientOptions.httpOptions.apiVersion;throw new Error("API version is not set.")}getBaseUrl(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.baseUrl!==void 0)return this.clientOptions.httpOptions.baseUrl;throw new Error("Base URL is not set.")}getRequestUrl(){return this.getRequestUrlInternal(this.clientOptions.httpOptions)}getHeaders(){if(this.clientOptions.httpOptions&&this.clientOptions.httpOptions.headers!==void 0)return this.clientOptions.httpOptions.headers;throw new Error("Headers are not set.")}getRequestUrlInternal(e){if(!e||e.baseUrl===void 0||e.apiVersion===void 0)throw new Error("HTTP options are not correctly set.");const i=[e.baseUrl.endsWith("/")?e.baseUrl.slice(0,-1):e.baseUrl];return e.apiVersion&&e.apiVersion!==""&&i.push(e.apiVersion),i.join("/")}getBaseResourcePath(){return`projects/${this.clientOptions.project}/locations/${this.clientOptions.location}`}getApiKey(){return this.clientOptions.apiKey}getWebsocketBaseUrl(){const e=this.getBaseUrl(),n=new URL(e);return n.protocol=n.protocol=="http:"?"ws":"wss",n.toString()}setBaseUrl(e){if(this.clientOptions.httpOptions)this.clientOptions.httpOptions.baseUrl=e;else throw new Error("HTTP options are not correctly set.")}constructUrl(e,n,i){const s=[this.getRequestUrlInternal(n)];return i&&s.push(this.getBaseResourcePath()),e!==""&&s.push(e),new URL(`${s.join("/")}`)}shouldPrependVertexProjectPath(e){return!(this.clientOptions.apiKey||!this.clientOptions.vertexai||e.path.startsWith("projects/")||e.httpMethod==="GET"&&e.path.startsWith("publishers/google/models"))}async request(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const i=this.shouldPrependVertexProjectPath(e),s=this.constructUrl(e.path,n,i);if(e.queryParams)for(const[l,u]of Object.entries(e.queryParams))s.searchParams.append(l,String(u));let a={};if(e.httpMethod==="GET"){if(e.body&&e.body!=="{}")throw new Error("Request body should be empty for GET request, but got non empty request body")}else a.body=e.body;return a=await this.includeExtraHttpOptionsToRequestInit(a,n,e.abortSignal),this.unaryApiCall(s,a,e.httpMethod)}patchHttpOptions(e,n){const i=JSON.parse(JSON.stringify(e));for(const[s,a]of Object.entries(n))typeof a=="object"?i[s]=Object.assign(Object.assign({},i[s]),a):a!==void 0&&(i[s]=a);return i}async requestStream(e){let n=this.clientOptions.httpOptions;e.httpOptions&&(n=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const i=this.shouldPrependVertexProjectPath(e),s=this.constructUrl(e.path,n,i);(!s.searchParams.has("alt")||s.searchParams.get("alt")!=="sse")&&s.searchParams.set("alt","sse");let a={};return a.body=e.body,a=await this.includeExtraHttpOptionsToRequestInit(a,n,e.abortSignal),this.streamApiCall(s,a,e.httpMethod)}async includeExtraHttpOptionsToRequestInit(e,n,i){if(n&&n.timeout||i){const s=new AbortController,a=s.signal;if(n.timeout&&(n==null?void 0:n.timeout)>0){const l=setTimeout(()=>s.abort(),n.timeout);l&&typeof l.unref=="function"&&l.unref()}i&&i.addEventListener("abort",()=>{s.abort()}),e.signal=a}return n&&n.extraBody!==null&&Wh(e,n.extraBody),e.headers=await this.getHeadersInternal(n),e}async unaryApiCall(e,n,i){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:i})).then(async s=>(await Ds(s),new In(s))).catch(s=>{throw s instanceof Error?s:new Error(JSON.stringify(s))})}async streamApiCall(e,n,i){return this.apiCall(e.toString(),Object.assign(Object.assign({},n),{method:i})).then(async s=>(await Ds(s),this.processStreamResponse(s))).catch(s=>{throw s instanceof Error?s:new Error(JSON.stringify(s))})}processStreamResponse(e){var n;return Ye(this,arguments,function*(){const s=(n=e==null?void 0:e.body)===null||n===void 0?void 0:n.getReader(),a=new TextDecoder("utf-8");if(!s)throw new Error("Response body is empty");try{let l="";for(;;){const{done:u,value:d}=yield W(s.read());if(u){if(l.trim().length>0)throw new Error("Incomplete JSON segment at the end");break}const c=a.decode(d,{stream:!0});try{const h=JSON.parse(c);if("error"in h){const p=JSON.parse(JSON.stringify(h.error)),g=p.status,y=p.code,x=`got status: ${g}. ${JSON.stringify(h)}`;if(y>=400&&y<600)throw new tn({message:x,status:y})}}catch(h){if(h.name==="ApiError")throw h}l+=c;let m=l.match(ks);for(;m;){const h=m[1];try{const p=new Response(h,{headers:e==null?void 0:e.headers,status:e==null?void 0:e.status,statusText:e==null?void 0:e.statusText});yield yield W(new In(p)),l=l.slice(m[0].length),m=l.match(ks)}catch(p){throw new Error(`exception parsing stream chunk ${h}. ${p}`)}}}}finally{s.releaseLock()}})}async apiCall(e,n){return fetch(e,n).catch(i=>{throw new Error(`exception ${i} sending request`)})}getDefaultHeaders(){const e={},n=Gh+" "+this.clientOptions.userAgentExtra;return e[Bh]=n,e[Ln]=n,e[$h]="application/json",e}async getHeadersInternal(e){const n=new Headers;if(e&&e.headers){for(const[i,s]of Object.entries(e.headers))n.append(i,s);e.timeout&&e.timeout>0&&n.append(Vh,String(Math.ceil(e.timeout/1e3)))}return await this.clientOptions.auth.addAuthHeaders(n),n}async uploadFile(e,n){var i;const s={};n!=null&&(s.mimeType=n.mimeType,s.name=n.name,s.displayName=n.displayName),s.name&&!s.name.startsWith("files/")&&(s.name=`files/${s.name}`);const a=this.clientOptions.uploader,l=await a.stat(e);s.sizeBytes=String(l.size);const u=(i=n==null?void 0:n.mimeType)!==null&&i!==void 0?i:l.type;if(u===void 0||u==="")throw new Error("Can not determine mimeType. Please provide mimeType in the config.");s.mimeType=u;const d=await this.fetchUploadUrl(s,n);return a.upload(e,d,this)}async downloadFile(e){await this.clientOptions.downloader.download(e,this)}async fetchUploadUrl(e,n){var i;let s={};n!=null&&n.httpOptions?s=n.httpOptions:s={apiVersion:"",headers:{"Content-Type":"application/json","X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${e.sizeBytes}`,"X-Goog-Upload-Header-Content-Type":`${e.mimeType}`}};const a={file:e},l=await this.request({path:L("upload/v1beta/files",a._url),body:JSON.stringify(a),httpMethod:"POST",httpOptions:s});if(!l||!(l!=null&&l.headers))throw new Error("Server did not return an HttpResponse or the returned HttpResponse did not have headers.");const u=(i=l==null?void 0:l.headers)===null||i===void 0?void 0:i["x-goog-upload-url"];if(u===void 0)throw new Error("Failed to get upload url. Server did not return the x-google-upload-url in the headers");return u}}async function Ds(t){var e;if(t===void 0)throw new Error("response is undefined");if(!t.ok){const n=t.status;let i;!((e=t.headers.get("content-type"))===null||e===void 0)&&e.includes("application/json")?i=await t.json():i={error:{message:await t.text(),code:t.status,status:t.statusText}};const s=JSON.stringify(i);throw n>=400&&n<600?new tn({message:s,status:n}):new Error(s)}}function Wh(t,e){if(!e||Object.keys(e).length===0)return;if(t.body instanceof Blob){console.warn("includeExtraBodyToRequestInit: extraBody provided but current request body is a Blob. extraBody will be ignored as merging is not supported for Blob bodies.");return}let n={};if(typeof t.body=="string"&&t.body.length>0)try{const a=JSON.parse(t.body);if(typeof a=="object"&&a!==null&&!Array.isArray(a))n=a;else{console.warn("includeExtraBodyToRequestInit: Original request body is valid JSON but not a non-array object. Skip applying extraBody to the request body.");return}}catch{console.warn("includeExtraBodyToRequestInit: Original request body is not valid JSON. Skip applying extraBody to the request body.");return}function i(a,l){const u=Object.assign({},a);for(const d in l)if(Object.prototype.hasOwnProperty.call(l,d)){const c=l[d],m=u[d];c&&typeof c=="object"&&!Array.isArray(c)&&m&&typeof m=="object"&&!Array.isArray(m)?u[d]=i(m,c):(m&&c&&typeof m!=typeof c&&console.warn(`includeExtraBodyToRequestInit:deepMerge: Type mismatch for key "${d}". Original type: ${typeof m}, New type: ${typeof c}. Overwriting.`),u[d]=c)}return u}const s=i(n,e);t.body=JSON.stringify(s)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Kh="mcp_used/unknown";function Jo(t){for(const e of t)if(ei(e)||typeof e=="object"&&"inputSchema"in e)return!0;return!1}function Oo(t){var e;const n=(e=t[Ln])!==null&&e!==void 0?e:"";t[Ln]=(n+` ${Kh}`).trimStart()}function Yh(t){var e,n,i;return(i=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(s=>ei(s)))!==null&&i!==void 0?i:!1}function zh(t){var e,n,i;return(i=(n=(e=t.config)===null||e===void 0?void 0:e.tools)===null||n===void 0?void 0:n.some(s=>!ei(s)))!==null&&i!==void 0?i:!1}function ei(t){return t!==null&&typeof t=="object"&&t instanceof ti}function Xh(t,e=100){return Ye(this,arguments,function*(){let i,s=0;for(;s<e;){const a=yield W(t.listTools({cursor:i}));for(const l of a.tools)yield yield W(l),s++;if(!a.nextCursor)break;i=a.nextCursor}})}class ti{constructor(e=[],n){this.mcpTools=[],this.functionNameToMcpClient={},this.mcpClients=e,this.config=n}static create(e,n){return new ti(e,n)}async initialize(){var e,n,i,s;if(this.mcpTools.length>0)return;const a={},l=[];for(const m of this.mcpClients)try{for(var u=!0,d=(n=void 0,ft(Xh(m))),c;c=await d.next(),e=c.done,!e;u=!0){s=c.value,u=!1;const h=s;l.push(h);const p=h.name;if(a[p])throw new Error(`Duplicate function name ${p} found in MCP tools. Please ensure function names are unique.`);a[p]=m}}catch(h){n={error:h}}finally{try{!u&&!e&&(i=d.return)&&await i.call(d)}finally{if(n)throw n.error}}this.mcpTools=l,this.functionNameToMcpClient=a}async tool(){return await this.initialize(),Oa(this.mcpTools,this.config)}async callTool(e){await this.initialize();const n=[];for(const i of e)if(i.name in this.functionNameToMcpClient){const s=this.functionNameToMcpClient[i.name];let a;this.config.timeout&&(a={timeout:this.config.timeout});const l=await s.callTool({name:i.name,arguments:i.args},void 0,a);n.push({functionResponse:{name:i.name,response:l.isError?{error:l}:l}})}return n}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */async function Qh(t,e,n){const i=new Fa;let s;n.data instanceof Blob?s=JSON.parse(await n.data.text()):s=JSON.parse(n.data);const a=of(s);Object.assign(i,a),e(i)}class Zh{constructor(e,n,i){this.apiClient=e,this.auth=n,this.webSocketFactory=i}async connect(e){var n,i;if(this.apiClient.isVertexAI())throw new Error("Live music is not supported for Vertex AI.");console.warn("Live music generation is experimental and may change in future versions.");const s=this.apiClient.getWebsocketBaseUrl(),a=this.apiClient.getApiVersion(),l=tp(this.apiClient.getDefaultHeaders()),u=this.apiClient.getApiKey(),d=`${s}/ws/google.ai.generativelanguage.${a}.GenerativeService.BidiGenerateMusic?key=${u}`;let c=()=>{};const m=new Promise(A=>{c=A}),h=e.callbacks,p=function(){c({})},g=this.apiClient,y={onopen:p,onmessage:A=>{Qh(g,h.onmessage,A)},onerror:(n=h==null?void 0:h.onerror)!==null&&n!==void 0?n:function(A){},onclose:(i=h==null?void 0:h.onclose)!==null&&i!==void 0?i:function(A){}},x=this.webSocketFactory.create(d,ep(l),y);x.connect(),await m;const T=J(this.apiClient,e.model),I=Uo({model:T}),w=Rn({setup:I});return x.send(JSON.stringify(w)),new jh(x,this.apiClient)}}class jh{constructor(e,n){this.conn=e,this.apiClient=n}async setWeightedPrompts(e){if(!e.weightedPrompts||Object.keys(e.weightedPrompts).length===0)throw new Error("Weighted prompts must be set and contain at least one entry.");const n=xd(e),i=$o(n);this.conn.send(JSON.stringify({clientContent:i}))}async setMusicGenerationConfig(e){e.musicGenerationConfig||(e.musicGenerationConfig={});const n=vd(e),i=Rn(n);this.conn.send(JSON.stringify(i))}sendPlaybackControl(e){const n=Rn({playbackControl:e});this.conn.send(JSON.stringify(n))}play(){this.sendPlaybackControl(He.PLAY)}pause(){this.sendPlaybackControl(He.PAUSE)}stop(){this.sendPlaybackControl(He.STOP)}resetContext(){this.sendPlaybackControl(He.RESET_CONTEXT)}close(){this.conn.close()}}function ep(t){const e={};return t.forEach((n,i)=>{e[i]=n}),e}function tp(t){const e=new Headers;for(const[n,i]of Object.entries(t))e.append(n,i);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const np="FunctionResponse request must have an `id` field from the response of a ToolCall.FunctionalCalls in Google AI.";async function ip(t,e,n){const i=new La;let s;n.data instanceof Blob?s=await n.data.text():n.data instanceof ArrayBuffer?s=new TextDecoder().decode(n.data):s=n.data;const a=JSON.parse(s);if(t.isVertexAI()){const l=zd(a);Object.assign(i,l)}else{const l=Yd(a);Object.assign(i,l)}e(i)}class sp{constructor(e,n,i){this.apiClient=e,this.auth=n,this.webSocketFactory=i,this.music=new Zh(this.apiClient,this.auth,this.webSocketFactory)}async connect(e){var n,i,s,a,l,u;const d=this.apiClient.getWebsocketBaseUrl(),c=this.apiClient.getApiVersion();let m;const h=this.apiClient.getDefaultHeaders();e.config&&e.config.tools&&Jo(e.config.tools)&&Oo(h);const p=lp(h);if(this.apiClient.isVertexAI())m=`${d}/ws/google.cloud.aiplatform.${c}.LlmBidiService/BidiGenerateContent`,await this.auth.addAuthHeaders(p);else{const b=this.apiClient.getApiKey();let k="BidiGenerateContent",R="key";b!=null&&b.startsWith("auth_tokens/")&&(console.warn("Warning: Ephemeral token support is experimental and may change in future versions."),c!=="v1alpha"&&console.warn("Warning: The SDK's ephemeral token support is in v1alpha only. Please use const ai = new GoogleGenAI({apiKey: token.name, httpOptions: { apiVersion: 'v1alpha' }}); before session connection."),k="BidiGenerateContentConstrained",R="access_token"),m=`${d}/ws/google.ai.generativelanguage.${c}.GenerativeService.${k}?${R}=${b}`}let g=()=>{};const y=new Promise(b=>{g=b}),x=e.callbacks,T=function(){var b;(b=x==null?void 0:x.onopen)===null||b===void 0||b.call(x),g({})},I=this.apiClient,w={onopen:T,onmessage:b=>{ip(I,x.onmessage,b)},onerror:(n=x==null?void 0:x.onerror)!==null&&n!==void 0?n:function(b){},onclose:(i=x==null?void 0:x.onclose)!==null&&i!==void 0?i:function(b){}},A=this.webSocketFactory.create(m,ap(p),w);A.connect(),await y;let _=J(this.apiClient,e.model);if(this.apiClient.isVertexAI()&&_.startsWith("publishers/")){const b=this.apiClient.getProject(),k=this.apiClient.getLocation();_=`projects/${b}/locations/${k}/`+_}let C={};this.apiClient.isVertexAI()&&((s=e.config)===null||s===void 0?void 0:s.responseModalities)===void 0&&(e.config===void 0?e.config={responseModalities:[Wt.AUDIO]}:e.config.responseModalities=[Wt.AUDIO]),!((a=e.config)===null||a===void 0)&&a.generationConfig&&console.warn("Setting `LiveConnectConfig.generation_config` is deprecated, please set the fields on `LiveConnectConfig` directly. This will become an error in a future version (not before Q3 2025).");const E=(u=(l=e.config)===null||l===void 0?void 0:l.tools)!==null&&u!==void 0?u:[],N=[];for(const b of E)if(this.isCallableTool(b)){const k=b;N.push(await k.tool())}else N.push(b);N.length>0&&(e.config.tools=N);const D={model:_,config:e.config,callbacks:e.callbacks};return this.apiClient.isVertexAI()?C=dd(this.apiClient,D):C=cd(this.apiClient,D),delete C.config,A.send(JSON.stringify(C)),new rp(A,this.apiClient)}isCallableTool(e){return"callTool"in e&&typeof e.callTool=="function"}}const op={turnComplete:!0};class rp{constructor(e,n){this.conn=e,this.apiClient=n}tLiveClientContent(e,n){if(n.turns!==null&&n.turns!==void 0){let i=[];try{i=he(n.turns),e.isVertexAI()?i=i.map(s=>Ze(s)):i=i.map(s=>nn(s))}catch{throw new Error(`Failed to parse client content "turns", type: '${typeof n.turns}'`)}return{clientContent:{turns:i,turnComplete:n.turnComplete}}}return{clientContent:{turnComplete:n.turnComplete}}}tLiveClienttToolResponse(e,n){let i=[];if(n.functionResponses==null)throw new Error("functionResponses is required.");if(Array.isArray(n.functionResponses)?i=n.functionResponses:i=[n.functionResponses],i.length===0)throw new Error("functionResponses is required.");for(const a of i){if(typeof a!="object"||a===null||!("name"in a)||!("response"in a))throw new Error(`Could not parse function response, type '${typeof a}'.`);if(!e.isVertexAI()&&!("id"in a))throw new Error(np)}return{toolResponse:{functionResponses:i}}}sendClientContent(e){e=Object.assign(Object.assign({},op),e);const n=this.tLiveClientContent(this.apiClient,e);this.conn.send(JSON.stringify(n))}sendRealtimeInput(e){let n={};this.apiClient.isVertexAI()?n={realtimeInput:yd(e)}:n={realtimeInput:gd(e)},this.conn.send(JSON.stringify(n))}sendToolResponse(e){if(e.functionResponses==null)throw new Error("Tool response parameters are required.");const n=this.tLiveClienttToolResponse(this.apiClient,e);this.conn.send(JSON.stringify(n))}close(){this.conn.close()}}function ap(t){const e={};return t.forEach((n,i)=>{e[i]=n}),e}function lp(t){const e=new Headers;for(const[n,i]of Object.entries(t))e.append(n,i);return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Ls=10;function Fs(t){var e,n,i;if(!((e=t==null?void 0:t.automaticFunctionCalling)===null||e===void 0)&&e.disable)return!0;let s=!1;for(const l of(n=t==null?void 0:t.tools)!==null&&n!==void 0?n:[])if(Ft(l)){s=!0;break}if(!s)return!0;const a=(i=t==null?void 0:t.automaticFunctionCalling)===null||i===void 0?void 0:i.maximumRemoteCalls;return a&&(a<0||!Number.isInteger(a))||a==0?(console.warn("Invalid maximumRemoteCalls value provided for automatic function calling. Disabled automatic function calling. Please provide a valid integer value greater than 0. maximumRemoteCalls provided:",a),!0):!1}function Ft(t){return"callTool"in t&&typeof t.callTool=="function"}function Us(t){var e;return!(!((e=t==null?void 0:t.automaticFunctionCalling)===null||e===void 0)&&e.ignoreCallHistory)}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */let up=class extends Ue{constructor(e){super(),this.apiClient=e,this.generateContent=async n=>{var i,s,a,l,u;const d=await this.processParamsForMcpUsage(n);if(this.maybeMoveToResponseJsonSchem(n),!Yh(n)||Fs(n.config))return await this.generateContentInternal(d);if(zh(n))throw new Error("Automatic function calling with CallableTools and Tools is not yet supported.");let c,m;const h=he(d.contents),p=(a=(s=(i=d.config)===null||i===void 0?void 0:i.automaticFunctionCalling)===null||s===void 0?void 0:s.maximumRemoteCalls)!==null&&a!==void 0?a:Ls;let g=0;for(;g<p&&(c=await this.generateContentInternal(d),!(!c.functionCalls||c.functionCalls.length===0));){const y=c.candidates[0].content,x=[];for(const T of(u=(l=n.config)===null||l===void 0?void 0:l.tools)!==null&&u!==void 0?u:[])if(Ft(T)){const w=await T.callTool(c.functionCalls);x.push(...w)}g++,m={role:"user",parts:x},d.contents=he(d.contents),d.contents.push(y),d.contents.push(m),Us(d.config)&&(h.push(y),h.push(m))}return Us(d.config)&&(c.automaticFunctionCallingHistory=h),c},this.generateContentStream=async n=>{if(this.maybeMoveToResponseJsonSchem(n),Fs(n.config)){const i=await this.processParamsForMcpUsage(n);return await this.generateContentStreamInternal(i)}else return await this.processAfcStream(n)},this.generateImages=async n=>await this.generateImagesInternal(n).then(i=>{var s;let a;const l=[];if(i!=null&&i.generatedImages)for(const d of i.generatedImages)d&&(d!=null&&d.safetyAttributes)&&((s=d==null?void 0:d.safetyAttributes)===null||s===void 0?void 0:s.contentType)==="Positive Prompt"?a=d==null?void 0:d.safetyAttributes:l.push(d);let u;return a?u={generatedImages:l,positivePromptSafetyAttributes:a}:u={generatedImages:l},u}),this.list=async n=>{var i;const l={config:Object.assign(Object.assign({},{queryBase:!0}),n==null?void 0:n.config)};if(this.apiClient.isVertexAI()&&!l.config.queryBase){if(!((i=l.config)===null||i===void 0)&&i.filter)throw new Error("Filtering tuned models list for Vertex AI is not currently supported");l.config.filter="labels.tune-type:*"}return new yt(Le.PAGED_ITEM_MODELS,u=>this.listInternal(u),await this.listInternal(l),l)},this.editImage=async n=>{const i={model:n.model,prompt:n.prompt,referenceImages:[],config:n.config};return n.referenceImages&&n.referenceImages&&(i.referenceImages=n.referenceImages.map(s=>s.toReferenceImageAPI())),await this.editImageInternal(i)},this.upscaleImage=async n=>{let i={numberOfImages:1,mode:"upscale"};n.config&&(i=Object.assign(Object.assign({},i),n.config));const s={model:n.model,image:n.image,upscaleFactor:n.upscaleFactor,config:i};return await this.upscaleImageInternal(s)}}maybeMoveToResponseJsonSchem(e){e.config&&e.config.responseSchema&&(e.config.responseJsonSchema||Object.keys(e.config.responseSchema).includes("$schema")&&(e.config.responseJsonSchema=e.config.responseSchema,delete e.config.responseSchema))}async processParamsForMcpUsage(e){var n,i,s;const a=(n=e.config)===null||n===void 0?void 0:n.tools;if(!a)return e;const l=await Promise.all(a.map(async d=>Ft(d)?await d.tool():d)),u={model:e.model,contents:e.contents,config:Object.assign(Object.assign({},e.config),{tools:l})};if(u.config.tools=l,e.config&&e.config.tools&&Jo(e.config.tools)){const d=(s=(i=e.config.httpOptions)===null||i===void 0?void 0:i.headers)!==null&&s!==void 0?s:{};let c=Object.assign({},d);Object.keys(c).length===0&&(c=this.apiClient.getDefaultHeaders()),Oo(c),u.config.httpOptions=Object.assign(Object.assign({},e.config.httpOptions),{headers:c})}return u}async initAfcToolsMap(e){var n,i,s;const a=new Map;for(const l of(i=(n=e.config)===null||n===void 0?void 0:n.tools)!==null&&i!==void 0?i:[])if(Ft(l)){const u=l,d=await u.tool();for(const c of(s=d.functionDeclarations)!==null&&s!==void 0?s:[]){if(!c.name)throw new Error("Function declaration name is required.");if(a.has(c.name))throw new Error(`Duplicate tool declaration name: ${c.name}`);a.set(c.name,u)}}return a}async processAfcStream(e){var n,i,s;const a=(s=(i=(n=e.config)===null||n===void 0?void 0:n.automaticFunctionCalling)===null||i===void 0?void 0:i.maximumRemoteCalls)!==null&&s!==void 0?s:Ls;let l=!1,u=0;const d=await this.initAfcToolsMap(e);return function(c,m,h){var p,g;return Ye(this,arguments,function*(){for(var y,x,T,I;u<a;){l&&(u++,l=!1);const C=yield W(c.processParamsForMcpUsage(h)),E=yield W(c.generateContentStreamInternal(C)),N=[],D=[];try{for(var w=!0,A=(x=void 0,ft(E)),_;_=yield W(A.next()),y=_.done,!y;w=!0){I=_.value,w=!1;const b=I;if(yield yield W(b),b.candidates&&(!((p=b.candidates[0])===null||p===void 0)&&p.content)){D.push(b.candidates[0].content);for(const k of(g=b.candidates[0].content.parts)!==null&&g!==void 0?g:[])if(u<a&&k.functionCall){if(!k.functionCall.name)throw new Error("Function call name was not returned by the model.");if(m.has(k.functionCall.name)){const R=yield W(m.get(k.functionCall.name).callTool([k.functionCall]));N.push(...R)}else throw new Error(`Automatic function calling was requested, but not all the tools the model used implement the CallableTool interface. Available tools: ${m.keys()}, mising tool: ${k.functionCall.name}`)}}}}catch(b){x={error:b}}finally{try{!w&&!y&&(T=A.return)&&(yield W(T.call(A)))}finally{if(x)throw x.error}}if(N.length>0){l=!0;const b=new tt;b.candidates=[{content:{role:"user",parts:N}}],yield yield W(b);const k=[];k.push(...D),k.push({role:"user",parts:N});const R=he(h.contents).concat(k);h.contents=R}else break}})}(this,d,e)}async generateContentInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Ms(this.apiClient,e);return u=L("{model}:generateContent",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Rs(m),p=new tt;return Object.assign(p,h),p})}else{const c=bs(this.apiClient,e);return u=L("{model}:generateContent",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Ps(m),p=new tt;return Object.assign(p,h),p})}}async generateContentStreamInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Ms(this.apiClient,e);return u=L("{model}:streamGenerateContent?alt=sse",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.requestStream({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}),l.then(function(h){return Ye(this,arguments,function*(){var p,g,y,x;try{for(var T=!0,I=ft(h),w;w=yield W(I.next()),p=w.done,!p;T=!0){x=w.value,T=!1;const _=Rs(yield W(x.json())),C=new tt;Object.assign(C,_),yield yield W(C)}}catch(A){g={error:A}}finally{try{!T&&!p&&(y=I.return)&&(yield W(y.call(I)))}finally{if(g)throw g.error}}})})}else{const c=bs(this.apiClient,e);return u=L("{model}:streamGenerateContent?alt=sse",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.requestStream({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}),l.then(function(h){return Ye(this,arguments,function*(){var p,g,y,x;try{for(var T=!0,I=ft(h),w;w=yield W(I.next()),p=w.done,!p;T=!0){x=w.value,T=!1;const _=Ps(yield W(x.json())),C=new tt;Object.assign(C,_),yield yield W(C)}}catch(A){g={error:A}}finally{try{!T&&!p&&(y=I.return)&&(yield W(y.call(I)))}finally{if(g)throw g.error}}})})}}async embedContent(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=ym(this.apiClient,e);return u=L("{model}:predict",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Th(m),p=new us;return Object.assign(p,h),p})}else{const c=Mf(this.apiClient,e);return u=L("{model}:batchEmbedContents",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Zm(m),p=new us;return Object.assign(p,h),p})}}async generateImagesInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=vm(this.apiClient,e);return u=L("{model}:predict",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=wh(m),p=new cs;return Object.assign(p,h),p})}else{const c=Rf(this.apiClient,e);return u=L("{model}:predict",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=th(m),p=new cs;return Object.assign(p,h),p})}}async editImageInternal(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI()){const u=Em(this.apiClient,e);return a=L("{model}:predict",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>{const c=Ah(d),m=new ba;return Object.assign(m,c),m})}else throw new Error("This method is only supported by the Vertex AI.")}async upscaleImageInternal(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI()){const u=Nm(this.apiClient,e);return a=L("{model}:predict",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>{const c=Eh(d),m=new Ma;return Object.assign(m,c),m})}else throw new Error("This method is only supported by the Vertex AI.")}async get(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=bm(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Dn(m))}else{const c=kf(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>kn(m))}}async listInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Pm(this.apiClient,e);return u=L("{models_url}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Mh(m),p=new ds;return Object.assign(p,h),p})}else{const c=Lf(this.apiClient,e);return u=L("{models_url}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=ih(m),p=new ds;return Object.assign(p,h),p})}}async update(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=km(this.apiClient,e);return u=L("{model}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Dn(m))}else{const c=Uf(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"PATCH",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>kn(m))}}async delete(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Dm(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(()=>{const m=Ph(),h=new fs;return Object.assign(h,m),h})}else{const c=$f(this.apiClient,e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"DELETE",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(()=>{const m=sh(),h=new fs;return Object.assign(h,m),h})}}async countTokens(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Fm(this.apiClient,e);return u=L("{model}:countTokens",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=Rh(m),p=new ms;return Object.assign(p,h),p})}else{const c=Bf(this.apiClient,e);return u=L("{model}:countTokens",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=oh(m),p=new ms;return Object.assign(p,h),p})}}async computeTokens(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI()){const u=Um(this.apiClient,e);return a=L("{model}:computeTokens",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>{const c=kh(d),m=new Pa;return Object.assign(m,c),m})}else throw new Error("This method is only supported by the Vertex AI.")}async generateVideos(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=Bm(this.apiClient,e);return u=L("{model}:predictLongRunning",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Uh(m))}else{const c=Jf(this.apiClient,e);return u=L("{model}:predictLongRunning",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"POST",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>uh(m))}}};/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function cp(t){const e={},n=o(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function dp(t){const e={},n=o(t,["operationName"]);n!=null&&r(e,["_url","operationName"],n);const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function fp(t){const e={},n=o(t,["operationName"]);n!=null&&r(e,["operationName"],n);const i=o(t,["resourceName"]);i!=null&&r(e,["_url","resourceName"],i);const s=o(t,["config"]);return s!=null&&r(e,["config"],s),e}function mp(t){const e={},n=o(t,["video","uri"]);n!=null&&r(e,["uri"],n);const i=o(t,["video","encodedVideo"]);i!=null&&r(e,["videoBytes"],we(i));const s=o(t,["encoding"]);return s!=null&&r(e,["mimeType"],s),e}function hp(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],mp(n)),e}function pp(t){const e={},n=o(t,["generatedSamples"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>hp(l))),r(e,["generatedVideos"],a)}const i=o(t,["raiMediaFilteredCount"]);i!=null&&r(e,["raiMediaFilteredCount"],i);const s=o(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function gp(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["metadata"]);i!=null&&r(e,["metadata"],i);const s=o(t,["done"]);s!=null&&r(e,["done"],s);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response","generateVideoResponse"]);return l!=null&&r(e,["response"],pp(l)),e}function yp(t){const e={},n=o(t,["gcsUri"]);n!=null&&r(e,["uri"],n);const i=o(t,["bytesBase64Encoded"]);i!=null&&r(e,["videoBytes"],we(i));const s=o(t,["mimeType"]);return s!=null&&r(e,["mimeType"],s),e}function xp(t){const e={},n=o(t,["_self"]);return n!=null&&r(e,["video"],yp(n)),e}function vp(t){const e={},n=o(t,["videos"]);if(n!=null){let a=n;Array.isArray(a)&&(a=a.map(l=>xp(l))),r(e,["generatedVideos"],a)}const i=o(t,["raiMediaFilteredCount"]);i!=null&&r(e,["raiMediaFilteredCount"],i);const s=o(t,["raiMediaFilteredReasons"]);return s!=null&&r(e,["raiMediaFilteredReasons"],s),e}function $s(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["metadata"]);i!=null&&r(e,["metadata"],i);const s=o(t,["done"]);s!=null&&r(e,["done"],s);const a=o(t,["error"]);a!=null&&r(e,["error"],a);const l=o(t,["response"]);return l!=null&&r(e,["response"],vp(l)),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Cp extends Ue{constructor(e){super(),this.apiClient=e}async getVideosOperation(e){const n=e.operation,i=e.config;if(n.name===void 0||n.name==="")throw new Error("Operation name is required.");if(this.apiClient.isVertexAI()){const s=n.name.split("/operations/")[0];let a;return i&&"httpOptions"in i&&(a=i.httpOptions),this.fetchPredictVideosOperationInternal({operationName:n.name,resourceName:s,config:{httpOptions:a}})}else return this.getVideosOperationInternal({operationName:n.name,config:i})}async getVideosOperationInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=dp(e);return u=L("{operationName}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>$s(m))}else{const c=cp(e);return u=L("{operationName}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>gp(m))}}async fetchPredictVideosOperationInternal(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI()){const u=fp(e);return a=L("{resourceName}:fetchPredictOperation",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>$s(d))}else throw new Error("This method is only supported by the Vertex AI.")}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function _p(t){const e={},n=o(t,["voiceName"]);return n!=null&&r(e,["voiceName"],n),e}function Ho(t){const e={},n=o(t,["prebuiltVoiceConfig"]);return n!=null&&r(e,["prebuiltVoiceConfig"],_p(n)),e}function Tp(t){const e={},n=o(t,["speaker"]);n!=null&&r(e,["speaker"],n);const i=o(t,["voiceConfig"]);return i!=null&&r(e,["voiceConfig"],Ho(i)),e}function Sp(t){const e={},n=o(t,["speakerVoiceConfigs"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>Tp(s))),r(e,["speakerVoiceConfigs"],i)}return e}function wp(t){const e={},n=o(t,["voiceConfig"]);n!=null&&r(e,["voiceConfig"],Ho(n));const i=o(t,["multiSpeakerVoiceConfig"]);i!=null&&r(e,["multiSpeakerVoiceConfig"],Sp(i));const s=o(t,["languageCode"]);return s!=null&&r(e,["languageCode"],s),e}function Ap(t){const e={},n=o(t,["fps"]);n!=null&&r(e,["fps"],n);const i=o(t,["endOffset"]);i!=null&&r(e,["endOffset"],i);const s=o(t,["startOffset"]);return s!=null&&r(e,["startOffset"],s),e}function Ep(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["data"]);n!=null&&r(e,["data"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Ip(t){const e={};if(o(t,["displayName"])!==void 0)throw new Error("displayName parameter is not supported in Gemini API.");const n=o(t,["fileUri"]);n!=null&&r(e,["fileUri"],n);const i=o(t,["mimeType"]);return i!=null&&r(e,["mimeType"],i),e}function Np(t){const e={},n=o(t,["videoMetadata"]);n!=null&&r(e,["videoMetadata"],Ap(n));const i=o(t,["thought"]);i!=null&&r(e,["thought"],i);const s=o(t,["inlineData"]);s!=null&&r(e,["inlineData"],Ep(s));const a=o(t,["fileData"]);a!=null&&r(e,["fileData"],Ip(a));const l=o(t,["thoughtSignature"]);l!=null&&r(e,["thoughtSignature"],l);const u=o(t,["codeExecutionResult"]);u!=null&&r(e,["codeExecutionResult"],u);const d=o(t,["executableCode"]);d!=null&&r(e,["executableCode"],d);const c=o(t,["functionCall"]);c!=null&&r(e,["functionCall"],c);const m=o(t,["functionResponse"]);m!=null&&r(e,["functionResponse"],m);const h=o(t,["text"]);return h!=null&&r(e,["text"],h),e}function bp(t){const e={},n=o(t,["parts"]);if(n!=null){let s=n;Array.isArray(s)&&(s=s.map(a=>Np(a))),r(e,["parts"],s)}const i=o(t,["role"]);return i!=null&&r(e,["role"],i),e}function Mp(t){const e={},n=o(t,["behavior"]);n!=null&&r(e,["behavior"],n);const i=o(t,["description"]);i!=null&&r(e,["description"],i);const s=o(t,["name"]);s!=null&&r(e,["name"],s);const a=o(t,["parameters"]);a!=null&&r(e,["parameters"],a);const l=o(t,["parametersJsonSchema"]);l!=null&&r(e,["parametersJsonSchema"],l);const u=o(t,["response"]);u!=null&&r(e,["response"],u);const d=o(t,["responseJsonSchema"]);return d!=null&&r(e,["responseJsonSchema"],d),e}function Pp(t){const e={},n=o(t,["startTime"]);n!=null&&r(e,["startTime"],n);const i=o(t,["endTime"]);return i!=null&&r(e,["endTime"],i),e}function Rp(t){const e={},n=o(t,["timeRangeFilter"]);return n!=null&&r(e,["timeRangeFilter"],Pp(n)),e}function kp(t){const e={},n=o(t,["mode"]);n!=null&&r(e,["mode"],n);const i=o(t,["dynamicThreshold"]);return i!=null&&r(e,["dynamicThreshold"],i),e}function Dp(t){const e={},n=o(t,["dynamicRetrievalConfig"]);return n!=null&&r(e,["dynamicRetrievalConfig"],kp(n)),e}function Lp(){return{}}function Fp(t){const e={},n=o(t,["functionDeclarations"]);if(n!=null){let d=n;Array.isArray(d)&&(d=d.map(c=>Mp(c))),r(e,["functionDeclarations"],d)}if(o(t,["retrieval"])!==void 0)throw new Error("retrieval parameter is not supported in Gemini API.");const i=o(t,["googleSearch"]);i!=null&&r(e,["googleSearch"],Rp(i));const s=o(t,["googleSearchRetrieval"]);if(s!=null&&r(e,["googleSearchRetrieval"],Dp(s)),o(t,["enterpriseWebSearch"])!==void 0)throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(o(t,["googleMaps"])!==void 0)throw new Error("googleMaps parameter is not supported in Gemini API.");o(t,["urlContext"])!=null&&r(e,["urlContext"],Lp());const l=o(t,["codeExecution"]);l!=null&&r(e,["codeExecution"],l);const u=o(t,["computerUse"]);return u!=null&&r(e,["computerUse"],u),e}function Up(t){const e={},n=o(t,["handle"]);if(n!=null&&r(e,["handle"],n),o(t,["transparent"])!==void 0)throw new Error("transparent parameter is not supported in Gemini API.");return e}function Vs(){return{}}function $p(t){const e={},n=o(t,["disabled"]);n!=null&&r(e,["disabled"],n);const i=o(t,["startOfSpeechSensitivity"]);i!=null&&r(e,["startOfSpeechSensitivity"],i);const s=o(t,["endOfSpeechSensitivity"]);s!=null&&r(e,["endOfSpeechSensitivity"],s);const a=o(t,["prefixPaddingMs"]);a!=null&&r(e,["prefixPaddingMs"],a);const l=o(t,["silenceDurationMs"]);return l!=null&&r(e,["silenceDurationMs"],l),e}function Vp(t){const e={},n=o(t,["automaticActivityDetection"]);n!=null&&r(e,["automaticActivityDetection"],$p(n));const i=o(t,["activityHandling"]);i!=null&&r(e,["activityHandling"],i);const s=o(t,["turnCoverage"]);return s!=null&&r(e,["turnCoverage"],s),e}function Bp(t){const e={},n=o(t,["targetTokens"]);return n!=null&&r(e,["targetTokens"],n),e}function qp(t){const e={},n=o(t,["triggerTokens"]);n!=null&&r(e,["triggerTokens"],n);const i=o(t,["slidingWindow"]);return i!=null&&r(e,["slidingWindow"],Bp(i)),e}function Gp(t){const e={},n=o(t,["proactiveAudio"]);return n!=null&&r(e,["proactiveAudio"],n),e}function Jp(t,e){const n={},i=o(t,["generationConfig"]);e!==void 0&&i!=null&&r(e,["setup","generationConfig"],i);const s=o(t,["responseModalities"]);e!==void 0&&s!=null&&r(e,["setup","generationConfig","responseModalities"],s);const a=o(t,["temperature"]);e!==void 0&&a!=null&&r(e,["setup","generationConfig","temperature"],a);const l=o(t,["topP"]);e!==void 0&&l!=null&&r(e,["setup","generationConfig","topP"],l);const u=o(t,["topK"]);e!==void 0&&u!=null&&r(e,["setup","generationConfig","topK"],u);const d=o(t,["maxOutputTokens"]);e!==void 0&&d!=null&&r(e,["setup","generationConfig","maxOutputTokens"],d);const c=o(t,["mediaResolution"]);e!==void 0&&c!=null&&r(e,["setup","generationConfig","mediaResolution"],c);const m=o(t,["seed"]);e!==void 0&&m!=null&&r(e,["setup","generationConfig","seed"],m);const h=o(t,["speechConfig"]);e!==void 0&&h!=null&&r(e,["setup","generationConfig","speechConfig"],wp(Zn(h)));const p=o(t,["enableAffectiveDialog"]);e!==void 0&&p!=null&&r(e,["setup","generationConfig","enableAffectiveDialog"],p);const g=o(t,["systemInstruction"]);e!==void 0&&g!=null&&r(e,["setup","systemInstruction"],bp(re(g)));const y=o(t,["tools"]);if(e!==void 0&&y!=null){let C=Xe(y);Array.isArray(C)&&(C=C.map(E=>Fp(ze(E)))),r(e,["setup","tools"],C)}const x=o(t,["sessionResumption"]);e!==void 0&&x!=null&&r(e,["setup","sessionResumption"],Up(x));const T=o(t,["inputAudioTranscription"]);e!==void 0&&T!=null&&r(e,["setup","inputAudioTranscription"],Vs());const I=o(t,["outputAudioTranscription"]);e!==void 0&&I!=null&&r(e,["setup","outputAudioTranscription"],Vs());const w=o(t,["realtimeInputConfig"]);e!==void 0&&w!=null&&r(e,["setup","realtimeInputConfig"],Vp(w));const A=o(t,["contextWindowCompression"]);e!==void 0&&A!=null&&r(e,["setup","contextWindowCompression"],qp(A));const _=o(t,["proactivity"]);return e!==void 0&&_!=null&&r(e,["setup","proactivity"],Gp(_)),n}function Op(t,e){const n={},i=o(e,["model"]);i!=null&&r(n,["setup","model"],J(t,i));const s=o(e,["config"]);return s!=null&&r(n,["config"],Jp(s,n)),n}function Hp(t,e,n){const i={},s=o(e,["expireTime"]);n!==void 0&&s!=null&&r(n,["expireTime"],s);const a=o(e,["newSessionExpireTime"]);n!==void 0&&a!=null&&r(n,["newSessionExpireTime"],a);const l=o(e,["uses"]);n!==void 0&&l!=null&&r(n,["uses"],l);const u=o(e,["liveConnectConstraints"]);n!==void 0&&u!=null&&r(n,["bidiGenerateContentSetup"],Op(t,u));const d=o(e,["lockAdditionalFields"]);return n!==void 0&&d!=null&&r(n,["fieldMask"],d),i}function Wp(t,e){const n={},i=o(e,["config"]);return i!=null&&r(n,["config"],Hp(t,i,n)),n}function Kp(t){const e={},n=o(t,["name"]);return n!=null&&r(e,["name"],n),e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Yp(t){const e=[];for(const n in t)if(Object.prototype.hasOwnProperty.call(t,n)){const i=t[n];if(typeof i=="object"&&i!=null&&Object.keys(i).length>0){const s=Object.keys(i).map(a=>`${n}.${a}`);e.push(...s)}else e.push(n)}return e.join(",")}function zp(t,e){let n=null;const i=t.bidiGenerateContentSetup;if(typeof i=="object"&&i!==null&&"setup"in i){const a=i.setup;typeof a=="object"&&a!==null?(t.bidiGenerateContentSetup=a,n=a):delete t.bidiGenerateContentSetup}else i!==void 0&&delete t.bidiGenerateContentSetup;const s=t.fieldMask;if(n){const a=Yp(n);if(Array.isArray(e==null?void 0:e.lockAdditionalFields)&&(e==null?void 0:e.lockAdditionalFields.length)===0)a?t.fieldMask=a:delete t.fieldMask;else if(e!=null&&e.lockAdditionalFields&&e.lockAdditionalFields.length>0&&s!==null&&Array.isArray(s)&&s.length>0){const l=["temperature","topK","topP","maxOutputTokens","responseModalities","seed","speechConfig"];let u=[];s.length>0&&(u=s.map(c=>l.includes(c)?`generationConfig.${c}`:c));const d=[];a&&d.push(a),u.length>0&&d.push(...u),d.length>0?t.fieldMask=d.join(","):delete t.fieldMask}else delete t.fieldMask}else s!==null&&Array.isArray(s)&&s.length>0?t.fieldMask=s.join(","):delete t.fieldMask;return t}class Xp extends Ue{constructor(e){super(),this.apiClient=e}async create(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("The client.tokens.create method is only supported by the Gemini Developer API.");{const u=Wp(this.apiClient,e);a=L("auth_tokens",u._url),l=u._query,delete u.config,delete u._url,delete u._query;const d=zp(u,e.config);return s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(d),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(c=>c.json()),s.then(c=>Kp(c))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */function Qp(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","name"],n);const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function Zp(t,e){const n={},i=o(t,["pageSize"]);e!==void 0&&i!=null&&r(e,["_query","pageSize"],i);const s=o(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const a=o(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function jp(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],Zp(n,e)),e}function eg(t){const e={},n=o(t,["textInput"]);n!=null&&r(e,["textInput"],n);const i=o(t,["output"]);return i!=null&&r(e,["output"],i),e}function tg(t){const e={};if(o(t,["gcsUri"])!==void 0)throw new Error("gcsUri parameter is not supported in Gemini API.");if(o(t,["vertexDatasetResource"])!==void 0)throw new Error("vertexDatasetResource parameter is not supported in Gemini API.");const n=o(t,["examples"]);if(n!=null){let i=n;Array.isArray(i)&&(i=i.map(s=>eg(s))),r(e,["examples","examples"],i)}return e}function ng(t,e){const n={};if(o(t,["validationDataset"])!==void 0)throw new Error("validationDataset parameter is not supported in Gemini API.");const i=o(t,["tunedModelDisplayName"]);if(e!==void 0&&i!=null&&r(e,["displayName"],i),o(t,["description"])!==void 0)throw new Error("description parameter is not supported in Gemini API.");const s=o(t,["epochCount"]);e!==void 0&&s!=null&&r(e,["tuningTask","hyperparameters","epochCount"],s);const a=o(t,["learningRateMultiplier"]);if(a!=null&&r(n,["tuningTask","hyperparameters","learningRateMultiplier"],a),o(t,["exportLastCheckpointOnly"])!==void 0)throw new Error("exportLastCheckpointOnly parameter is not supported in Gemini API.");if(o(t,["adapterSize"])!==void 0)throw new Error("adapterSize parameter is not supported in Gemini API.");const l=o(t,["batchSize"]);e!==void 0&&l!=null&&r(e,["tuningTask","hyperparameters","batchSize"],l);const u=o(t,["learningRate"]);return e!==void 0&&u!=null&&r(e,["tuningTask","hyperparameters","learningRate"],u),n}function ig(t){const e={},n=o(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const i=o(t,["trainingDataset"]);i!=null&&r(e,["tuningTask","trainingData"],tg(i));const s=o(t,["config"]);return s!=null&&r(e,["config"],ng(s,e)),e}function sg(t){const e={},n=o(t,["name"]);n!=null&&r(e,["_url","name"],n);const i=o(t,["config"]);return i!=null&&r(e,["config"],i),e}function og(t,e){const n={},i=o(t,["pageSize"]);e!==void 0&&i!=null&&r(e,["_query","pageSize"],i);const s=o(t,["pageToken"]);e!==void 0&&s!=null&&r(e,["_query","pageToken"],s);const a=o(t,["filter"]);return e!==void 0&&a!=null&&r(e,["_query","filter"],a),n}function rg(t){const e={},n=o(t,["config"]);return n!=null&&r(e,["config"],og(n,e)),e}function ag(t,e){const n={},i=o(t,["gcsUri"]);e!==void 0&&i!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],i);const s=o(t,["vertexDatasetResource"]);if(e!==void 0&&s!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],s),o(t,["examples"])!==void 0)throw new Error("examples parameter is not supported in Vertex AI.");return n}function lg(t,e){const n={},i=o(t,["gcsUri"]);i!=null&&r(n,["validationDatasetUri"],i);const s=o(t,["vertexDatasetResource"]);return e!==void 0&&s!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],s),n}function ug(t,e){const n={},i=o(t,["validationDataset"]);e!==void 0&&i!=null&&r(e,["supervisedTuningSpec"],lg(i,n));const s=o(t,["tunedModelDisplayName"]);e!==void 0&&s!=null&&r(e,["tunedModelDisplayName"],s);const a=o(t,["description"]);e!==void 0&&a!=null&&r(e,["description"],a);const l=o(t,["epochCount"]);e!==void 0&&l!=null&&r(e,["supervisedTuningSpec","hyperParameters","epochCount"],l);const u=o(t,["learningRateMultiplier"]);e!==void 0&&u!=null&&r(e,["supervisedTuningSpec","hyperParameters","learningRateMultiplier"],u);const d=o(t,["exportLastCheckpointOnly"]);e!==void 0&&d!=null&&r(e,["supervisedTuningSpec","exportLastCheckpointOnly"],d);const c=o(t,["adapterSize"]);if(e!==void 0&&c!=null&&r(e,["supervisedTuningSpec","hyperParameters","adapterSize"],c),o(t,["batchSize"])!==void 0)throw new Error("batchSize parameter is not supported in Vertex AI.");if(o(t,["learningRate"])!==void 0)throw new Error("learningRate parameter is not supported in Vertex AI.");return n}function cg(t){const e={},n=o(t,["baseModel"]);n!=null&&r(e,["baseModel"],n);const i=o(t,["trainingDataset"]);i!=null&&r(e,["supervisedTuningSpec","trainingDatasetUri"],ag(i,e));const s=o(t,["config"]);return s!=null&&r(e,["config"],ug(s,e)),e}function dg(t){const e={},n=o(t,["name"]);n!=null&&r(e,["model"],n);const i=o(t,["name"]);return i!=null&&r(e,["endpoint"],i),e}function Wo(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["state"]);i!=null&&r(e,["state"],So(i));const s=o(t,["createTime"]);s!=null&&r(e,["createTime"],s);const a=o(t,["tuningTask","startTime"]);a!=null&&r(e,["startTime"],a);const l=o(t,["tuningTask","completeTime"]);l!=null&&r(e,["endTime"],l);const u=o(t,["updateTime"]);u!=null&&r(e,["updateTime"],u);const d=o(t,["description"]);d!=null&&r(e,["description"],d);const c=o(t,["baseModel"]);c!=null&&r(e,["baseModel"],c);const m=o(t,["_self"]);m!=null&&r(e,["tunedModel"],dg(m));const h=o(t,["distillationSpec"]);h!=null&&r(e,["distillationSpec"],h);const p=o(t,["experiment"]);p!=null&&r(e,["experiment"],p);const g=o(t,["labels"]);g!=null&&r(e,["labels"],g);const y=o(t,["pipelineJob"]);y!=null&&r(e,["pipelineJob"],y);const x=o(t,["satisfiesPzi"]);x!=null&&r(e,["satisfiesPzi"],x);const T=o(t,["satisfiesPzs"]);T!=null&&r(e,["satisfiesPzs"],T);const I=o(t,["serviceAccount"]);I!=null&&r(e,["serviceAccount"],I);const w=o(t,["tunedModelDisplayName"]);return w!=null&&r(e,["tunedModelDisplayName"],w),e}function fg(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["tunedModels"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>Wo(a))),r(e,["tuningJobs"],s)}return e}function mg(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["metadata"]);i!=null&&r(e,["metadata"],i);const s=o(t,["done"]);s!=null&&r(e,["done"],s);const a=o(t,["error"]);return a!=null&&r(e,["error"],a),e}function hg(t){const e={},n=o(t,["checkpointId"]);n!=null&&r(e,["checkpointId"],n);const i=o(t,["epoch"]);i!=null&&r(e,["epoch"],i);const s=o(t,["step"]);s!=null&&r(e,["step"],s);const a=o(t,["endpoint"]);return a!=null&&r(e,["endpoint"],a),e}function pg(t){const e={},n=o(t,["model"]);n!=null&&r(e,["model"],n);const i=o(t,["endpoint"]);i!=null&&r(e,["endpoint"],i);const s=o(t,["checkpoints"]);if(s!=null){let a=s;Array.isArray(a)&&(a=a.map(l=>hg(l))),r(e,["checkpoints"],a)}return e}function Fn(t){const e={},n=o(t,["name"]);n!=null&&r(e,["name"],n);const i=o(t,["state"]);i!=null&&r(e,["state"],So(i));const s=o(t,["createTime"]);s!=null&&r(e,["createTime"],s);const a=o(t,["startTime"]);a!=null&&r(e,["startTime"],a);const l=o(t,["endTime"]);l!=null&&r(e,["endTime"],l);const u=o(t,["updateTime"]);u!=null&&r(e,["updateTime"],u);const d=o(t,["error"]);d!=null&&r(e,["error"],d);const c=o(t,["description"]);c!=null&&r(e,["description"],c);const m=o(t,["baseModel"]);m!=null&&r(e,["baseModel"],m);const h=o(t,["tunedModel"]);h!=null&&r(e,["tunedModel"],pg(h));const p=o(t,["supervisedTuningSpec"]);p!=null&&r(e,["supervisedTuningSpec"],p);const g=o(t,["tuningDataStats"]);g!=null&&r(e,["tuningDataStats"],g);const y=o(t,["encryptionSpec"]);y!=null&&r(e,["encryptionSpec"],y);const x=o(t,["partnerModelTuningSpec"]);x!=null&&r(e,["partnerModelTuningSpec"],x);const T=o(t,["distillationSpec"]);T!=null&&r(e,["distillationSpec"],T);const I=o(t,["experiment"]);I!=null&&r(e,["experiment"],I);const w=o(t,["labels"]);w!=null&&r(e,["labels"],w);const A=o(t,["pipelineJob"]);A!=null&&r(e,["pipelineJob"],A);const _=o(t,["satisfiesPzi"]);_!=null&&r(e,["satisfiesPzi"],_);const C=o(t,["satisfiesPzs"]);C!=null&&r(e,["satisfiesPzs"],C);const E=o(t,["serviceAccount"]);E!=null&&r(e,["serviceAccount"],E);const N=o(t,["tunedModelDisplayName"]);return N!=null&&r(e,["tunedModelDisplayName"],N),e}function gg(t){const e={},n=o(t,["nextPageToken"]);n!=null&&r(e,["nextPageToken"],n);const i=o(t,["tuningJobs"]);if(i!=null){let s=i;Array.isArray(s)&&(s=s.map(a=>Fn(a))),r(e,["tuningJobs"],s)}return e}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class yg extends Ue{constructor(e){super(),this.apiClient=e,this.get=async n=>await this.getInternal(n),this.list=async(n={})=>new yt(Le.PAGED_ITEM_TUNING_JOBS,i=>this.listInternal(i),await this.listInternal(n),n),this.tune=async n=>{if(this.apiClient.isVertexAI())return await this.tuneInternal(n);{const i=await this.tuneMldevInternal(n);let s="";return i.metadata!==void 0&&i.metadata.tunedModel!==void 0?s=i.metadata.tunedModel:i.name!==void 0&&i.name.includes("/operations/")&&(s=i.name.split("/operations/")[0]),{name:s,state:En.JOB_STATE_QUEUED}}}}async getInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=sg(e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>Fn(m))}else{const c=Qp(e);return u=L("{name}",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>Wo(m))}}async listInternal(e){var n,i,s,a;let l,u="",d={};if(this.apiClient.isVertexAI()){const c=rg(e);return u=L("tuningJobs",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(m=>m.json()),l.then(m=>{const h=gg(m),p=new hs;return Object.assign(p,h),p})}else{const c=jp(e);return u=L("tunedModels",c._url),d=c._query,delete c.config,delete c._url,delete c._query,l=this.apiClient.request({path:u,queryParams:d,body:JSON.stringify(c),httpMethod:"GET",httpOptions:(s=e.config)===null||s===void 0?void 0:s.httpOptions,abortSignal:(a=e.config)===null||a===void 0?void 0:a.abortSignal}).then(m=>m.json()),l.then(m=>{const h=fg(m),p=new hs;return Object.assign(p,h),p})}}async tuneInternal(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI()){const u=cg(e);return a=L("tuningJobs",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>Fn(d))}else throw new Error("This method is only supported by the Vertex AI.")}async tuneMldevInternal(e){var n,i;let s,a="",l={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const u=ig(e);return a=L("tunedModels",u._url),l=u._query,delete u.config,delete u._url,delete u._query,s=this.apiClient.request({path:a,queryParams:l,body:JSON.stringify(u),httpMethod:"POST",httpOptions:(n=e.config)===null||n===void 0?void 0:n.httpOptions,abortSignal:(i=e.config)===null||i===void 0?void 0:i.abortSignal}).then(d=>d.json()),s.then(d=>mg(d))}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class xg{async download(e,n){throw new Error("Download to file is not supported in the browser, please use a browser compliant download like an <a> tag.")}}const vg=1024*1024*8,Cg=3,_g=1e3,Tg=2,hn="x-goog-upload-status";async function Sg(t,e,n){var i,s,a;let l=0,u=0,d=new In(new Response),c="upload";for(l=t.size;u<l;){const h=Math.min(vg,l-u),p=t.slice(u,u+h);u+h>=l&&(c+=", finalize");let g=0,y=_g;for(;g<Cg&&(d=await n.request({path:"",body:p,httpMethod:"POST",httpOptions:{apiVersion:"",baseUrl:e,headers:{"X-Goog-Upload-Command":c,"X-Goog-Upload-Offset":String(u),"Content-Length":String(h)}}}),!(!((i=d==null?void 0:d.headers)===null||i===void 0)&&i[hn]));)g++,await Ag(y),y=y*Tg;if(u+=h,((s=d==null?void 0:d.headers)===null||s===void 0?void 0:s[hn])!=="active")break;if(l<=u)throw new Error("All content has been uploaded, but the upload status is not finalized.")}const m=await(d==null?void 0:d.json());if(((a=d==null?void 0:d.headers)===null||a===void 0?void 0:a[hn])!=="final")throw new Error("Failed to upload file: Upload status is not finalized.");return m.file}async function wg(t){return{size:t.size,type:t.type}}function Ag(t){return new Promise(e=>setTimeout(e,t))}class Eg{async upload(e,n,i){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await Sg(e,n,i)}async stat(e){if(typeof e=="string")throw new Error("File path is not supported in browser uploader.");return await wg(e)}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */class Ig{create(e,n,i){return new Ng(e,n,i)}}class Ng{constructor(e,n,i){this.url=e,this.headers=n,this.callbacks=i}connect(){this.ws=new WebSocket(this.url),this.ws.onopen=this.callbacks.onopen,this.ws.onerror=this.callbacks.onerror,this.ws.onclose=this.callbacks.onclose,this.ws.onmessage=this.callbacks.onmessage}send(e){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.send(e)}close(){if(this.ws===void 0)throw new Error("WebSocket is not connected");this.ws.close()}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Bs="x-goog-api-key";class bg{constructor(e){this.apiKey=e}async addAuthHeaders(e){if(e.get(Bs)===null){if(this.apiKey.startsWith("auth_tokens/"))throw new Error("Ephemeral tokens are only supported by the live API.");if(!this.apiKey)throw new Error("API key is missing. Please provide a valid API key.");e.append(Bs,this.apiKey)}}}/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */const Mg="gl-node/";class qs{constructor(e){var n;if(e.apiKey==null)throw new Error("An API Key must be set when running in a browser");if(e.project||e.location)throw new Error("Vertex AI project based authentication is not supported on browser runtimes. Please do not provide a project or location.");this.vertexai=(n=e.vertexai)!==null&&n!==void 0?n:!1,this.apiKey=e.apiKey;const i=Na(e,void 0,void 0);i&&(e.httpOptions?e.httpOptions.baseUrl=i:e.httpOptions={baseUrl:i}),this.apiVersion=e.apiVersion;const s=new bg(this.apiKey);this.apiClient=new Hh({auth:s,apiVersion:this.apiVersion,apiKey:this.apiKey,vertexai:this.vertexai,httpOptions:e.httpOptions,userAgentExtra:Mg+"web",uploader:new Eg,downloader:new xg}),this.models=new up(this.apiClient),this.live=new sp(this.apiClient,s,new Ig),this.batches=new Ql(this.apiClient),this.chats=new tc(this.models,this.apiClient),this.caches=new Zu(this.apiClient),this.files=new hc(this.apiClient),this.operations=new Cp(this.apiClient),this.authTokens=new Xp(this.apiClient),this.tunings=new yg(this.apiClient)}}function $(t,e,n,i,s){if(typeof e=="function"?t!==e||!0:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(t,n),n}function v(t,e,n,i){if(n==="a"&&!i)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?t!==e||!i:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return n==="m"?i:n==="a"?i.call(t):i?i.value:e.get(t)}let Ko=function(){const{crypto:t}=globalThis;if(t!=null&&t.randomUUID)return Ko=t.randomUUID.bind(t),t.randomUUID();const e=new Uint8Array(1),n=t?()=>t.getRandomValues(e)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,i=>(+i^n()&15>>+i/4).toString(16))};function Un(t){return typeof t=="object"&&t!==null&&("name"in t&&t.name==="AbortError"||"message"in t&&String(t.message).includes("FetchRequestCanceledException"))}const $n=t=>{if(t instanceof Error)return t;if(typeof t=="object"&&t!==null){try{if(Object.prototype.toString.call(t)==="[object Error]"){const e=new Error(t.message,t.cause?{cause:t.cause}:{});return t.stack&&(e.stack=t.stack),t.cause&&!e.cause&&(e.cause=t.cause),t.name&&(e.name=t.name),e}}catch{}try{return new Error(JSON.stringify(t))}catch{}}return new Error(t)};class V extends Error{}class ie extends V{constructor(e,n,i,s){super(`${ie.makeMessage(e,n,i)}`),this.status=e,this.headers=s,this.requestID=s==null?void 0:s.get("x-request-id"),this.error=n;const a=n;this.code=a==null?void 0:a.code,this.param=a==null?void 0:a.param,this.type=a==null?void 0:a.type}static makeMessage(e,n,i){const s=n!=null&&n.message?typeof n.message=="string"?n.message:JSON.stringify(n.message):n?JSON.stringify(n):i;return e&&s?`${e} ${s}`:e?`${e} status code (no body)`:s||"(no status code or body)"}static generate(e,n,i,s){if(!e||!s)return new on({message:i,cause:$n(n)});const a=n==null?void 0:n.error;return e===400?new Yo(e,a,i,s):e===401?new zo(e,a,i,s):e===403?new Xo(e,a,i,s):e===404?new Qo(e,a,i,s):e===409?new Zo(e,a,i,s):e===422?new jo(e,a,i,s):e===429?new er(e,a,i,s):e>=500?new tr(e,a,i,s):new ie(e,a,i,s)}}class me extends ie{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0)}}class on extends ie{constructor({message:e,cause:n}){super(void 0,void 0,e||"Connection error.",void 0),n&&(this.cause=n)}}class ni extends on{constructor({message:e}={}){super({message:e??"Request timed out."})}}class Yo extends ie{}class zo extends ie{}class Xo extends ie{}class Qo extends ie{}class Zo extends ie{}class jo extends ie{}class er extends ie{}class tr extends ie{}class nr extends V{constructor(){super("Could not parse response content as the length limit was reached")}}class ir extends V{constructor(){super("Could not parse response content as the request was rejected by the content filter")}}class it extends Error{constructor(e){super(e)}}const Pg=/^[a-z][a-z0-9+.-]*:/i,Rg=t=>Pg.test(t);let le=t=>(le=Array.isArray,le(t)),Gs=le;function kg(t){return typeof t!="object"?{}:t??{}}function Dg(t){if(!t)return!0;for(const e in t)return!1;return!0}function Lg(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function pn(t){return t!=null&&typeof t=="object"&&!Array.isArray(t)}const Fg=(t,e)=>{if(typeof e!="number"||!Number.isInteger(e))throw new V(`${t} must be an integer`);if(e<0)throw new V(`${t} must be a positive integer`);return e},Ug=t=>{try{return JSON.parse(t)}catch{return}},xt=t=>new Promise(e=>setTimeout(e,t)),Ge="5.10.2",$g=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u";function Vg(){return typeof Deno<"u"&&Deno.build!=null?"deno":typeof EdgeRuntime<"u"?"edge":Object.prototype.toString.call(typeof globalThis.process<"u"?globalThis.process:0)==="[object process]"?"node":"unknown"}const Bg=()=>{var n;const t=Vg();if(t==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":Os(Deno.build.os),"X-Stainless-Arch":Js(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((n=Deno.version)==null?void 0:n.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(t==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":Os(globalThis.process.platform??"unknown"),"X-Stainless-Arch":Js(globalThis.process.arch??"unknown"),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version??"unknown"};const e=qg();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ge,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function qg(){if(typeof navigator>"u"||!navigator)return null;const t=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:e,pattern:n}of t){const i=n.exec(navigator.userAgent);if(i){const s=i[1]||0,a=i[2]||0,l=i[3]||0;return{browser:e,version:`${s}.${a}.${l}`}}}return null}const Js=t=>t==="x32"?"x32":t==="x86_64"||t==="x64"?"x64":t==="arm"?"arm":t==="aarch64"||t==="arm64"?"arm64":t?`other:${t}`:"unknown",Os=t=>(t=t.toLowerCase(),t.includes("ios")?"iOS":t==="android"?"Android":t==="darwin"?"MacOS":t==="win32"?"Windows":t==="freebsd"?"FreeBSD":t==="openbsd"?"OpenBSD":t==="linux"?"Linux":t?`Other:${t}`:"Unknown");let Hs;const Gg=()=>Hs??(Hs=Bg());function Jg(){if(typeof fetch<"u")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new OpenAI({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function sr(...t){const e=globalThis.ReadableStream;if(typeof e>"u")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new e(...t)}function or(t){let e=Symbol.asyncIterator in t?t[Symbol.asyncIterator]():t[Symbol.iterator]();return sr({start(){},async pull(n){const{done:i,value:s}=await e.next();i?n.close():n.enqueue(s)},async cancel(){var n;await((n=e.return)==null?void 0:n.call(e))}})}function rr(t){if(t[Symbol.asyncIterator])return t;const e=t.getReader();return{async next(){try{const n=await e.read();return n!=null&&n.done&&e.releaseLock(),n}catch(n){throw e.releaseLock(),n}},async return(){const n=e.cancel();return e.releaseLock(),await n,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function Og(t){var i,s;if(t===null||typeof t!="object")return;if(t[Symbol.asyncIterator]){await((s=(i=t[Symbol.asyncIterator]()).return)==null?void 0:s.call(i));return}const e=t.getReader(),n=e.cancel();e.releaseLock(),await n}const Hg=({headers:t,body:e})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(e)}),ar="RFC3986",lr=t=>String(t),Ws={RFC1738:t=>String(t).replace(/%20/g,"+"),RFC3986:lr},Wg="RFC1738";let Vn=(t,e)=>(Vn=Object.hasOwn??Function.prototype.call.bind(Object.prototype.hasOwnProperty),Vn(t,e));const ye=(()=>{const t=[];for(let e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t})(),gn=1024,Kg=(t,e,n,i,s)=>{if(t.length===0)return t;let a=t;if(typeof t=="symbol"?a=Symbol.prototype.toString.call(t):typeof t!="string"&&(a=String(t)),n==="iso-8859-1")return escape(a).replace(/%u[0-9a-f]{4}/gi,function(u){return"%26%23"+parseInt(u.slice(2),16)+"%3B"});let l="";for(let u=0;u<a.length;u+=gn){const d=a.length>=gn?a.slice(u,u+gn):a,c=[];for(let m=0;m<d.length;++m){let h=d.charCodeAt(m);if(h===45||h===46||h===95||h===126||h>=48&&h<=57||h>=65&&h<=90||h>=97&&h<=122||s===Wg&&(h===40||h===41)){c[c.length]=d.charAt(m);continue}if(h<128){c[c.length]=ye[h];continue}if(h<2048){c[c.length]=ye[192|h>>6]+ye[128|h&63];continue}if(h<55296||h>=57344){c[c.length]=ye[224|h>>12]+ye[128|h>>6&63]+ye[128|h&63];continue}m+=1,h=65536+((h&1023)<<10|d.charCodeAt(m)&1023),c[c.length]=ye[240|h>>18]+ye[128|h>>12&63]+ye[128|h>>6&63]+ye[128|h&63]}l+=c.join("")}return l};function Yg(t){return!t||typeof t!="object"?!1:!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))}function Ks(t,e){if(le(t)){const n=[];for(let i=0;i<t.length;i+=1)n.push(e(t[i]));return n}return e(t)}const ur={brackets(t){return String(t)+"[]"},comma:"comma",indices(t,e){return String(t)+"["+e+"]"},repeat(t){return String(t)}},cr=function(t,e){Array.prototype.push.apply(t,le(e)?e:[e])};let Ys;const Z={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:Kg,encodeValuesOnly:!1,format:ar,formatter:lr,indices:!1,serializeDate(t){return(Ys??(Ys=Function.prototype.call.bind(Date.prototype.toISOString)))(t)},skipNulls:!1,strictNullHandling:!1};function zg(t){return typeof t=="string"||typeof t=="number"||typeof t=="boolean"||typeof t=="symbol"||typeof t=="bigint"}const yn={};function dr(t,e,n,i,s,a,l,u,d,c,m,h,p,g,y,x,T,I){let w=t,A=I,_=0,C=!1;for(;(A=A.get(yn))!==void 0&&!C;){const k=A.get(t);if(_+=1,typeof k<"u"){if(k===_)throw new RangeError("Cyclic object value");C=!0}typeof A.get(yn)>"u"&&(_=0)}if(typeof c=="function"?w=c(e,w):w instanceof Date?w=p==null?void 0:p(w):n==="comma"&&le(w)&&(w=Ks(w,function(k){return k instanceof Date?p==null?void 0:p(k):k})),w===null){if(a)return d&&!x?d(e,Z.encoder,T,"key",g):e;w=""}if(zg(w)||Yg(w)){if(d){const k=x?e:d(e,Z.encoder,T,"key",g);return[(y==null?void 0:y(k))+"="+(y==null?void 0:y(d(w,Z.encoder,T,"value",g)))]}return[(y==null?void 0:y(e))+"="+(y==null?void 0:y(String(w)))]}const E=[];if(typeof w>"u")return E;let N;if(n==="comma"&&le(w))x&&d&&(w=Ks(w,d)),N=[{value:w.length>0?w.join(",")||null:void 0}];else if(le(c))N=c;else{const k=Object.keys(w);N=m?k.sort(m):k}const D=u?String(e).replace(/\./g,"%2E"):String(e),b=i&&le(w)&&w.length===1?D+"[]":D;if(s&&le(w)&&w.length===0)return b+"[]";for(let k=0;k<N.length;++k){const R=N[k],K=typeof R=="object"&&typeof R.value<"u"?R.value:w[R];if(l&&K===null)continue;const fe=h&&u?R.replace(/\./g,"%2E"):R,z=le(w)?typeof n=="function"?n(b,fe):b:b+(h?"."+fe:"["+fe+"]");I.set(t,_);const X=new WeakMap;X.set(yn,I),cr(E,dr(K,z,n,i,s,a,l,u,n==="comma"&&x&&le(w)?null:d,c,m,h,p,g,y,x,T,X))}return E}function Xg(t=Z){if(typeof t.allowEmptyArrays<"u"&&typeof t.allowEmptyArrays!="boolean")throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(typeof t.encodeDotInKeys<"u"&&typeof t.encodeDotInKeys!="boolean")throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(t.encoder!==null&&typeof t.encoder<"u"&&typeof t.encoder!="function")throw new TypeError("Encoder has to be a function.");const e=t.charset||Z.charset;if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let n=ar;if(typeof t.format<"u"){if(!Vn(Ws,t.format))throw new TypeError("Unknown format option provided.");n=t.format}const i=Ws[n];let s=Z.filter;(typeof t.filter=="function"||le(t.filter))&&(s=t.filter);let a;if(t.arrayFormat&&t.arrayFormat in ur?a=t.arrayFormat:"indices"in t?a=t.indices?"indices":"repeat":a=Z.arrayFormat,"commaRoundTrip"in t&&typeof t.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");const l=typeof t.allowDots>"u"?t.encodeDotInKeys?!0:Z.allowDots:!!t.allowDots;return{addQueryPrefix:typeof t.addQueryPrefix=="boolean"?t.addQueryPrefix:Z.addQueryPrefix,allowDots:l,allowEmptyArrays:typeof t.allowEmptyArrays=="boolean"?!!t.allowEmptyArrays:Z.allowEmptyArrays,arrayFormat:a,charset:e,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:Z.charsetSentinel,commaRoundTrip:!!t.commaRoundTrip,delimiter:typeof t.delimiter>"u"?Z.delimiter:t.delimiter,encode:typeof t.encode=="boolean"?t.encode:Z.encode,encodeDotInKeys:typeof t.encodeDotInKeys=="boolean"?t.encodeDotInKeys:Z.encodeDotInKeys,encoder:typeof t.encoder=="function"?t.encoder:Z.encoder,encodeValuesOnly:typeof t.encodeValuesOnly=="boolean"?t.encodeValuesOnly:Z.encodeValuesOnly,filter:s,format:n,formatter:i,serializeDate:typeof t.serializeDate=="function"?t.serializeDate:Z.serializeDate,skipNulls:typeof t.skipNulls=="boolean"?t.skipNulls:Z.skipNulls,sort:typeof t.sort=="function"?t.sort:null,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:Z.strictNullHandling}}function Qg(t,e={}){let n=t;const i=Xg(e);let s,a;typeof i.filter=="function"?(a=i.filter,n=a("",n)):le(i.filter)&&(a=i.filter,s=a);const l=[];if(typeof n!="object"||n===null)return"";const u=ur[i.arrayFormat],d=u==="comma"&&i.commaRoundTrip;s||(s=Object.keys(n)),i.sort&&s.sort(i.sort);const c=new WeakMap;for(let p=0;p<s.length;++p){const g=s[p];i.skipNulls&&n[g]===null||cr(l,dr(n[g],g,u,d,i.allowEmptyArrays,i.strictNullHandling,i.skipNulls,i.encodeDotInKeys,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset,c))}const m=l.join(i.delimiter);let h=i.addQueryPrefix===!0?"?":"";return i.charsetSentinel&&(i.charset==="iso-8859-1"?h+="utf8=%26%2310003%3B&":h+="utf8=%E2%9C%93&"),m.length>0?h+m:""}function Zg(t){let e=0;for(const s of t)e+=s.length;const n=new Uint8Array(e);let i=0;for(const s of t)n.set(s,i),i+=s.length;return n}let zs;function ii(t){let e;return(zs??(e=new globalThis.TextEncoder,zs=e.encode.bind(e)))(t)}let Xs;function Qs(t){let e;return(Xs??(e=new globalThis.TextDecoder,Xs=e.decode.bind(e)))(t)}var ue,ce;class rn{constructor(){ue.set(this,void 0),ce.set(this,void 0),$(this,ue,new Uint8Array),$(this,ce,null)}decode(e){if(e==null)return[];const n=e instanceof ArrayBuffer?new Uint8Array(e):typeof e=="string"?ii(e):e;$(this,ue,Zg([v(this,ue,"f"),n]));const i=[];let s;for(;(s=jg(v(this,ue,"f"),v(this,ce,"f")))!=null;){if(s.carriage&&v(this,ce,"f")==null){$(this,ce,s.index);continue}if(v(this,ce,"f")!=null&&(s.index!==v(this,ce,"f")+1||s.carriage)){i.push(Qs(v(this,ue,"f").subarray(0,v(this,ce,"f")-1))),$(this,ue,v(this,ue,"f").subarray(v(this,ce,"f"))),$(this,ce,null);continue}const a=v(this,ce,"f")!==null?s.preceding-1:s.preceding,l=Qs(v(this,ue,"f").subarray(0,a));i.push(l),$(this,ue,v(this,ue,"f").subarray(s.index)),$(this,ce,null)}return i}flush(){return v(this,ue,"f").length?this.decode(`
`):[]}}ue=new WeakMap,ce=new WeakMap;rn.NEWLINE_CHARS=new Set([`
`,"\r"]);rn.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function jg(t,e){for(let s=e??0;s<t.length;s++){if(t[s]===10)return{preceding:s,index:s+1,carriage:!1};if(t[s]===13)return{preceding:s,index:s+1,carriage:!0}}return null}function ey(t){for(let i=0;i<t.length-1;i++){if(t[i]===10&&t[i+1]===10||t[i]===13&&t[i+1]===13)return i+2;if(t[i]===13&&t[i+1]===10&&i+3<t.length&&t[i+2]===13&&t[i+3]===10)return i+4}return-1}const Yt={off:0,error:200,warn:300,info:400,debug:500},Zs=(t,e,n)=>{if(t){if(Lg(Yt,t))return t;te(n).warn(`${e} was set to ${JSON.stringify(t)}, expected one of ${JSON.stringify(Object.keys(Yt))}`)}};function st(){}function Et(t,e,n){return!e||Yt[t]>Yt[n]?st:e[t].bind(e)}const ty={error:st,warn:st,info:st,debug:st};let js=new WeakMap;function te(t){const e=t.logger,n=t.logLevel??"off";if(!e)return ty;const i=js.get(e);if(i&&i[0]===n)return i[1];const s={error:Et("error",e,n),warn:Et("warn",e,n),info:Et("info",e,n),debug:Et("debug",e,n)};return js.set(e,[n,s]),s}const Pe=t=>(t.options&&(t.options={...t.options},delete t.options.headers),t.headers&&(t.headers=Object.fromEntries((t.headers instanceof Headers?[...t.headers]:Object.entries(t.headers)).map(([e,n])=>[e,e.toLowerCase()==="authorization"||e.toLowerCase()==="cookie"||e.toLowerCase()==="set-cookie"?"***":n]))),"retryOfRequestLogID"in t&&(t.retryOfRequestLogID&&(t.retryOf=t.retryOfRequestLogID),delete t.retryOfRequestLogID),t);var nt;class ve{constructor(e,n,i){this.iterator=e,nt.set(this,void 0),this.controller=n,$(this,nt,i)}static fromSSEResponse(e,n,i){let s=!1;const a=i?te(i):console;async function*l(){if(s)throw new V("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let u=!1;try{for await(const d of ny(e,n))if(!u){if(d.data.startsWith("[DONE]")){u=!0;continue}if(d.event===null||d.event.startsWith("response.")||d.event.startsWith("image_edit.")||d.event.startsWith("image_generation.")||d.event.startsWith("transcript.")){let c;try{c=JSON.parse(d.data)}catch(m){throw a.error("Could not parse message into JSON:",d.data),a.error("From chunk:",d.raw),m}if(c&&c.error)throw new ie(void 0,c.error,void 0,e.headers);yield c}else{let c;try{c=JSON.parse(d.data)}catch(m){throw console.error("Could not parse message into JSON:",d.data),console.error("From chunk:",d.raw),m}if(d.event=="error")throw new ie(void 0,c.error,c.message,void 0);yield{event:d.event,data:c}}}u=!0}catch(d){if(Un(d))return;throw d}finally{u||n.abort()}}return new ve(l,n,i)}static fromReadableStream(e,n,i){let s=!1;async function*a(){const u=new rn,d=rr(e);for await(const c of d)for(const m of u.decode(c))yield m;for(const c of u.flush())yield c}async function*l(){if(s)throw new V("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let u=!1;try{for await(const d of a())u||d&&(yield JSON.parse(d));u=!0}catch(d){if(Un(d))return;throw d}finally{u||n.abort()}}return new ve(l,n,i)}[(nt=new WeakMap,Symbol.asyncIterator)](){return this.iterator()}tee(){const e=[],n=[],i=this.iterator(),s=a=>({next:()=>{if(a.length===0){const l=i.next();e.push(l),n.push(l)}return a.shift()}});return[new ve(()=>s(e),this.controller,v(this,nt,"f")),new ve(()=>s(n),this.controller,v(this,nt,"f"))]}toReadableStream(){const e=this;let n;return sr({async start(){n=e[Symbol.asyncIterator]()},async pull(i){try{const{value:s,done:a}=await n.next();if(a)return i.close();const l=ii(JSON.stringify(s)+`
`);i.enqueue(l)}catch(s){i.error(s)}},async cancel(){var i;await((i=n.return)==null?void 0:i.call(n))}})}}async function*ny(t,e){if(!t.body)throw e.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new V("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new V("Attempted to iterate over a response with no body");const n=new sy,i=new rn,s=rr(t.body);for await(const a of iy(s))for(const l of i.decode(a)){const u=n.decode(l);u&&(yield u)}for(const a of i.flush()){const l=n.decode(a);l&&(yield l)}}async function*iy(t){let e=new Uint8Array;for await(const n of t){if(n==null)continue;const i=n instanceof ArrayBuffer?new Uint8Array(n):typeof n=="string"?ii(n):n;let s=new Uint8Array(e.length+i.length);s.set(e),s.set(i,e.length),e=s;let a;for(;(a=ey(e))!==-1;)yield e.slice(0,a),e=e.slice(a)}e.length>0&&(yield e)}class sy{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;const a={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],a}if(this.chunks.push(e),e.startsWith(":"))return null;let[n,i,s]=oy(e,":");return s.startsWith(" ")&&(s=s.substring(1)),n==="event"?this.event=s:n==="data"&&this.data.push(s),null}}function oy(t,e){const n=t.indexOf(e);return n!==-1?[t.substring(0,n),e,t.substring(n+e.length)]:[t,"",""]}async function fr(t,e){const{response:n,requestLogID:i,retryOfRequestLogID:s,startTime:a}=e,l=await(async()=>{var h;if(e.options.stream)return te(t).debug("response",n.status,n.url,n.headers,n.body),e.options.__streamClass?e.options.__streamClass.fromSSEResponse(n,e.controller,t):ve.fromSSEResponse(n,e.controller,t);if(n.status===204)return null;if(e.options.__binaryResponse)return n;const u=n.headers.get("content-type"),d=(h=u==null?void 0:u.split(";")[0])==null?void 0:h.trim();if((d==null?void 0:d.includes("application/json"))||(d==null?void 0:d.endsWith("+json"))){const p=await n.json();return mr(p,n)}return await n.text()})();return te(t).debug(`[${i}] response parsed`,Pe({retryOfRequestLogID:s,url:n.url,status:n.status,body:l,durationMs:Date.now()-a})),l}function mr(t,e){return!t||typeof t!="object"||Array.isArray(t)?t:Object.defineProperty(t,"_request_id",{value:e.headers.get("x-request-id"),enumerable:!1})}var ot;class an extends Promise{constructor(e,n,i=fr){super(s=>{s(null)}),this.responsePromise=n,this.parseResponse=i,ot.set(this,void 0),$(this,ot,e)}_thenUnwrap(e){return new an(v(this,ot,"f"),this.responsePromise,async(n,i)=>mr(e(await this.parseResponse(n,i),i),i.response))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){const[e,n]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:n,request_id:n.headers.get("x-request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(e=>this.parseResponse(v(this,ot,"f"),e))),this.parsedPromise}then(e,n){return this.parse().then(e,n)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}ot=new WeakMap;var It;class hr{constructor(e,n,i,s){It.set(this,void 0),$(this,It,e),this.options=s,this.response=n,this.body=i}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){const e=this.nextPageRequestOptions();if(!e)throw new V("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await v(this,It,"f").requestAPIList(this.constructor,e)}async*iterPages(){let e=this;for(yield e;e.hasNextPage();)e=await e.getNextPage(),yield e}async*[(It=new WeakMap,Symbol.asyncIterator)](){for await(const e of this.iterPages())for(const n of e.getPaginatedItems())yield n}}class ry extends an{constructor(e,n,i){super(e,n,async(s,a)=>new i(s,a.response,await fr(s,a),a.options))}async*[Symbol.asyncIterator](){const e=await this;for await(const n of e)yield n}}class ln extends hr{constructor(e,n,i,s){super(e,n,i,s),this.data=i.data||[],this.object=i.object}getPaginatedItems(){return this.data??[]}nextPageRequestOptions(){return null}}class Y extends hr{constructor(e,n,i,s){super(e,n,i,s),this.data=i.data||[],this.has_more=i.has_more||!1}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var i;const e=this.getPaginatedItems(),n=(i=e[e.length-1])==null?void 0:i.id;return n?{...this.options,query:{...kg(this.options.query),after:n}}:null}}const pr=()=>{var t;if(typeof File>"u"){const{process:e}=globalThis,n=typeof((t=e==null?void 0:e.versions)==null?void 0:t.node)=="string"&&parseInt(e.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(n?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function mt(t,e,n){return pr(),new File(t,e??"unknown_file",n)}function Ut(t){return(typeof t=="object"&&t!==null&&("name"in t&&t.name&&String(t.name)||"url"in t&&t.url&&String(t.url)||"filename"in t&&t.filename&&String(t.filename)||"path"in t&&t.path&&String(t.path))||"").split(/[\\/]/).pop()||void 0}const gr=t=>t!=null&&typeof t=="object"&&typeof t[Symbol.asyncIterator]=="function",Fe=async(t,e)=>({...t,body:await ly(t.body,e)}),eo=new WeakMap;function ay(t){const e=typeof t=="function"?t:t.fetch,n=eo.get(e);if(n)return n;const i=(async()=>{try{const s="Response"in e?e.Response:(await e("data:,")).constructor,a=new FormData;return a.toString()!==await new s(a).text()}catch{return!0}})();return eo.set(e,i),i}const ly=async(t,e)=>{if(!await ay(e))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");const n=new FormData;return await Promise.all(Object.entries(t||{}).map(([i,s])=>Bn(n,i,s))),n},uy=t=>t instanceof Blob&&"name"in t,Bn=async(t,e,n)=>{if(n!==void 0){if(n==null)throw new TypeError(`Received null for "${e}"; to pass null in FormData, you must use the string 'null'`);if(typeof n=="string"||typeof n=="number"||typeof n=="boolean")t.append(e,String(n));else if(n instanceof Response)t.append(e,mt([await n.blob()],Ut(n)));else if(gr(n))t.append(e,mt([await new Response(or(n)).blob()],Ut(n)));else if(uy(n))t.append(e,n,Ut(n));else if(Array.isArray(n))await Promise.all(n.map(i=>Bn(t,e+"[]",i)));else if(typeof n=="object")await Promise.all(Object.entries(n).map(([i,s])=>Bn(t,`${e}[${i}]`,s)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${n} instead`)}},yr=t=>t!=null&&typeof t=="object"&&typeof t.size=="number"&&typeof t.type=="string"&&typeof t.text=="function"&&typeof t.slice=="function"&&typeof t.arrayBuffer=="function",cy=t=>t!=null&&typeof t=="object"&&typeof t.name=="string"&&typeof t.lastModified=="number"&&yr(t),dy=t=>t!=null&&typeof t=="object"&&typeof t.url=="string"&&typeof t.blob=="function";async function fy(t,e,n){if(pr(),t=await t,cy(t))return t instanceof File?t:mt([await t.arrayBuffer()],t.name);if(dy(t)){const s=await t.blob();return e||(e=new URL(t.url).pathname.split(/[\\/]/).pop()),mt(await qn(s),e,n)}const i=await qn(t);if(e||(e=Ut(t)),!(n!=null&&n.type)){const s=i.find(a=>typeof a=="object"&&"type"in a&&a.type);typeof s=="string"&&(n={...n,type:s})}return mt(i,e,n)}async function qn(t){var n;let e=[];if(typeof t=="string"||ArrayBuffer.isView(t)||t instanceof ArrayBuffer)e.push(t);else if(yr(t))e.push(t instanceof Blob?t:await t.arrayBuffer());else if(gr(t))for await(const i of t)e.push(...await qn(i));else{const i=(n=t==null?void 0:t.constructor)==null?void 0:n.name;throw new Error(`Unexpected data type: ${typeof t}${i?`; constructor: ${i}`:""}${my(t)}`)}return e}function my(t){return typeof t!="object"||t===null?"":`; props: [${Object.getOwnPropertyNames(t).map(n=>`"${n}"`).join(", ")}]`}class B{constructor(e){this._client=e}}function xr(t){return t.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}const to=Object.freeze(Object.create(null)),hy=(t=xr)=>function(n,...i){if(n.length===1)return n[0];let s=!1;const a=[],l=n.reduce((m,h,p)=>{var x;/[?#]/.test(h)&&(s=!0);const g=i[p];let y=(s?encodeURIComponent:t)(""+g);return p!==i.length&&(g==null||typeof g=="object"&&g.toString===((x=Object.getPrototypeOf(Object.getPrototypeOf(g.hasOwnProperty??to)??to))==null?void 0:x.toString))&&(y=g+"",a.push({start:m.length+h.length,length:y.length,error:`Value of type ${Object.prototype.toString.call(g).slice(8,-1)} is not a valid path parameter`})),m+h+(p===i.length?"":y)},""),u=l.split(/[?#]/,1)[0],d=new RegExp("(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)","gi");let c;for(;(c=d.exec(u))!==null;)a.push({start:c.index,length:c[0].length,error:`Value "${c[0]}" can't be safely passed as a path parameter`});if(a.sort((m,h)=>m.start-h.start),a.length>0){let m=0;const h=a.reduce((p,g)=>{const y=" ".repeat(g.start-m),x="^".repeat(g.length);return m=g.start+g.length,p+y+x},"");throw new V(`Path parameters result in path with invalid segments:
${a.map(p=>p.error).join(`
`)}
${l}
${h}`)}return l},M=hy(xr);let vr=class extends B{list(e,n={},i){return this._client.getAPIList(M`/chat/completions/${e}/messages`,Y,{query:n,...i})}};function py(t){return typeof t.parse=="function"}const zt=t=>(t==null?void 0:t.role)==="assistant",Cr=t=>(t==null?void 0:t.role)==="tool";var Gn,$t,Vt,rt,at,Bt,lt,_e,ut,Xt,Qt,Je,_r;class si{constructor(){Gn.add(this),this.controller=new AbortController,$t.set(this,void 0),Vt.set(this,()=>{}),rt.set(this,()=>{}),at.set(this,void 0),Bt.set(this,()=>{}),lt.set(this,()=>{}),_e.set(this,{}),ut.set(this,!1),Xt.set(this,!1),Qt.set(this,!1),Je.set(this,!1),$(this,$t,new Promise((e,n)=>{$(this,Vt,e,"f"),$(this,rt,n,"f")})),$(this,at,new Promise((e,n)=>{$(this,Bt,e,"f"),$(this,lt,n,"f")})),v(this,$t,"f").catch(()=>{}),v(this,at,"f").catch(()=>{})}_run(e){setTimeout(()=>{e().then(()=>{this._emitFinal(),this._emit("end")},v(this,Gn,"m",_r).bind(this))},0)}_connected(){this.ended||(v(this,Vt,"f").call(this),this._emit("connect"))}get ended(){return v(this,ut,"f")}get errored(){return v(this,Xt,"f")}get aborted(){return v(this,Qt,"f")}abort(){this.controller.abort()}on(e,n){return(v(this,_e,"f")[e]||(v(this,_e,"f")[e]=[])).push({listener:n}),this}off(e,n){const i=v(this,_e,"f")[e];if(!i)return this;const s=i.findIndex(a=>a.listener===n);return s>=0&&i.splice(s,1),this}once(e,n){return(v(this,_e,"f")[e]||(v(this,_e,"f")[e]=[])).push({listener:n,once:!0}),this}emitted(e){return new Promise((n,i)=>{$(this,Je,!0),e!=="error"&&this.once("error",i),this.once(e,n)})}async done(){$(this,Je,!0),await v(this,at,"f")}_emit(e,...n){if(v(this,ut,"f"))return;e==="end"&&($(this,ut,!0),v(this,Bt,"f").call(this));const i=v(this,_e,"f")[e];if(i&&(v(this,_e,"f")[e]=i.filter(s=>!s.once),i.forEach(({listener:s})=>s(...n))),e==="abort"){const s=n[0];!v(this,Je,"f")&&!(i!=null&&i.length)&&Promise.reject(s),v(this,rt,"f").call(this,s),v(this,lt,"f").call(this,s),this._emit("end");return}if(e==="error"){const s=n[0];!v(this,Je,"f")&&!(i!=null&&i.length)&&Promise.reject(s),v(this,rt,"f").call(this,s),v(this,lt,"f").call(this,s),this._emit("end")}}_emitFinal(){}}$t=new WeakMap,Vt=new WeakMap,rt=new WeakMap,at=new WeakMap,Bt=new WeakMap,lt=new WeakMap,_e=new WeakMap,ut=new WeakMap,Xt=new WeakMap,Qt=new WeakMap,Je=new WeakMap,Gn=new WeakSet,_r=function(e){if($(this,Xt,!0),e instanceof Error&&e.name==="AbortError"&&(e=new me),e instanceof me)return $(this,Qt,!0),this._emit("abort",e);if(e instanceof V)return this._emit("error",e);if(e instanceof Error){const n=new V(e.message);return n.cause=e,this._emit("error",n)}return this._emit("error",new V(String(e)))};function oi(t){return(t==null?void 0:t.$brand)==="auto-parseable-response-format"}function vt(t){return(t==null?void 0:t.$brand)==="auto-parseable-tool"}function gy(t,e){return!e||!Tr(e)?{...t,choices:t.choices.map(n=>({...n,message:{...n.message,parsed:null,...n.message.tool_calls?{tool_calls:n.message.tool_calls}:void 0}}))}:ri(t,e)}function ri(t,e){const n=t.choices.map(i=>{var s;if(i.finish_reason==="length")throw new nr;if(i.finish_reason==="content_filter")throw new ir;return{...i,message:{...i.message,...i.message.tool_calls?{tool_calls:((s=i.message.tool_calls)==null?void 0:s.map(a=>xy(e,a)))??void 0}:void 0,parsed:i.message.content&&!i.message.refusal?yy(e,i.message.content):null}}});return{...t,choices:n}}function yy(t,e){var n,i;return((n=t.response_format)==null?void 0:n.type)!=="json_schema"?null:((i=t.response_format)==null?void 0:i.type)==="json_schema"?"$parseRaw"in t.response_format?t.response_format.$parseRaw(e):JSON.parse(e):null}function xy(t,e){var i;const n=(i=t.tools)==null?void 0:i.find(s=>{var a;return((a=s.function)==null?void 0:a.name)===e.function.name});return{...e,function:{...e.function,parsed_arguments:vt(n)?n.$parseRaw(e.function.arguments):n!=null&&n.function.strict?JSON.parse(e.function.arguments):null}}}function vy(t,e){var i;if(!t)return!1;const n=(i=t.tools)==null?void 0:i.find(s=>{var a;return((a=s.function)==null?void 0:a.name)===e.function.name});return vt(n)||(n==null?void 0:n.function.strict)||!1}function Tr(t){var e;return oi(t.response_format)?!0:((e=t.tools)==null?void 0:e.some(n=>vt(n)||n.type==="function"&&n.function.strict===!0))??!1}function Cy(t){for(const e of t??[]){if(e.type!=="function")throw new V(`Currently only \`function\` tool types support auto-parsing; Received \`${e.type}\``);if(e.function.strict!==!0)throw new V(`The \`${e.function.name}\` tool is not marked with \`strict: true\`. Only strict function tools can be auto-parsed`)}}var ae,Jn,Zt,On,Hn,Wn,Sr,wr;const _y=10;class Ar extends si{constructor(){super(...arguments),ae.add(this),this._chatCompletions=[],this.messages=[]}_addChatCompletion(e){var i;this._chatCompletions.push(e),this._emit("chatCompletion",e);const n=(i=e.choices[0])==null?void 0:i.message;return n&&this._addMessage(n),e}_addMessage(e,n=!0){if("content"in e||(e.content=null),this.messages.push(e),n){if(this._emit("message",e),Cr(e)&&e.content)this._emit("functionToolCallResult",e.content);else if(zt(e)&&e.tool_calls)for(const i of e.tool_calls)i.type==="function"&&this._emit("functionToolCall",i.function)}}async finalChatCompletion(){await this.done();const e=this._chatCompletions[this._chatCompletions.length-1];if(!e)throw new V("stream ended without producing a ChatCompletion");return e}async finalContent(){return await this.done(),v(this,ae,"m",Jn).call(this)}async finalMessage(){return await this.done(),v(this,ae,"m",Zt).call(this)}async finalFunctionToolCall(){return await this.done(),v(this,ae,"m",On).call(this)}async finalFunctionToolCallResult(){return await this.done(),v(this,ae,"m",Hn).call(this)}async totalUsage(){return await this.done(),v(this,ae,"m",Wn).call(this)}allChatCompletions(){return[...this._chatCompletions]}_emitFinal(){const e=this._chatCompletions[this._chatCompletions.length-1];e&&this._emit("finalChatCompletion",e);const n=v(this,ae,"m",Zt).call(this);n&&this._emit("finalMessage",n);const i=v(this,ae,"m",Jn).call(this);i&&this._emit("finalContent",i);const s=v(this,ae,"m",On).call(this);s&&this._emit("finalFunctionToolCall",s);const a=v(this,ae,"m",Hn).call(this);a!=null&&this._emit("finalFunctionToolCallResult",a),this._chatCompletions.some(l=>l.usage)&&this._emit("totalUsage",v(this,ae,"m",Wn).call(this))}async _createChatCompletion(e,n,i){const s=i==null?void 0:i.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),v(this,ae,"m",Sr).call(this,n);const a=await e.chat.completions.create({...n,stream:!1},{...i,signal:this.controller.signal});return this._connected(),this._addChatCompletion(ri(a,n))}async _runChatCompletion(e,n,i){for(const s of n.messages)this._addMessage(s,!1);return await this._createChatCompletion(e,n,i)}async _runTools(e,n,i){var g,y,x;const s="tool",{tool_choice:a="auto",stream:l,...u}=n,d=typeof a!="string"&&((g=a==null?void 0:a.function)==null?void 0:g.name),{maxChatCompletions:c=_y}=i||{},m=n.tools.map(T=>{if(vt(T)){if(!T.$callback)throw new V("Tool given to `.runTools()` that does not have an associated function");return{type:"function",function:{function:T.$callback,name:T.function.name,description:T.function.description||"",parameters:T.function.parameters,parse:T.$parseRaw,strict:!0}}}return T}),h={};for(const T of m)T.type==="function"&&(h[T.function.name||T.function.function.name]=T.function);const p="tools"in n?m.map(T=>T.type==="function"?{type:"function",function:{name:T.function.name||T.function.function.name,parameters:T.function.parameters,description:T.function.description,strict:T.function.strict}}:T):void 0;for(const T of n.messages)this._addMessage(T,!1);for(let T=0;T<c;++T){const w=(y=(await this._createChatCompletion(e,{...u,tool_choice:a,tools:p,messages:[...this.messages]},i)).choices[0])==null?void 0:y.message;if(!w)throw new V("missing message in ChatCompletion response");if(!((x=w.tool_calls)!=null&&x.length))return;for(const A of w.tool_calls){if(A.type!=="function")continue;const _=A.id,{name:C,arguments:E}=A.function,N=h[C];if(N){if(d&&d!==C){const R=`Invalid tool_call: ${JSON.stringify(C)}. ${JSON.stringify(d)} requested. Please try again`;this._addMessage({role:s,tool_call_id:_,content:R});continue}}else{const R=`Invalid tool_call: ${JSON.stringify(C)}. Available options are: ${Object.keys(h).map(K=>JSON.stringify(K)).join(", ")}. Please try again`;this._addMessage({role:s,tool_call_id:_,content:R});continue}let D;try{D=py(N)?await N.parse(E):E}catch(R){const K=R instanceof Error?R.message:String(R);this._addMessage({role:s,tool_call_id:_,content:K});continue}const b=await N.function(D,this),k=v(this,ae,"m",wr).call(this,b);if(this._addMessage({role:s,tool_call_id:_,content:k}),d)return}}}}ae=new WeakSet,Jn=function(){return v(this,ae,"m",Zt).call(this).content??null},Zt=function(){let e=this.messages.length;for(;e-- >0;){const n=this.messages[e];if(zt(n))return{...n,content:n.content??null,refusal:n.refusal??null}}throw new V("stream ended without producing a ChatCompletionMessage with role=assistant")},On=function(){var e,n;for(let i=this.messages.length-1;i>=0;i--){const s=this.messages[i];if(zt(s)&&((e=s==null?void 0:s.tool_calls)!=null&&e.length))return(n=s.tool_calls.at(-1))==null?void 0:n.function}},Hn=function(){for(let e=this.messages.length-1;e>=0;e--){const n=this.messages[e];if(Cr(n)&&n.content!=null&&typeof n.content=="string"&&this.messages.some(i=>{var s;return i.role==="assistant"&&((s=i.tool_calls)==null?void 0:s.some(a=>a.type==="function"&&a.id===n.tool_call_id))}))return n.content}},Wn=function(){const e={completion_tokens:0,prompt_tokens:0,total_tokens:0};for(const{usage:n}of this._chatCompletions)n&&(e.completion_tokens+=n.completion_tokens,e.prompt_tokens+=n.prompt_tokens,e.total_tokens+=n.total_tokens);return e},Sr=function(e){if(e.n!=null&&e.n>1)throw new V("ChatCompletion convenience helpers only support n=1 at this time. To use n>1, please use chat.completions.create() directly.")},wr=function(e){return typeof e=="string"?e:e===void 0?"undefined":JSON.stringify(e)};class ai extends Ar{static runTools(e,n,i){const s=new ai,a={...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,n,a)),s}_addMessage(e,n=!0){super._addMessage(e,n),zt(e)&&e.content&&this._emit("content",e.content)}}const Er=1,Ir=2,Nr=4,br=8,Mr=16,Pr=32,Rr=64,kr=128,Dr=256,Lr=kr|Dr,Fr=Mr|Pr|Lr|Rr,Ur=Er|Ir|Fr,$r=Nr|br,Ty=Ur|$r,j={STR:Er,NUM:Ir,ARR:Nr,OBJ:br,NULL:Mr,BOOL:Pr,NAN:Rr,INFINITY:kr,MINUS_INFINITY:Dr,INF:Lr,SPECIAL:Fr,ATOM:Ur,COLLECTION:$r,ALL:Ty};class Sy extends Error{}class wy extends Error{}function Ay(t,e=j.ALL){if(typeof t!="string")throw new TypeError(`expecting str, got ${typeof t}`);if(!t.trim())throw new Error(`${t} is empty`);return Ey(t.trim(),e)}const Ey=(t,e)=>{const n=t.length;let i=0;const s=p=>{throw new Sy(`${p} at position ${i}`)},a=p=>{throw new wy(`${p} at position ${i}`)},l=()=>(h(),i>=n&&s("Unexpected end of input"),t[i]==='"'?u():t[i]==="{"?d():t[i]==="["?c():t.substring(i,i+4)==="null"||j.NULL&e&&n-i<4&&"null".startsWith(t.substring(i))?(i+=4,null):t.substring(i,i+4)==="true"||j.BOOL&e&&n-i<4&&"true".startsWith(t.substring(i))?(i+=4,!0):t.substring(i,i+5)==="false"||j.BOOL&e&&n-i<5&&"false".startsWith(t.substring(i))?(i+=5,!1):t.substring(i,i+8)==="Infinity"||j.INFINITY&e&&n-i<8&&"Infinity".startsWith(t.substring(i))?(i+=8,1/0):t.substring(i,i+9)==="-Infinity"||j.MINUS_INFINITY&e&&1<n-i&&n-i<9&&"-Infinity".startsWith(t.substring(i))?(i+=9,-1/0):t.substring(i,i+3)==="NaN"||j.NAN&e&&n-i<3&&"NaN".startsWith(t.substring(i))?(i+=3,NaN):m()),u=()=>{const p=i;let g=!1;for(i++;i<n&&(t[i]!=='"'||g&&t[i-1]==="\\");)g=t[i]==="\\"?!g:!1,i++;if(t.charAt(i)=='"')try{return JSON.parse(t.substring(p,++i-Number(g)))}catch(y){a(String(y))}else if(j.STR&e)try{return JSON.parse(t.substring(p,i-Number(g))+'"')}catch{return JSON.parse(t.substring(p,t.lastIndexOf("\\"))+'"')}s("Unterminated string literal")},d=()=>{i++,h();const p={};try{for(;t[i]!=="}";){if(h(),i>=n&&j.OBJ&e)return p;const g=u();h(),i++;try{const y=l();Object.defineProperty(p,g,{value:y,writable:!0,enumerable:!0,configurable:!0})}catch(y){if(j.OBJ&e)return p;throw y}h(),t[i]===","&&i++}}catch{if(j.OBJ&e)return p;s("Expected '}' at end of object")}return i++,p},c=()=>{i++;const p=[];try{for(;t[i]!=="]";)p.push(l()),h(),t[i]===","&&i++}catch{if(j.ARR&e)return p;s("Expected ']' at end of array")}return i++,p},m=()=>{if(i===0){t==="-"&&j.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(t)}catch(g){if(j.NUM&e)try{return t[t.length-1]==="."?JSON.parse(t.substring(0,t.lastIndexOf("."))):JSON.parse(t.substring(0,t.lastIndexOf("e")))}catch{}a(String(g))}}const p=i;for(t[i]==="-"&&i++;t[i]&&!",]}".includes(t[i]);)i++;i==n&&!(j.NUM&e)&&s("Unterminated number literal");try{return JSON.parse(t.substring(p,i))}catch{t.substring(p,i)==="-"&&j.NUM&e&&s("Not sure what '-' is");try{return JSON.parse(t.substring(p,t.lastIndexOf("e")))}catch(y){a(String(y))}}},h=()=>{for(;i<n&&` 
\r	`.includes(t[i]);)i++};return l()},no=t=>Ay(t,j.ALL^j.NUM);var Q,Ce,$e,Ee,xn,Nt,vn,Cn,_n,bt,Tn,io;class gt extends Ar{constructor(e){super(),Q.add(this),Ce.set(this,void 0),$e.set(this,void 0),Ee.set(this,void 0),$(this,Ce,e),$(this,$e,[])}get currentChatCompletionSnapshot(){return v(this,Ee,"f")}static fromReadableStream(e){const n=new gt(null);return n._run(()=>n._fromReadableStream(e)),n}static createChatCompletion(e,n,i){const s=new gt(n);return s._run(()=>s._runChatCompletion(e,{...n,stream:!0},{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createChatCompletion(e,n,i){var l;super._createChatCompletion;const s=i==null?void 0:i.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),v(this,Q,"m",xn).call(this);const a=await e.chat.completions.create({...n,stream:!0},{...i,signal:this.controller.signal});this._connected();for await(const u of a)v(this,Q,"m",vn).call(this,u);if((l=a.controller.signal)!=null&&l.aborted)throw new me;return this._addChatCompletion(v(this,Q,"m",bt).call(this))}async _fromReadableStream(e,n){var l;const i=n==null?void 0:n.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort())),v(this,Q,"m",xn).call(this),this._connected();const s=ve.fromReadableStream(e,this.controller);let a;for await(const u of s)a&&a!==u.id&&this._addChatCompletion(v(this,Q,"m",bt).call(this)),v(this,Q,"m",vn).call(this,u),a=u.id;if((l=s.controller.signal)!=null&&l.aborted)throw new me;return this._addChatCompletion(v(this,Q,"m",bt).call(this))}[(Ce=new WeakMap,$e=new WeakMap,Ee=new WeakMap,Q=new WeakSet,xn=function(){this.ended||$(this,Ee,void 0)},Nt=function(n){let i=v(this,$e,"f")[n.index];return i||(i={content_done:!1,refusal_done:!1,logprobs_content_done:!1,logprobs_refusal_done:!1,done_tool_calls:new Set,current_tool_call_index:null},v(this,$e,"f")[n.index]=i,i)},vn=function(n){var s,a,l,u,d,c,m,h,p,g,y,x,T,I,w;if(this.ended)return;const i=v(this,Q,"m",io).call(this,n);this._emit("chunk",n,i);for(const A of n.choices){const _=i.choices[A.index];A.delta.content!=null&&((s=_.message)==null?void 0:s.role)==="assistant"&&((a=_.message)!=null&&a.content)&&(this._emit("content",A.delta.content,_.message.content),this._emit("content.delta",{delta:A.delta.content,snapshot:_.message.content,parsed:_.message.parsed})),A.delta.refusal!=null&&((l=_.message)==null?void 0:l.role)==="assistant"&&((u=_.message)!=null&&u.refusal)&&this._emit("refusal.delta",{delta:A.delta.refusal,snapshot:_.message.refusal}),((d=A.logprobs)==null?void 0:d.content)!=null&&((c=_.message)==null?void 0:c.role)==="assistant"&&this._emit("logprobs.content.delta",{content:(m=A.logprobs)==null?void 0:m.content,snapshot:((h=_.logprobs)==null?void 0:h.content)??[]}),((p=A.logprobs)==null?void 0:p.refusal)!=null&&((g=_.message)==null?void 0:g.role)==="assistant"&&this._emit("logprobs.refusal.delta",{refusal:(y=A.logprobs)==null?void 0:y.refusal,snapshot:((x=_.logprobs)==null?void 0:x.refusal)??[]});const C=v(this,Q,"m",Nt).call(this,_);_.finish_reason&&(v(this,Q,"m",_n).call(this,_),C.current_tool_call_index!=null&&v(this,Q,"m",Cn).call(this,_,C.current_tool_call_index));for(const E of A.delta.tool_calls??[])C.current_tool_call_index!==E.index&&(v(this,Q,"m",_n).call(this,_),C.current_tool_call_index!=null&&v(this,Q,"m",Cn).call(this,_,C.current_tool_call_index)),C.current_tool_call_index=E.index;for(const E of A.delta.tool_calls??[]){const N=(T=_.message.tool_calls)==null?void 0:T[E.index];N!=null&&N.type&&((N==null?void 0:N.type)==="function"?this._emit("tool_calls.function.arguments.delta",{name:(I=N.function)==null?void 0:I.name,index:E.index,arguments:N.function.arguments,parsed_arguments:N.function.parsed_arguments,arguments_delta:((w=E.function)==null?void 0:w.arguments)??""}):(N==null||N.type,void 0))}}},Cn=function(n,i){var l,u,d;if(v(this,Q,"m",Nt).call(this,n).done_tool_calls.has(i))return;const a=(l=n.message.tool_calls)==null?void 0:l[i];if(!a)throw new Error("no tool call snapshot");if(!a.type)throw new Error("tool call snapshot missing `type`");if(a.type==="function"){const c=(d=(u=v(this,Ce,"f"))==null?void 0:u.tools)==null?void 0:d.find(m=>m.type==="function"&&m.function.name===a.function.name);this._emit("tool_calls.function.arguments.done",{name:a.function.name,index:i,arguments:a.function.arguments,parsed_arguments:vt(c)?c.$parseRaw(a.function.arguments):c!=null&&c.function.strict?JSON.parse(a.function.arguments):null})}else a.type},_n=function(n){var s,a;const i=v(this,Q,"m",Nt).call(this,n);if(n.message.content&&!i.content_done){i.content_done=!0;const l=v(this,Q,"m",Tn).call(this);this._emit("content.done",{content:n.message.content,parsed:l?l.$parseRaw(n.message.content):null})}n.message.refusal&&!i.refusal_done&&(i.refusal_done=!0,this._emit("refusal.done",{refusal:n.message.refusal})),(s=n.logprobs)!=null&&s.content&&!i.logprobs_content_done&&(i.logprobs_content_done=!0,this._emit("logprobs.content.done",{content:n.logprobs.content})),(a=n.logprobs)!=null&&a.refusal&&!i.logprobs_refusal_done&&(i.logprobs_refusal_done=!0,this._emit("logprobs.refusal.done",{refusal:n.logprobs.refusal}))},bt=function(){if(this.ended)throw new V("stream has ended, this shouldn't happen");const n=v(this,Ee,"f");if(!n)throw new V("request ended without sending any chunks");return $(this,Ee,void 0),$(this,$e,[]),Iy(n,v(this,Ce,"f"))},Tn=function(){var i;const n=(i=v(this,Ce,"f"))==null?void 0:i.response_format;return oi(n)?n:null},io=function(n){var i,s,a,l;let u=v(this,Ee,"f");const{choices:d,...c}=n;u?Object.assign(u,c):u=$(this,Ee,{...c,choices:[]});for(const{delta:m,finish_reason:h,index:p,logprobs:g=null,...y}of n.choices){let x=u.choices[p];if(x||(x=u.choices[p]={finish_reason:h,index:p,message:{},logprobs:g,...y}),g)if(!x.logprobs)x.logprobs=Object.assign({},g);else{const{content:E,refusal:N,...D}=g;Object.assign(x.logprobs,D),E&&((i=x.logprobs).content??(i.content=[]),x.logprobs.content.push(...E)),N&&((s=x.logprobs).refusal??(s.refusal=[]),x.logprobs.refusal.push(...N))}if(h&&(x.finish_reason=h,v(this,Ce,"f")&&Tr(v(this,Ce,"f")))){if(h==="length")throw new nr;if(h==="content_filter")throw new ir}if(Object.assign(x,y),!m)continue;const{content:T,refusal:I,function_call:w,role:A,tool_calls:_,...C}=m;if(Object.assign(x.message,C),I&&(x.message.refusal=(x.message.refusal||"")+I),A&&(x.message.role=A),w&&(x.message.function_call?(w.name&&(x.message.function_call.name=w.name),w.arguments&&((a=x.message.function_call).arguments??(a.arguments=""),x.message.function_call.arguments+=w.arguments)):x.message.function_call=w),T&&(x.message.content=(x.message.content||"")+T,!x.message.refusal&&v(this,Q,"m",Tn).call(this)&&(x.message.parsed=no(x.message.content))),_){x.message.tool_calls||(x.message.tool_calls=[]);for(const{index:E,id:N,type:D,function:b,...k}of _){const R=(l=x.message.tool_calls)[E]??(l[E]={});Object.assign(R,k),N&&(R.id=N),D&&(R.type=D),b&&(R.function??(R.function={name:b.name??"",arguments:""})),b!=null&&b.name&&(R.function.name=b.name),b!=null&&b.arguments&&(R.function.arguments+=b.arguments,vy(v(this,Ce,"f"),R)&&(R.function.parsed_arguments=no(R.function.arguments)))}}}return u},Symbol.asyncIterator)](){const e=[],n=[];let i=!1;return this.on("chunk",s=>{const a=n.shift();a?a.resolve(s):e.push(s)}),this.on("end",()=>{i=!0;for(const s of n)s.resolve(void 0);n.length=0}),this.on("abort",s=>{i=!0;for(const a of n)a.reject(s);n.length=0}),this.on("error",s=>{i=!0;for(const a of n)a.reject(s);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:i?{value:void 0,done:!0}:new Promise((a,l)=>n.push({resolve:a,reject:l})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new ve(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}function Iy(t,e){const{id:n,choices:i,created:s,model:a,system_fingerprint:l,...u}=t,d={...u,id:n,choices:i.map(({message:c,finish_reason:m,index:h,logprobs:p,...g})=>{if(!m)throw new V(`missing finish_reason for choice ${h}`);const{content:y=null,function_call:x,tool_calls:T,...I}=c,w=c.role;if(!w)throw new V(`missing role for choice ${h}`);if(x){const{arguments:A,name:_}=x;if(A==null)throw new V(`missing function_call.arguments for choice ${h}`);if(!_)throw new V(`missing function_call.name for choice ${h}`);return{...g,message:{content:y,function_call:{arguments:A,name:_},role:w,refusal:c.refusal??null},finish_reason:m,index:h,logprobs:p}}return T?{...g,index:h,finish_reason:m,logprobs:p,message:{...I,role:w,content:y,refusal:c.refusal??null,tool_calls:T.map((A,_)=>{const{function:C,type:E,id:N,...D}=A,{arguments:b,name:k,...R}=C||{};if(N==null)throw new V(`missing choices[${h}].tool_calls[${_}].id
${Mt(t)}`);if(E==null)throw new V(`missing choices[${h}].tool_calls[${_}].type
${Mt(t)}`);if(k==null)throw new V(`missing choices[${h}].tool_calls[${_}].function.name
${Mt(t)}`);if(b==null)throw new V(`missing choices[${h}].tool_calls[${_}].function.arguments
${Mt(t)}`);return{...D,id:N,type:E,function:{...R,name:k,arguments:b}}})}}:{...g,message:{...I,content:y,role:w,refusal:c.refusal??null},finish_reason:m,index:h,logprobs:p}}),created:s,model:a,object:"chat.completion",...l?{system_fingerprint:l}:{}};return gy(d,e)}function Mt(t){return JSON.stringify(t)}class jt extends gt{static fromReadableStream(e){const n=new jt(null);return n._run(()=>n._fromReadableStream(e)),n}static runTools(e,n,i){const s=new jt(n),a={...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"runTools"}};return s._run(()=>s._runTools(e,n,a)),s}}let li=class extends B{constructor(){super(...arguments),this.messages=new vr(this._client)}create(e,n){return this._client.post("/chat/completions",{body:e,...n,stream:e.stream??!1})}retrieve(e,n){return this._client.get(M`/chat/completions/${e}`,n)}update(e,n,i){return this._client.post(M`/chat/completions/${e}`,{body:n,...i})}list(e={},n){return this._client.getAPIList("/chat/completions",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/chat/completions/${e}`,n)}parse(e,n){return Cy(e.tools),this._client.chat.completions.create(e,{...n,headers:{...n==null?void 0:n.headers,"X-Stainless-Helper-Method":"chat.completions.parse"}})._thenUnwrap(i=>ri(i,e))}runTools(e,n){return e.stream?jt.runTools(this._client,e,n):ai.runTools(this._client,e,n)}stream(e,n){return gt.createChatCompletion(this._client,e,n)}};li.Messages=vr;class ui extends B{constructor(){super(...arguments),this.completions=new li(this._client)}}ui.Completions=li;const Vr=Symbol("brand.privateNullableHeaders");function*Ny(t){if(!t)return;if(Vr in t){const{values:i,nulls:s}=t;yield*i.entries();for(const a of s)yield[a,null];return}let e=!1,n;t instanceof Headers?n=t.entries():Gs(t)?n=t:(e=!0,n=Object.entries(t??{}));for(let i of n){const s=i[0];if(typeof s!="string")throw new TypeError("expected header name to be a string");const a=Gs(i[1])?i[1]:[i[1]];let l=!1;for(const u of a)u!==void 0&&(e&&!l&&(l=!0,yield[s,null]),yield[s,u])}}const F=t=>{const e=new Headers,n=new Set;for(const i of t){const s=new Set;for(const[a,l]of Ny(i)){const u=a.toLowerCase();s.has(u)||(e.delete(a),s.add(u)),l===null?(e.delete(a),n.add(u)):(e.append(a,l),n.delete(u))}}return{[Vr]:!0,values:e,nulls:n}};class Br extends B{create(e,n){return this._client.post("/audio/speech",{body:e,...n,headers:F([{Accept:"application/octet-stream"},n==null?void 0:n.headers]),__binaryResponse:!0})}}class qr extends B{create(e,n){return this._client.post("/audio/transcriptions",Fe({body:e,...n,stream:e.stream??!1,__metadata:{model:e.model}},this._client))}}class Gr extends B{create(e,n){return this._client.post("/audio/translations",Fe({body:e,...n,__metadata:{model:e.model}},this._client))}}class Ct extends B{constructor(){super(...arguments),this.transcriptions=new qr(this._client),this.translations=new Gr(this._client),this.speech=new Br(this._client)}}Ct.Transcriptions=qr;Ct.Translations=Gr;Ct.Speech=Br;class Jr extends B{create(e,n){return this._client.post("/batches",{body:e,...n})}retrieve(e,n){return this._client.get(M`/batches/${e}`,n)}list(e={},n){return this._client.getAPIList("/batches",Y,{query:e,...n})}cancel(e,n){return this._client.post(M`/batches/${e}/cancel`,n)}}class Or extends B{create(e,n){return this._client.post("/assistants",{body:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,n){return this._client.get(M`/assistants/${e}`,{...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,n,i){return this._client.post(M`/assistants/${e}`,{body:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(e={},n){return this._client.getAPIList("/assistants",Y,{query:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}delete(e,n){return this._client.delete(M`/assistants/${e}`,{...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}}class Hr extends B{create(e,n){return this._client.post("/realtime/sessions",{body:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}}class Wr extends B{create(e,n){return this._client.post("/realtime/transcription_sessions",{body:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}}class un extends B{constructor(){super(...arguments),this.sessions=new Hr(this._client),this.transcriptionSessions=new Wr(this._client)}}un.Sessions=Hr;un.TranscriptionSessions=Wr;class Kr extends B{create(e,n,i){return this._client.post(M`/threads/${e}/messages`,{body:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(e,n,i){const{thread_id:s}=n;return this._client.get(M`/threads/${s}/messages/${e}`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(e,n,i){const{thread_id:s,...a}=n;return this._client.post(M`/threads/${s}/messages/${e}`,{body:a,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(e,n={},i){return this._client.getAPIList(M`/threads/${e}/messages`,Y,{query:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(e,n,i){const{thread_id:s}=n;return this._client.delete(M`/threads/${s}/messages/${e}`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}class Yr extends B{retrieve(e,n,i){const{thread_id:s,run_id:a,...l}=n;return this._client.get(M`/threads/${s}/runs/${a}/steps/${e}`,{query:l,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(e,n,i){const{thread_id:s,...a}=n;return this._client.getAPIList(M`/threads/${s}/runs/${e}/steps`,Y,{query:a,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}const by=t=>{if(typeof Buffer<"u"){const e=Buffer.from(t,"base64");return Array.from(new Float32Array(e.buffer,e.byteOffset,e.length/Float32Array.BYTES_PER_ELEMENT))}else{const e=atob(t),n=e.length,i=new Uint8Array(n);for(let s=0;s<n;s++)i[s]=e.charCodeAt(s);return Array.from(new Float32Array(i.buffer))}};var Sn={};const Ve=t=>{var e,n,i,s;if(typeof globalThis.process<"u")return((e=Sn==null?void 0:Sn[t])==null?void 0:e.trim())??void 0;if(typeof globalThis.Deno<"u")return(s=(i=(n=globalThis.Deno.env)==null?void 0:n.get)==null?void 0:i.call(n,t))==null?void 0:s.trim()};var ne,ke,Kn,xe,qt,pe,De,We,Re,en,de,Gt,Jt,ht,ct,dt,so,oo,ro,ao,lo,uo,co;class pt extends si{constructor(){super(...arguments),ne.add(this),Kn.set(this,[]),xe.set(this,{}),qt.set(this,{}),pe.set(this,void 0),De.set(this,void 0),We.set(this,void 0),Re.set(this,void 0),en.set(this,void 0),de.set(this,void 0),Gt.set(this,void 0),Jt.set(this,void 0),ht.set(this,void 0)}[(Kn=new WeakMap,xe=new WeakMap,qt=new WeakMap,pe=new WeakMap,De=new WeakMap,We=new WeakMap,Re=new WeakMap,en=new WeakMap,de=new WeakMap,Gt=new WeakMap,Jt=new WeakMap,ht=new WeakMap,ne=new WeakSet,Symbol.asyncIterator)](){const e=[],n=[];let i=!1;return this.on("event",s=>{const a=n.shift();a?a.resolve(s):e.push(s)}),this.on("end",()=>{i=!0;for(const s of n)s.resolve(void 0);n.length=0}),this.on("abort",s=>{i=!0;for(const a of n)a.reject(s);n.length=0}),this.on("error",s=>{i=!0;for(const a of n)a.reject(s);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:i?{value:void 0,done:!0}:new Promise((a,l)=>n.push({resolve:a,reject:l})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}static fromReadableStream(e){const n=new ke;return n._run(()=>n._fromReadableStream(e)),n}async _fromReadableStream(e,n){var a;const i=n==null?void 0:n.signal;i&&(i.aborted&&this.controller.abort(),i.addEventListener("abort",()=>this.controller.abort())),this._connected();const s=ve.fromReadableStream(e,this.controller);for await(const l of s)v(this,ne,"m",ct).call(this,l);if((a=s.controller.signal)!=null&&a.aborted)throw new me;return this._addRun(v(this,ne,"m",dt).call(this))}toReadableStream(){return new ve(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}static createToolAssistantStream(e,n,i,s){const a=new ke;return a._run(()=>a._runToolAssistantStream(e,n,i,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),a}async _createToolAssistantStream(e,n,i,s){var d;const a=s==null?void 0:s.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const l={...i,stream:!0},u=await e.submitToolOutputs(n,l,{...s,signal:this.controller.signal});this._connected();for await(const c of u)v(this,ne,"m",ct).call(this,c);if((d=u.controller.signal)!=null&&d.aborted)throw new me;return this._addRun(v(this,ne,"m",dt).call(this))}static createThreadAssistantStream(e,n,i){const s=new ke;return s._run(()=>s._threadAssistantStream(e,n,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),s}static createAssistantStream(e,n,i,s){const a=new ke;return a._run(()=>a._runAssistantStream(e,n,i,{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),a}currentEvent(){return v(this,Gt,"f")}currentRun(){return v(this,Jt,"f")}currentMessageSnapshot(){return v(this,pe,"f")}currentRunStepSnapshot(){return v(this,ht,"f")}async finalRunSteps(){return await this.done(),Object.values(v(this,xe,"f"))}async finalMessages(){return await this.done(),Object.values(v(this,qt,"f"))}async finalRun(){if(await this.done(),!v(this,De,"f"))throw Error("Final run was not received.");return v(this,De,"f")}async _createThreadAssistantStream(e,n,i){var u;const s=i==null?void 0:i.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort()));const a={...n,stream:!0},l=await e.createAndRun(a,{...i,signal:this.controller.signal});this._connected();for await(const d of l)v(this,ne,"m",ct).call(this,d);if((u=l.controller.signal)!=null&&u.aborted)throw new me;return this._addRun(v(this,ne,"m",dt).call(this))}async _createAssistantStream(e,n,i,s){var d;const a=s==null?void 0:s.signal;a&&(a.aborted&&this.controller.abort(),a.addEventListener("abort",()=>this.controller.abort()));const l={...i,stream:!0},u=await e.create(n,l,{...s,signal:this.controller.signal});this._connected();for await(const c of u)v(this,ne,"m",ct).call(this,c);if((d=u.controller.signal)!=null&&d.aborted)throw new me;return this._addRun(v(this,ne,"m",dt).call(this))}static accumulateDelta(e,n){for(const[i,s]of Object.entries(n)){if(!e.hasOwnProperty(i)){e[i]=s;continue}let a=e[i];if(a==null){e[i]=s;continue}if(i==="index"||i==="type"){e[i]=s;continue}if(typeof a=="string"&&typeof s=="string")a+=s;else if(typeof a=="number"&&typeof s=="number")a+=s;else if(pn(a)&&pn(s))a=this.accumulateDelta(a,s);else if(Array.isArray(a)&&Array.isArray(s)){if(a.every(l=>typeof l=="string"||typeof l=="number")){a.push(...s);continue}for(const l of s){if(!pn(l))throw new Error(`Expected array delta entry to be an object but got: ${l}`);const u=l.index;if(u==null)throw console.error(l),new Error("Expected array delta entry to have an `index` property");if(typeof u!="number")throw new Error(`Expected array delta entry \`index\` property to be a number but got ${u}`);const d=a[u];d==null?a.push(l):a[u]=this.accumulateDelta(d,l)}continue}else throw Error(`Unhandled record type: ${i}, deltaValue: ${s}, accValue: ${a}`);e[i]=a}return e}_addRun(e){return e}async _threadAssistantStream(e,n,i){return await this._createThreadAssistantStream(n,e,i)}async _runAssistantStream(e,n,i,s){return await this._createAssistantStream(n,e,i,s)}async _runToolAssistantStream(e,n,i,s){return await this._createToolAssistantStream(n,e,i,s)}}ke=pt,ct=function(e){if(!this.ended)switch($(this,Gt,e),v(this,ne,"m",ro).call(this,e),e.event){case"thread.created":break;case"thread.run.created":case"thread.run.queued":case"thread.run.in_progress":case"thread.run.requires_action":case"thread.run.completed":case"thread.run.incomplete":case"thread.run.failed":case"thread.run.cancelling":case"thread.run.cancelled":case"thread.run.expired":v(this,ne,"m",co).call(this,e);break;case"thread.run.step.created":case"thread.run.step.in_progress":case"thread.run.step.delta":case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":v(this,ne,"m",oo).call(this,e);break;case"thread.message.created":case"thread.message.in_progress":case"thread.message.delta":case"thread.message.completed":case"thread.message.incomplete":v(this,ne,"m",so).call(this,e);break;case"error":throw new Error("Encountered an error event in event processing - errors should be processed earlier")}},dt=function(){if(this.ended)throw new V("stream has ended, this shouldn't happen");if(!v(this,De,"f"))throw Error("Final run has not been received");return v(this,De,"f")},so=function(e){const[n,i]=v(this,ne,"m",lo).call(this,e,v(this,pe,"f"));$(this,pe,n),v(this,qt,"f")[n.id]=n;for(const s of i){const a=n.content[s.index];(a==null?void 0:a.type)=="text"&&this._emit("textCreated",a.text)}switch(e.event){case"thread.message.created":this._emit("messageCreated",e.data);break;case"thread.message.in_progress":break;case"thread.message.delta":if(this._emit("messageDelta",e.data.delta,n),e.data.delta.content)for(const s of e.data.delta.content){if(s.type=="text"&&s.text){let a=s.text,l=n.content[s.index];if(l&&l.type=="text")this._emit("textDelta",a,l.text);else throw Error("The snapshot associated with this text delta is not text or missing")}if(s.index!=v(this,We,"f")){if(v(this,Re,"f"))switch(v(this,Re,"f").type){case"text":this._emit("textDone",v(this,Re,"f").text,v(this,pe,"f"));break;case"image_file":this._emit("imageFileDone",v(this,Re,"f").image_file,v(this,pe,"f"));break}$(this,We,s.index)}$(this,Re,n.content[s.index])}break;case"thread.message.completed":case"thread.message.incomplete":if(v(this,We,"f")!==void 0){const s=e.data.content[v(this,We,"f")];if(s)switch(s.type){case"image_file":this._emit("imageFileDone",s.image_file,v(this,pe,"f"));break;case"text":this._emit("textDone",s.text,v(this,pe,"f"));break}}v(this,pe,"f")&&this._emit("messageDone",e.data),$(this,pe,void 0)}},oo=function(e){const n=v(this,ne,"m",ao).call(this,e);switch($(this,ht,n),e.event){case"thread.run.step.created":this._emit("runStepCreated",e.data);break;case"thread.run.step.delta":const i=e.data.delta;if(i.step_details&&i.step_details.type=="tool_calls"&&i.step_details.tool_calls&&n.step_details.type=="tool_calls")for(const a of i.step_details.tool_calls)a.index==v(this,en,"f")?this._emit("toolCallDelta",a,n.step_details.tool_calls[a.index]):(v(this,de,"f")&&this._emit("toolCallDone",v(this,de,"f")),$(this,en,a.index),$(this,de,n.step_details.tool_calls[a.index]),v(this,de,"f")&&this._emit("toolCallCreated",v(this,de,"f")));this._emit("runStepDelta",e.data.delta,n);break;case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":$(this,ht,void 0),e.data.step_details.type=="tool_calls"&&v(this,de,"f")&&(this._emit("toolCallDone",v(this,de,"f")),$(this,de,void 0)),this._emit("runStepDone",e.data,n);break}},ro=function(e){v(this,Kn,"f").push(e),this._emit("event",e)},ao=function(e){switch(e.event){case"thread.run.step.created":return v(this,xe,"f")[e.data.id]=e.data,e.data;case"thread.run.step.delta":let n=v(this,xe,"f")[e.data.id];if(!n)throw Error("Received a RunStepDelta before creation of a snapshot");let i=e.data;if(i.delta){const s=ke.accumulateDelta(n,i.delta);v(this,xe,"f")[e.data.id]=s}return v(this,xe,"f")[e.data.id];case"thread.run.step.completed":case"thread.run.step.failed":case"thread.run.step.cancelled":case"thread.run.step.expired":case"thread.run.step.in_progress":v(this,xe,"f")[e.data.id]=e.data;break}if(v(this,xe,"f")[e.data.id])return v(this,xe,"f")[e.data.id];throw new Error("No snapshot available")},lo=function(e,n){let i=[];switch(e.event){case"thread.message.created":return[e.data,i];case"thread.message.delta":if(!n)throw Error("Received a delta with no existing snapshot (there should be one from message creation)");let s=e.data;if(s.delta.content)for(const a of s.delta.content)if(a.index in n.content){let l=n.content[a.index];n.content[a.index]=v(this,ne,"m",uo).call(this,a,l)}else n.content[a.index]=a,i.push(a);return[n,i];case"thread.message.in_progress":case"thread.message.completed":case"thread.message.incomplete":if(n)return[n,i];throw Error("Received thread message event with no existing snapshot")}throw Error("Tried to accumulate a non-message event")},uo=function(e,n){return ke.accumulateDelta(n,e)},co=function(e){switch($(this,Jt,e.data),e.event){case"thread.run.created":break;case"thread.run.queued":break;case"thread.run.in_progress":break;case"thread.run.requires_action":case"thread.run.cancelled":case"thread.run.failed":case"thread.run.completed":case"thread.run.expired":case"thread.run.incomplete":$(this,De,e.data),v(this,de,"f")&&(this._emit("toolCallDone",v(this,de,"f")),$(this,de,void 0));break}};let ci=class extends B{constructor(){super(...arguments),this.steps=new Yr(this._client)}create(e,n,i){const{include:s,...a}=n;return this._client.post(M`/threads/${e}/runs`,{query:{include:s},body:a,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers]),stream:n.stream??!1})}retrieve(e,n,i){const{thread_id:s}=n;return this._client.get(M`/threads/${s}/runs/${e}`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(e,n,i){const{thread_id:s,...a}=n;return this._client.post(M`/threads/${s}/runs/${e}`,{body:a,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(e,n={},i){return this._client.getAPIList(M`/threads/${e}/runs`,Y,{query:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}cancel(e,n,i){const{thread_id:s}=n;return this._client.post(M`/threads/${s}/runs/${e}/cancel`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async createAndPoll(e,n,i){const s=await this.create(e,n,i);return await this.poll(s.id,{thread_id:e},i)}createAndStream(e,n,i){return pt.createAssistantStream(e,this._client.beta.threads.runs,n,i)}async poll(e,n,i){var a;const s=F([i==null?void 0:i.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=i==null?void 0:i.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const{data:l,response:u}=await this.retrieve(e,n,{...i,headers:{...i==null?void 0:i.headers,...s}}).withResponse();switch(l.status){case"queued":case"in_progress":case"cancelling":let d=5e3;if(i!=null&&i.pollIntervalMs)d=i.pollIntervalMs;else{const c=u.headers.get("openai-poll-after-ms");if(c){const m=parseInt(c);isNaN(m)||(d=m)}}await xt(d);break;case"requires_action":case"incomplete":case"cancelled":case"completed":case"failed":case"expired":return l}}}stream(e,n,i){return pt.createAssistantStream(e,this._client.beta.threads.runs,n,i)}submitToolOutputs(e,n,i){const{thread_id:s,...a}=n;return this._client.post(M`/threads/${s}/runs/${e}/submit_tool_outputs`,{body:a,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers]),stream:n.stream??!1})}async submitToolOutputsAndPoll(e,n,i){const s=await this.submitToolOutputs(e,n,i);return await this.poll(s.id,n,i)}submitToolOutputsStream(e,n,i){return pt.createToolAssistantStream(e,this._client.beta.threads.runs,n,i)}};ci.Steps=Yr;class cn extends B{constructor(){super(...arguments),this.runs=new ci(this._client),this.messages=new Kr(this._client)}create(e={},n){return this._client.post("/threads",{body:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,n){return this._client.get(M`/threads/${e}`,{...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,n,i){return this._client.post(M`/threads/${e}`,{body:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(e,n){return this._client.delete(M`/threads/${e}`,{...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}createAndRun(e,n){return this._client.post("/threads/runs",{body:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers]),stream:e.stream??!1})}async createAndRunPoll(e,n){const i=await this.createAndRun(e,n);return await this.runs.poll(i.id,{thread_id:i.thread_id},n)}createAndRunStream(e,n){return pt.createThreadAssistantStream(e,this._client.beta.threads,n)}}cn.Runs=ci;cn.Messages=Kr;class _t extends B{constructor(){super(...arguments),this.realtime=new un(this._client),this.assistants=new Or(this._client),this.threads=new cn(this._client)}}_t.Realtime=un;_t.Assistants=Or;_t.Threads=cn;class zr extends B{create(e,n){return this._client.post("/completions",{body:e,...n,stream:e.stream??!1})}}class Xr extends B{retrieve(e,n,i){const{container_id:s}=n;return this._client.get(M`/containers/${s}/files/${e}/content`,{...i,headers:F([{Accept:"application/binary"},i==null?void 0:i.headers]),__binaryResponse:!0})}}let di=class extends B{constructor(){super(...arguments),this.content=new Xr(this._client)}create(e,n,i){return this._client.post(M`/containers/${e}/files`,Fe({body:n,...i},this._client))}retrieve(e,n,i){const{container_id:s}=n;return this._client.get(M`/containers/${s}/files/${e}`,i)}list(e,n={},i){return this._client.getAPIList(M`/containers/${e}/files`,Y,{query:n,...i})}delete(e,n,i){const{container_id:s}=n;return this._client.delete(M`/containers/${s}/files/${e}`,{...i,headers:F([{Accept:"*/*"},i==null?void 0:i.headers])})}};di.Content=Xr;class fi extends B{constructor(){super(...arguments),this.files=new di(this._client)}create(e,n){return this._client.post("/containers",{body:e,...n})}retrieve(e,n){return this._client.get(M`/containers/${e}`,n)}list(e={},n){return this._client.getAPIList("/containers",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/containers/${e}`,{...n,headers:F([{Accept:"*/*"},n==null?void 0:n.headers])})}}fi.Files=di;class Qr extends B{create(e,n){const i=!!e.encoding_format;let s=i?e.encoding_format:"base64";i&&te(this._client).debug("embeddings/user defined encoding_format:",e.encoding_format);const a=this._client.post("/embeddings",{body:{...e,encoding_format:s},...n});return i?a:(te(this._client).debug("embeddings/decoding base64 embeddings from base64"),a._thenUnwrap(l=>(l&&l.data&&l.data.forEach(u=>{const d=u.embedding;u.embedding=by(d)}),l)))}}class Zr extends B{retrieve(e,n,i){const{eval_id:s,run_id:a}=n;return this._client.get(M`/evals/${s}/runs/${a}/output_items/${e}`,i)}list(e,n,i){const{eval_id:s,...a}=n;return this._client.getAPIList(M`/evals/${s}/runs/${e}/output_items`,Y,{query:a,...i})}}class mi extends B{constructor(){super(...arguments),this.outputItems=new Zr(this._client)}create(e,n,i){return this._client.post(M`/evals/${e}/runs`,{body:n,...i})}retrieve(e,n,i){const{eval_id:s}=n;return this._client.get(M`/evals/${s}/runs/${e}`,i)}list(e,n={},i){return this._client.getAPIList(M`/evals/${e}/runs`,Y,{query:n,...i})}delete(e,n,i){const{eval_id:s}=n;return this._client.delete(M`/evals/${s}/runs/${e}`,i)}cancel(e,n,i){const{eval_id:s}=n;return this._client.post(M`/evals/${s}/runs/${e}`,i)}}mi.OutputItems=Zr;class hi extends B{constructor(){super(...arguments),this.runs=new mi(this._client)}create(e,n){return this._client.post("/evals",{body:e,...n})}retrieve(e,n){return this._client.get(M`/evals/${e}`,n)}update(e,n,i){return this._client.post(M`/evals/${e}`,{body:n,...i})}list(e={},n){return this._client.getAPIList("/evals",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/evals/${e}`,n)}}hi.Runs=mi;let jr=class extends B{create(e,n){return this._client.post("/files",Fe({body:e,...n},this._client))}retrieve(e,n){return this._client.get(M`/files/${e}`,n)}list(e={},n){return this._client.getAPIList("/files",Y,{query:e,...n})}delete(e,n){return this._client.delete(M`/files/${e}`,n)}content(e,n){return this._client.get(M`/files/${e}/content`,{...n,headers:F([{Accept:"application/binary"},n==null?void 0:n.headers]),__binaryResponse:!0})}async waitForProcessing(e,{pollInterval:n=5e3,maxWait:i=30*60*1e3}={}){const s=new Set(["processed","error","deleted"]),a=Date.now();let l=await this.retrieve(e);for(;!l.status||!s.has(l.status);)if(await xt(n),l=await this.retrieve(e),Date.now()-a>i)throw new ni({message:`Giving up on waiting for file ${e} to finish processing after ${i} milliseconds.`});return l}};class ea extends B{}let ta=class extends B{run(e,n){return this._client.post("/fine_tuning/alpha/graders/run",{body:e,...n})}validate(e,n){return this._client.post("/fine_tuning/alpha/graders/validate",{body:e,...n})}};class pi extends B{constructor(){super(...arguments),this.graders=new ta(this._client)}}pi.Graders=ta;class na extends B{create(e,n,i){return this._client.getAPIList(M`/fine_tuning/checkpoints/${e}/permissions`,ln,{body:n,method:"post",...i})}retrieve(e,n={},i){return this._client.get(M`/fine_tuning/checkpoints/${e}/permissions`,{query:n,...i})}delete(e,n,i){const{fine_tuned_model_checkpoint:s}=n;return this._client.delete(M`/fine_tuning/checkpoints/${s}/permissions/${e}`,i)}}let gi=class extends B{constructor(){super(...arguments),this.permissions=new na(this._client)}};gi.Permissions=na;class ia extends B{list(e,n={},i){return this._client.getAPIList(M`/fine_tuning/jobs/${e}/checkpoints`,Y,{query:n,...i})}}class yi extends B{constructor(){super(...arguments),this.checkpoints=new ia(this._client)}create(e,n){return this._client.post("/fine_tuning/jobs",{body:e,...n})}retrieve(e,n){return this._client.get(M`/fine_tuning/jobs/${e}`,n)}list(e={},n){return this._client.getAPIList("/fine_tuning/jobs",Y,{query:e,...n})}cancel(e,n){return this._client.post(M`/fine_tuning/jobs/${e}/cancel`,n)}listEvents(e,n={},i){return this._client.getAPIList(M`/fine_tuning/jobs/${e}/events`,Y,{query:n,...i})}pause(e,n){return this._client.post(M`/fine_tuning/jobs/${e}/pause`,n)}resume(e,n){return this._client.post(M`/fine_tuning/jobs/${e}/resume`,n)}}yi.Checkpoints=ia;class je extends B{constructor(){super(...arguments),this.methods=new ea(this._client),this.jobs=new yi(this._client),this.checkpoints=new gi(this._client),this.alpha=new pi(this._client)}}je.Methods=ea;je.Jobs=yi;je.Checkpoints=gi;je.Alpha=pi;class sa extends B{}class xi extends B{constructor(){super(...arguments),this.graderModels=new sa(this._client)}}xi.GraderModels=sa;class oa extends B{createVariation(e,n){return this._client.post("/images/variations",Fe({body:e,...n},this._client))}edit(e,n){return this._client.post("/images/edits",Fe({body:e,...n,stream:e.stream??!1},this._client))}generate(e,n){return this._client.post("/images/generations",{body:e,...n,stream:e.stream??!1})}}class ra extends B{retrieve(e,n){return this._client.get(M`/models/${e}`,n)}list(e){return this._client.getAPIList("/models",ln,e)}delete(e,n){return this._client.delete(M`/models/${e}`,n)}}class aa extends B{create(e,n){return this._client.post("/moderations",{body:e,...n})}}function My(t,e){return!e||!Ry(e)?{...t,output_parsed:null,output:t.output.map(n=>n.type==="function_call"?{...n,parsed_arguments:null}:n.type==="message"?{...n,content:n.content.map(i=>({...i,parsed:null}))}:n)}:la(t,e)}function la(t,e){const n=t.output.map(s=>{if(s.type==="function_call")return{...s,parsed_arguments:Ly(e,s)};if(s.type==="message"){const a=s.content.map(l=>l.type==="output_text"?{...l,parsed:Py(e,l.text)}:l);return{...s,content:a}}return s}),i=Object.assign({},t,{output:n});return Object.getOwnPropertyDescriptor(t,"output_text")||Yn(i),Object.defineProperty(i,"output_parsed",{enumerable:!0,get(){for(const s of i.output)if(s.type==="message"){for(const a of s.content)if(a.type==="output_text"&&a.parsed!==null)return a.parsed}return null}}),i}function Py(t,e){var n,i,s,a;return((i=(n=t.text)==null?void 0:n.format)==null?void 0:i.type)!=="json_schema"?null:"$parseRaw"in((s=t.text)==null?void 0:s.format)?((a=t.text)==null?void 0:a.format).$parseRaw(e):JSON.parse(e)}function Ry(t){var e;return!!oi((e=t.text)==null?void 0:e.format)}function ky(t){return(t==null?void 0:t.$brand)==="auto-parseable-tool"}function Dy(t,e){return t.find(n=>n.type==="function"&&n.name===e)}function Ly(t,e){const n=Dy(t.tools??[],e.name);return{...e,...e,parsed_arguments:ky(n)?n.$parseRaw(e.arguments):n!=null&&n.strict?JSON.parse(e.arguments):null}}function Yn(t){const e=[];for(const n of t.output)if(n.type==="message")for(const i of n.content)i.type==="output_text"&&e.push(i.text);t.output_text=e.join("")}var Be,Pt,Ie,Rt,fo,mo,ho,po;class vi extends si{constructor(e){super(),Be.add(this),Pt.set(this,void 0),Ie.set(this,void 0),Rt.set(this,void 0),$(this,Pt,e)}static createResponse(e,n,i){const s=new vi(n);return s._run(()=>s._createOrRetrieveResponse(e,n,{...i,headers:{...i==null?void 0:i.headers,"X-Stainless-Helper-Method":"stream"}})),s}async _createOrRetrieveResponse(e,n,i){var u;const s=i==null?void 0:i.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),v(this,Be,"m",fo).call(this);let a,l=null;"response_id"in n?(a=await e.responses.retrieve(n.response_id,{stream:!0},{...i,signal:this.controller.signal,stream:!0}),l=n.starting_after??null):a=await e.responses.create({...n,stream:!0},{...i,signal:this.controller.signal}),this._connected();for await(const d of a)v(this,Be,"m",mo).call(this,d,l);if((u=a.controller.signal)!=null&&u.aborted)throw new me;return v(this,Be,"m",ho).call(this)}[(Pt=new WeakMap,Ie=new WeakMap,Rt=new WeakMap,Be=new WeakSet,fo=function(){this.ended||$(this,Ie,void 0)},mo=function(n,i){if(this.ended)return;const s=(l,u)=>{(i==null||u.sequence_number>i)&&this._emit(l,u)},a=v(this,Be,"m",po).call(this,n);switch(s("event",n),n.type){case"response.output_text.delta":{const l=a.output[n.output_index];if(!l)throw new V(`missing output at index ${n.output_index}`);if(l.type==="message"){const u=l.content[n.content_index];if(!u)throw new V(`missing content at index ${n.content_index}`);if(u.type!=="output_text")throw new V(`expected content to be 'output_text', got ${u.type}`);s("response.output_text.delta",{...n,snapshot:u.text})}break}case"response.function_call_arguments.delta":{const l=a.output[n.output_index];if(!l)throw new V(`missing output at index ${n.output_index}`);l.type==="function_call"&&s("response.function_call_arguments.delta",{...n,snapshot:l.arguments});break}default:s(n.type,n);break}},ho=function(){if(this.ended)throw new V("stream has ended, this shouldn't happen");const n=v(this,Ie,"f");if(!n)throw new V("request ended without sending any events");$(this,Ie,void 0);const i=Fy(n,v(this,Pt,"f"));return $(this,Rt,i),i},po=function(n){let i=v(this,Ie,"f");if(!i){if(n.type!=="response.created")throw new V(`When snapshot hasn't been set yet, expected 'response.created' event, got ${n.type}`);return i=$(this,Ie,n.response),i}switch(n.type){case"response.output_item.added":{i.output.push(n.item);break}case"response.content_part.added":{const s=i.output[n.output_index];if(!s)throw new V(`missing output at index ${n.output_index}`);s.type==="message"&&s.content.push(n.part);break}case"response.output_text.delta":{const s=i.output[n.output_index];if(!s)throw new V(`missing output at index ${n.output_index}`);if(s.type==="message"){const a=s.content[n.content_index];if(!a)throw new V(`missing content at index ${n.content_index}`);if(a.type!=="output_text")throw new V(`expected content to be 'output_text', got ${a.type}`);a.text+=n.delta}break}case"response.function_call_arguments.delta":{const s=i.output[n.output_index];if(!s)throw new V(`missing output at index ${n.output_index}`);s.type==="function_call"&&(s.arguments+=n.delta);break}case"response.completed":{$(this,Ie,n.response);break}}return i},Symbol.asyncIterator)](){const e=[],n=[];let i=!1;return this.on("event",s=>{const a=n.shift();a?a.resolve(s):e.push(s)}),this.on("end",()=>{i=!0;for(const s of n)s.resolve(void 0);n.length=0}),this.on("abort",s=>{i=!0;for(const a of n)a.reject(s);n.length=0}),this.on("error",s=>{i=!0;for(const a of n)a.reject(s);n.length=0}),{next:async()=>e.length?{value:e.shift(),done:!1}:i?{value:void 0,done:!0}:new Promise((a,l)=>n.push({resolve:a,reject:l})).then(a=>a?{value:a,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}async finalResponse(){await this.done();const e=v(this,Rt,"f");if(!e)throw new V("stream ended without producing a ChatCompletion");return e}}function Fy(t,e){return My(t,e)}class ua extends B{list(e,n={},i){return this._client.getAPIList(M`/responses/${e}/input_items`,Y,{query:n,...i})}}class Ci extends B{constructor(){super(...arguments),this.inputItems=new ua(this._client)}create(e,n){return this._client.post("/responses",{body:e,...n,stream:e.stream??!1})._thenUnwrap(i=>("object"in i&&i.object==="response"&&Yn(i),i))}retrieve(e,n={},i){return this._client.get(M`/responses/${e}`,{query:n,...i,stream:(n==null?void 0:n.stream)??!1})._thenUnwrap(s=>("object"in s&&s.object==="response"&&Yn(s),s))}delete(e,n){return this._client.delete(M`/responses/${e}`,{...n,headers:F([{Accept:"*/*"},n==null?void 0:n.headers])})}parse(e,n){return this._client.responses.create(e,n)._thenUnwrap(i=>la(i,e))}stream(e,n){return vi.createResponse(this._client,e,n)}cancel(e,n){return this._client.post(M`/responses/${e}/cancel`,n)}}Ci.InputItems=ua;class ca extends B{create(e,n,i){return this._client.post(M`/uploads/${e}/parts`,Fe({body:n,...i},this._client))}}class _i extends B{constructor(){super(...arguments),this.parts=new ca(this._client)}create(e,n){return this._client.post("/uploads",{body:e,...n})}cancel(e,n){return this._client.post(M`/uploads/${e}/cancel`,n)}complete(e,n,i){return this._client.post(M`/uploads/${e}/complete`,{body:n,...i})}}_i.Parts=ca;const Uy=async t=>{const e=await Promise.allSettled(t),n=e.filter(s=>s.status==="rejected");if(n.length){for(const s of n)console.error(s.reason);throw new Error(`${n.length} promise(s) failed - see the above errors`)}const i=[];for(const s of e)s.status==="fulfilled"&&i.push(s.value);return i};class da extends B{create(e,n,i){return this._client.post(M`/vector_stores/${e}/file_batches`,{body:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(e,n,i){const{vector_store_id:s}=n;return this._client.get(M`/vector_stores/${s}/file_batches/${e}`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}cancel(e,n,i){const{vector_store_id:s}=n;return this._client.post(M`/vector_stores/${s}/file_batches/${e}/cancel`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async createAndPoll(e,n,i){const s=await this.create(e,n);return await this.poll(e,s.id,i)}listFiles(e,n,i){const{vector_store_id:s,...a}=n;return this._client.getAPIList(M`/vector_stores/${s}/file_batches/${e}/files`,Y,{query:a,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async poll(e,n,i){var a;const s=F([i==null?void 0:i.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=i==null?void 0:i.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const{data:l,response:u}=await this.retrieve(n,{vector_store_id:e},{...i,headers:s}).withResponse();switch(l.status){case"in_progress":let d=5e3;if(i!=null&&i.pollIntervalMs)d=i.pollIntervalMs;else{const c=u.headers.get("openai-poll-after-ms");if(c){const m=parseInt(c);isNaN(m)||(d=m)}}await xt(d);break;case"failed":case"cancelled":case"completed":return l}}}async uploadAndPoll(e,{files:n,fileIds:i=[]},s){if(n==null||n.length==0)throw new Error("No `files` provided to process. If you've already uploaded files you should use `.createAndPoll()` instead");const a=(s==null?void 0:s.maxConcurrency)??5,l=Math.min(a,n.length),u=this._client,d=n.values(),c=[...i];async function m(p){for(let g of p){const y=await u.files.create({file:g,purpose:"assistants"},s);c.push(y.id)}}const h=Array(l).fill(d).map(m);return await Uy(h),await this.createAndPoll(e,{file_ids:c})}}class fa extends B{create(e,n,i){return this._client.post(M`/vector_stores/${e}/files`,{body:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}retrieve(e,n,i){const{vector_store_id:s}=n;return this._client.get(M`/vector_stores/${s}/files/${e}`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}update(e,n,i){const{vector_store_id:s,...a}=n;return this._client.post(M`/vector_stores/${s}/files/${e}`,{body:a,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(e,n={},i){return this._client.getAPIList(M`/vector_stores/${e}/files`,Y,{query:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}delete(e,n,i){const{vector_store_id:s}=n;return this._client.delete(M`/vector_stores/${s}/files/${e}`,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}async createAndPoll(e,n,i){const s=await this.create(e,n,i);return await this.poll(e,s.id,i)}async poll(e,n,i){var a;const s=F([i==null?void 0:i.headers,{"X-Stainless-Poll-Helper":"true","X-Stainless-Custom-Poll-Interval":((a=i==null?void 0:i.pollIntervalMs)==null?void 0:a.toString())??void 0}]);for(;;){const l=await this.retrieve(n,{vector_store_id:e},{...i,headers:s}).withResponse(),u=l.data;switch(u.status){case"in_progress":let d=5e3;if(i!=null&&i.pollIntervalMs)d=i.pollIntervalMs;else{const c=l.response.headers.get("openai-poll-after-ms");if(c){const m=parseInt(c);isNaN(m)||(d=m)}}await xt(d);break;case"failed":case"completed":return u}}}async upload(e,n,i){const s=await this._client.files.create({file:n,purpose:"assistants"},i);return this.create(e,{file_id:s.id},i)}async uploadAndPoll(e,n,i){const s=await this.upload(e,n,i);return await this.poll(e,s.id,i)}content(e,n,i){const{vector_store_id:s}=n;return this._client.getAPIList(M`/vector_stores/${s}/files/${e}/content`,ln,{...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}class dn extends B{constructor(){super(...arguments),this.files=new fa(this._client),this.fileBatches=new da(this._client)}create(e,n){return this._client.post("/vector_stores",{body:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}retrieve(e,n){return this._client.get(M`/vector_stores/${e}`,{...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}update(e,n,i){return this._client.post(M`/vector_stores/${e}`,{body:n,...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}list(e={},n){return this._client.getAPIList("/vector_stores",Y,{query:e,...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}delete(e,n){return this._client.delete(M`/vector_stores/${e}`,{...n,headers:F([{"OpenAI-Beta":"assistants=v2"},n==null?void 0:n.headers])})}search(e,n,i){return this._client.getAPIList(M`/vector_stores/${e}/search`,ln,{body:n,method:"post",...i,headers:F([{"OpenAI-Beta":"assistants=v2"},i==null?void 0:i.headers])})}}dn.Files=fa;dn.FileBatches=da;var Oe,ma,Ot;class ha extends B{constructor(){super(...arguments),Oe.add(this)}async unwrap(e,n,i=this._client.webhookSecret,s=300){return await this.verifySignature(e,n,i,s),JSON.parse(e)}async verifySignature(e,n,i=this._client.webhookSecret,s=300){if(typeof crypto>"u"||typeof crypto.subtle.importKey!="function"||typeof crypto.subtle.verify!="function")throw new Error("Webhook signature verification is only supported when the `crypto` global is defined");v(this,Oe,"m",ma).call(this,i);const a=F([n]).values,l=v(this,Oe,"m",Ot).call(this,a,"webhook-signature"),u=v(this,Oe,"m",Ot).call(this,a,"webhook-timestamp"),d=v(this,Oe,"m",Ot).call(this,a,"webhook-id"),c=parseInt(u,10);if(isNaN(c))throw new it("Invalid webhook timestamp format");const m=Math.floor(Date.now()/1e3);if(m-c>s)throw new it("Webhook timestamp is too old");if(c>m+s)throw new it("Webhook timestamp is too new");const h=l.split(" ").map(x=>x.startsWith("v1,")?x.substring(3):x),p=i.startsWith("whsec_")?Buffer.from(i.replace("whsec_",""),"base64"):Buffer.from(i,"utf-8"),g=d?`${d}.${u}.${e}`:`${u}.${e}`,y=await crypto.subtle.importKey("raw",p,{name:"HMAC",hash:"SHA-256"},!1,["verify"]);for(const x of h)try{const T=Buffer.from(x,"base64");if(await crypto.subtle.verify("HMAC",y,T,new TextEncoder().encode(g)))return}catch{continue}throw new it("The given webhook signature does not match the expected signature")}}Oe=new WeakSet,ma=function(e){if(typeof e!="string"||e.length===0)throw new Error("The webhook secret must either be set using the env var, OPENAI_WEBHOOK_SECRET, on the client class, OpenAI({ webhookSecret: '123' }), or passed to this function")},Ot=function(e,n){if(!e)throw new Error("Headers are required");const i=e.get(n);if(i==null)throw new Error(`Missing required header: ${n}`);return i};var zn,Ti,Ht,pa;class q{constructor({baseURL:e=Ve("OPENAI_BASE_URL"),apiKey:n=Ve("OPENAI_API_KEY"),organization:i=Ve("OPENAI_ORG_ID")??null,project:s=Ve("OPENAI_PROJECT_ID")??null,webhookSecret:a=Ve("OPENAI_WEBHOOK_SECRET")??null,...l}={}){if(zn.add(this),Ht.set(this,void 0),this.completions=new zr(this),this.chat=new ui(this),this.embeddings=new Qr(this),this.files=new jr(this),this.images=new oa(this),this.audio=new Ct(this),this.moderations=new aa(this),this.models=new ra(this),this.fineTuning=new je(this),this.graders=new xi(this),this.vectorStores=new dn(this),this.webhooks=new ha(this),this.beta=new _t(this),this.batches=new Jr(this),this.uploads=new _i(this),this.responses=new Ci(this),this.evals=new hi(this),this.containers=new fi(this),n===void 0)throw new V("The OPENAI_API_KEY environment variable is missing or empty; either provide it, or instantiate the OpenAI client with an apiKey option, like new OpenAI({ apiKey: 'My API Key' }).");const u={apiKey:n,organization:i,project:s,webhookSecret:a,...l,baseURL:e||"https://api.openai.com/v1"};if(!u.dangerouslyAllowBrowser&&$g())throw new V(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new OpenAI({ apiKey, dangerouslyAllowBrowser: true });

https://help.openai.com/en/articles/5112595-best-practices-for-api-key-safety
`);this.baseURL=u.baseURL,this.timeout=u.timeout??Ti.DEFAULT_TIMEOUT,this.logger=u.logger??console;const d="warn";this.logLevel=d,this.logLevel=Zs(u.logLevel,"ClientOptions.logLevel",this)??Zs(Ve("OPENAI_LOG"),"process.env['OPENAI_LOG']",this)??d,this.fetchOptions=u.fetchOptions,this.maxRetries=u.maxRetries??2,this.fetch=u.fetch??Jg(),$(this,Ht,Hg),this._options=u,this.apiKey=n,this.organization=i,this.project=s,this.webhookSecret=a}withOptions(e){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetch:this.fetch,fetchOptions:this.fetchOptions,apiKey:this.apiKey,organization:this.organization,project:this.project,webhookSecret:this.webhookSecret,...e})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:e,nulls:n}){}async authHeaders(e){return F([{Authorization:`Bearer ${this.apiKey}`}])}stringifyQuery(e){return Qg(e,{arrayFormat:"brackets"})}getUserAgent(){return`${this.constructor.name}/JS ${Ge}`}defaultIdempotencyKey(){return`stainless-node-retry-${Ko()}`}makeStatusError(e,n,i,s){return ie.generate(e,n,i,s)}buildURL(e,n,i){const s=!v(this,zn,"m",pa).call(this)&&i||this.baseURL,a=Rg(e)?new URL(e):new URL(s+(s.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),l=this.defaultQuery();return Dg(l)||(n={...l,...n}),typeof n=="object"&&n&&!Array.isArray(n)&&(a.search=this.stringifyQuery(n)),a.toString()}async prepareOptions(e){}async prepareRequest(e,{url:n,options:i}){}get(e,n){return this.methodRequest("get",e,n)}post(e,n){return this.methodRequest("post",e,n)}patch(e,n){return this.methodRequest("patch",e,n)}put(e,n){return this.methodRequest("put",e,n)}delete(e,n){return this.methodRequest("delete",e,n)}methodRequest(e,n,i){return this.request(Promise.resolve(i).then(s=>({method:e,path:n,...s})))}request(e,n=null){return new an(this,this.makeRequest(e,n,void 0))}async makeRequest(e,n,i){var I,w;const s=await e,a=s.maxRetries??this.maxRetries;n==null&&(n=a),await this.prepareOptions(s);const{req:l,url:u,timeout:d}=await this.buildRequest(s,{retryCount:a-n});await this.prepareRequest(l,{url:u,options:s});const c="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),m=i===void 0?"":`, retryOf: ${i}`,h=Date.now();if(te(this).debug(`[${c}] sending request`,Pe({retryOfRequestLogID:i,method:s.method,url:u,options:s,headers:l.headers})),(I=s.signal)!=null&&I.aborted)throw new me;const p=new AbortController,g=await this.fetchWithTimeout(u,l,d,p).catch($n),y=Date.now();if(g instanceof Error){const A=`retrying, ${n} attempts remaining`;if((w=s.signal)!=null&&w.aborted)throw new me;const _=Un(g)||/timed? ?out/i.test(String(g)+("cause"in g?String(g.cause):""));if(n)return te(this).info(`[${c}] connection ${_?"timed out":"failed"} - ${A}`),te(this).debug(`[${c}] connection ${_?"timed out":"failed"} (${A})`,Pe({retryOfRequestLogID:i,url:u,durationMs:y-h,message:g.message})),this.retryRequest(s,n,i??c);throw te(this).info(`[${c}] connection ${_?"timed out":"failed"} - error; no more retries left`),te(this).debug(`[${c}] connection ${_?"timed out":"failed"} (error; no more retries left)`,Pe({retryOfRequestLogID:i,url:u,durationMs:y-h,message:g.message})),_?new ni:new on({cause:g})}const x=[...g.headers.entries()].filter(([A])=>A==="x-request-id").map(([A,_])=>", "+A+": "+JSON.stringify(_)).join(""),T=`[${c}${m}${x}] ${l.method} ${u} ${g.ok?"succeeded":"failed"} with status ${g.status} in ${y-h}ms`;if(!g.ok){const A=await this.shouldRetry(g);if(n&&A){const b=`retrying, ${n} attempts remaining`;return await Og(g.body),te(this).info(`${T} - ${b}`),te(this).debug(`[${c}] response error (${b})`,Pe({retryOfRequestLogID:i,url:g.url,status:g.status,headers:g.headers,durationMs:y-h})),this.retryRequest(s,n,i??c,g.headers)}const _=A?"error; no more retries left":"error; not retryable";te(this).info(`${T} - ${_}`);const C=await g.text().catch(b=>$n(b).message),E=Ug(C),N=E?void 0:C;throw te(this).debug(`[${c}] response error (${_})`,Pe({retryOfRequestLogID:i,url:g.url,status:g.status,headers:g.headers,message:N,durationMs:Date.now()-h})),this.makeStatusError(g.status,E,N,g.headers)}return te(this).info(T),te(this).debug(`[${c}] response start`,Pe({retryOfRequestLogID:i,url:g.url,status:g.status,headers:g.headers,durationMs:y-h})),{response:g,options:s,controller:p,requestLogID:c,retryOfRequestLogID:i,startTime:h}}getAPIList(e,n,i){return this.requestAPIList(n,{method:"get",path:e,...i})}requestAPIList(e,n){const i=this.makeRequest(n,null,void 0);return new ry(this,i,e)}async fetchWithTimeout(e,n,i,s){const{signal:a,method:l,...u}=n||{};a&&a.addEventListener("abort",()=>s.abort());const d=setTimeout(()=>s.abort(),i),c=globalThis.ReadableStream&&u.body instanceof globalThis.ReadableStream||typeof u.body=="object"&&u.body!==null&&Symbol.asyncIterator in u.body,m={signal:s.signal,...c?{duplex:"half"}:{},method:"GET",...u};l&&(m.method=l.toUpperCase());try{return await this.fetch.call(void 0,e,m)}finally{clearTimeout(d)}}async shouldRetry(e){const n=e.headers.get("x-should-retry");return n==="true"?!0:n==="false"?!1:e.status===408||e.status===409||e.status===429||e.status>=500}async retryRequest(e,n,i,s){let a;const l=s==null?void 0:s.get("retry-after-ms");if(l){const d=parseFloat(l);Number.isNaN(d)||(a=d)}const u=s==null?void 0:s.get("retry-after");if(u&&!a){const d=parseFloat(u);Number.isNaN(d)?a=Date.parse(u)-Date.now():a=d*1e3}if(!(a&&0<=a&&a<60*1e3)){const d=e.maxRetries??this.maxRetries;a=this.calculateDefaultRetryTimeoutMillis(n,d)}return await xt(a),this.makeRequest(e,n-1,i)}calculateDefaultRetryTimeoutMillis(e,n){const a=n-e,l=Math.min(.5*Math.pow(2,a),8),u=1-Math.random()*.25;return l*u*1e3}async buildRequest(e,{retryCount:n=0}={}){const i={...e},{method:s,path:a,query:l,defaultBaseURL:u}=i,d=this.buildURL(a,l,u);"timeout"in i&&Fg("timeout",i.timeout),i.timeout=i.timeout??this.timeout;const{bodyHeaders:c,body:m}=this.buildBody({options:i}),h=await this.buildHeaders({options:e,method:s,bodyHeaders:c,retryCount:n});return{req:{method:s,headers:h,...i.signal&&{signal:i.signal},...globalThis.ReadableStream&&m instanceof globalThis.ReadableStream&&{duplex:"half"},...m&&{body:m},...this.fetchOptions??{},...i.fetchOptions??{}},url:d,timeout:i.timeout}}async buildHeaders({options:e,method:n,bodyHeaders:i,retryCount:s}){let a={};this.idempotencyHeader&&n!=="get"&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),a[this.idempotencyHeader]=e.idempotencyKey);const l=F([a,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(s),...e.timeout?{"X-Stainless-Timeout":String(Math.trunc(e.timeout/1e3))}:{},...Gg(),"OpenAI-Organization":this.organization,"OpenAI-Project":this.project},await this.authHeaders(e),this._options.defaultHeaders,i,e.headers]);return this.validateHeaders(l),l.values}buildBody({options:{body:e,headers:n}}){if(!e)return{bodyHeaders:void 0,body:void 0};const i=F([n]);return ArrayBuffer.isView(e)||e instanceof ArrayBuffer||e instanceof DataView||typeof e=="string"&&i.values.has("content-type")||e instanceof Blob||e instanceof FormData||e instanceof URLSearchParams||globalThis.ReadableStream&&e instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:e}:typeof e=="object"&&(Symbol.asyncIterator in e||Symbol.iterator in e&&"next"in e&&typeof e.next=="function")?{bodyHeaders:void 0,body:or(e)}:v(this,Ht,"f").call(this,{body:e,headers:i})}}Ti=q,Ht=new WeakMap,zn=new WeakSet,pa=function(){return this.baseURL!=="https://api.openai.com/v1"};q.OpenAI=Ti;q.DEFAULT_TIMEOUT=6e5;q.OpenAIError=V;q.APIError=ie;q.APIConnectionError=on;q.APIConnectionTimeoutError=ni;q.APIUserAbortError=me;q.NotFoundError=Qo;q.ConflictError=Zo;q.RateLimitError=er;q.BadRequestError=Yo;q.AuthenticationError=zo;q.InternalServerError=tr;q.PermissionDeniedError=Xo;q.UnprocessableEntityError=jo;q.InvalidWebhookSignatureError=it;q.toFile=fy;q.Completions=zr;q.Chat=ui;q.Embeddings=Qr;q.Files=jr;q.Images=oa;q.Audio=Ct;q.Moderations=aa;q.Models=ra;q.FineTuning=je;q.Graders=xi;q.VectorStores=dn;q.Webhooks=ha;q.Beta=_t;q.Batches=Jr;q.Uploads=_i;q.Responses=Ci;q.Evals=hi;q.Containers=fi;class $y{constructor(){this.config=null,this.textGeminiClient=null,this.textOpenaiClient=null,this.imageGeminiClient=null,this.imageOpenaiClient=null}async getConfig(){var e,n,i,s,a,l,u,d,c,m,h;if(!this.config)try{const p=await oe.getAIConfigForClient();if(console.log("🔍 获取到的AI配置:",{textProvider:((e=p.config)==null?void 0:e.textProvider)||((n=p.config)==null?void 0:n.provider),imageProvider:((i=p.config)==null?void 0:i.imageProvider)||((s=p.config)==null?void 0:s.provider),hasTextApiKey:!!((a=p.config)!=null&&a.textApiKey||(l=p.config)!=null&&l.apiKey),hasImageApiKey:!!((u=p.config)!=null&&u.imageApiKey||(d=p.config)!=null&&d.apiKey),textModel:(c=p.config)==null?void 0:c.textModel,imageModel:(m=p.config)==null?void 0:m.imageModel,isActive:(h=p.config)==null?void 0:h.isActive}),p.config&&p.config.isActive){const g=p.config;if(g.textProvider&&g.imageProvider){if(!g.textApiKey||!g.imageApiKey)throw console.error("❌ 新格式配置中缺少文本或图片API Key"),new Error("配置中缺少文本或图片API Key");this.config={textProvider:g.textProvider,textApiKey:g.textApiKey,textBaseUrl:g.textBaseUrl,textModel:g.textModel||"gemini-2.5-flash",imageProvider:g.imageProvider,imageApiKey:g.imageApiKey,imageBaseUrl:g.imageBaseUrl,imageModel:g.imageModel||"imagen-3.0-generate-002",isActive:g.isActive,provider:g.textProvider,apiKey:g.textApiKey,baseUrl:g.textBaseUrl},console.log(`🔧 使用分离配置 - 文本: ${this.config.textProvider}/${this.config.textModel}, 图片: ${this.config.imageProvider}/${this.config.imageModel}`)}else{if(!g.apiKey)throw console.error("❌ 旧格式配置中缺少API Key"),new Error("配置中缺少API Key");this.config={textProvider:g.provider||"gemini",textApiKey:g.apiKey,textBaseUrl:g.baseUrl,textModel:g.textModel||"gemini-2.5-flash",imageProvider:g.provider||"gemini",imageApiKey:g.apiKey,imageBaseUrl:g.baseUrl,imageModel:g.imageModel||"imagen-3.0-generate-002",isActive:g.isActive,provider:g.provider||"gemini",apiKey:g.apiKey,baseUrl:g.baseUrl},console.log(`🔧 使用统一配置 - 供应商: ${this.config.provider}, 文本模型: ${this.config.textModel}, 图片模型: ${this.config.imageModel}`)}}else throw new Error("没有活跃的AI配置")}catch(p){console.log("获取用户AI配置失败，使用默认Gemini配置:",p);const{apiKey:g}=await oe.getAIApiKey();this.config={textProvider:"gemini",textApiKey:g,textModel:"gemini-2.5-flash",imageProvider:"gemini",imageApiKey:g,imageModel:"imagen-3.0-generate-002",isActive:!0,provider:"gemini",apiKey:g}}return this.config}async initializeClients(){const e=await this.getConfig();if(!e.textApiKey||!e.imageApiKey)throw new Error("文本或图片API Key未配置或为空");if(console.log(`🔧 初始化AI客户端 - 文本: ${e.textProvider}, 图片: ${e.imageProvider}`),e.textProvider==="gemini")this.textGeminiClient||(this.textGeminiClient=new qs({apiKey:e.textApiKey}),console.log("✅ 文本Gemini客户端初始化成功"));else if(e.textProvider==="openai")try{this.textOpenaiClient=new q({apiKey:e.textApiKey,baseURL:e.textBaseUrl,dangerouslyAllowBrowser:!0}),console.log(`✅ 文本OpenAI客户端初始化成功 - baseURL: ${e.textBaseUrl}`)}catch(n){throw console.error("❌ 文本OpenAI客户端初始化失败:",n),n}if(e.imageProvider==="gemini")this.imageGeminiClient||(this.imageGeminiClient=new qs({apiKey:e.imageApiKey}),console.log("✅ 图片Gemini客户端初始化成功"));else if(e.imageProvider==="openai")try{this.imageOpenaiClient=new q({apiKey:e.imageApiKey,baseURL:e.imageBaseUrl,dangerouslyAllowBrowser:!0}),console.log(`✅ 图片OpenAI客户端初始化成功 - baseURL: ${e.imageBaseUrl}`)}catch(n){throw console.error("❌ 图片OpenAI客户端初始化失败:",n),n}}async generateText(e){var s,a;await this.initializeClients();const n=await this.getConfig(),i=e.model||n.textModel;if(console.log(`📝 使用 ${n.textProvider} 的模型 ${i} 生成文本`),n.textProvider==="gemini")return(await this.textGeminiClient.models.generateContent({model:i,contents:e.prompt,config:e.responseFormat==="json"?{responseMimeType:"application/json"}:void 0})).text||"";if(n.textProvider==="openai")return((a=(s=(await this.textOpenaiClient.chat.completions.create({model:i,messages:[{role:"user",content:e.prompt}],response_format:e.responseFormat==="json"?{type:"json_object"}:void 0})).choices[0])==null?void 0:s.message)==null?void 0:a.content)||"";throw new Error(`不支持的文本供应商: ${n.textProvider}`)}async generateImage(e){var s,a,l,u;await this.initializeClients();const n=await this.getConfig(),i=e.model||n.imageModel;if(console.log(`🎨 使用 ${n.imageProvider} 的模型 ${i} 生成图片`),n.imageProvider==="gemini")return`data:image/jpeg;base64,${((l=(a=(s=(await this.imageGeminiClient.models.generateImages({model:i,prompt:e.prompt,config:{numberOfImages:e.numberOfImages||1,outputMimeType:"image/jpeg",aspectRatio:e.aspectRatio||"1:1"}})).generatedImages)==null?void 0:s[0])==null?void 0:a.image)==null?void 0:l.imageBytes)||""}`;if(n.imageProvider==="openai")return console.log("🖼️ OpenAI图片生成参数:",{model:i,prompt:e.prompt,n:e.numberOfImages||1,size:e.size||"1024x1024",response_format:"b64_json"}),`data:image/png;base64,${((u=(await this.imageOpenaiClient.images.generate({model:i,prompt:e.prompt,n:e.numberOfImages||1,size:e.size||"1024x1024",response_format:"b64_json"})).data[0])==null?void 0:u.b64_json)||""}`;throw new Error(`不支持的图片供应商: ${n.imageProvider}`)}async testConnection(){var e,n;try{await this.initializeClients();const i=await this.getConfig();console.log(`🧪 测试文本: ${i.textProvider}, 图片: ${i.imageProvider} API连接...`);let s="";return i.textProvider==="gemini"?s=(await this.textGeminiClient.models.generateContent({model:i.textModel,contents:'Hello, this is a test message. Please respond with "Test successful".'})).text||"":i.textProvider==="openai"&&(s=((n=(e=(await this.textOpenaiClient.chat.completions.create({model:i.textModel,messages:[{role:"user",content:'Hello, this is a test message. Please respond with "Test successful".'}]})).choices[0])==null?void 0:e.message)==null?void 0:n.content)||""),s.length>0?{success:!0,message:`API测试成功！文本: ${i.textProvider}/${i.textModel}, 图片: ${i.imageProvider}/${i.imageModel}`,details:{textResponse:s.substring(0,100)+"...",textProvider:i.textProvider,imageProvider:i.imageProvider}}:{success:!1,message:`文本API测试失败: ${i.textProvider}`,details:{textResponse:s}}}catch(i){return console.error("❌ API测试失败:",i),{success:!1,message:`API测试失败: ${i instanceof Error?i.message:"未知错误"}`,details:{error:i instanceof Error?i.message:i}}}}resetConfig(){console.log("🔄 重置AI配置缓存"),this.config=null,this.geminiClient=null,this.openaiClient=null}}const go=new $y,Vy=()=>{const[t,e]=P.useState(null),[n,i]=P.useState(null),[s,a]=P.useState(!0),[l,u]=P.useState("overview"),[d,c]=P.useState(!1);P.useEffect(()=>{m()},[]);const m=async()=>{try{a(!0);const[g,y]=await Promise.all([kt.getSystemStats(),kt.getStorageAnalysis()]);e(g),i(y)}catch(g){console.error("加载系统统计失败:",g)}finally{a(!1)}},h=async()=>{try{c(!0),await kt.cleanupStorage(),alert("存储清理完成！"),m()}catch(g){console.error("存储清理失败:",g),alert("存储清理失败")}finally{c(!1)}},p=g=>{const y=parseInt(g);return y<50?"bg-green-500":y<80?"bg-yellow-500":"bg-red-500"};return s?f.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:f.jsxs("div",{className:"animate-pulse",children:[f.jsx("div",{className:"h-4 bg-gray-200 rounded w-1/4 mb-4"}),f.jsxs("div",{className:"space-y-3",children:[f.jsx("div",{className:"h-3 bg-gray-200 rounded"}),f.jsx("div",{className:"h-3 bg-gray-200 rounded w-5/6"}),f.jsx("div",{className:"h-3 bg-gray-200 rounded w-4/6"})]})]})}):t?f.jsxs("div",{className:"bg-white rounded-lg shadow",children:[f.jsx("div",{className:"border-b border-gray-200",children:f.jsx("nav",{className:"flex space-x-8 px-6",children:[{key:"overview",label:"📊 系统概览"},{key:"storage",label:"💾 存储分析"},{key:"cleanup",label:"🧹 清理工具"}].map(g=>f.jsx("button",{onClick:()=>u(g.key),className:`py-4 px-1 border-b-2 font-medium text-sm ${l===g.key?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:g.label},g.key))})}),f.jsxs("div",{className:"p-6",children:[l==="overview"&&f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[f.jsx("h3",{className:"text-lg font-semibold mb-3",children:"💽 磁盘使用情况"}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-4",children:[f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-2xl font-bold text-blue-600",children:t.disk.total}),f.jsx("div",{className:"text-sm text-gray-600",children:"总容量"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-2xl font-bold text-orange-600",children:t.disk.used}),f.jsx("div",{className:"text-sm text-gray-600",children:"已使用"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-2xl font-bold text-green-600",children:t.disk.available}),f.jsx("div",{className:"text-sm text-gray-600",children:"可用空间"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-2xl font-bold text-red-600",children:t.disk.usagePercent}),f.jsx("div",{className:"text-sm text-gray-600",children:"使用率"})]})]}),f.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:f.jsx("div",{className:`h-3 rounded-full ${p(t.disk.usagePercent)}`,style:{width:t.disk.usagePercent}})})]}),f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[f.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🚀 应用程序占用"}),f.jsx("div",{className:"mb-4",children:f.jsxs("div",{className:"text-xl font-bold text-purple-600",children:["总大小: ",t.application.totalSize]})}),f.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:t.application.components.map((g,y)=>f.jsxs("div",{className:"bg-white rounded p-3 border",children:[f.jsx("div",{className:"font-medium",children:g.name}),f.jsx("div",{className:"text-sm text-gray-600",children:g.size})]},y))})]}),f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[f.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🎵 音频文件统计"}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-2xl font-bold text-blue-600",children:t.audio.fileCount}),f.jsx("div",{className:"text-sm text-gray-600",children:"音频文件数量"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-2xl font-bold text-green-600",children:t.audio.totalSize}),f.jsx("div",{className:"text-sm text-gray-600",children:"音频文件总大小"})]})]})]}),f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[f.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🗄️ 数据库统计"}),f.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-xl font-bold text-blue-600",children:t.database.articles}),f.jsx("div",{className:"text-sm text-gray-600",children:"文章数量"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-xl font-bold text-green-600",children:t.database.users}),f.jsx("div",{className:"text-sm text-gray-600",children:"用户数量"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-xl font-bold text-purple-600",children:t.database.audioFiles}),f.jsx("div",{className:"text-sm text-gray-600",children:"音频文件"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-xl font-bold text-orange-600",children:t.database.size}),f.jsx("div",{className:"text-sm text-gray-600",children:"数据库大小"})]})]})]}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[f.jsx("h3",{className:"text-lg font-semibold mb-3",children:"🧠 内存使用"}),f.jsxs("div",{className:"space-y-2",children:[f.jsxs("div",{className:"flex justify-between",children:[f.jsx("span",{children:"总内存:"}),f.jsx("span",{className:"font-medium",children:t.memory.total})]}),f.jsxs("div",{className:"flex justify-between",children:[f.jsx("span",{children:"已使用:"}),f.jsx("span",{className:"font-medium text-orange-600",children:t.memory.used})]}),f.jsxs("div",{className:"flex justify-between",children:[f.jsx("span",{children:"可用:"}),f.jsx("span",{className:"font-medium text-green-600",children:t.memory.free})]})]})]}),f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[f.jsx("h3",{className:"text-lg font-semibold mb-3",children:"⚡ 系统状态"}),f.jsxs("div",{className:"space-y-2",children:[f.jsxs("div",{className:"flex justify-between",children:[f.jsx("span",{children:"CPU使用率:"}),f.jsx("span",{className:"font-medium",children:t.cpu.usage})]}),f.jsxs("div",{className:"flex justify-between",children:[f.jsx("span",{children:"系统运行时间:"}),f.jsx("span",{className:"font-medium",children:t.uptime})]})]})]})]})]}),l==="storage"&&n&&f.jsxs("div",{className:"space-y-6",children:[f.jsx("h3",{className:"text-lg font-semibold",children:"📁 详细存储分析"}),f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4",children:[f.jsx("h4",{className:"font-semibold mb-3",children:"🎵 音频文件详情"}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-xl font-bold",children:n.audioFiles.totalFiles}),f.jsx("div",{className:"text-sm text-gray-600",children:"总文件数"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-xl font-bold",children:n.audioFiles.totalSize}),f.jsx("div",{className:"text-sm text-gray-600",children:"总大小"})]}),f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"text-xl font-bold",children:n.audioFiles.averageSize}),f.jsx("div",{className:"text-sm text-gray-600",children:"平均大小"})]})]}),n.audioFiles.largestFiles.length>0&&f.jsxs("div",{children:[f.jsx("h5",{className:"font-medium mb-2",children:"最大的音频文件:"}),f.jsx("div",{className:"space-y-2",children:n.audioFiles.largestFiles.map((g,y)=>f.jsx("div",{className:"bg-white rounded p-3 border",children:f.jsxs("div",{className:"flex justify-between items-center",children:[f.jsxs("div",{children:[f.jsx("div",{className:"font-medium",children:g.articleTitle}),f.jsx("div",{className:"text-sm text-gray-600",children:g.fileName})]}),f.jsxs("div",{className:"text-right",children:[f.jsx("div",{className:"font-medium",children:g.size}),f.jsx("div",{className:"text-sm text-gray-600",children:new Date(g.uploadDate).toLocaleDateString()})]})]})},y))})]})]})]}),l==="cleanup"&&f.jsxs("div",{className:"space-y-6",children:[f.jsx("h3",{className:"text-lg font-semibold",children:"🧹 存储清理工具"}),f.jsx("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"flex-shrink-0",children:f.jsx("svg",{className:"h-5 w-5 text-yellow-400",fill:"currentColor",viewBox:"0 0 20 20",children:f.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),f.jsxs("div",{className:"ml-3",children:[f.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"注意"}),f.jsx("div",{className:"mt-2 text-sm text-yellow-700",children:f.jsx("p",{children:"清理操作将删除临时文件和缓存，这个操作不可逆。请确保您了解这些操作的影响。"})})]})]})}),f.jsxs("div",{className:"bg-white border rounded-lg p-4",children:[f.jsx("h4",{className:"font-semibold mb-3",children:"可清理的内容:"}),f.jsxs("ul",{className:"space-y-2 mb-4",children:[f.jsxs("li",{className:"flex items-center",children:[f.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"7天前的日志文件"]}),f.jsxs("li",{className:"flex items-center",children:[f.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"npm缓存文件"]}),f.jsxs("li",{className:"flex items-center",children:[f.jsx("span",{className:"w-2 h-2 bg-blue-500 rounded-full mr-3"}),"临时文件"]})]}),f.jsx("button",{onClick:h,disabled:d,className:"bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:d?"清理中...":"开始清理"})]})]})]}),f.jsx("div",{className:"border-t border-gray-200 px-6 py-3",children:f.jsx("button",{onClick:m,disabled:s,className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed",children:s?"刷新中...":"刷新数据"})})]}):f.jsx("div",{className:"bg-white rounded-lg shadow p-6",children:f.jsx("p",{className:"text-red-600",children:"无法加载系统统计信息"})})},By=()=>{const[t,e]=P.useState([]),[n,i]=P.useState(!0),[s,a]=P.useState(!1),[l,u]=P.useState(null),[d,c]=P.useState(!1),[m,h]=P.useState({version:"",type:"minor",title:"",description:"",changes:[""]});P.useEffect(()=>{p()},[]);const p=async()=>{try{i(!0);const C=await qe.getSystemLogs({limit:50});e(C.logs)}catch(C){console.error("加载日志失败:",C)}finally{i(!1)}},g=async C=>{C.preventDefault();const E=m.changes.filter(N=>N.trim());if(E.length===0){alert("请至少添加一条变更记录");return}try{const N={...m,changes:E};l?await qe.updateSystemLog(l.id,N):await qe.createSystemLog(N),a(!1),u(null),h({version:"",type:"minor",title:"",description:"",changes:[""]}),p()}catch(N){console.error("保存日志失败:",N),alert("保存失败，请检查版本号是否重复")}},y=C=>{u(C),h({version:C.version,type:C.type,title:C.title||"",description:C.description||"",changes:C.changes.length>0?C.changes:[""]}),a(!0)},x=async C=>{if(confirm("确定要删除这条日志吗？"))try{await qe.deleteSystemLog(C),p()}catch(E){console.error("删除日志失败:",E),alert("删除失败")}},T=()=>{h(C=>({...C,changes:[...C.changes,""]}))},I=(C,E)=>{h(N=>({...N,changes:N.changes.map((D,b)=>b===C?E:D)}))},w=C=>{h(E=>({...E,changes:E.changes.filter((N,D)=>D!==C)}))},A=async()=>{if(confirm("确定要导入历史日志吗？这将添加所有历史版本记录。"))try{c(!0);const C=[{version:"v2.2.0",date:"2025-01-11",type:"minor",title:"播客功能上线",description:"为Joker期刊系统添加完整的音频播客功能，提供多媒体阅读体验",changes:["🎵 播客功能：支持为文章添加音频播客，提供完整的多媒体阅读体验","🎧 音频播放器：专业的音频播放控件，支持进度控制、音量调节、时长显示","📤 音频上传：管理员可上传最大50MB的音频文件，支持拖拽上传","🎛️ 播放控制：播放/暂停、进度跳转、音量控制等完整功能","📊 音频管理：支持音频信息编辑、删除等管理功能","🔒 权限控制：只有管理员和编辑可以上传/删除音频文件","📱 响应式设计：音频播放器适配各种屏幕尺寸"]},{version:"v2.1.3",date:"2025-01-11",type:"patch",title:"AI模型升级",description:"全面升级到Gemini 2.5 Flash模型，提升生成性能",changes:["🤖 AI模型升级：全面切换到Gemini 2.5 Flash模型，提升生成速度和质量","⚡ 性能优化：新模型响应更快，生成内容更加稳定","🔧 统一配置：前后端AI调用统一使用最新的模型版本","📝 改进生成：文章内容和图片生成都使用优化后的模型"]},{version:"v2.1.2",date:"2025-01-11",type:"patch",title:"用户去重统计",description:"优化观看次数统计，避免重复计数",changes:["👤 用户去重统计：同一用户只会被统计一次观看次数，避免重复刷新增加浏览量","🔍 智能识别：基于用户ID（已登录）+ IP地址 + 浏览器指纹进行用户识别","💾 本地缓存：使用localStorage记录已访问文章，减少不必要的API调用","📊 真实统计：新增观看记录表，提供更准确的用户行为分析","🎯 精确去重：确保统计数据的真实性和准确性"]},{version:"v2.1.1",date:"2025-01-11",type:"patch",title:"修复观看次数统计",description:"解决观看次数不更新的问题",changes:["🔧 修复观看次数统计：解决前端未调用后端API导致观看次数不更新的问题","📊 优化统计逻辑：移除重复的观看次数增加逻辑，确保统计准确性","⚡ 实时更新：进入文章页面时自动获取最新的观看次数并显示","🎯 精确计算：每次访问文章详情页都会正确增加观看次数"]},{version:"v2.1.0",date:"2025-01-11",type:"minor",title:"观看统计与批量管理",description:"新增观看次数统计和批量删除功能",changes:["📊 新增观看次数统计：文章详情页自动统计观看次数，实时显示浏览量","🗂️ 批量删除功能：管理员可多选文章进行批量删除，提升管理效率","✏️ 观看次数编辑：管理员可手动修改文章观看次数，支持数据调整","☑️ 全选功能：支持一键全选/取消全选所有文章，便于批量操作","🎯 智能选择：选中文章数量实时显示，操作状态清晰可见","🔒 权限控制：批量删除和观看次数编辑仅限管理员和编辑使用","📈 数据展示：文章列表和详情页都显示观看次数，数据透明化"]},{version:"v2.0.1",date:"2025-01-11",type:"patch",title:"UI优化与修复",description:"修复重复显示问题，优化用户体验",changes:["🔧 修复UI重复问题：移除重复的'文章内容尚未生成'提示，优化用户体验","📝 修复日志显示：解决更新日志中重复列表符号的显示问题","🎯 优化内容生成：改进prompt避免重复生成标题和摘要，从正文部分开始延续","✨ 智能内容衔接：生成详细内容时会基于已有的标题、作者、摘要信息继续写作","🎨 界面清理：移除冗余的提示信息，让界面更加简洁明了"]},{version:"v2.0.0",date:"2025-01-11",type:"major",title:"Joker 2.0 重大更新",description:"期刊正式更名为Joker，新增双模式系统",changes:["🃏 重大更名：期刊名称从'Jocker'正式更名为'Joker'，修正了所有前端显示","🎭 双模式系统：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）","🎓 学术生产力：严肃模式生成符合顶级期刊标准的专业文章，成为真正的学术工具","🎪 娱乐保留：小丑模式继续提供幽默、戏谑的学术文章","⚙️ 智能切换：根据模式自动使用不同的AI提示词模板","🔬 严谨标准：严肃模式确保科学方法论、统计分析、引用格式的正确性","🎨 全新界面：生成弹窗提供直观的模式选择体验","📚 完整更新：DOI、引用格式、PDF文件名等全面更新为新期刊名称","🔧 技术优化：修复了多个渲染和显示问题，提升系统稳定性"]},{version:"v1.8.0",date:"2025-01-11",type:"minor",title:"生成模式选择",description:"新增小丑模式和严肃模式选择",changes:["🎭 新增生成模式选择：支持小丑模式（戏谑文章）和严肃模式（专业学术文章）","🎓 严肃模式功能：生成符合顶级期刊发表标准的专业学术文章，可作为真正的学术生产力工具","🎪 小丑模式保留：继续支持原有的戏谑、幽默学术文章生成","⚙️ 智能提示词切换：根据选择的模式自动使用不同的AI提示词模板","🔬 严谨学术标准：严肃模式确保科学方法论正确、统计分析合理、引用格式标准","🎨 直观模式选择：生成和重新生成弹窗中提供清晰的模式选择界面"]},{version:"v1.7.6",date:"2025-01-11",type:"patch",title:"图片生成优化",description:"修复图片生成问题，优化错误处理",changes:["🔧 修复图片生成问题：限制AI提示词长度在400字符以内，避免413错误","⚡ 优化错误处理：图片生成失败时保存提示词并跳过到下一张，避免进度卡死","🎨 修复后台布局：解决文章标题过长时编辑删除按钮被挤出容器的问题","📱 改进响应式设计：后台文章管理界面按钮现在垂直排列，确保始终可点击","🖼️ 修复失败图片显示：失败的图片现在会在Figures画廊中显示，可以点击重新生成","💡 改进用户提示：文章中的失败占位符会提示用户到Figures画廊重新生成","📝 修复列表渲染问题：解决有序列表和无序列表在Markdown渲染时丢失的问题","🔧 优化Markdown解析：改进marked配置和列表格式处理，确保列表正确显示"]},{version:"v1.7.5",date:"2025-01-11",type:"minor",title:"文章编辑功能",description:"新增文章编辑功能和权限管理",changes:["✏️ 新增文章编辑功能：管理员和编辑可以直接编辑文章的Markdown内容","🎯 权限分级管理：管理员可生成和重新生成文章，编辑只能编辑现有内容","📝 实时编辑界面：提供专业的Markdown编辑器，支持语法提示和格式指导","🔄 自动重新渲染：编辑保存后自动重新渲染文章内容，包括图片和引用","🛡️ 角色权限控制：基于用户角色显示不同的操作按钮和功能","💾 即时保存功能：编辑内容可即时保存到数据库并更新显示"]},{version:"v1.7.4",date:"2025-01-11",type:"patch",title:"文献引用优化",description:"优化文献引用格式和PDF导出",changes:["📚 文献引用上标化：文献引用现在显示为学术期刊标准的上标格式，保留中括号","🔗 引用点击跳转：点击文献引用可平滑滚动到对应参考文献并高亮显示","📄 优化PDF导出：移除PDF中多余的'Peer Reviewed'和分类标签行","🔧 修复加粗渲染：改进紧贴数字和Figure标题的加粗标记处理","✨ 引用格式支持：支持[1], [1,2], [1-3], [1,3-5]等多种引用格式","🎨 视觉优化：引用点击后临时高亮效果，提升用户体验"]},{version:"v1.7.3",date:"2025-01-11",type:"minor",title:"文章生成流程优化",description:"分离基本信息生成和详细内容生成",changes:["🎯 优化文章生成流程：分离基本信息生成和详细内容生成","📝 首页生成文章：现在只需输入主题，生成标题、作者、摘要和封面","⚙️ 详情页生成正文：在文章详情页可自定义字数、图片数量和参考文献数量","🎨 新增生成参数弹窗：用户可在生成正文时设置文章长度、图片数量和参考文献数量","📚 新增参考文献数量控制：支持自定义1-10篇参考文献，默认3-5篇","🔄 改进重新生成功能：支持自定义参数重新生成文章内容","📄 优化PDF导出：文件名现在使用DOI格式","📊 新增生成进度显示：实时显示文章生成进度"]}];await qe.importHistoryLogs(C),alert("历史日志导入成功！"),p()}catch(C){console.error("导入历史日志失败:",C),alert("导入失败，请检查网络连接")}finally{c(!1)}},_=C=>{switch(C){case"major":return"bg-red-100 text-red-800";case"minor":return"bg-blue-100 text-blue-800";case"patch":return"bg-green-100 text-green-800";case"hotfix":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};return f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"flex justify-between items-center",children:[f.jsx("h2",{className:"text-2xl font-bold",children:"📋 日志管理"}),f.jsxs("div",{className:"flex space-x-3",children:[f.jsx("button",{onClick:A,disabled:d,className:"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 disabled:bg-gray-400",children:d?"导入中...":"📥 导入历史日志"}),f.jsx("button",{onClick:()=>{a(!0),u(null),h({version:"",type:"minor",title:"",description:"",changes:[""]})},className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:"➕ 添加日志"})]})]}),s&&f.jsxs("div",{className:"bg-white border rounded-lg p-6",children:[f.jsx("h3",{className:"text-lg font-semibold mb-4",children:l?"编辑日志":"创建新日志"}),f.jsxs("form",{onSubmit:g,className:"space-y-4",children:[f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"版本号 *"}),f.jsx("input",{type:"text",value:m.version,onChange:C=>h(E=>({...E,version:C.target.value})),placeholder:"如: v2.3.0",className:"w-full border border-gray-300 rounded-md px-3 py-2",required:!0})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"类型 *"}),f.jsxs("select",{value:m.type,onChange:C=>h(E=>({...E,type:C.target.value})),className:"w-full border border-gray-300 rounded-md px-3 py-2",children:[f.jsx("option",{value:"major",children:"🚀 重大更新"}),f.jsx("option",{value:"minor",children:"✨ 功能更新"}),f.jsx("option",{value:"patch",children:"🔧 修复更新"}),f.jsx("option",{value:"hotfix",children:"🚨 紧急修复"})]})]})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"标题"}),f.jsx("input",{type:"text",value:m.title,onChange:C=>h(E=>({...E,title:C.target.value})),placeholder:"可选的更新标题",className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"描述"}),f.jsx("textarea",{value:m.description,onChange:C=>h(E=>({...E,description:C.target.value})),placeholder:"可选的详细描述",rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"变更列表 *"}),m.changes.map((C,E)=>f.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[f.jsx("input",{type:"text",value:C,onChange:N=>I(E,N.target.value),placeholder:"输入变更内容",className:"flex-1 border border-gray-300 rounded-md px-3 py-2"}),m.changes.length>1&&f.jsx("button",{type:"button",onClick:()=>w(E),className:"text-red-600 hover:text-red-800",children:"❌"})]},E)),f.jsx("button",{type:"button",onClick:T,className:"text-blue-600 hover:text-blue-800 text-sm",children:"➕ 添加变更项"})]}),f.jsxs("div",{className:"flex space-x-3",children:[f.jsx("button",{type:"submit",className:"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700",children:l?"更新":"创建"}),f.jsx("button",{type:"button",onClick:()=>{a(!1),u(null)},className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-400",children:"取消"})]})]})]}),f.jsxs("div",{className:"bg-white rounded-lg shadow",children:[f.jsx("div",{className:"p-4 border-b border-gray-200",children:f.jsx("h3",{className:"text-lg font-semibold",children:"现有日志"})}),n?f.jsx("div",{className:"p-6 text-center",children:"加载中..."}):t.length===0?f.jsx("div",{className:"p-6 text-center text-gray-500",children:"暂无日志记录"}):f.jsx("div",{className:"divide-y divide-gray-200",children:t.map(C=>f.jsx("div",{className:"p-4 hover:bg-gray-50",children:f.jsxs("div",{className:"flex justify-between items-start",children:[f.jsxs("div",{className:"flex-1",children:[f.jsxs("div",{className:"flex items-center space-x-3 mb-2",children:[f.jsx("span",{className:"font-mono text-lg",children:C.version}),f.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${_(C.type)}`,children:C.type}),f.jsx("span",{className:"text-sm text-gray-500",children:new Date(C.date).toLocaleDateString()})]}),C.title&&f.jsx("h4",{className:"font-medium text-gray-900 mb-1",children:C.title}),f.jsxs("div",{className:"text-sm text-gray-600",children:[C.changes.slice(0,2).map((E,N)=>f.jsxs("div",{children:["• ",E]},N)),C.changes.length>2&&f.jsxs("div",{className:"text-gray-400",children:["... 还有 ",C.changes.length-2," 项"]})]})]}),f.jsxs("div",{className:"flex space-x-2 ml-4",children:[f.jsx("button",{onClick:()=>y(C),className:"text-blue-600 hover:text-blue-800 text-sm",children:"编辑"}),f.jsx("button",{onClick:()=>x(C.id),className:"text-red-600 hover:text-red-800 text-sm",children:"删除"})]})]})},C.id))})]})]})},qy=({onUploadSuccess:t,onUploadError:e})=>{const[n,i]=P.useState(!1),[s,a]=P.useState(null),[l,u]=P.useState(""),[d,c]=P.useState(""),m=P.useRef(null),h=y=>{var I;const x=(I=y.target.files)==null?void 0:I[0];if(!x)return;if(!x.type.startsWith("image/")){e==null||e("请选择图片文件");return}if(x.size>10*1024*1024){e==null||e("图片文件不能超过10MB");return}const T=new FileReader;T.onload=w=>{var _;const A=(_=w.target)==null?void 0:_.result;a(A)},T.readAsDataURL(x)},p=async()=>{if(!s){e==null||e("请先选择图片");return}i(!0);try{const y=await wa.uploadWebsiteCover({coverUrl:s,title:l||"网站封面",description:d||"网站主页封面图片"});t==null||t(y),a(null),u(""),c(""),m.current&&(m.current.value="")}catch(y){e==null||e(y instanceof Error?y.message:"上传失败")}finally{i(!1)}},g=()=>{a(null),u(""),c(""),m.current&&(m.current.value="")};return f.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[f.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"🖼️ 网站封面上传"}),f.jsxs("div",{className:"mb-4",children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择封面图片"}),f.jsx("input",{ref:m,type:"file",accept:"image/*",onChange:h,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-purple-50 file:text-purple-700 hover:file:bg-purple-100"}),f.jsx("p",{className:"mt-1 text-xs text-gray-500",children:"支持 JPG、PNG、GIF 格式，最大 10MB"})]}),s&&f.jsxs("div",{className:"mb-4",children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"预览效果"}),f.jsxs("div",{className:"relative",children:[f.jsx("img",{src:s,alt:"封面预览",className:"w-full h-64 object-cover rounded-lg border border-gray-200",style:{objectPosition:"center center"}}),f.jsx("div",{className:"mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md",children:f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:"flex-shrink-0",children:f.jsx("svg",{className:"h-5 w-5 text-blue-400",fill:"currentColor",viewBox:"0 0 20 20",children:f.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),f.jsxs("div",{className:"ml-3",children:[f.jsx("h4",{className:"text-sm font-medium text-blue-800",children:"💡 竖图优化建议"}),f.jsxs("p",{className:"mt-1 text-sm text-blue-700",children:["系统会自动使用 ",f.jsx("code",{children:"object-fit: cover"})," 来优化显示效果，确保图片在不同屏幕尺寸下都能美观展示。 竖图会自动居中裁剪，重点内容请放在图片中央区域。"]})]})]})})]})]}),s&&f.jsxs(f.Fragment,{children:[f.jsxs("div",{className:"mb-4",children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"标题 (可选)"}),f.jsx("input",{type:"text",value:l,onChange:y=>u(y.target.value),placeholder:"网站封面",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"})]}),f.jsxs("div",{className:"mb-6",children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"描述 (可选)"}),f.jsx("textarea",{value:d,onChange:y=>c(y.target.value),placeholder:"网站主页封面图片",rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500"})]}),f.jsxs("div",{className:"flex space-x-3",children:[f.jsx("button",{onClick:p,disabled:n,className:"flex-1 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors",children:n?"上传中...":"🚀 上传封面"}),f.jsx("button",{onClick:g,disabled:n,className:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors",children:"取消"})]})]})]})},Gy=({onUploadSuccess:t,onUploadError:e})=>{const[n,i]=P.useState(!1),[s,a]=P.useState(null),[l,u]=P.useState(""),[d,c]=P.useState(""),[m,h]=P.useState(""),p=y=>{var I;const x=(I=y.target.files)==null?void 0:I[0];if(!x)return;if(!x.type.startsWith("image/")){e("请选择图片文件");return}if(x.size>10*1024*1024){e("图片文件不能超过10MB");return}const T=new FileReader;T.onload=w=>{var _;const A=(_=w.target)==null?void 0:_.result;a(A)},T.readAsDataURL(x)},g=async()=>{if(!s){e("请先选择图片");return}if(!l.trim()){e("请输入广告标题");return}i(!0);try{const y=await oe.uploadAdvertisementCover({imageUrl:s,title:l.trim(),description:d.trim(),linkUrl:m.trim()||"#"});t(y),a(null),u(""),c(""),h("");const x=document.getElementById("ad-cover-input");x&&(x.value="")}catch(y){console.error("上传广告封面失败:",y),e(y instanceof Error?y.message:"上传失败")}finally{i(!1)}};return f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"📢 广告封面设置"}),f.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"设置侧边栏广告区域的封面图片和相关信息。"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"选择广告图片"}),f.jsx("input",{id:"ad-cover-input",type:"file",accept:"image/*",onChange:p,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"}),f.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"支持 JPG、PNG、GIF 格式，最大 10MB"})]}),s&&f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片预览"}),f.jsx("div",{className:"border border-gray-200 rounded-lg p-4 bg-gray-50",children:f.jsx("img",{src:s,alt:"广告预览",className:"max-w-full h-auto max-h-64 mx-auto rounded-md shadow-sm"})})]}),f.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"广告标题 *"}),f.jsx("input",{type:"text",value:l,onChange:y=>u(y.target.value),placeholder:"例如：Confused by Science?",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"广告描述"}),f.jsx("input",{type:"text",value:d,onChange:y=>c(y.target.value),placeholder:"例如：You're not alone. Try our new coffee mug.",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"点击链接"}),f.jsx("input",{type:"url",value:m,onChange:y=>h(y.target.value),placeholder:"例如：https://example.com/shop",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]})]}),f.jsx("div",{className:"flex justify-end",children:f.jsx("button",{onClick:g,disabled:n||!s||!l.trim(),className:`px-6 py-2 rounded-md font-medium transition-colors ${n||!s||!l.trim()?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:n?"上传中...":"上传广告封面"})})]})},Jy=({onUpdateSuccess:t,onUpdateError:e})=>{const[n,i]=P.useState("gemini"),[s,a]=P.useState(""),[l,u]=P.useState(""),[d,c]=P.useState("gemini-2.0-flash-exp"),[m,h]=P.useState("gemini"),[p,g]=P.useState(""),[y,x]=P.useState(""),[T,I]=P.useState("imagen-3.0-generate-001"),[w,A]=P.useState(!0),[_,C]=P.useState(!1),[E,N]=P.useState(!1),[D,b]=P.useState(null),[k,R]=P.useState(!1),[K,fe]=P.useState(!1),[z,X]=P.useState(null),[ee,Tt]=P.useState("gemini"),[be,St]=P.useState(""),[Me,fn]=P.useState("");P.useEffect(()=>{S()},[]),P.useEffect(()=>{n==="gemini"?(c("gemini-2.0-flash-exp"),u("")):n==="openai"&&(c("gpt-4o"),u("https://api.openai.com/v1"))},[n]),P.useEffect(()=>{m==="gemini"?(I("imagen-3.0-generate-001"),x("")):m==="openai"&&(I("dall-e-3"),x("https://api.openai.com/v1"))},[m]);const S=async()=>{try{const U=await oe.getAIConfig();b(U.config),U.config&&(U.config.textProvider&&U.config.imageProvider?(C(!0),i(U.config.textProvider),u(U.config.textBaseUrl||""),h(U.config.imageProvider),x(U.config.imageBaseUrl||"")):(C(!1),i(U.config.provider||"gemini"),u(U.config.baseUrl||""),h(U.config.provider||"gemini"),x(U.config.baseUrl||"")),A(U.config.isActive),c(U.config.textModel),I(U.config.imageModel))}catch(U){console.error("加载AI配置失败:",U)}},O=async U=>{if(U.preventDefault(),_){if(!s.trim()||!p.trim()){e("请输入文本和图片的API Key");return}if(n==="openai"&&!l.trim()){e("文本OpenAI格式需要提供基础URL");return}if(m==="openai"&&!y.trim()){e("图片OpenAI格式需要提供基础URL");return}}else{if(!be.trim()){e("请输入API Key");return}if(ee==="openai"&&!Me.trim()){e("OpenAI格式需要提供基础URL");return}}if(!d.trim()){e("请输入文本生成模型");return}if(!T.trim()){e("请输入图片生成模型");return}N(!0);try{const Ae=_?{textProvider:n,textApiKey:s.trim(),textBaseUrl:n==="openai"?l.trim():void 0,textModel:d.trim(),imageProvider:m,imageApiKey:p.trim(),imageBaseUrl:m==="openai"?y.trim():void 0,imageModel:T.trim(),isActive:w}:{provider:ee,apiKey:be.trim(),baseUrl:ee==="openai"?Me.trim():void 0,isActive:w,textModel:d.trim(),imageModel:T.trim()},ga=await oe.setAIConfig(Ae);t("AI API配置设置成功！"),b(ga.config),_?(a(""),g("")):St("")}catch(Ae){console.error("设置AI配置失败:",Ae),e(Ae instanceof Error?Ae.message:"设置AI配置失败")}finally{N(!1)}},H=async()=>{if(D){N(!0);try{const U=await oe.setAIConfig({provider:D.provider,apiKey:D.apiKeyPreview.replace("...",""),baseUrl:D.baseUrl,isActive:!w,textModel:D.textModel,imageModel:D.imageModel});A(!w),b(U.config),t(`AI配置已${w?"禁用":"启用"}`)}catch(U){console.error("切换AI配置状态失败:",U),e("切换状态失败")}finally{N(!1)}}},ge=async()=>{if(!D){X({success:!1,message:"请先保存配置后再测试"});return}fe(!0),X(null);try{go.resetConfig();const U=await go.testConnection();X(U),U.success?t(U.message):e(U.message)}catch(U){const Ae=U instanceof Error?U.message:"测试连接失败";X({success:!1,message:Ae,details:{error:U}}),e(Ae)}finally{fe(!1)}};return f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("h4",{className:"text-lg font-medium text-gray-900 mb-4",children:"🤖 AI API 配置管理"}),f.jsx("p",{className:"text-sm text-gray-600 mb-4",children:"设置自定义的AI API配置。支持Gemini和OpenAI格式的API。当设置了自定义配置时，系统将优先使用您提供的配置而不是服务器默认配置。"})]}),D&&f.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 border border-gray-200",children:[f.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-2",children:"当前配置"}),f.jsxs("div",{className:"space-y-2 text-sm",children:[f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-gray-600",children:"供应商:"}),f.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${D.provider==="gemini"?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800"}`,children:D.provider==="gemini"?"Google Gemini":"OpenAI"})]}),f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-gray-600",children:"API Key:"}),f.jsx("span",{className:"font-mono text-gray-900",children:D.apiKeyPreview})]}),D.baseUrl&&f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-gray-600",children:"基础URL:"}),f.jsx("span",{className:"font-mono text-gray-900 text-xs",children:D.baseUrl})]}),f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-gray-600",children:"状态:"}),f.jsx("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${D.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"}`,children:D.isActive?"已启用":"已禁用"})]}),f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-gray-600",children:"文本模型:"}),f.jsx("span",{className:"font-mono text-gray-900 text-sm",children:D.textModel})]}),f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-gray-600",children:"图片模型:"}),f.jsx("span",{className:"font-mono text-gray-900 text-sm",children:D.imageModel})]}),f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("span",{className:"text-gray-600",children:"更新时间:"}),f.jsx("span",{className:"text-gray-900",children:new Date(D.updatedAt).toLocaleString("zh-CN")})]})]}),f.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200 flex gap-2",children:[f.jsx("button",{onClick:H,disabled:E,className:`px-3 py-1 rounded-md text-sm font-medium transition-colors ${D.isActive?"bg-red-100 text-red-700 hover:bg-red-200":"bg-green-100 text-green-700 hover:bg-green-200"} disabled:opacity-50 disabled:cursor-not-allowed`,children:E?"处理中...":D.isActive?"禁用":"启用"}),f.jsx("button",{onClick:ge,disabled:K||!D.isActive,className:"px-3 py-1 rounded-md text-sm font-medium transition-colors bg-blue-100 text-blue-700 hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed",children:K?"测试中...":"🧪 测试连接"})]})]}),z&&f.jsx("div",{className:`rounded-lg p-4 border ${z.success?"bg-green-50 border-green-200":"bg-red-50 border-red-200"}`,children:f.jsxs("div",{className:"flex items-start",children:[f.jsx("div",{className:`flex-shrink-0 ${z.success?"text-green-400":"text-red-400"}`,children:z.success?f.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:f.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):f.jsx("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 20 20",children:f.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),f.jsxs("div",{className:"ml-3",children:[f.jsx("h3",{className:`text-sm font-medium ${z.success?"text-green-800":"text-red-800"}`,children:z.success?"✅ 连接测试成功":"❌ 连接测试失败"}),f.jsxs("div",{className:`mt-2 text-sm ${z.success?"text-green-700":"text-red-700"}`,children:[f.jsx("p",{children:z.message}),z.details&&f.jsxs("details",{className:"mt-2",children:[f.jsx("summary",{className:"cursor-pointer font-medium",children:"查看详细信息"}),f.jsx("pre",{className:"mt-1 text-xs bg-white bg-opacity-50 p-2 rounded border overflow-auto",children:JSON.stringify(z.details,null,2)})]})]})]})]})}),f.jsxs("form",{onSubmit:O,className:"space-y-4",children:[f.jsx("div",{children:f.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[D?"更新":"设置"," AI API配置"]})}),f.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"配置模式"}),f.jsxs("div",{className:"space-y-2",children:[f.jsxs("label",{className:"flex items-center",children:[f.jsx("input",{type:"radio",name:"configMode",checked:!_,onChange:()=>C(!1),className:"mr-2"}),f.jsx("span",{className:"text-sm",children:"统一配置（文本和图片使用相同供应商）"})]}),f.jsxs("label",{className:"flex items-center",children:[f.jsx("input",{type:"radio",name:"configMode",checked:_,onChange:()=>C(!0),className:"mr-2"}),f.jsx("span",{className:"text-sm",children:"分离配置（文本和图片使用不同供应商）"})]})]}),f.jsx("p",{className:"text-xs text-blue-600 mt-2",children:"💡 分离配置可以让您使用便宜的API生成文本，用高质量API生成图片"})]}),_?f.jsxs(f.Fragment,{children:[f.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[f.jsx("h4",{className:"text-sm font-medium text-green-800 mb-3",children:"📝 文本生成配置"}),f.jsx("div",{className:"space-y-3",children:f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"文本供应商"}),f.jsxs("select",{value:n,onChange:U=>i(U.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500",children:[f.jsx("option",{value:"gemini",children:"Google Gemini"}),f.jsx("option",{value:"openai",children:"OpenAI 格式"})]})]})})]}),f.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[f.jsx("h4",{className:"text-sm font-medium text-purple-800 mb-3",children:"🎨 图片生成配置"}),f.jsx("div",{className:"space-y-3",children:f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片供应商"}),f.jsxs("select",{value:m,onChange:U=>h(U.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500",children:[f.jsx("option",{value:"gemini",children:"Google Gemini"}),f.jsx("option",{value:"openai",children:"OpenAI 格式"})]})]})})]})]}):f.jsx(f.Fragment,{children:f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"AI 供应商"}),f.jsxs("select",{value:ee,onChange:U=>Tt(U.target.value),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500",children:[f.jsx("option",{value:"gemini",children:"Google Gemini"}),f.jsx("option",{value:"openai",children:"OpenAI 格式"})]}),f.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"选择您要使用的AI服务供应商（文本和图片共用）"})]})}),_?f.jsxs(f.Fragment,{children:[f.jsxs("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:[f.jsx("label",{className:"block text-sm font-medium text-green-800 mb-2",children:"📝 文本生成 API Key"}),f.jsxs("div",{className:"relative",children:[f.jsx("input",{type:k?"text":"password",value:s,onChange:U=>a(U.target.value),placeholder:n==="gemini"?"输入文本生成的Gemini API Key":"输入文本生成的OpenAI API Key",className:"w-full border border-green-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-green-500 font-mono text-sm"}),f.jsx("button",{type:"button",onClick:()=>R(!k),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:k?f.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):f.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),f.jsx("p",{className:"text-xs text-green-600 mt-1",children:"💰 推荐使用便宜的API供应商生成文本内容"})]}),f.jsxs("div",{className:"bg-purple-50 border border-purple-200 rounded-lg p-4",children:[f.jsx("label",{className:"block text-sm font-medium text-purple-800 mb-2",children:"🎨 图片生成 API Key"}),f.jsxs("div",{className:"relative",children:[f.jsx("input",{type:k?"text":"password",value:p,onChange:U=>g(U.target.value),placeholder:m==="gemini"?"输入图片生成的Gemini API Key":"输入图片生成的OpenAI API Key",className:"w-full border border-purple-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"}),f.jsx("button",{type:"button",onClick:()=>R(!k),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:k?f.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):f.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),f.jsx("p",{className:"text-xs text-purple-600 mt-1",children:"🎨 推荐使用高质量的API供应商生成精美图片"})]})]}):f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"API Key"}),f.jsxs("div",{className:"relative",children:[f.jsx("input",{type:k?"text":"password",value:be,onChange:U=>St(U.target.value),placeholder:ee==="gemini"?"输入您的Gemini API Key (例如: AIzaSy...)":"输入您的OpenAI API Key (例如: sk-...)",className:"w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),f.jsx("button",{type:"button",onClick:()=>R(!k),className:"absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:k?f.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"})}):f.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),f.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})})]}),f.jsx("p",{className:"text-xs text-gray-500 mt-1",children:ee==="gemini"?f.jsxs(f.Fragment,{children:["💡 您可以在 ",f.jsx("a",{href:"https://aistudio.google.com/app/apikey",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"Google AI Studio"})," 获取免费的API Key"]}):f.jsxs(f.Fragment,{children:["💡 您可以在 ",f.jsx("a",{href:"https://platform.openai.com/api-keys",target:"_blank",rel:"noopener noreferrer",className:"text-blue-600 hover:underline",children:"OpenAI Platform"})," 获取API Key，或使用兼容OpenAI格式的第三方API"]})})]}),ee==="openai"&&f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"基础URL"}),f.jsx("input",{type:"text",value:Me,onChange:U=>fn(U.target.value),placeholder:"例如: https://api.openai.com/v1",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),f.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"OpenAI API的基础URL，支持第三方兼容API"})]}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"文本生成模型"}),f.jsx("input",{type:"text",value:d,onChange:U=>c(U.target.value),placeholder:ee==="gemini"?"例如: gemini-2.0-flash-exp":"例如: gpt-4o",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),f.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"用于文章内容生成的模型"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"图片生成模型"}),f.jsx("input",{type:"text",value:T,onChange:U=>I(U.target.value),placeholder:ee==="gemini"?"例如: imagen-3.0-generate-001":"例如: dall-e-3",className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"}),f.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"用于图片生成的模型"})]})]}),f.jsxs("div",{className:"flex items-center",children:[f.jsx("input",{type:"checkbox",id:"isActive",checked:w,onChange:U=>A(U.target.checked),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),f.jsx("label",{htmlFor:"isActive",className:"ml-2 block text-sm text-gray-700",children:"立即启用此配置"})]}),f.jsx("div",{className:"flex justify-end",children:f.jsx("button",{type:"submit",disabled:E||!d.trim()||!T.trim()||(_?!s.trim()||!p.trim()||n==="openai"&&!l.trim()||m==="openai"&&!y.trim():!be.trim()||ee==="openai"&&!Me.trim()),className:`px-4 py-2 rounded-md font-medium transition-colors ${E||!d.trim()||!T.trim()||(_?!s.trim()||!p.trim()||n==="openai"&&!l.trim()||m==="openai"&&!y.trim():!be.trim()||ee==="openai"&&!Me.trim())?"bg-gray-300 text-gray-500 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}`,children:E?"设置中...":D?"更新配置":"设置配置"})})]}),f.jsxs("div",{className:"bg-blue-50 rounded-lg p-4 border border-blue-200",children:[f.jsx("h5",{className:"text-sm font-medium text-blue-900 mb-2",children:"📋 使用说明"}),f.jsxs("ul",{className:"text-sm text-blue-800 space-y-1",children:[f.jsx("li",{children:"• 支持Google Gemini和OpenAI格式的API"}),f.jsxs("li",{children:["• ",f.jsx("strong",{children:"🆕 支持分离配置：文本和图片可以使用不同的供应商和API Key"})]}),f.jsx("li",{children:"• 分离配置可以让您用便宜的API生成文本，用高质量API生成图片"}),f.jsx("li",{children:"• 设置自定义配置后，系统将优先使用您的配置进行AI生成"}),f.jsx("li",{children:"• 文本模型用于生成文章内容，图片模型用于生成文章配图"}),f.jsx("li",{children:"• OpenAI格式支持第三方兼容API（如Claude、通义千问等）"}),f.jsx("li",{children:"• 如果配置失效，系统会自动回退到服务器默认配置"}),f.jsx("li",{children:"• 您可以随时禁用自定义配置，系统将使用服务器默认设置"}),f.jsx("li",{children:"• 所有配置会安全存储，只有管理员可以查看和修改"})]})]})]})},Oy=({onUpdateSuccess:t,onUpdateError:e})=>{const[n,i]=P.useState("🤡"),[s,a]=P.useState(""),[l,u]=P.useState(!1),[d,c]=P.useState(!1),[m,h]=P.useState(!1),[p,g]=P.useState("emoji"),y=["🤡","🎭","🎪","🎨","🔬","📚","📖","📝","🧪","🔍","💡","🎯","🚀","⚡","🌟","💎","🎲","🎳","🎮","🎸","🎺","🎻","🎤","🎧"],x=async()=>{u(!0);try{const _=await(await fetch("/api/admin/journal-icon")).json();if(_.success){const C=_.data.icon;i(C),a(C),g(C.startsWith("data:image/")?"image":"emoji")}}catch(A){console.error("加载期刊图标失败:",A),e==null||e("加载期刊图标失败")}finally{u(!1)}},T=async()=>{if(!s.trim()){e==null||e("请输入或选择一个图标");return}c(!0);try{await oe.setJournalIcon(s.trim()),i(s.trim()),t==null||t("期刊图标更新成功！")}catch(A){console.error("保存期刊图标失败:",A),e==null||e("保存期刊图标失败")}finally{c(!1)}},I=()=>{a(n),g(n.startsWith("data:image/")?"image":"emoji")},w=async A=>{var C;const _=(C=A.target.files)==null?void 0:C[0];if(_){if(!_.type.startsWith("image/")){e==null||e("请选择图片文件");return}if(_.size>2*1024*1024){e==null||e("图片文件大小不能超过2MB");return}h(!0);try{const E=new FileReader;E.onload=N=>{var b;const D=(b=N.target)==null?void 0:b.result;a(D),g("image"),h(!1)},E.onerror=()=>{e==null||e("图片读取失败"),h(!1)},E.readAsDataURL(_)}catch(E){console.error("图片上传失败:",E),e==null||e("图片上传失败"),h(!1)}}};return P.useEffect(()=>{x()},[]),f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"期刊图标管理"}),f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx("span",{className:"text-sm text-gray-500",children:"当前图标:"}),f.jsx("span",{className:"text-2xl",children:n})]})]}),l?f.jsx("div",{className:"flex items-center justify-center py-8",children:f.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"选择图标类型"}),f.jsxs("div",{className:"flex space-x-4",children:[f.jsxs("label",{className:"flex items-center",children:[f.jsx("input",{type:"radio",value:"emoji",checked:p==="emoji",onChange:A=>g(A.target.value),className:"mr-2"}),f.jsx("span",{className:"text-sm text-gray-700",children:"Emoji 表情"})]}),f.jsxs("label",{className:"flex items-center",children:[f.jsx("input",{type:"radio",value:"image",checked:p==="image",onChange:A=>g(A.target.value),className:"mr-2"}),f.jsx("span",{className:"text-sm text-gray-700",children:"上传图片"})]})]})]}),p==="emoji"?f.jsxs(f.Fragment,{children:[f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"自定义 Emoji"}),f.jsxs("div",{className:"flex space-x-2",children:[f.jsx("input",{type:"text",value:s.startsWith("data:image/")?"":s,onChange:A=>a(A.target.value),placeholder:"输入 emoji...",className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),f.jsx("div",{className:"flex items-center justify-center w-12 h-10 border border-gray-300 rounded-md bg-gray-50",children:s&&!s.startsWith("data:image/")&&f.jsx("span",{className:"text-xl",children:s})})]})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"或选择预设 Emoji"}),f.jsx("div",{className:"grid grid-cols-8 gap-2",children:y.map((A,_)=>f.jsx("button",{onClick:()=>a(A),className:`w-12 h-12 flex items-center justify-center text-xl border-2 rounded-lg transition-colors ${s===A?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,children:A},_))})]})]}):f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"上传图标图片"}),f.jsxs("div",{className:"flex items-center space-x-4",children:[f.jsxs("div",{className:"flex-1",children:[f.jsx("input",{type:"file",accept:"image/*",onChange:w,disabled:m,className:"block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 disabled:opacity-50"}),f.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"支持 JPG、PNG、GIF 格式，建议尺寸 64x64px，文件大小不超过 2MB"})]}),f.jsx("div",{className:"flex items-center justify-center w-16 h-16 border border-gray-300 rounded-md bg-gray-50",children:m?f.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"}):s.startsWith("data:image/")?f.jsx("img",{src:s,alt:"图标预览",className:"w-12 h-12 object-contain rounded"}):f.jsx("span",{className:"text-xs text-gray-400",children:"预览"})})]})]}),f.jsxs("div",{className:"flex justify-end space-x-3 pt-4",children:[f.jsx("button",{onClick:I,disabled:d,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50",children:"重置"}),f.jsxs("button",{onClick:T,disabled:d||s===n,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2",children:[d&&f.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),f.jsx("span",{children:d?"保存中...":"保存图标"})]})]}),f.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:[f.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"使用说明"}),f.jsxs("ul",{className:"text-sm text-blue-800 space-y-1",children:[f.jsxs("li",{children:["• ",f.jsx("strong",{children:"Emoji 模式"}),"：可以使用任何 Emoji 表情作为期刊图标，如 🤡 🎭 🔬 等"]}),f.jsxs("li",{children:["• ",f.jsx("strong",{children:"图片模式"}),"：上传自定义图片作为期刊图标，建议使用正方形图片，尺寸 64x64px"]}),f.jsx("li",{children:"• 图标会显示在网站标题、PDF导出、浏览器标签页等位置"}),f.jsx("li",{children:"• 建议选择与期刊主题相关的图标，保持专业性"}),f.jsx("li",{children:"• 图片文件会转换为 base64 格式存储，无需外部链接"})]})]})]})]})},Hy=({onNavigateHome:t})=>{const[e,n]=P.useState([]),[i,s]=P.useState(!0),[a,l]=P.useState(null),[u,d]=P.useState(null),[c,m]=P.useState("articles"),[h,p]=P.useState(new Set),[g,y]=P.useState(!1),[x,T]=P.useState({}),[I,w]=P.useState([]),[A,_]=P.useState(!1);P.useEffect(()=>{C()},[]),P.useEffect(()=>{c==="users"&&fe()},[c]);const C=async()=>{try{s(!0);const S=await oe.getArticles({limit:50});n(S.data)}catch(S){l("获取文章列表失败"),console.error(S)}finally{s(!1)}},E=S=>{d({...S})},N=async()=>{if(u)try{const S=await wn.updateArticle(u.id,{title:u.title,author:u.author,category:u.category,excerpt:u.excerpt,content:u.content,published:u.published,featured:u.featured});n(O=>O.map(H=>H.id===u.id?S:H)),d(null),alert("文章保存成功！")}catch(S){alert("保存文章失败"),console.error(S)}},D=async S=>{if(confirm("确定要删除这篇文章吗？"))try{await wn.deleteArticle(S),n(O=>O.filter(H=>H.id!==S)),alert("文章删除成功！")}catch(O){alert("删除文章失败"),console.error(O)}},b=async()=>{if(h.size===0){alert("请先选择要删除的文章");return}if(confirm(`确定要删除选中的 ${h.size} 篇文章吗？此操作不可恢复！`)){y(!0);try{const S=Array.from(h);await oe.batchDeleteArticles(S),n(O=>O.filter(H=>!h.has(H.id))),p(new Set),alert(`成功删除 ${S.length} 篇文章！`)}catch(S){alert("批量删除失败"),console.error(S)}finally{y(!1)}}},k=S=>{p(O=>{const H=new Set(O);return H.has(S)?H.delete(S):H.add(S),H})},R=()=>{h.size===e.length?p(new Set):p(new Set(e.map(S=>S.id)))},K=async(S,O)=>{if(O<0){alert("观看次数不能为负数");return}try{await oe.updateArticleViews(S,O),n(H=>H.map(ge=>ge.id===S?{...ge,views:O}:ge)),T(H=>{const ge={...H};return delete ge[S],ge}),alert("观看次数更新成功！")}catch(H){alert("更新观看次数失败"),console.error(H)}},fe=async()=>{console.log("开始加载用户列表..."),console.log("adminApi:",oe),_(!0);try{const S=await oe.getAllUsers();console.log("用户列表响应:",S),w(S.users)}catch(S){console.error("加载用户列表失败:",S),alert("加载用户列表失败: "+(S instanceof Error?S.message:"未知错误"))}finally{_(!1)}},z=async(S,O,H)=>{try{switch(O){case"status":await oe.updateUserStatus(S,H);break;case"role":await oe.updateUserRole(S,H);break;case"delete":if(confirm("确定要删除这个用户吗？此操作不可恢复。"))await oe.deleteUser(S);else return;break}await fe(),alert("操作成功")}catch(ge){console.error("操作失败:",ge),alert("操作失败，请重试")}},X=S=>{switch(S){case"ADMIN":return"bg-red-100 text-red-800";case"EDITOR":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},ee=S=>{switch(S){case"ACTIVE":return"bg-green-100 text-green-800";case"DISABLED":return"bg-red-100 text-red-800";case"SUSPENDED":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},Tt=S=>new Date(S).toLocaleString("zh-CN"),be=()=>u?f.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:f.jsxs("div",{className:"bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto",children:[f.jsx("h3",{className:"text-xl font-bold mb-4",children:"编辑文章"}),f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"标题"}),f.jsx("input",{type:"text",value:u.title,onChange:S=>d({...u,title:S.target.value}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"作者"}),f.jsx("input",{type:"text",value:u.author,onChange:S=>d({...u,author:S.target.value}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"分类"}),f.jsx("input",{type:"text",value:u.category,onChange:S=>d({...u,category:S.target.value}),className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"摘要"}),f.jsx("textarea",{value:u.excerpt,onChange:S=>d({...u,excerpt:S.target.value}),rows:3,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),f.jsxs("div",{children:[f.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"内容"}),f.jsx("textarea",{value:u.content||"",onChange:S=>d({...u,content:S.target.value}),rows:8,className:"w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),f.jsxs("div",{className:"flex items-center space-x-4",children:[f.jsxs("label",{className:"flex items-center",children:[f.jsx("input",{type:"checkbox",checked:u.published,onChange:S=>d({...u,published:S.target.checked}),className:"mr-2"}),"已发布"]}),f.jsxs("label",{className:"flex items-center",children:[f.jsx("input",{type:"checkbox",checked:u.featured,onChange:S=>d({...u,featured:S.target.checked}),className:"mr-2"}),"推荐文章"]})]})]}),f.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[f.jsx("button",{onClick:()=>d(null),className:"px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"取消"}),f.jsx("button",{onClick:N,className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700",children:"保存"})]})]})}):null,St=()=>f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{className:"flex justify-between items-center",children:[f.jsx("h2",{className:"text-2xl font-bold",children:"文章管理"}),f.jsxs("div",{className:"flex space-x-2",children:[h.size>0&&f.jsx("button",{onClick:b,disabled:g,className:"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50",children:g?"删除中...":`删除选中 (${h.size})`}),f.jsx("button",{onClick:C,className:"px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700",children:"刷新列表"})]})]}),e.length>0&&f.jsxs("div",{className:"flex items-center space-x-4 bg-gray-50 p-3 rounded-md",children:[f.jsxs("label",{className:"flex items-center",children:[f.jsx("input",{type:"checkbox",checked:h.size===e.length&&e.length>0,onChange:R,className:"mr-2"}),"全选 (",e.length," 篇文章)"]}),h.size>0&&f.jsxs("span",{className:"text-sm text-gray-600",children:["已选择 ",h.size," 篇文章"]})]}),i?f.jsx("div",{className:"text-center py-8",children:"加载中..."}):a?f.jsx("div",{className:"text-center py-8 text-red-600",children:a}):f.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:f.jsx("ul",{className:"divide-y divide-gray-200",children:e.map(S=>f.jsx("li",{className:"px-6 py-4",children:f.jsxs("div",{className:"flex items-start gap-4",children:[f.jsx("div",{className:"flex-shrink-0 pt-1",children:f.jsx("input",{type:"checkbox",checked:h.has(S.id),onChange:()=>k(S.id),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"})}),f.jsxs("div",{className:"flex-1 min-w-0",children:[f.jsxs("div",{className:"flex items-start justify-between gap-2",children:[f.jsx("h3",{className:"text-lg font-medium text-gray-900 break-words flex-1",children:S.title}),f.jsxs("div",{className:"flex items-center space-x-2 flex-shrink-0",children:[S.published&&f.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800",children:"已发布"}),S.featured&&f.jsx("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800",children:"推荐"})]})]}),f.jsxs("div",{className:"mt-1 text-sm text-gray-600 flex items-center space-x-4",children:[f.jsxs("span",{children:["作者: ",S.author]}),f.jsxs("span",{children:["分类: ",S.category]}),f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx("span",{children:"浏览:"}),x[S.id]!==void 0?f.jsxs("div",{className:"flex items-center space-x-1",children:[f.jsx("input",{type:"number",value:x[S.id],onChange:O=>T(H=>({...H,[S.id]:parseInt(O.target.value)||0})),className:"w-20 px-1 py-0.5 text-xs border border-gray-300 rounded",min:"0"}),f.jsx("button",{onClick:()=>K(S.id,x[S.id]),className:"px-2 py-0.5 bg-green-600 text-white text-xs rounded hover:bg-green-700",children:"保存"}),f.jsx("button",{onClick:()=>T(O=>{const H={...O};return delete H[S.id],H}),className:"px-2 py-0.5 bg-gray-600 text-white text-xs rounded hover:bg-gray-700",children:"取消"})]}):f.jsx("button",{onClick:()=>T(O=>({...O,[S.id]:S.views||0})),className:"text-blue-600 hover:text-blue-800 underline",children:S.views||0})]}),f.jsxs("span",{children:["点赞: ",S.likes]})]}),f.jsx("p",{className:"mt-1 text-sm text-gray-500 line-clamp-2",children:S.excerpt})]}),f.jsxs("div",{className:"flex flex-col space-y-2 flex-shrink-0",children:[f.jsx("button",{onClick:()=>E(S),className:"px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 whitespace-nowrap",children:"编辑"}),f.jsx("button",{onClick:()=>D(S.id),className:"px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 whitespace-nowrap",children:"删除"})]})]})},S.id))})})]}),Me=()=>f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"用户管理"}),f.jsxs("div",{className:"text-sm text-gray-500",children:["共 ",I.length," 个用户"]})]}),A?f.jsx("div",{className:"flex items-center justify-center py-12",children:f.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})}):f.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-md",children:f.jsx("ul",{className:"divide-y divide-gray-200",children:I.map(S=>f.jsx("li",{className:"px-6 py-4",children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{className:"flex items-center space-x-4",children:[f.jsx("div",{className:"flex-shrink-0",children:f.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:f.jsx("span",{className:"text-sm font-medium text-gray-700",children:S.username.charAt(0).toUpperCase()})})}),f.jsxs("div",{className:"flex-1 min-w-0",children:[f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:S.username}),f.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${X(S.role)}`,children:S.role}),f.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${ee(S.status)}`,children:S.status})]}),f.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[f.jsx("p",{className:"text-sm text-gray-500",children:S.email}),f.jsxs("p",{className:"text-sm text-gray-500",children:["文章: ",S._count.articles]}),f.jsxs("p",{className:"text-sm text-gray-500",children:["注册: ",Tt(S.createdAt)]}),S.lastLogin&&f.jsxs("p",{className:"text-sm text-gray-500",children:["最后登录: ",Tt(S.lastLogin)]})]})]})]}),f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsxs("select",{value:S.status,onChange:O=>z(S.id,"status",O.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[f.jsx("option",{value:"ACTIVE",children:"激活"}),f.jsx("option",{value:"DISABLED",children:"禁用"}),f.jsx("option",{value:"SUSPENDED",children:"暂停"})]}),f.jsxs("select",{value:S.role,onChange:O=>z(S.id,"role",O.target.value),className:"text-sm border border-gray-300 rounded px-2 py-1",children:[f.jsx("option",{value:"USER",children:"用户"}),f.jsx("option",{value:"EDITOR",children:"编辑"}),f.jsx("option",{value:"ADMIN",children:"管理员"})]}),f.jsx("button",{onClick:()=>z(S.id,"delete"),className:"text-red-600 hover:text-red-800 text-sm px-2 py-1 border border-red-300 rounded hover:bg-red-50",children:"删除"})]})]})},S.id))})})]}),fn=()=>f.jsxs("div",{className:"space-y-6",children:[f.jsx("div",{className:"bg-white shadow rounded-lg",children:f.jsxs("div",{className:"px-4 py-5 sm:p-6",children:[f.jsx("h3",{className:"text-lg leading-6 font-medium text-gray-900 mb-4",children:"🌐 网站管理"}),f.jsx("p",{className:"text-sm text-gray-600 mb-6",children:"管理网站的外观和设置，包括主页封面图片等。"}),f.jsx(qy,{onUploadSuccess:S=>{console.log("封面上传成功:",S),alert("封面上传成功！刷新页面查看效果。")},onUploadError:S=>{console.error("封面上传失败:",S),alert("封面上传失败: "+S)}})]})}),f.jsx("div",{className:"bg-white shadow rounded-lg",children:f.jsx("div",{className:"px-4 py-5 sm:p-6",children:f.jsx(Gy,{onUploadSuccess:S=>{console.log("广告封面上传成功:",S),alert("广告封面上传成功！刷新页面查看效果。")},onUploadError:S=>{console.error("广告封面上传失败:",S),alert("广告封面上传失败: "+S)}})})}),f.jsx("div",{className:"bg-white shadow rounded-lg",children:f.jsx("div",{className:"px-4 py-5 sm:p-6",children:f.jsx(Oy,{onUpdateSuccess:S=>{console.log("期刊图标更新成功:",S),alert(S)},onUpdateError:S=>{console.error("期刊图标更新失败:",S),alert("期刊图标更新失败: "+S)}})})}),f.jsx("div",{className:"bg-white shadow rounded-lg",children:f.jsx("div",{className:"px-4 py-5 sm:p-6",children:f.jsx(Jy,{onUpdateSuccess:S=>{console.log("AI配置更新成功:",S),alert(S)},onUpdateError:S=>{console.error("AI配置更新失败:",S),alert("AI配置更新失败: "+S)}})})})]});return f.jsxs("div",{className:"min-h-screen bg-gray-50",children:[f.jsx("nav",{className:"bg-white shadow",children:f.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:f.jsxs("div",{className:"flex justify-between h-16",children:[f.jsx("div",{className:"flex items-center",children:f.jsx("button",{onClick:t,className:"text-xl font-bold text-gray-900 hover:text-gray-700",children:"← 返回首页"})}),f.jsx("div",{className:"flex items-center space-x-4",children:f.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"Jocker 管理后台"})})]})})}),f.jsxs("div",{className:"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8",children:[f.jsx("div",{className:"mb-6",children:f.jsxs("nav",{className:"flex space-x-8",children:[f.jsx("button",{onClick:()=>m("articles"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="articles"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"文章管理"}),f.jsx("button",{onClick:()=>m("users"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="users"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"用户管理"}),f.jsx("button",{onClick:()=>m("system"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="system"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"📊 系统监控"}),f.jsx("button",{onClick:()=>m("logs"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="logs"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"📋 日志管理"}),f.jsx("button",{onClick:()=>m("website"),className:`py-2 px-1 border-b-2 font-medium text-sm ${c==="website"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"网站管理"})]})}),c==="articles"?St():c==="users"?Me():c==="system"?f.jsx(Vy,{}):c==="logs"?f.jsx(By,{}):fn()]}),be()]})},rx=Object.freeze(Object.defineProperty({__proto__:null,AdminPage:Hy},Symbol.toStringTag,{value:"Module"})),Wy=({onNavigateHome:t,onLoginSuccess:e})=>{const[n,i]=P.useState(!1),[s,a]=P.useState(""),[l,u]=P.useState(""),[d,c]=P.useState(""),[m,h]=P.useState(""),[p,g]=P.useState(""),[y,x]=P.useState(""),[T,I]=P.useState(!1),w=async _=>{_.preventDefault(),I(!0),g(""),x("");try{if(n){if(l!==m){g("密码确认不匹配"),I(!1);return}if(l.length<6){g("密码长度至少为 6 位"),I(!1);return}const{token:C,user:E}=await An.register({email:s,password:l,username:d});x("注册成功！您现在是普通用户，可以浏览文章。"),localStorage.setItem("jocker_admin_token",C),localStorage.setItem("jocker_admin_logged_in","false"),localStorage.setItem("jocker_admin_user",JSON.stringify(E)),localStorage.setItem("jocker_user_role",E.role||"USER"),e(),setTimeout(()=>{t()},3e3)}else{const{token:C,user:E}=await An.login({email:s,password:l}),N=E.role==="ADMIN";localStorage.setItem("jocker_admin_token",C),localStorage.setItem("jocker_admin_logged_in",N?"true":"false"),localStorage.setItem("jocker_admin_user",JSON.stringify(E)),localStorage.setItem("jocker_user_role",E.role||"USER"),e(),N||(x("登录成功！您现在可以浏览文章。"),setTimeout(()=>{t()},2e3))}}catch(C){console.error(n?"注册失败:":"登录失败:",C),g(C instanceof Error?C.message:n?"注册失败，请重试":"登录失败，请重试")}finally{I(!1)}},A=()=>{i(!n),g(""),x(""),a(""),u(""),c(""),h("")};return f.jsxs("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[f.jsx("div",{className:"absolute top-4 left-4",children:f.jsx("button",{onClick:t,className:"inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500",children:"← 返回首页"})}),f.jsxs("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[f.jsxs("div",{className:"text-center",children:[f.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-2",children:"Joker"}),f.jsx("p",{className:"text-sm text-gray-600",children:"The Journal of Outrageous Claims and Kooky Experiments Research"})]}),f.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:n?"用户注册":"用户登录"}),f.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:n?"创建新账户以浏览文章和使用功能":"登录您的账户（管理员可访问后台管理）"})]}),f.jsx("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:f.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[f.jsxs("form",{className:"space-y-6",onSubmit:w,children:[n&&f.jsxs("div",{children:[f.jsx("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:"用户名"}),f.jsx("div",{className:"mt-1",children:f.jsx("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,value:d,onChange:_=>c(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:"请输入用户名"})})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:"邮箱"}),f.jsx("div",{className:"mt-1",children:f.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,value:s,onChange:_=>a(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:"请输入邮箱地址"})})]}),f.jsxs("div",{children:[f.jsx("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:"密码"}),f.jsx("div",{className:"mt-1",children:f.jsx("input",{id:"password",name:"password",type:"password",autoComplete:n?"new-password":"current-password",required:!0,value:l,onChange:_=>u(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:n?"请输入密码（至少6位）":"请输入密码"})})]}),n&&f.jsxs("div",{children:[f.jsx("label",{htmlFor:"confirmPassword",className:"block text-sm font-medium text-gray-700",children:"确认密码"}),f.jsx("div",{className:"mt-1",children:f.jsx("input",{id:"confirmPassword",name:"confirmPassword",type:"password",autoComplete:"new-password",required:!0,value:m,onChange:_=>h(_.target.value),className:"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-purple-500 focus:border-purple-500 sm:text-sm",placeholder:"请再次输入密码"})})]}),p&&f.jsx("div",{className:"rounded-md bg-red-50 p-4",children:f.jsx("div",{className:"flex",children:f.jsxs("div",{className:"ml-3",children:[f.jsx("h3",{className:"text-sm font-medium text-red-800",children:n?"注册失败":"登录失败"}),f.jsx("div",{className:"mt-2 text-sm text-red-700",children:f.jsx("p",{children:p})})]})})}),y&&f.jsx("div",{className:"rounded-md bg-green-50 p-4",children:f.jsx("div",{className:"flex",children:f.jsxs("div",{className:"ml-3",children:[f.jsx("h3",{className:"text-sm font-medium text-green-800",children:"成功"}),f.jsx("div",{className:"mt-2 text-sm text-green-700",children:f.jsx("p",{children:y})})]})})}),f.jsx("div",{children:f.jsx("button",{type:"submit",disabled:T,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed",children:T?n?"注册中...":"登录中...":n?"注册":"登录"})})]}),f.jsxs("div",{className:"mt-6",children:[f.jsxs("div",{className:"relative",children:[f.jsx("div",{className:"absolute inset-0 flex items-center",children:f.jsx("div",{className:"w-full border-t border-gray-300"})}),f.jsx("div",{className:"relative flex justify-center text-sm",children:f.jsx("span",{className:"px-2 bg-white text-gray-500",children:n?"已有账户？":"没有账户？"})})]}),f.jsx("div",{className:"mt-6 text-center",children:f.jsx("button",{type:"button",onClick:A,className:"text-purple-600 hover:text-purple-500 font-medium",children:n?"点击登录":"点击注册"})}),f.jsx("div",{className:"mt-4",children:f.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-md p-4",children:f.jsxs("div",{className:"flex",children:[f.jsx("div",{className:"flex-shrink-0",children:f.jsx("svg",{className:"h-5 w-5 text-blue-400",viewBox:"0 0 20 20",fill:"currentColor",children:f.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),f.jsxs("div",{className:"ml-3",children:[f.jsx("h3",{className:"text-sm font-medium text-blue-800",children:n?"注册说明":"登录说明"}),f.jsx("div",{className:"mt-2 text-sm text-blue-700",children:n?f.jsxs(f.Fragment,{children:[f.jsx("p",{children:"注册后您将成为普通用户，可以浏览所有文章"}),f.jsx("p",{children:"管理员账户可以生成和管理文章"})]}):f.jsxs(f.Fragment,{children:[f.jsx("p",{children:"管理员账户可以访问文章生成功能"}),f.jsx("p",{children:"普通用户可以浏览所有文章内容"})]})})]})]})})})]})]})})]})},ax=Object.freeze(Object.defineProperty({__proto__:null,LoginPage:Wy},Symbol.toStringTag,{value:"Module"}));export{rx as A,ax as L,Yy as R,wa as a,oe as b,go as c,wn as d,Ta as e,_a as f,f as j,qe as l,P as r};
