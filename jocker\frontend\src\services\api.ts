// API 客户端服务
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api';

// 调试信息
console.log('API_BASE_URL:', API_BASE_URL);
console.log('VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);

// API 响应类型
interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

// 文章类型（与后端保持一致）
export interface Article {
  id: number;
  title: string;
  author: string;
  category: string;
  excerpt: string;
  content?: string | null;
  imageUrl: string;
  imagePrompt?: string;
  published: boolean;
  featured: boolean;
  views: number;
  likes: number;
  createdAt: string;
  updatedAt: string;
  userId?: number | null;
}

// 分页响应类型
interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 通用请求函数
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
    },
  };

  const config = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, config);
    const data: ApiResponse<T> = await response.json();

    if (!data.success) {
      throw new Error(data.message || '请求失败');
    }

    return data.data as T;
  } catch (error) {
    console.error('API 请求失败:', error);
    throw error;
  }
}

// 带认证的请求函数
async function authenticatedRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<T> {
  const token = localStorage.getItem('jocker_admin_token');

  if (!token) {
    throw new Error('未找到认证 Token，请先登录');
  }

  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
  };

  const config = { ...defaultOptions, ...options };

  try {
    const response = await fetch(url, config);
    const data: ApiResponse<T> = await response.json();

    if (!data.success) {
      throw new Error(data.message || '请求失败');
    }

    return data.data as T;
  } catch (error) {
    console.error('认证请求失败:', error);
    throw error;
  }
}

// 文章相关 API
export const articleApi = {
  // 获取文章列表
  getArticles: async (params?: {
    page?: number;
    limit?: number;
    q?: string;
    category?: string;
    author?: string;
    published?: boolean;
    featured?: boolean;
  }): Promise<PaginatedResponse<Article>> => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, String(value));
        }
      });
    }
    
    const query = searchParams.toString();
    return apiRequest<PaginatedResponse<Article>>(
      `/articles${query ? `?${query}` : ''}`
    );
  },

  // 获取单篇文章
  getArticle: async (id: number): Promise<Article> => {
    return apiRequest<Article>(`/articles/${id}`);
  },

  // 获取热门文章
  getTrending: async (limit: number = 5): Promise<Article[]> => {
    return apiRequest<Article[]>(`/articles/trending?limit=${limit}`);
  },

  // 获取推荐文章
  getFeatured: async (limit: number = 3): Promise<Article[]> => {
    return apiRequest<Article[]>(`/articles/featured?limit=${limit}`);
  },

  // 点赞文章
  likeArticle: async (id: number): Promise<Article> => {
    return apiRequest<Article>(`/articles/${id}/like`, {
      method: 'POST',
    });
  },

  // 创建文章
  createArticle: async (articleData: {
    title: string;
    author: string;
    category: string;
    excerpt: string;
    imageUrl: string;
    imagePrompt?: string;
    published?: boolean;
    featured?: boolean;
  }): Promise<Article> => {
    return apiRequest<Article>('/articles', {
      method: 'POST',
      body: JSON.stringify(articleData),
    });
  },

  // 更新文章
  updateArticle: async (id: number, articleData: {
    title?: string;
    author?: string;
    category?: string;
    excerpt?: string;
    content?: string;
    imageUrl?: string;
    imagePrompt?: string;
    published?: boolean;
    featured?: boolean;
  }): Promise<Article> => {
    return apiRequest<Article>(`/articles/${id}`, {
      method: 'PUT',
      body: JSON.stringify(articleData),
    });
  },

  // 删除文章
  deleteArticle: async (id: number): Promise<void> => {
    return apiRequest<void>(`/articles/${id}`, {
      method: 'DELETE',
    });
  },
};

// AI 相关 API（仅保留同行评议功能）
export const aiApi = {

  // AI 生成同行评议
  generateReview: async (params: {
    author: string;
    title: string;
    abstract: string;
  }): Promise<{ review: string }> => {
    return apiRequest<{ review: string }>('/ai/generate-review', {
      method: 'POST',
      body: JSON.stringify(params),
    });
  },

  // 测试 Google AI API 连接
  testConnection: async (): Promise<{ testResult: string; timestamp: string }> => {
    return apiRequest<{ testResult: string; timestamp: string }>('/ai/test-connection', {
      method: 'GET',
    });
  },
};

// 认证相关 API
export const authApi = {
  // 用户注册
  register: async (userData: {
    email: string;
    password: string;
    username: string;
  }): Promise<{ token: string; user: any }> => {
    return apiRequest<{ token: string; user: any }>('/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  },

  // 用户登录
  login: async (credentials: {
    email: string;
    password: string;
  }): Promise<{ token: string; user: any }> => {
    return apiRequest<{ token: string; user: any }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
  },

  // 验证 Token
  verifyToken: async (): Promise<{ valid: boolean; user: any }> => {
    return authenticatedRequest<{ valid: boolean; user: any }>('/auth/verify');
  },

  // 获取用户信息
  getProfile: async (): Promise<any> => {
    return authenticatedRequest<any>('/auth/profile');
  },
};

// 管理员相关 API
export const adminApi = {
  // 获取文章列表（管理员专用，包含content字段）
  getArticles: async (params?: {
    page?: number;
    limit?: number;
    q?: string;
    category?: string;
    author?: string;
    published?: boolean;
    featured?: boolean;
  }): Promise<PaginatedResponse<Article>> => {
    const searchParams = new URLSearchParams();
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, String(value));
        }
      });
    }

    const query = searchParams.toString();
    return authenticatedRequest<PaginatedResponse<Article>>(
      `/admin/articles${query ? `?${query}` : ''}`
    );
  },

  // 获取 Google AI API Key（仅管理员）
  getAIApiKey: async (): Promise<{ apiKey: string }> => {
    return authenticatedRequest<{ apiKey: string }>('/admin/ai-key');
  },

  // 保存文章内容（仅管理员）
  saveArticleContent: async (articleId: number, content: string): Promise<{ success: boolean }> => {
    return authenticatedRequest<{ success: boolean }>(`/admin/articles/${articleId}/content`, {
      method: 'PUT',
      body: JSON.stringify({ content }),
    });
  },

  // 获取文章图片列表（仅管理员）
  getArticleFigures: async (articleId: number): Promise<{ figures: any[] }> => {
    return authenticatedRequest<{ figures: any[] }>(`/admin/articles/${articleId}/figures`);
  },

  // 重新生成图片（仅管理员）
  regenerateFigure: async (figureId: string): Promise<{ figureId: string }> => {
    return authenticatedRequest<{ figureId: string }>(`/admin/figures/${figureId}/regenerate`, {
      method: 'POST',
    });
  },

  // 保存图片（仅管理员）
  saveFigure: async (figureData: any): Promise<{ figureId: string }> => {
    return authenticatedRequest<{ figureId: string }>('/admin/figures', {
      method: 'POST',
      body: JSON.stringify(figureData),
    });
  },

  // 清理文章的所有图片记录（仅管理员）
  clearArticleFigures: async (articleId: number): Promise<{ deletedCount: number }> => {
    return authenticatedRequest<{ deletedCount: number }>(`/admin/articles/${articleId}/figures`, {
      method: 'DELETE',
    });
  },

  // 上传创始人头像（仅管理员）
  uploadFounderAvatar: async (avatarData: {
    avatarUrl: string;
    name?: string;
    title?: string;
    description?: string;
  }): Promise<{ founder: any }> => {
    return authenticatedRequest<{ founder: any }>('/admin/founder-avatar', {
      method: 'POST',
      body: JSON.stringify(avatarData),
    });
  },

  // 获取创始人头像（所有人可访问）
  getFounderAvatar: async (): Promise<{ founder: any }> => {
    return apiRequest<{ founder: any }>('/admin/founder-avatar');
  },

  // 上传广告封面（仅管理员）
  uploadAdvertisementCover: async (adData: {
    imageUrl: string;
    title: string;
    description?: string;
    linkUrl?: string;
  }): Promise<{ advertisement: any }> => {
    return authenticatedRequest<{ advertisement: any }>('/admin/advertisement-cover', {
      method: 'POST',
      body: JSON.stringify(adData),
    });
  },

  // 获取广告封面（所有人可访问）
  getAdvertisementCover: async (): Promise<{ advertisement: any }> => {
    return apiRequest<{ advertisement: any }>('/admin/advertisement-cover');
  },

  // 设置AI API配置（仅管理员）
  setAIConfig: async (configData: {
    provider: 'gemini' | 'openai';
    apiKey: string;
    baseUrl?: string;
    isActive: boolean;
    textModel: string;
    imageModel: string;
  }): Promise<{ config: any }> => {
    return authenticatedRequest<{ config: any }>('/admin/ai-config', {
      method: 'POST',
      body: JSON.stringify(configData),
    });
  },

  // 获取AI API配置（仅管理员）
  getAIConfig: async (): Promise<{ config: any }> => {
    return authenticatedRequest<{ config: any }>('/admin/ai-config');
  },

  // 获取AI API配置用于前端调用（包含完整API Key）
  getAIConfigForClient: async (): Promise<{ config: any }> => {
    return authenticatedRequest<{ config: any }>('/admin/ai-config-for-client');
  },

  // 更改密码
  changePassword: async (data: { currentPassword: string; newPassword: string }): Promise<any> => {
    return authenticatedRequest<any>('/admin/change-password', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // 设置Gemini API Key（仅管理员）- 向后兼容
  setGeminiKey: async (keyData: {
    apiKey: string;
    isActive: boolean;
    textModel: string;
    imageModel: string;
  }): Promise<{ config: any }> => {
    return authenticatedRequest<{ config: any }>('/admin/gemini-key', {
      method: 'POST',
      body: JSON.stringify(keyData),
    });
  },

  // 获取Gemini API Key配置（仅管理员）- 向后兼容
  getGeminiKey: async (): Promise<{ config: any }> => {
    return authenticatedRequest<{ config: any }>('/admin/gemini-key');
  },

  // 用户管理相关 API（仅管理员）
  getAllUsers: async (): Promise<{ users: any[] }> => {
    return authenticatedRequest<{ users: any[] }>('/admin/users');
  },

  updateUserStatus: async (userId: number, status: string): Promise<{ user: any }> => {
    return authenticatedRequest<{ user: any }>(`/admin/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    });
  },

  updateUserRole: async (userId: number, role: string): Promise<{ user: any }> => {
    return authenticatedRequest<{ user: any }>(`/admin/users/${userId}/role`, {
      method: 'PUT',
      body: JSON.stringify({ role }),
    });
  },

  deleteUser: async (userId: number): Promise<{ deletedUser: any }> => {
    return authenticatedRequest<{ deletedUser: any }>(`/admin/users/${userId}`, {
      method: 'DELETE',
    });
  },

  // 批量删除文章
  batchDeleteArticles: async (articleIds: number[]): Promise<{ deletedCount: number }> => {
    return authenticatedRequest<{ deletedCount: number }>('/articles/batch', {
      method: 'DELETE',
      body: JSON.stringify({ articleIds }),
    });
  },

  // 更新文章观看次数
  updateArticleViews: async (articleId: number, views: number): Promise<{ id: number; title: string; views: number }> => {
    return authenticatedRequest<{ id: number; title: string; views: number }>(`/articles/${articleId}/views`, {
      method: 'PUT',
      body: JSON.stringify({ views }),
    });
  },
};

// 音频相关API
export const audioApi = {
  // 获取文章音频信息
  getArticleAudio: async (articleId: number): Promise<any> => {
    const response = await fetch(`${API_BASE_URL}/audio/articles/${articleId}`);
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message || '获取音频信息失败');
    }
    return result.data;
  },

  // 删除文章音频
  deleteArticleAudio: async (articleId: number): Promise<void> => {
    return authenticatedRequest<void>(`/audio/articles/${articleId}`, {
      method: 'DELETE',
    });
  },

  // 更新音频信息
  updateArticleAudio: async (articleId: number, data: { title?: string; description?: string; duration?: number }): Promise<any> => {
    return authenticatedRequest<any>(`/audio/articles/${articleId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },
};

// 系统监控相关API
export const systemApi = {
  // 获取系统统计信息
  getSystemStats: async (): Promise<any> => {
    return authenticatedRequest<any>('/system/stats');
  },

  // 获取存储分析
  getStorageAnalysis: async (): Promise<any> => {
    return authenticatedRequest<any>('/system/storage');
  },

  // 清理存储
  cleanupStorage: async (): Promise<any> => {
    return authenticatedRequest<any>('/system/cleanup', {
      method: 'POST',
    });
  },
};

// 日志管理相关API
export const logApi = {
  // 获取系统日志列表
  getSystemLogs: async (params?: { page?: number; limit?: number; type?: string }): Promise<any> => {
    const queryParams = new URLSearchParams();
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.limit) queryParams.append('limit', params.limit.toString());
    if (params?.type) queryParams.append('type', params.type);

    const url = `/logs${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    const response = await fetch(`${API_BASE_URL}${url}`);
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message || '获取日志失败');
    }
    return result.data;
  },

  // 创建系统日志
  createSystemLog: async (data: {
    version: string;
    type: 'major' | 'minor' | 'patch' | 'hotfix';
    title?: string;
    description?: string;
    changes: string[];
  }): Promise<any> => {
    return authenticatedRequest<any>('/logs', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  },

  // 更新系统日志
  updateSystemLog: async (id: string, data: any): Promise<any> => {
    return authenticatedRequest<any>(`/logs/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  },

  // 删除系统日志
  deleteSystemLog: async (id: string): Promise<any> => {
    return authenticatedRequest<any>(`/logs/${id}`, {
      method: 'DELETE',
    });
  },

  // 获取日志统计
  getLogStats: async (): Promise<any> => {
    const response = await fetch(`${API_BASE_URL}/logs/stats`);
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message || '获取日志统计失败');
    }
    return result.data;
  },

  // 导入历史日志
  importHistoryLogs: async (logs: any[]): Promise<any> => {
    return authenticatedRequest<any>('/logs/import', {
      method: 'POST',
      body: JSON.stringify({ logs }),
    });
  },

  // 上传网站封面
  uploadWebsiteCover: async (coverData: { coverUrl: string; title?: string; description?: string }): Promise<any> => {
    return authenticatedRequest<any>('/admin/website-cover', {
      method: 'POST',
      body: JSON.stringify(coverData),
    });
  },

  // 获取网站封面
  getWebsiteCover: async (): Promise<any> => {
    const response = await fetch(`${API_BASE_URL}/admin/website-cover`);
    const result = await response.json();
    if (!result.success) {
      throw new Error(result.message || '获取网站封面失败');
    }
    return result.data;
  },
};

// 健康检查
export const healthCheck = async (): Promise<any> => {
  return apiRequest('/health');
};

// 统一的 API 对象
export const api = {
  ...articleApi,
  ...aiApi,
  ...authApi,
  ...adminApi,
  ...audioApi,
  ...systemApi,
  ...logApi,
  healthCheck
};
