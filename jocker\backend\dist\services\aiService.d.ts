import { Article, AIGenerateArticlesRequest, AIGenerateContentRequest } from '../types';
export declare const generateArticles: (request: AIGenerateArticlesRequest) => Promise<Article[]>;
export declare const generateSummary: (articleId: number) => Promise<string>;
export declare const generateReview: (request: AIGenerateContentRequest) => Promise<string>;
export declare const generateContent: (article: any) => Promise<string>;
export declare const generateArticleContentWithFigures: (article: any) => Promise<string>;
export declare const generateArticleContent: (request: AIGenerateContentRequest) => Promise<string>;
export declare const generatePeerReview: (author: string, title: string, abstract: string) => Promise<string>;
//# sourceMappingURL=aiService.d.ts.map