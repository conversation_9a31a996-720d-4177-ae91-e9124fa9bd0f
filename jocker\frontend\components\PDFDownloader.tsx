import React, { useState } from 'react';
import { Article } from '../types';

interface PDFDownloaderProps {
  article: Article;
  content: string;
  journalIcon?: string;
}

// 生成DOI格式的文件名
const generateDOI = (article: Article): string => {
  const year = new Date(article.createdAt).getFullYear();
  const month = String(new Date(article.createdAt).getMonth() + 1).padStart(2, '0');
  const day = String(new Date(article.createdAt).getDate()).padStart(2, '0');

  // 清理标题，移除特殊字符，保留字母数字和空格
  const cleanTitle = article.title
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/\s+/g, '-') // 空格替换为连字符
    .toLowerCase()
    .substring(0, 50); // 限制长度

  return `10.1000_joker.${year}.${month}.${day}.${article.id}.${cleanTitle}`;
};

export const PDFDownloader: React.FC<PDFDownloaderProps> = ({ article, content, journalIcon = '🤡' }) => {
  const [isGenerating, setIsGenerating] = useState(false);

  const generatePDF = async () => {
    setIsGenerating(true);

    try {
      // 使用浏览器的打印功能生成 PDF，这样可以保留所有图片和公式
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('无法打开打印窗口，请检查浏览器弹窗设置');
      }

      // 找到文章内容的主要容器
      const articleElement = document.querySelector('article');
      if (!articleElement) {
        throw new Error('找不到文章内容');
      }

      // 生成包含完整内容的 HTML
      const pdfHTML = generatePrintHTML(articleElement);

      printWindow.document.write(pdfHTML);
      printWindow.document.close();

      // 等待内容加载完成
      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          // 不自动关闭窗口，让用户选择保存为 PDF
        }, 1000);
      };

    } catch (error) {
      console.error('PDF 生成失败:', error);
      alert(`PDF 生成失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsGenerating(false);
    }
  };

  // 生成包含完整内容的打印 HTML
  const generatePrintHTML = (articleElement: Element): string => {
    // 克隆文章内容
    const clonedContent = articleElement.cloneNode(true) as HTMLElement;

    // 移除不需要的元素（包括各种图标和按钮）
    const elementsToRemove = clonedContent.querySelectorAll(`
      button,
      .admin-actions,
      .no-print,
      .regenerate-button,
      svg,
      .download-icon,
      .expand-icon,
      .zoom-icon,
      .success-icon,
      .loading-icon,
      .icon,
      [class*="icon"],
      [class*="Icon"],
      .cursor-pointer,
      .hover\\:scale-105,
      .transition-transform,
      footer,
      .footer
    `);
    elementsToRemove.forEach(el => el.remove());

    // 移除音频播放器组件（更精确的选择）
    const audioPlayers = clonedContent.querySelectorAll('.no-print, [class*="audio-player"]');
    audioPlayers.forEach(el => el.remove());

    // 移除包含特定音频播放器文本的元素，但要更精确
    const audioElements = clonedContent.querySelectorAll('*');
    audioElements.forEach(el => {
      const text = el.textContent?.trim() || '';
      const elementClasses = el.className || '';

      // 只移除明确的音频播放器相关元素
      if (
        // 包含音频播放器图标和控制文本的元素
        (text.includes('🎧') && text.includes('播客时长')) ||
        // 音频上传提示文本
        (text === '添加播客音频' || text === '为这篇文章添加音频播客，让读者可以收听内容') ||
        // 包含音频播放器类名的元素
        elementClasses.includes('audio-player') ||
        elementClasses.includes('no-print') ||
        // 音频控制按钮
        (el.tagName === 'BUTTON' && (text.includes('播放') || text.includes('暂停') || text.includes('下载音频'))) ||
        // 音频进度条
        (el.tagName === 'INPUT' && el.getAttribute('type') === 'range') ||
        // 音频元素本身
        el.tagName === 'AUDIO'
      ) {
        el.remove();
      }
    });

    // 移除 JournalHeader 组件（避免与 PDF 页眉重复）
    const journalHeaders = clonedContent.querySelectorAll('*');
    journalHeaders.forEach(el => {
      const text = el.textContent?.trim() || '';
      // 检查是否是期刊标题组件
      if (text.includes('Joker: Journal of Satirical Science') &&
          text.includes('Volume') &&
          text.includes('Issue') &&
          !text.includes('Systematic Approaches')) { // 避免误删文章标题
        // 移除整个期刊标题容器
        let elementToRemove = el;
        // 尝试找到包含期刊信息的父容器
        while (elementToRemove.parentElement &&
               elementToRemove.parentElement !== clonedContent) {
          const parentText = elementToRemove.parentElement.textContent || '';
          if (parentText.includes('DOI:') && parentText.includes('ISSN:')) {
            elementToRemove = elementToRemove.parentElement;
            break;
          }
          elementToRemove = elementToRemove.parentElement;
        }
        elementToRemove.remove();
      }
    });

    // 移除页脚相关内容（Cite This Article, About Joker 等）
    const footerSections = clonedContent.querySelectorAll('*');
    footerSections.forEach(el => {
      const text = el.textContent?.trim() || '';
      // 移除包含特定文本的元素
      if (text.includes('Cite This Article') ||
          text.includes('About Joker') ||
          text.includes('Editorial Board') ||
          text.includes('Editor-in-Chief') ||
          text.includes('Associate Editor') ||
          text.includes('Managing Editor') ||
          text.includes('No actual science was harmed') ||
          text.includes('Dr. Serious McFunnyface') ||
          text.includes('Prof. Witty Researcher') ||
          text.includes('Dr. Academic Humor') ||
          text.includes('© 2025 Joker: Journal of Satirical Science') ||
          (text.includes('Retrieved') && text.includes('from http://joker-journal.com'))) {
        // 移除整个包含元素
        let elementToRemove = el;
        // 尝试找到更大的容器
        while (elementToRemove.parentElement &&
               elementToRemove.parentElement !== clonedContent &&
               elementToRemove.parentElement.children.length === 1) {
          elementToRemove = elementToRemove.parentElement;
        }
        elementToRemove.remove();
      }
    });

    // 移除 JournalFooter 组件
    const journalFooters = clonedContent.querySelectorAll('.bg-gray-50, .border-t');
    journalFooters.forEach(footer => {
      const footerText = footer.textContent || '';
      if (footerText.includes('Cite This Article') ||
          footerText.includes('About Joker') ||
          footerText.includes('Editorial Board')) {
        footer.remove();
      }
    });

    // 移除所有内联 SVG 元素
    const svgElements = clonedContent.querySelectorAll('svg');
    svgElements.forEach(el => el.remove());

    // 移除原始LaTeX源码文本，保留渲染后的公式
    const textNodes = [];
    const walker = document.createTreeWalker(
      clonedContent,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );

    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node);
    }

    textNodes.forEach(textNode => {
      const text = textNode.textContent || '';
      // 检查是否包含LaTeX公式标记
      if (text.includes('$$') || text.includes('\\(') || text.includes('\\[')) {
        // 检查父元素是否已经被KaTeX渲染
        const parent = textNode.parentElement;
        if (parent && !parent.classList.contains('katex') && !parent.classList.contains('katex-display')) {
          // 这是未渲染的LaTeX源码，移除它
          const cleanedText = text
            .replace(/\$\$[\s\S]*?\$\$/g, '') // 移除块级公式
            .replace(/\$[^$]*\$/g, '') // 移除行内公式
            .replace(/\\[\[\(][\s\S]*?\\[\]\)]/g, '') // 移除其他LaTeX标记
            .trim();

          if (cleanedText) {
            textNode.textContent = cleanedText;
          } else {
            // 如果清理后没有内容，移除整个文本节点
            textNode.remove();
          }
        }
      }
    });

    // 移除 Peer Review Badge 组件
    const peerReviewBadges = clonedContent.querySelectorAll('.bg-green-100, .text-green-800');
    peerReviewBadges.forEach(el => {
      if (el.textContent?.includes('Peer Reviewed')) {
        el.remove();
      }
    });

    // 移除包含 "Peer Reviewed" 文本的元素
    const peerReviewTexts = clonedContent.querySelectorAll('*');
    peerReviewTexts.forEach(el => {
      if (el.textContent?.trim() === 'Peer Reviewed' && el.tagName !== 'BODY') {
        el.remove();
      }
    });

    // 移除包含分类信息的元素（如 "Neurofashion Studies"），但要更精确
    const categoryElements = clonedContent.querySelectorAll('*');
    categoryElements.forEach(el => {
      const text = el.textContent?.trim();
      // 只移除明确的分类标签，避免误删文章内容
      if (text &&
          (text === 'Neurofashion Studies' ||
           text === 'Satirical Science' ||
           text === 'Academic Humor' ||
           text === 'Peer Review Studies') &&
          el.children.length === 0 &&
          el.tagName !== 'BODY' &&
          el.tagName !== 'H1' &&
          el.tagName !== 'H2' &&
          el.tagName !== 'H3') {
        // 这是分类标签，移除它
        el.remove();
      }
    });

    // 移除所有可能的图标容器
    const iconContainers = clonedContent.querySelectorAll('.w-4, .w-5, .w-6, .w-8, .w-12, .h-4, .h-5, .h-6, .h-8, .h-12');
    iconContainers.forEach(el => {
      // 如果容器只包含图标相关内容，则移除
      if (el.textContent?.trim() === '' || el.querySelector('svg')) {
        el.remove();
      }
    });

    // 移除独立的 Figures 部分（在文章末尾的图片画廊）
    // 方法1: 查找所有 h2 标题为 "Figures" 的元素
    const figuresTitles = clonedContent.querySelectorAll('h2');
    figuresTitles.forEach(title => {
      if (title.textContent?.trim() === 'Figures') {
        // 移除标题和其父容器
        const container = title.closest('div');
        if (container) {
          container.remove();
        } else {
          title.remove();
        }
      }
    });

    // 方法2: 查找包含图片网格的容器
    const gridContainers = clonedContent.querySelectorAll('.grid');
    gridContainers.forEach(grid => {
      // 如果网格包含图片卡片，则可能是图片画廊
      if (grid.querySelector('.bg-white.rounded-lg.shadow-sm') ||
          grid.querySelector('[class*="grid-cols"]')) {
        // 检查是否在 Figures 部分
        const parentDiv = grid.closest('div');
        if (parentDiv && parentDiv.querySelector('h2')) {
          const h2 = parentDiv.querySelector('h2');
          if (h2?.textContent?.includes('Figures')) {
            parentDiv.remove();
          }
        }
      }
    });

    // 方法3: 移除包含 "Figure 1", "Figure 2" 等文本的卡片容器
    const figureCards = clonedContent.querySelectorAll('.bg-white.rounded-lg.shadow-sm');
    figureCards.forEach(card => {
      const figureText = card.textContent;
      if (figureText?.includes('Figure ') && figureText?.includes('Ready')) {
        // 这是图片卡片，移除它
        card.remove();
      }
    });

    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // 生成DOI格式的文件名
    const doiFileName = generateDOI(article);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="UTF-8">
        <title>${doiFileName}</title>
        <style>
          @page {
            size: A4;
            margin: 2cm;
          }

          @media print {
            body {
              font-family: 'Times New Roman', serif;
              font-size: 12pt;
              line-height: 1.6;
              color: #000;
              background: white;
            }

            /* 隐藏所有不需要的元素 */
            .no-print,
            button,
            .admin-actions,
            svg,
            .download-icon,
            .expand-icon,
            .zoom-icon,
            .success-icon,
            .loading-icon,
            .icon,
            [class*="icon"],
            [class*="Icon"],
            .cursor-pointer,
            .hover\\:scale-105,
            .transition-transform,
            .w-4, .w-5, .w-6, .w-8, .w-12,
            .h-4, .h-5, .h-6, .h-8, .h-12 {
              display: none !important;
            }

            /* 特别隐藏可能的图标容器 */
            div:empty,
            span:empty {
              display: none !important;
            }

            h1, h2, h3 {
              page-break-after: avoid;
              color: #000;
            }

            .figure-container {
              page-break-inside: avoid;
              margin: 20px 0;
            }

            .figure-container img {
              max-width: 100%;
              height: auto;
              display: block;
              margin: 0 auto;
            }

            .math-display, .MathJax_Display {
              page-break-inside: avoid;
              margin: 15px 0;
            }

            .page-break {
              page-break-before: always;
            }

            /* 强制 Article Metrics 横向布局 */
            .grid {
              display: grid !important;
            }

            .grid-cols-2,
            .grid-cols-4,
            .md\\:grid-cols-4 {
              grid-template-columns: repeat(4, 1fr) !important;
            }

            .gap-4 {
              gap: 1rem !important;
            }

            .text-center {
              text-align: center !important;
            }
          }

          body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #000;
            max-width: 100%;
            margin: 0;
            padding: 20px;
            background: white;
          }

          /* 在所有情况下都隐藏这些元素 */
          .no-print,
          button,
          .admin-actions,
          svg,
          .download-icon,
          .expand-icon,
          .zoom-icon,
          .success-icon,
          .loading-icon,
          .icon,
          [class*="icon"]:not(.figure-container *),
          [class*="Icon"]:not(.figure-container *),
          .cursor-pointer:not(.figure-container *),
          .hover\\:scale-105,
          .transition-transform:not(.figure-container *),
          /* 隐藏音频播放器相关元素 */
          audio,
          .audio-player,
          [class*="audio"],
          input[type="range"] {
            display: none !important;
          }

          /* 隐藏空的容器 */
          div:empty:not(.figure-container),
          span:empty:not(.figure-container) {
            display: none !important;
          }

          .pdf-header {
            text-align: center;
            border-bottom: 1px solid #E5E7EB;
            padding: 10px 0 15px 0;
            margin-bottom: 25px;
            background-color: #F9FAFB;
            border-radius: 4px 4px 0 0;
          }

          .journal-title {
            font-size: 14pt;
            font-weight: 600;
            color: #7C3AED;
            margin: 0;
            position: relative;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .journal-icon {
            font-size: 16pt;
            opacity: 0.8;
          }

          .journal-info {
            font-size: 8pt;
            color: #666;
            margin: 2px 0;
            line-height: 1.2;
          }

          .article-content {
            margin-top: 20px;
          }

          /* 文章标题应该是最大的 */
          .article-content h1:first-of-type,
          .article-content .text-4xl,
          .article-content .text-3xl {
            font-size: 24pt !important;
            color: #1F2937;
            margin: 20px 0 15px 0;
            text-align: center;
            font-weight: bold;
          }

          .article-content h1 {
            font-size: 18pt;
            color: #1F2937;
            margin: 20px 0 15px 0;
          }

          .article-content h2 {
            font-size: 14pt;
            color: #1F2937;
            margin: 20px 0 10px 0;
            border-bottom: 1px solid #E5E7EB;
            padding-bottom: 5px;
          }

          .article-content h3 {
            font-size: 12pt;
            color: #374151;
            margin: 15px 0 8px 0;
          }

          .article-content p {
            margin: 8px 0;
            text-align: justify;
          }

          /* 强制 Article Metrics 横向布局 */
          .grid {
            display: grid !important;
          }

          .grid-cols-2 {
            grid-template-columns: repeat(2, 1fr) !important;
          }

          .grid-cols-4,
          .md\\:grid-cols-4 {
            grid-template-columns: repeat(4, 1fr) !important;
          }

          .gap-4 {
            gap: 1rem !important;
          }

          .text-center {
            text-align: center !important;
          }

          /* Article Metrics 特定样式 */
          .article-content .grid.grid-cols-2 {
            grid-template-columns: repeat(4, 1fr) !important;
          }

          .article-content img {
            max-width: 90%;
            height: auto;
            display: block;
            margin: 15px auto;
            border: 1px solid #E5E7EB;
            border-radius: 4px;
          }

          .figure-container {
            margin: 20px 0;
            text-align: center;
            page-break-inside: avoid;
          }

          .figure-container p {
            font-size: 10pt;
            color: #6B7280;
            font-style: italic;
            margin-top: 8px;
          }

          .abstract-box {
            background-color: #F9FAFB;
            border-left: 4px solid #7C3AED;
            padding: 15px;
            margin: 20px 0;
            page-break-inside: avoid;
          }

          .citation-box {
            background-color: #F3F4F6;
            border: 1px solid #D1D5DB;
            padding: 15px;
            margin: 20px 0;
            page-break-inside: avoid;
          }

          .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #E5E7EB;
            text-align: center;
            font-size: 9pt;
            color: #6B7280;
          }

          /* 确保 MathJax 公式正确显示 */
          .MathJax {
            font-size: inherit !important;
          }

          .MathJax_Display {
            margin: 10px 0 !important;
          }

          /* 确保上标样式在PDF中正确显示 */
          sup {
            vertical-align: super !important;
            font-size: 0.75em !important;
            line-height: 0 !important;
          }

          /* 确保参考文献序号在PDF中显示 */
          .space-y-4 > div {
            display: flex !important;
            margin-bottom: 1rem !important;
          }

          .flex-shrink-0 {
            flex-shrink: 0 !important;
            width: 2rem !important;
          }

          .flex-1 {
            flex: 1 !important;
          }

          /* 防止引用链接在PDF中断行 */
          sup a {
            white-space: nowrap !important;
            text-decoration: none !important;
            color: #2563eb !important;
          }

          /* 确保参考文献编号不会被隐藏 */
          [id^="ref-"] {
            page-break-inside: avoid !important;
          }
        </style>
      </head>
      <body>
        <!-- PDF Header -->
        <div class="pdf-header">
          <h1 class="journal-title">
            <span class="journal-icon">${journalIcon}</span>
            Jocker: Journal of Satirical Science
          </h1>
          <p class="journal-info">
            Volume ${new Date().getFullYear()}, Issue ${Math.ceil(new Date().getMonth() / 3)}, ${new Date().getFullYear()}
          </p>
          <p class="journal-info">
            DOI: 10.1000/joker.${new Date().getFullYear()}.${article.id}
          </p>
        </div>

        <!-- Article Content -->
        <div class="article-content">
          ${clonedContent.innerHTML}
        </div>

        <!-- Footer -->
        <div class="footer">
          <p>© ${new Date().getFullYear()} Joker: Journal of Satirical Science. All rights reserved.</p>
          <p>Generated on ${currentDate}</p>
        </div>
      </body>
      </html>
    `;
  };

  return (
    <button
      onClick={generatePDF}
      disabled={isGenerating || !content}
      className="w-full px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
    >
      {isGenerating ? (
        <>
          <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          准备打印...
        </>
      ) : (
        <>
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          📄 Download PDF
        </>
      )}
    </button>
  );
};
