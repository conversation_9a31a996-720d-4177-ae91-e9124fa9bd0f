const { PrismaClient } = require('@prisma/client');

async function migrateAIConfig() {
  const prisma = new PrismaClient();

  try {
    console.log('🔄 开始迁移AI配置...');

    // 检查是否存在旧的gemini_configs表
    let existingGeminiConfig = null;
    try {
      const result = await prisma.$queryRaw`SELECT name FROM sqlite_master WHERE type='table' AND name='gemini_configs';`;
      if (result.length > 0) {
        console.log('📋 发现现有的gemini_configs表');
        
        // 获取现有配置
        const configs = await prisma.$queryRaw`SELECT * FROM gemini_configs ORDER BY updatedAt DESC LIMIT 1;`;
        if (configs.length > 0) {
          existingGeminiConfig = configs[0];
          console.log('✅ 找到现有Gemini配置:', {
            id: existingGeminiConfig.id,
            isActive: existingGeminiConfig.isActive,
            textModel: existingGeminiConfig.textModel,
            imageModel: existingGeminiConfig.imageModel
          });
        }
      }
    } catch (error) {
      console.log('ℹ️ 没有找到现有的gemini_configs表');
    }

    // 创建新的ai_configs表
    console.log('🏗️ 创建ai_configs表...');
    await prisma.$executeRaw`
      CREATE TABLE IF NOT EXISTS "ai_configs" (
        "id" TEXT NOT NULL PRIMARY KEY,
        "provider" TEXT NOT NULL DEFAULT 'gemini',
        "apiKey" TEXT NOT NULL,
        "baseUrl" TEXT,
        "isActive" BOOLEAN NOT NULL DEFAULT true,
        "textModel" TEXT NOT NULL DEFAULT 'gemini-2.0-flash-exp',
        "imageModel" TEXT NOT NULL DEFAULT 'imagen-3.0-generate-001',
        "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        "updatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `;

    // 如果有现有配置，迁移到新表
    if (existingGeminiConfig) {
      console.log('📦 迁移现有配置到新表...');
      
      // 生成新的ID
      const newId = 'ai_config_' + Date.now();
      
      await prisma.$executeRaw`
        INSERT INTO "ai_configs" (
          "id", "provider", "apiKey", "baseUrl", "isActive", 
          "textModel", "imageModel", "createdAt", "updatedAt"
        ) VALUES (
          ${newId}, 
          'gemini', 
          ${existingGeminiConfig.apiKey}, 
          NULL, 
          ${existingGeminiConfig.isActive}, 
          ${existingGeminiConfig.textModel}, 
          ${existingGeminiConfig.imageModel}, 
          ${existingGeminiConfig.createdAt}, 
          ${existingGeminiConfig.updatedAt}
        );
      `;
      
      console.log('✅ 配置迁移完成');
    } else {
      console.log('ℹ️ 没有现有配置需要迁移');
    }

    // 删除旧表
    try {
      console.log('🗑️ 删除旧的gemini_configs表...');
      await prisma.$executeRaw`DROP TABLE IF EXISTS "gemini_configs";`;
      console.log('✅ 旧表删除完成');
    } catch (error) {
      console.log('ℹ️ 旧表删除跳过:', error.message);
    }

    console.log('🎉 AI配置迁移完成！');

    // 验证迁移结果
    const newConfigs = await prisma.$queryRaw`SELECT * FROM ai_configs;`;
    console.log('📊 迁移后的配置数量:', newConfigs.length);
    if (newConfigs.length > 0) {
      console.log('📋 新配置预览:', {
        provider: newConfigs[0].provider,
        isActive: newConfigs[0].isActive,
        textModel: newConfigs[0].textModel,
        imageModel: newConfigs[0].imageModel
      });
    }

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  migrateAIConfig()
    .then(() => {
      console.log('✅ 迁移脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 迁移脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { migrateAIConfig };
