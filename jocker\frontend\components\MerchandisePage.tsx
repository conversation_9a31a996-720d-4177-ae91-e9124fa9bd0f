import React, { useState, useEffect } from 'react';
import { SharedBackground } from './SharedBackground';
import { isUserAdmin } from '../src/utils/authUtils';
import { adminApi } from '../src/services/api';

interface MerchandisePageProps {
  onNavigateHome: () => void;
}

interface MerchandiseItem {
  id: string;
  title: string;
  subtitle: string;
  description: string[];
  features: string[];
  price: string;
  colors?: string[];
  imageUrl?: string;
}

export const MerchandisePage: React.FC<MerchandisePageProps> = ({ onNavigateHome }) => {
  const [language, setLanguage] = useState<'en' | 'zh'>('en');
  const [isAdmin, setIsAdmin] = useState(false);
  const [uploadingImages, setUploadingImages] = useState<{[key: string]: boolean}>({});
  const [merchandiseImages, setMerchandiseImages] = useState<{[key: string]: string}>({});

  useEffect(() => {
    setIsAdmin(isUserAdmin());
    loadMerchandiseImages();
  }, []);

  const loadMerchandiseImages = async () => {
    try {
      // 这里可以添加API调用来获取已上传的图片
      // const images = await adminApi.getMerchandiseImages();
      // setMerchandiseImages(images);
    } catch (error) {
      console.error('加载周边图片失败:', error);
    }
  };

  const handleImageUpload = async (itemId: string, file: File) => {
    if (!file) return;

    setUploadingImages(prev => ({ ...prev, [itemId]: true }));
    
    try {
      // 这里可以添加图片上传API
      // const imageUrl = await adminApi.uploadMerchandiseImage(itemId, file);
      // setMerchandiseImages(prev => ({ ...prev, [itemId]: imageUrl }));
      
      // 临时模拟上传
      const imageUrl = URL.createObjectURL(file);
      setMerchandiseImages(prev => ({ ...prev, [itemId]: imageUrl }));
      
      alert('图片上传成功！');
    } catch (error) {
      console.error('图片上传失败:', error);
      alert('图片上传失败，请重试');
    } finally {
      setUploadingImages(prev => ({ ...prev, [itemId]: false }));
    }
  };

  const content = {
    en: {
      title: "JOCKER Merchandise",
      subtitle: "Academic Chaos, Now Wearable! 🎪",
      description: "Show your support for questionable research and peer review by clowns with our exclusive merchandise collection.",
      gptCredit: "Product descriptions lovingly crafted by GPT with a PhD in Creative Nonsense 🤖✨",
      items: [
        {
          id: "hat",
          title: "🧢 \"Peer Reviewed by a Clown\" Hat",
          subtitle: "Trucker Cap or Bucket Hat",
          description: [
            "🤡 Icon + Embroidered text:",
            "\"Peer Reviewed by a Clown. Still more rigorous than some journals.\""
          ],
          features: [
            "Hat brim interior features fake review comments like: \"Lack of relevance? More like lack of caring.\"",
            "Available colors: Academic Black / Crisis Red / Ridiculous Purple",
            "One size fits most heads (and egos)",
            "Perfect for conferences where you want to make a statement"
          ],
          price: "$24.99"
        },
        {
          id: "notepad",
          title: "📄 \"Rejection Slip\" Notepad",
          subtitle: "Tear-off Sticky Notes",
          description: [
            "Looks like official journal review papers, actually tear-off sticky notes.",
            "Each page contains content like:"
          ],
          features: [
            "❌ \"We regret to inform you that your paper has been rejected for being too interesting.\"",
            "Reviewer #2: \"This gave me feelings. Please don't.\"",
            "Recommendation: Banish the author to Reviewer Hell.",
            "Perfect for leaving passive-aggressive notes",
            "Great gift for PhD students who need daily reminders of academic reality"
          ],
          price: "$12.99"
        },
        {
          id: "candle",
          title: "🧪 \"Academic Breakdown\" Scented Candle",
          subtitle: "Aromatherapy for the Academically Distressed",
          description: [
            "Available scents:",
            "\"Burnt Thesis\" / \"Reviewer 2's Tears\" / \"Late-night PowerPoint Regret\""
          ],
          features: [
            "Light this candle and fill your room with the comforting aroma of coffee, despair, and unanswered emails.",
            "Hidden Easter egg: Burns halfway to reveal the message: \"You should really be writing.\"",
            "40-hour burn time (perfect for one all-nighter)",
            "Comes with a complimentary existential crisis"
          ],
          price: "$19.99"
        }
      ]
    },
    zh: {
      title: "JOCKER 周边商品",
      subtitle: "学术混乱，现在可穿戴！🎪",
      description: "用我们的独家周边商品系列，展示你对可疑研究和小丑同行评议的支持。",
      gptCredit: "产品描述由拥有创意胡说博士学位的GPT倾情制作 🤖✨",
      items: [
        {
          id: "hat",
          title: "🧢 \"小丑同行评议\" 帽子",
          subtitle: "卡车帽或渔夫帽",
          description: [
            "🤡 图标 + 刺绣文字：",
            "\"小丑同行评议。仍比某些期刊更严格。\""
          ],
          features: [
            "帽檐内侧印有虚假审稿意见，如：\"缺乏相关性？更像是缺乏关心。\"",
            "可选颜色：学术黑 / 危机红 / 滑稽紫",
            "均码适合大多数头部（和自我）",
            "完美适合想要表态的会议场合"
          ],
          price: "¥168"
        },
        {
          id: "notepad",
          title: "📄 \"拒稿单\" 便签本",
          subtitle: "撕页便签纸",
          description: [
            "看起来像正规期刊审稿意见，实际是撕页便签纸。",
            "每页内容类似："
          ],
          features: [
            "❌ \"很遗憾地通知您，您的论文因过于有趣而被拒绝。\"",
            "审稿人#2：\"这让我有了感觉。请不要这样。\"",
            "建议：将作者放逐到审稿人地狱。",
            "完美的被动攻击便条",
            "送给需要每日学术现实提醒的博士生的绝佳礼物"
          ],
          price: "¥88"
        },
        {
          id: "candle",
          title: "🧪 \"学术崩溃\" 香薰蜡烛",
          subtitle: "学术困扰者的芳香疗法",
          description: [
            "可选香型：",
            "\"烧焦的论文\" / \"审稿人2的眼泪\" / \"深夜PPT悔恨\""
          ],
          features: [
            "点燃这支蜡烛，让房间充满咖啡、绝望和未回邮件的舒适香气。",
            "隐藏彩蛋：烧到一半会露出信息：\"你真的应该在写作。\"",
            "40小时燃烧时间（完美适合一个通宵）",
            "附赠存在主义危机"
          ],
          price: "¥128"
        }
      ]
    }
  };

  const currentContent = content[language];

  return (
    <SharedBackground>
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-100">
        <div className="max-w-6xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={onNavigateHome}
                className="flex items-center text-purple-600 hover:text-purple-800 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Home
              </button>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => setLanguage('en')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    language === 'en' 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  EN
                </button>
                <button
                  onClick={() => setLanguage('zh')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    language === 'zh' 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  中文
                </button>
              </div>
            </div>
            
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              🛍️ {currentContent.title}
            </h1>
            <p className="text-xl text-purple-600 mb-4">
              {currentContent.subtitle}
            </p>
            <p className="text-gray-700 max-w-3xl mx-auto leading-relaxed mb-2">
              {currentContent.description}
            </p>
            <p className="text-sm text-gray-500 italic">
              {currentContent.gptCredit}
            </p>
          </div>

          {/* Merchandise Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {currentContent.items.map((item, index) => (
              <div key={item.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                {/* Image Section */}
                <div className="relative h-64 bg-gradient-to-br from-gray-100 to-gray-200">
                  {merchandiseImages[item.id] ? (
                    <img 
                      src={merchandiseImages[item.id]} 
                      alt={item.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <div className="text-6xl mb-2">
                          {item.id === 'hat' ? '🧢' : item.id === 'notepad' ? '📄' : '🧪'}
                        </div>
                        <p className="text-gray-500 text-sm">Product Image</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Admin Upload Button */}
                  {isAdmin && (
                    <div className="absolute top-2 right-2">
                      <label className="bg-blue-600 text-white px-2 py-1 rounded text-xs cursor-pointer hover:bg-blue-700 transition-colors">
                        {uploadingImages[item.id] ? '上传中...' : '上传图片'}
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(item.id, file);
                          }}
                          disabled={uploadingImages[item.id]}
                        />
                      </label>
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-1">
                    {item.title}
                  </h3>
                  <p className="text-purple-600 text-sm mb-3">
                    {item.subtitle}
                  </p>
                  
                  <div className="mb-4">
                    {item.description.map((desc, i) => (
                      <p key={i} className="text-gray-700 text-sm mb-1">
                        {desc}
                      </p>
                    ))}
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2 text-sm">Features:</h4>
                    <ul className="space-y-1">
                      {item.features.map((feature, i) => (
                        <li key={i} className="text-gray-600 text-xs flex items-start">
                          <span className="text-purple-500 mr-1 mt-0.5">•</span>
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-purple-600">
                      {item.price}
                    </span>
                    <button className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm">
                      {language === 'en' ? 'Add to Cart' : '加入购物车'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Disclaimer Section */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 text-center">
              {language === 'en' ? '🚨 Important Disclaimer' : '🚨 重要免责声明'}
            </h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <p className="text-yellow-800 text-sm text-center">
                {language === 'en'
                  ? "⚠️ These products are 100% fictional and exist only in the realm of academic satire. No actual merchandise will be shipped. Your money will remain safely in your wallet, unlike your sanity after reading our journal."
                  : "⚠️ 这些产品100%虚构，仅存在于学术讽刺的领域中。不会发货任何实际商品。你的钱会安全地留在钱包里，不像你读了我们期刊后的理智。"}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl mb-2">🎭</div>
                <h3 className="font-semibold text-purple-800 mb-1">
                  {language === 'en' ? 'Peer Reviewed' : '同行评议'}
                </h3>
                <p className="text-purple-600 text-xs">
                  {language === 'en' ? 'By actual clowns' : '由真正的小丑完成'}
                </p>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl mb-2">🚀</div>
                <h3 className="font-semibold text-blue-800 mb-1">
                  {language === 'en' ? 'Fast Shipping' : '快速发货'}
                </h3>
                <p className="text-blue-600 text-xs">
                  {language === 'en' ? 'Delivered via imagination' : '通过想象力配送'}
                </p>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-2xl mb-2">💰</div>
                <h3 className="font-semibold text-green-800 mb-1">
                  {language === 'en' ? 'Money Back Guarantee' : '退款保证'}
                </h3>
                <p className="text-green-600 text-xs">
                  {language === 'en' ? "We can't take what you don't give" : '我们不能拿走你没给的'}
                </p>
              </div>
            </div>
          </div>

          {/* Fun Footer */}
          <div className="text-center bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-lg p-8 text-white">
            <div className="text-6xl mb-4">🛒</div>
            <h3 className="text-2xl font-bold mb-2">
              {language === 'en' ? 'Ready to Shop?' : '准备购物？'}
            </h3>
            <p className="mb-4 opacity-90">
              {language === 'en'
                ? "Unfortunately, our shopping cart is as fictional as our peer review process!"
                : "不幸的是，我们的购物车和我们的同行评议流程一样虚构！"}
            </p>
            <div className="flex justify-center space-x-4 text-sm">
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                {language === 'en' ? '🎪 100% Satirical' : '🎪 100%讽刺'}
              </span>
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                {language === 'en' ? '🤡 Clown Approved' : '🤡 小丑认证'}
              </span>
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                {language === 'en' ? '📚 Academic Humor' : '📚 学术幽默'}
              </span>
            </div>
            <p className="text-xs mt-4 opacity-75">
              {language === 'en'
                ? "* No actual products, money, or dignity were harmed in the making of this page."
                : "* 在制作此页面过程中，没有真正的产品、金钱或尊严受到伤害。"}
            </p>
          </div>
        </div>
      </div>
    </SharedBackground>
  );
};
