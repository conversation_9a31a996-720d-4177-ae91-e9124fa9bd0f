import React, { useState, useEffect } from 'react';
import { SharedBackground } from './SharedBackground';
import { isUserAdmin } from '../src/utils/authUtils';
import { adminApi } from '../src/services/api';
import { CustomList } from './CustomListItem';
import { MockShoppingCartModal } from './MockShoppingCartModal';

interface MerchandisePageProps {
  onNavigateHome: () => void;
}

interface MerchandiseItem {
  id: string;
  title: string;
  subtitle: string;
  description: string[];
  features: string[];
  price: string;
  colors?: string[];
  imageUrl?: string;
}

export const MerchandisePage: React.FC<MerchandisePageProps> = ({ onNavigateHome }) => {
  const [language, setLanguage] = useState<'en' | 'zh'>('en');
  const [isAdmin, setIsAdmin] = useState(false);
  const [uploadingImages, setUploadingImages] = useState<{[key: string]: boolean}>({});
  const [merchandiseImages, setMerchandiseImages] = useState<{[key: string]: string}>({});
  const [showCartModal, setShowCartModal] = useState(false);
  const [selectedItem, setSelectedItem] = useState<{ name: string; price: string } | null>(null);

  useEffect(() => {
    setIsAdmin(isUserAdmin());
    loadMerchandiseImages();
  }, []);

  const loadMerchandiseImages = async () => {
    try {
      const response = await adminApi.getMerchandiseImages();
      setMerchandiseImages(response.images || {});
    } catch (error) {
      console.error('加载周边图片失败:', error);
    }
  };

  const handleImageUpload = async (itemId: string, file: File) => {
    if (!file) return;

    setUploadingImages(prev => ({ ...prev, [itemId]: true }));

    try {
      // 将文件转换为base64
      const reader = new FileReader();
      reader.onload = async (e) => {
        const base64 = e.target?.result as string;

        try {
          await adminApi.uploadMerchandiseImage({
            itemId,
            imageUrl: base64,
            title: `${itemId} 商品图片`
          });

          // 重新加载图片
          await loadMerchandiseImages();
          alert('图片上传成功！');
        } catch (uploadError) {
          console.error('图片上传失败:', uploadError);
          alert('图片上传失败，请重试');
        } finally {
          setUploadingImages(prev => ({ ...prev, [itemId]: false }));
        }
      };

      reader.readAsDataURL(file);
    } catch (error) {
      console.error('图片处理失败:', error);
      alert('图片处理失败，请重试');
      setUploadingImages(prev => ({ ...prev, [itemId]: false }));
    }
  };

  const handleAddToCart = (itemTitle: string, itemPrice: string) => {
    setSelectedItem({ name: itemTitle, price: itemPrice });
    setShowCartModal(true);
  };

  const handleCloseModal = () => {
    setShowCartModal(false);
    setSelectedItem(null);
  };

  const content = {
    en: {
      title: "JOCKER Merchandise",
      subtitle: "Academic Chaos, Now Wearable! 🎪",
      description: "Show your support for questionable research and peer review by clowns with our exclusive merchandise collection.",
      gptCredit: "Product descriptions lovingly crafted by GPT with a PhD in Creative Nonsense 🤖✨",
      items: [
        {
          id: "hat",
          title: "🧢 \"Peer Reviewed by a Clown\" Hat",
          subtitle: "Trucker Cap or Bucket Hat",
          description: [
            "🤡 Icon + Embroidered text:",
            "\"Peer Reviewed by a Clown. Still more rigorous than some journals.\""
          ],
          features: [
            "Hat brim interior features fake review comments like: \"Lack of relevance? More like lack of caring.\"",
            "Available colors: Academic Black / Crisis Red / Ridiculous Purple",
            "One size fits most heads (and egos)",
            "Perfect for conferences where you want to make a statement"
          ],
          price: "$24.99"
        },
        {
          id: "bag",
          title: "🎒 Grey Parrot Crossbody Bag",
          subtitle: "\"Carry Your Intelligence Online\"",
          description: [
            "Shaped like an African Grey Parrot, wings are two small pockets",
            "Printed with: \"I'm not a bird, I'm the intelligence officer\""
          ],
          features: [
            "Funny detail: \"Voice recognition failed...\" warning label",
            "Two wing pockets for storing your academic dignity",
            "Adjustable crossbody strap for hands-free existential crisis",
            "Water-resistant material (tears not included)",
            "Perfect size for carrying rejection letters and broken dreams"
          ],
          price: "$34.99"
        },
        {
          id: "plushie",
          title: "🧸 \"Lying Flat Guide\" Plush Toy",
          subtitle: "Grey Parrot's Sleep Specialist",
          description: [
            "Grey parrot lying flat + printed with \"Already wasted today, please do not disturb\"",
            "Has recording function, says \"I'm not lazy, I'm storing energy\""
          ],
          features: [
            "Premium soft plush material for maximum comfort during academic breakdowns",
            "Built-in voice recorder with 10-second message capacity",
            "Pre-recorded motivational phrases like \"Productivity is overrated\"",
            "Perfect pillow for power naps between paper rejections",
            "Comes with a PhD in Advanced Procrastination"
          ],
          price: "$29.99"
        }
      ]
    },
    zh: {
      title: "JOCKER 周边商品",
      subtitle: "学术混乱，现在可穿戴！🎪",
      description: "用我们的独家周边商品系列，展示你对可疑研究和小丑同行评议的支持。",
      gptCredit: "产品描述由拥有创意胡说博士学位的GPT倾情制作 🤖✨",
      items: [
        {
          id: "hat",
          title: "🧢 \"小丑同行评议\" 帽子",
          subtitle: "卡车帽或渔夫帽",
          description: [
            "🤡 图标 + 刺绣文字：",
            "\"小丑同行评议。仍比某些期刊更严格。\""
          ],
          features: [
            "帽檐内侧印有虚假审稿意见，如：\"缺乏相关性？更像是缺乏关心。\"",
            "可选颜色：学术黑 / 危机红 / 滑稽紫",
            "均码适合大多数头部（和自我）",
            "完美适合想要表态的会议场合"
          ],
          price: "¥168"
        },
        {
          id: "bag",
          title: "🎒 灰鹦鹉斜挎小包",
          subtitle: "\"随身带着你的智商上线\"",
          description: [
            "外形是一只非洲灰鹦鹉，翅膀是两个小口袋",
            "包上印着\"我不是鸟，我是智商担当\""
          ],
          features: [
            "搞笑细节：\"智能语音识别失败中...\"的提示语标签",
            "两个翅膀口袋，用于存放你的学术尊严",
            "可调节斜挎带，解放双手进行存在主义危机",
            "防水材料（不包括眼泪）",
            "完美尺寸，可装下拒稿信和破碎的梦想"
          ],
          price: "¥238"
        },
        {
          id: "plushie",
          title: "🧸 \"躺平指南\" 毛绒玩具",
          subtitle: "灰鹦鹉的睡觉专家",
          description: [
            "灰鹦鹉摊在地上+印着\"今日已废，请勿打扰\"",
            "有录音功能，会说\"我不是懒，我在蓄力\""
          ],
          features: [
            "优质柔软毛绒材料，在学术崩溃时提供最大舒适度",
            "内置录音器，可录制10秒信息",
            "预录励志短语如\"生产力被高估了\"",
            "完美的枕头，适合在论文被拒后小憩",
            "附赠高级拖延症博士学位"
          ],
          price: "¥198"
        }
      ]
    }
  };

  const currentContent = content[language];

  return (
    <SharedBackground>
      <div className="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-100">
        <div className="max-w-6xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-between mb-4">
              <button
                onClick={onNavigateHome}
                className="flex items-center text-purple-600 hover:text-purple-800 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Home
              </button>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => setLanguage('en')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    language === 'en' 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  EN
                </button>
                <button
                  onClick={() => setLanguage('zh')}
                  className={`px-3 py-1 rounded-full text-sm transition-colors ${
                    language === 'zh' 
                      ? 'bg-purple-600 text-white' 
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  中文
                </button>
              </div>
            </div>
            
            <h1 className="text-4xl font-bold text-gray-900 mb-2">
              🛍️ {currentContent.title}
            </h1>
            <p className="text-xl text-purple-600 mb-4">
              {currentContent.subtitle}
            </p>
            <p className="text-gray-700 max-w-3xl mx-auto leading-relaxed mb-2">
              {currentContent.description}
            </p>
            <p className="text-sm text-gray-500 italic">
              {currentContent.gptCredit}
            </p>
          </div>

          {/* Merchandise Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-8">
            {currentContent.items.map((item, index) => (
              <div key={item.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                {/* Image Section */}
                <div className="relative h-64 bg-gradient-to-br from-gray-100 to-gray-200">
                  {merchandiseImages[item.id] ? (
                    <img 
                      src={merchandiseImages[item.id]} 
                      alt={item.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <div className="text-center">
                        <div className="text-6xl mb-2">
                          {item.id === 'hat' ? '🧢' : item.id === 'bag' ? '🎒' : '🧸'}
                        </div>
                        <p className="text-gray-500 text-sm">Product Image</p>
                      </div>
                    </div>
                  )}
                  
                  {/* Admin Upload Button */}
                  {isAdmin && (
                    <div className="absolute top-2 right-2">
                      <label className="bg-blue-600 text-white px-2 py-1 rounded text-xs cursor-pointer hover:bg-blue-700 transition-colors">
                        {uploadingImages[item.id] ? '上传中...' : '上传图片'}
                        <input
                          type="file"
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleImageUpload(item.id, file);
                          }}
                          disabled={uploadingImages[item.id]}
                        />
                      </label>
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-1">
                    {item.title}
                  </h3>
                  <p className="text-purple-600 text-sm mb-3">
                    {item.subtitle}
                  </p>
                  
                  <div className="mb-4">
                    {item.description.map((desc, i) => (
                      <p key={i} className="text-gray-700 text-sm mb-1">
                        {desc}
                      </p>
                    ))}
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2 text-sm">
                      {language === 'en' ? 'Features:' : '特色功能：'}
                    </h4>
                    <CustomList
                      items={item.features}
                      icon="•"
                      color="text-purple-500"
                      className="space-y-1"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-2xl font-bold text-purple-600">
                      {item.price}
                    </span>
                    <button
                      onClick={() => handleAddToCart(item.title, item.price)}
                      className="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-sm transform hover:scale-105 active:scale-95"
                    >
                      {language === 'en' ? 'Add to Cart' : '加入购物车'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Disclaimer Section */}
          <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4 text-center">
              {language === 'en' ? '🚨 Important Disclaimer' : '🚨 重要免责声明'}
            </h2>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <p className="text-yellow-800 text-sm text-center">
                {language === 'en'
                  ? "⚠️ These products are 100% fictional and exist only in the realm of academic satire. No actual merchandise will be shipped. Your money will remain safely in your wallet, unlike your sanity after reading our journal."
                  : "⚠️ 这些产品100%虚构，仅存在于学术讽刺的领域中。不会发货任何实际商品。你的钱会安全地留在钱包里，不像你读了我们期刊后的理智。"}
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl mb-2">🎭</div>
                <h3 className="font-semibold text-purple-800 mb-1">
                  {language === 'en' ? 'Peer Reviewed' : '同行评议'}
                </h3>
                <p className="text-purple-600 text-xs">
                  {language === 'en' ? 'By actual clowns' : '由真正的小丑完成'}
                </p>
              </div>

              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl mb-2">🚀</div>
                <h3 className="font-semibold text-blue-800 mb-1">
                  {language === 'en' ? 'Fast Shipping' : '快速发货'}
                </h3>
                <p className="text-blue-600 text-xs">
                  {language === 'en' ? 'Delivered via imagination' : '通过想象力配送'}
                </p>
              </div>

              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-2xl mb-2">💰</div>
                <h3 className="font-semibold text-green-800 mb-1">
                  {language === 'en' ? 'Money Back Guarantee' : '退款保证'}
                </h3>
                <p className="text-green-600 text-xs">
                  {language === 'en' ? "We can't take what you don't give" : '我们不能拿走你没给的'}
                </p>
              </div>
            </div>
          </div>

          {/* Fun Footer */}
          <div className="text-center bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg shadow-lg p-8 text-white">
            <div className="text-6xl mb-4">🛒</div>
            <h3 className="text-2xl font-bold mb-2">
              {language === 'en' ? 'Ready to Shop?' : '准备购物？'}
            </h3>
            <p className="mb-4 opacity-90">
              {language === 'en'
                ? "Unfortunately, our shopping cart is as fictional as our peer review process!"
                : "不幸的是，我们的购物车和我们的同行评议流程一样虚构！"}
            </p>
            <div className="flex justify-center space-x-4 text-sm">
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                {language === 'en' ? '🎪 100% Satirical' : '🎪 100%讽刺'}
              </span>
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                {language === 'en' ? '🤡 Clown Approved' : '🤡 小丑认证'}
              </span>
              <span className="bg-white bg-opacity-20 px-3 py-1 rounded-full">
                {language === 'en' ? '📚 Academic Humor' : '📚 学术幽默'}
              </span>
            </div>
            <p className="text-xs mt-4 opacity-75">
              {language === 'en'
                ? "* No actual products, money, or dignity were harmed in the making of this page."
                : "* 在制作此页面过程中，没有真正的产品、金钱或尊严受到伤害。"}
            </p>
          </div>
        </div>
      </div>

      {/* 恶搞购物车弹窗 */}
      {selectedItem && (
        <MockShoppingCartModal
          isOpen={showCartModal}
          onClose={handleCloseModal}
          itemName={selectedItem.name}
          itemPrice={selectedItem.price}
          language={language}
        />
      )}
    </SharedBackground>
  );
};
