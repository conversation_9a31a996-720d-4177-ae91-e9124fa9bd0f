import React, { useState, useEffect } from 'react';
import { SearchIcon } from './icons';
import { aiApi, articleApi, adminApi } from '../src/services/api';
import { aiService } from '../src/services/aiService';
import { UserProfileCard } from './UserProfileCard';

interface HeaderProps {
    onNavigateHome: () => void;
    onNavigateToSubmit: () => void;
    onSearch: (query: string) => void;
    onArticleCreated?: () => void; // 新增：文章创建后的回调
    onNavigateToLogin?: () => void; // 新增：导航到登录页面
    onNavigateToChangelog?: () => void; // 新增：导航到更新日志页面
    onNavigateToResearchArticles?: () => void; // 新增：导航到研究文章页面
    onNavigateToNewsComment?: () => void; // 新增：导航到新闻评论页面
    onNavigateToAbout?: () => void; // 新增：导航到关于页面
    isLoggedIn?: boolean; // 新增：登录状态
    onLogout?: () => void; // 新增：登出功能
    onNavigateToAdmin?: () => void; // 新增：导航到后台管理
    isAdmin?: boolean; // 新增：管理员状态
}

export const Header: React.FC<HeaderProps> = ({
  onNavigateHome,
  onNavigateToSubmit,
  onSearch,
  onArticleCreated,
  onNavigateToLogin,
  onNavigateToChangelog,
  onNavigateToResearchArticles,
  onNavigateToNewsComment,
  onNavigateToAbout,
  isLoggedIn,
  onLogout,
  onNavigateToAdmin,
  isAdmin
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showGenerateModal, setShowGenerateModal] = useState<boolean>(false);
  const [articleTheme, setArticleTheme] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [journalIcon, setJournalIcon] = useState<string>('🤡');

  // 添加缺失的状态变量
  const [wordCount, setWordCount] = useState<string>('');
  const [figureCount, setFigureCount] = useState<string>('');

  // 获取当前用户信息和期刊图标
  useEffect(() => {
    const userStr = localStorage.getItem('jocker_admin_user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        setCurrentUser(user);
      } catch (error) {
        console.error('解析用户信息失败:', error);
      }
    }

    // 加载期刊图标
    const loadJournalIcon = async () => {
      try {
        const response = await adminApi.getJournalIcon();
        if (response.icon) {
          setJournalIcon(response.icon);
        }
      } catch (error) {
        // 使用默认图标
        console.log('使用默认期刊图标');
      }
    };

    loadJournalIcon();
  }, [isLoggedIn]);

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      onSearch(searchQuery.trim());
      setSearchQuery('');
    }
  };

  const handleShowGenerateModal = () => {
    setShowGenerateModal(true);
    setArticleTheme('');
    setWordCount('');
    setFigureCount('');
  };

  const handleGenerateAndSaveArticle = async (theme: string) => {
    try {
      setIsGenerating(true);
      setShowGenerateModal(false);

      // 检查管理员权限并获取 API Key
      if (!isLoggedIn) {
        throw new Error('只有管理员可以生成文章');
      }

      // 重置AI服务配置缓存，确保使用最新配置
      aiService.resetConfig();

      const themeText = theme.trim() ? `Theme/Topic: "${theme}"` : 'Choose any mundane or silly topic';

      const prompt = `
        Create 1 article for the satirical scientific journal "Jocker".
        This article should be about mundane or silly topics, but treated with extreme academic seriousness.
        ${themeText}

        This is for generating the basic article information (title, author, abstract, cover image).
        The detailed content will be generated later when the user opens the article.

        Requirements:
        - Focus specifically on the provided theme/topic if given
        - Maintain satirical academic tone while staying true to the theme
        - Use proper academic language for the abstract
        - Create a compelling abstract that summarizes the "research"

        Please provide:
        - "id": 1
        - "category": short category name in English (related to the theme)
        - "title": witty academic title in English (directly related to the theme)
        - "author": funny author name in English (e.g., puns)
        - "excerpt": brief academic abstract in English (2-3 sentences, highlighting the theme and methodology)
        - "image_prompt": descriptive prompt for realistic AI image generator related to the specific article theme, in English

        Return only a valid JSON object.
        Important: Ensure the output is a single valid JSON object with no other content.
        All string values must be enclosed in double quotes.
      `;

      const textResponse = await aiService.generateText({
        prompt,
        responseFormat: 'json'
      });

      let jsonStr = textResponse.trim();

      // 🔍 调试：打印原始AI响应
      console.log('🤖 AI原始响应:', jsonStr);

      const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
      const match = jsonStr.match(fenceRegex);
      if (match && match[2]) {
        jsonStr = match[2].trim();
        console.log('🔧 移除代码块标记后:', jsonStr);
      }

      let rawArticle;
      try {
        rawArticle = JSON.parse(jsonStr);
        console.log('✅ JSON解析成功:', rawArticle);
      } catch (parseError) {
        console.error('❌ JSON解析失败:', parseError);
        console.log('🔍 尝试解析的字符串:', jsonStr);
        throw new Error('AI返回的不是有效的JSON格式');
      }

      // 状态更新将通过弹窗显示

      // 生成图片
      const imageUrl = await aiService.generateImage({
        prompt: rawArticle.image_prompt,
        numberOfImages: 1,
        aspectRatio: '1:1'
      });

      // 保存到后端

      // 准备发送到后端的数据
      const articleData = {
        title: rawArticle.title,
        author: rawArticle.author,
        category: rawArticle.category,
        excerpt: rawArticle.excerpt,
        imageUrl,
        imagePrompt: rawArticle.image_prompt,
        published: true,
        featured: false,
      };

      // 🔍 调试：打印发送到后端的数据
      console.log('📤 发送到后端的数据:', articleData);

      // 验证必需字段
      const requiredFields = ['title', 'author', 'category', 'excerpt'];
      const missingFields = requiredFields.filter(field => !articleData[field] || articleData[field].trim() === '');
      if (missingFields.length > 0) {
        console.error('❌ 缺少必需字段:', missingFields);
        throw new Error(`缺少必需字段: ${missingFields.join(', ')}`);
      }

      // 保存到后端
      const savedArticle = await articleApi.createArticle(articleData);

      // 通知父组件刷新文章列表
      if (onArticleCreated) {
        onArticleCreated();
      }

      alert(`✅ 文章创建成功: ${savedArticle.title}`);

    } catch (error) {
      console.error('❌ 生成并保存文章失败:', error);

      // 详细错误信息
      if (error instanceof Error) {
        console.error('🔍 错误详情:', {
          message: error.message,
          stack: error.stack
        });

        // 如果是API错误，显示更具体的信息
        if (error.message.includes('请求数据验证失败')) {
          alert('数据验证失败：AI生成的内容格式不正确，请重试');
        } else {
          alert(`生成文章失败：${error.message}`);
        }
      } else {
        alert('生成文章失败，请重试');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  const NavButton: React.FC<{onClick: () => void, children: React.ReactNode}> = ({ onClick, children }) => (
    <button onClick={onClick} className="text-base font-medium text-gray-500 hover:text-gray-900">
        {children}
    </button>
  );

  return (
    <header className="bg-white shadow-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center py-4 border-b-2 border-gray-100">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button onClick={onNavigateHome} className="relative">
              <span className="sr-only">Jocker</span>
              <div className="relative flex items-center">
                {/* 背景图标 */}
                <div className="absolute inset-0 flex items-center justify-center opacity-20 text-6xl pointer-events-none">
                  {journalIcon}
                </div>
                {/* 前景文字 */}
                <h1 className="relative text-4xl font-black text-gray-800 font-serif tracking-tighter px-4 py-2">
                  Jocker
                </h1>
              </div>
            </button>
          </div>

          {/* Navigation - Left aligned */}
          <nav className="hidden md:flex space-x-8 ml-10">
            <NavButton onClick={onNavigateToResearchArticles}>Research Articles</NavButton>
            <NavButton onClick={onNavigateToNewsComment}>News & Comment</NavButton>
            <NavButton onClick={onNavigateToChangelog}>Changelog</NavButton>
            <NavButton onClick={onNavigateToSubmit}>Submit</NavButton>
            <NavButton onClick={onNavigateToAbout}>About</NavButton>
          </nav>

          {/* Right side - Search and user actions */}
          <div className="flex items-center ml-auto space-x-4">
             <form onSubmit={handleSearchSubmit} className="relative">
                <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search articles..."
                    className="hidden sm:block bg-gray-100 border-2 border-gray-200 rounded-full py-2 pl-4 pr-10 focus:outline-none focus:bg-white focus:border-purple-500 transition-all duration-300 w-40 focus:w-64"
                />
                <button type="submit" className="absolute right-0 top-0 mt-3 mr-3 text-gray-500 hover:text-purple-700">
                    <SearchIcon />
                </button>
             </form>
            {isLoggedIn ? (
              <>
                {/* 管理员专用功能 */}
                {isAdmin && (
                  <>
                    <button
                      onClick={handleShowGenerateModal}
                      disabled={isGenerating}
                      className="ml-2 whitespace-nowrap inline-flex items-center justify-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isGenerating ? '🔄 生成中...' : '🤖 生成文章'}
                    </button>
                    <button
                      onClick={onNavigateToAdmin}
                      className="ml-2 whitespace-nowrap inline-flex items-center justify-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                    >
                      🔧 后台
                    </button>
                  </>
                )}

                {/* 所有登录用户都有的功能 */}
                <button
                  onClick={() => setShowUserProfile(true)}
                  className="ml-2 text-sm text-gray-600 hover:text-gray-800 transition-colors flex items-center space-x-2 bg-gray-50 hover:bg-gray-100 px-3 py-2 rounded-lg"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {currentUser?.username ? currentUser.username.charAt(0).toUpperCase() : (isAdmin ? 'A' : 'U')}
                  </div>
                  <div className="text-left">
                    <div className="font-medium">
                      欢迎，{currentUser?.username || (isAdmin ? 'admin管理员' : '用户')}
                    </div>
                    {isAdmin && <div className="text-xs text-red-600">管理员权限</div>}
                  </div>
                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                <button
                  onClick={onLogout}
                  className="ml-2 whitespace-nowrap inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-red-600 hover:bg-red-700"
                >
                  登出
                </button>
              </>
            ) : (
              <button
                onClick={onNavigateToLogin}
                className="ml-2 whitespace-nowrap inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-purple-700 hover:bg-purple-800"
              >
                登录
              </button>
            )}
          </div>
        </div>
      </div>

      {/* 生成文章弹窗 */}
      {showGenerateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-lg w-full mx-4">
            <h3 className="text-lg font-medium text-gray-900 mb-4">生成新文章</h3>

            <div className="mb-4">
              <label htmlFor="theme" className="block text-sm font-medium text-gray-700 mb-2">
                文章主题 (可选)
              </label>
              <input
                id="theme"
                type="text"
                value={articleTheme}
                onChange={(e) => setArticleTheme(e.target.value)}
                placeholder="例如：咖啡杯的科学研究、袜子失踪现象..."
                className="w-full border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              />
              <p className="mt-2 text-sm text-gray-500">
                💡 留空将随机生成主题。AI 将围绕您的主题构建文章内容。
              </p>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
                📝 <strong>提示：</strong>这里只生成文章的基本信息（标题、作者、摘要、封面图）。
                文章的详细内容和图片将在您打开文章详情页时生成，届时可以自定义字数和图片数量。
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowGenerateModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                取消
              </button>
              <button
                onClick={() => handleGenerateAndSaveArticle(articleTheme)}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                生成文章基本信息
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 用户信息卡片 */}
      {showUserProfile && currentUser && (
        <UserProfileCard
          isVisible={showUserProfile}
          onClose={() => setShowUserProfile(false)}
          userInfo={{
            id: currentUser.id,
            username: currentUser.username,
            name: currentUser.name,
            role: currentUser.role || (isAdmin ? 'ADMIN' : 'USER'),
            createdAt: currentUser.createdAt || new Date().toISOString(),
            lastLoginAt: currentUser.lastLoginAt
          }}
        />
      )}
    </header>
  );
};