import React, { useState } from 'react';
import { SharedBackground } from './SharedBackground';

interface PressPageProps {
  onNavigateHome: () => void;
}

export const PressPage: React.FC<PressPageProps> = ({ onNavigateHome }) => {
  const [language, setLanguage] = useState<'en' | 'zh'>('en');

  const content = {
    en: {
      title: "Press Coverage",
      subtitle: "What the World is Saying About JOCKER 📰",
      description: "From prestigious academic journals to confused readers worldwide, JOCKER has been making waves in the scientific community. Here's what they're saying about us (and what we're saying they said about us).",
      
      sections: [
        {
          category: "🏆 Prestigious Academic Endorsements",
          items: [
            {
              source: "Nature (Former Editor-in-Chief)",
              quote: "We can't compete with them. Their peer review process involves actual clowns, and somehow it's more rigorous than ours.",
              date: "March 2024",
              verified: false
            },
            {
              source: "Science Magazine Editorial Board",
              quote: "JOCKER has revolutionized academic publishing by proving that satire and scholarship can coexist. We're considering hiring their clown reviewers.",
              date: "February 2024", 
              verified: false
            },
            {
              source: "Cell Press CEO",
              quote: "I've never seen anything like it. They publish papers on 'The Aerodynamics of Flying Pigs' and somehow it's more cited than our cancer research.",
              date: "January 2024",
              verified: false
            }
          ]
        },
        {
          category: "📺 Media Coverage",
          items: [
            {
              source: "BBC Science Correspondent",
              quote: "JOCKER represents either the future of academic publishing or the complete breakdown of scientific discourse. Possibly both.",
              date: "April 2024",
              verified: false
            },
            {
              source: "The Guardian Higher Education",
              quote: "Finally, a journal that admits what we all knew: academic publishing is a circus. At least JOCKER embraces it.",
              date: "March 2024",
              verified: false
            },
            {
              source: "Scientific American",
              quote: "We tried to debunk JOCKER's methodology, but their clown peer reviewers made more sense than our editorial board.",
              date: "February 2024",
              verified: false
            }
          ]
        },
        {
          category: "👥 Reader Testimonials",
          items: [
            {
              source: "Dr. Anonymous, Harvard",
              quote: "I laugh while learning, and learn while questioning my entire existence. JOCKER has ruined me for serious academic journals.",
              date: "Ongoing",
              verified: true
            },
            {
              source: "Graduate Student, MIT",
              quote: "I cited a JOCKER paper in my thesis as a joke. My advisor loved it more than my actual research.",
              date: "May 2024",
              verified: true
            },
            {
              source: "Professor Emeritus, Oxford",
              quote: "In 40 years of academia, I've never seen anything so brilliantly absurd. I'm considering switching fields to Clown Studies.",
              date: "April 2024",
              verified: true
            },
            {
              source: "Confused Undergraduate",
              quote: "I thought this was a real journal until I read the paper on 'Quantum Mechanics of Rubber Ducks.' Still more understandable than my textbook.",
              date: "March 2024",
              verified: true
            }
          ]
        },
        {
          category: "🤡 Industry Recognition",
          items: [
            {
              source: "International Clown Association",
              quote: "JOCKER has elevated clowning to an academic discipline. We're proud to have our members on their peer review board.",
              date: "June 2024",
              verified: true
            },
            {
              source: "Circus Performers Union",
              quote: "Finally, a journal that understands that academia is just another form of performance art. Bravo!",
              date: "May 2024",
              verified: true
            }
          ]
        }
      ],
      
      disclaimer: "* Some quotes may be entirely fictional, but the sentiment is probably accurate.",
      contact: "For press inquiries, please contact our Media Relations <NAME_EMAIL>",
      note: "📝 Note: All press coverage is subject to review by our Editorial Circus. Fact-checking is performed by trained seals."
    },
    zh: {
      title: "媒体报道",
      subtitle: "全世界对《JOCKER》的评价 📰", 
      description: "从权威学术期刊到困惑的读者，《JOCKER》在科学界掀起了波澜。以下是他们对我们的评价（以及我们说他们对我们的评价）。",
      
      sections: [
        {
          category: "🏆 权威学术认可",
          items: [
            {
              source: "《自然》杂志（前主编）",
              quote: "我们打不过他们。他们的同行评议流程真的有小丑参与，但不知为何比我们的更严格。",
              date: "2024年3月",
              verified: false
            },
            {
              source: "《科学》杂志编委会",
              quote: "《JOCKER》通过证明讽刺和学术可以共存，革命性地改变了学术出版。我们正在考虑聘请他们的小丑审稿人。",
              date: "2024年2月",
              verified: false
            },
            {
              source: "Cell出版社CEO",
              quote: "我从未见过这样的事。他们发表《飞猪的空气动力学》这样的论文，引用量竟然比我们的癌症研究还高。",
              date: "2024年1月",
              verified: false
            }
          ]
        },
        {
          category: "📺 媒体报道",
          items: [
            {
              source: "BBC科学记者",
              quote: "《JOCKER》代表了学术出版的未来，或者是科学话语的完全崩溃。可能两者皆是。",
              date: "2024年4月",
              verified: false
            },
            {
              source: "《卫报》高等教育版",
              quote: "终于有一本期刊承认了我们都知道的事实：学术出版就是一个马戏团。至少《JOCKER》拥抱了这一点。",
              date: "2024年3月",
              verified: false
            },
            {
              source: "《科学美国人》",
              quote: "我们试图揭穿《JOCKER》的方法论，但他们的小丑同行评议员比我们的编委会更有道理。",
              date: "2024年2月",
              verified: false
            }
          ]
        },
        {
          category: "👥 读者感言",
          items: [
            {
              source: "匿名博士，哈佛大学",
              quote: "我边笑边学习，边学习边质疑自己的整个存在。《JOCKER》让我再也无法认真对待严肃的学术期刊了。",
              date: "持续中",
              verified: true
            },
            {
              source: "研究生，MIT",
              quote: "我开玩笑地在论文中引用了《JOCKER》的文章。我的导师比我的实际研究更喜欢它。",
              date: "2024年5月",
              verified: true
            },
            {
              source: "荣誉教授，牛津大学",
              quote: "在40年的学术生涯中，我从未见过如此精彩荒诞的东西。我正在考虑转行研究小丑学。",
              date: "2024年4月",
              verified: true
            },
            {
              source: "困惑的本科生",
              quote: "我以为这是真正的期刊，直到我读到《橡皮鸭的量子力学》。但仍然比我的教科书更容易理解。",
              date: "2024年3月",
              verified: true
            }
          ]
        },
        {
          category: "🤡 行业认可",
          items: [
            {
              source: "国际小丑协会",
              quote: "《JOCKER》将小丑表演提升为学术学科。我们很自豪有会员在他们的同行评议委员会任职。",
              date: "2024年6月",
              verified: true
            },
            {
              source: "马戏团表演者工会",
              quote: "终于有一本期刊理解学术界只是另一种表演艺术形式。太棒了！",
              date: "2024年5月",
              verified: true
            }
          ]
        }
      ],
      
      disclaimer: "* 部分引用可能完全虚构，但情感可能是准确的。",
      contact: "如需媒体咨询，请联系我们的媒体关系小丑：<EMAIL>",
      note: "📝 注意：所有媒体报道均需经过我们的编辑马戏团审查。事实核查由训练有素的海豹执行。"
    }
  };

  const currentContent = content[language];

  return (
    <div className="min-h-screen">
      <SharedBackground />
      
      <div className="relative z-10 min-h-screen">
        {/* Header */}
        <header className="bg-white/90 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
          <div className="max-w-6xl mx-auto px-4 py-4">
            <div className="flex items-center justify-between">
              <button
                onClick={onNavigateHome}
                className="flex items-center space-x-2 text-purple-600 hover:text-purple-800 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                <span className="font-medium">Back to Journal</span>
              </button>
              
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setLanguage(language === 'en' ? 'zh' : 'en')}
                  className="px-3 py-1 bg-purple-100 text-purple-700 rounded-full text-sm hover:bg-purple-200 transition-colors"
                >
                  {language === 'en' ? '中文' : 'English'}
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="max-w-4xl mx-auto px-4 py-12">
          {/* Title Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              {currentContent.title}
            </h1>
            <p className="text-xl text-purple-600 mb-6">
              {currentContent.subtitle}
            </p>
            <p className="text-gray-700 leading-relaxed max-w-3xl mx-auto">
              {currentContent.description}
            </p>
          </div>

          {/* Press Coverage Sections */}
          <div className="space-y-12">
            {currentContent.sections.map((section, sectionIndex) => (
              <div key={sectionIndex} className="bg-white rounded-lg shadow-lg p-8">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">
                  {section.category}
                </h2>
                
                <div className="space-y-6">
                  {section.items.map((item, itemIndex) => (
                    <div key={itemIndex} className="border-l-4 border-purple-500 pl-6 py-4 bg-gray-50 rounded-r-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h3 className="font-semibold text-gray-900 flex items-center">
                            {item.source}
                            {item.verified ? (
                              <span className="ml-2 text-green-500 text-sm">✓ Verified</span>
                            ) : (
                              <span className="ml-2 text-orange-500 text-sm">⚠ Unverified</span>
                            )}
                          </h3>
                          <p className="text-sm text-gray-500">{item.date}</p>
                        </div>
                      </div>
                      
                      <blockquote className="text-gray-700 italic text-lg leading-relaxed">
                        "{item.quote}"
                      </blockquote>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>

          {/* Footer Notes */}
          <div className="mt-12 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div className="space-y-4 text-sm text-gray-700">
              <p className="font-medium text-yellow-800">
                {currentContent.disclaimer}
              </p>
              <p>
                {currentContent.contact}
              </p>
              <p className="italic">
                {currentContent.note}
              </p>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
